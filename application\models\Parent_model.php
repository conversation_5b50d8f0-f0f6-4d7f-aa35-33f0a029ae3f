<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Parent_model extends CI_Model
{
	private $yearId;
 	private $current_branch;
	public function __construct()
	{
		parent::__construct();
		$this->yearId = $this->acad_year->getAcadYearId();
	 	$this->current_branch = $this->authorization->getCurrentBranch();
		date_default_timezone_set('Asia/Kolkata');
        //$this->load->library('filemanager');
	}
	public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }
    
	public function getStudentIdOfLoggedInParent()
	{
		$parentAvatarId = $this->authorization->getAvatarId();
		if (empty($parentAvatarId)) {
			log_message('error', 'parent_model->getStudentIdOfLoggedInParent: Invalid parent avatar ID');
			return 0;
		}

		//Get Student Id of the logged in parent
		$studentRecord = $this->db_readonly->select('sr.std_id as studentId')
			->from('student_relation sr')
			->join('parent p', 'sr.relation_id=p.id')
			->join('avatar a', 'a.stakeholder_id=p.id')
			->where('a.avatar_type', '2')
			->where('a.id', $parentAvatarId)
			->get()->row();

			if(!empty($studentRecord->studentId)){
				return $studentRecord->studentId;
			}else{
				log_message('error', 'parent_model->getStudentIdOfLoggedInParent: Student ID not found');
				return 0;
			}
	}


	//Get next N events for parents. fetches from cache, cache refreshes avery 3 hours
	public function getParentEvents($student_board, $n)
	{
		$event_updated_at = $this->session->userdata('event_updated_at');
		$time = date('d-m-Y H:i:s');
		if(!$event_updated_at) {
			//to force update at first call
			$event_updated_at = date('d-m-Y H:i:s', strtotime('-181 minutes', strtotime($time)));
			$this->session->set_userdata('event_updated_at', $time);
		}
		$difference = round((strtotime($time) - strtotime($event_updated_at)) / 60);
		$events = [];
		if($difference > 180) { //3 hours (180 minutes)
			$today = date('Y-m-d');
			$sql = "SELECT `event_name`,`from_date`,`to_date`,`event_type` FROM `school_calender` WHERE  ((`from_date`>='$today') AND (`to_date`>='$today' OR `to_date` IS NULL)) AND `applicable_to`!=1 AND board=$student_board ORDER BY `from_date`,`to_date` LIMIT $n";
			$query = $this->db_readonly->query($sql);
			$events = $query->result();
			$this->session->set_userdata('calender_events', $events);
			$this->session->set_userdata('event_updated_at', $time);
		} else {
			$events = $this->session->userdata('calender_events');
		}
		return $events;
		/*$events = $this->session->userdata('parentEvents');

		if (!empty($events)) {
			return $events;
		}*/

		$today = date('Y-m-d');
		$sql = "SELECT `event_name`,`from_date`,`to_date`,`event_type` FROM `school_calender` WHERE  ((`from_date`>='$today') AND (`to_date`>='$today' OR `to_date` IS NULL)) AND `applicable_to`!=1 ORDER BY `from_date`,`to_date` LIMIT $n";
		$query = $this->db_readonly->query($sql);
		$events = $query->result();

		// $this->session->set_userdata('parentEvents', $events);
		return $events;
	}

	public function getClassIdOfLoggedInParent()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$studentData = $this->getStudentDataById($studentId);

		return $studentData;
	}

	public function get_class_section_name($student_id) {
		$isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent)?$this->parentcache->getParentCache()->isNextYearStudent:'0';
		$JoinyearId = $this->yearId;
		if($isNewStudent){
			$JoinyearId =  $this->acad_year->getPromotionAcadYearId();
		}
		$row = $this->db_readonly->select("concat(cs.class_name, cs.section_name) as class_section_name")
				->from('student_admission sa')
				->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$JoinyearId")
				->join('class_section cs', "cs.id=sy.class_section_id")
				->where('student_admission_id', $student_id)
				->get()->row();

		if (empty($row)) {
			return '';
		} else {
			return $row->class_section_name;
		}
	}

	// public function getStudentNameById($studentId)
	// {
	// 	// $student_data = $this->parentcache->getParentCache();
	// 	// if(empty($student_data)){
	// 	// 	return false;
	// 	// }
	// 	$names = [];
	// 	if($student_data->first_name != '') {
	// 		$names[] = $student_data->first_name;
	// 	}
	// 	if($student_data->last_name != '') {
	// 		$names[] = $student_data->last_name;
	// 	}
	// 	$student = new stdClass();
	// 	$student->stdId = $student_data->id;
	// 	$student->stdName = implode(" ", $names);
	// 	$student->first_name = $student_data->first_name;
	// 	$student->last_name = $student_data->last_name;
	// 	return $student;
	// }

	public function check_parent_reset_password_required(){
		$parentId = $this->authorization->getAvatarStakeHolderId();

		$userId=$this->db_readonly->select("user_id")->from("avatar")->where("stakeholder_id",$parentId)->where("avatar_type",2)->get()->row();

		return $this->db_readonly->select("username,reset_password_required")
		->from("users")
		->where("id",$userId->user_id)
		->where("active",1)->get()->row();
	}

	public function getStudentDataById($studentId)
	{
	
		//TODO: Can we cache the student data in session or somewhere?
	 	/**
	     * If isNextYearStudent is not set in cache, then, we assume that he is a old student.
	     * For new students, we will display only FEES and PROFILE. Other features are hidden.
	     */
		$isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent)?$this->parentcache->getParentCache()->isNextYearStudent:'0';
		$JoinyearId = $this->yearId;
		if($isNewStudent){
			$JoinyearId =  $this->acad_year->getPromotionAcadYearId();
		}

		$studentData = $this->db_readonly->select("sa.id as stdId, concat(ifnull(sa.first_name,''),ifnull(sa.student_middle_name,''),' ',ifnull(sa.last_name,'')) as stdName, sa.first_name,ifnull(sa.family_picture_url,'') as family_picture_url, sa.last_name, c.id as classId, cs.id as sectionId, c.class_name as className, cs.is_placeholder, cs.section_name as sectionName, sa.dob, sa.mother_tongue, sa.gender, sy.id as stdYearId, sy.picture_url , sy.student_house, sy.id as student_id, sy.roll_no,sa.admission_no  as admissionNo, u.id as student_userId, u.email as studentEmail, nationality, caste, religion, aadhar_no, sy.high_quality_picture_url, sa.blood_group, sy.stop, sy.pickup_mode, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status, sy.profile_confirmed, date_format(sy.profile_confirmed_date,'%d-%m-%Y') as profile_confirmed_date, sa.email as student_email, sa.email_password as student_email_password, ifnull(sem.sem_name,'NA') as semester,sy.profile_status,sy.profile_status_changed_date,sa.student_remarks,sy.combination,sa.category,name_as_per_aadhar,student_mobile_no,enrollment_number,cmc.combination_name,ifnull(sy.alpha_rollnum,'') as alpha_rollnum,ifnull(sa.preferred_contact_no,'-') as preferred_contact_no,ifnull(sa.point_of_contact,'') as point_of_contact")
			->from('student_admission sa')
			->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$JoinyearId")
			->join('class_master_combinations cmc','cmc.id=sy.combination_id','left')
			// ->join('student_health sh',"sa.id=sh.student_id", 'left')
			->join('class c', 'sy.class_id=c.id', 'left')
			->join('class_section cs', 'cs.id=sy.class_section_id', 'left')
			->join('semester sem', 'sy.semester=sem.id', 'left')
		   	->join('avatar a','sa.id=a.stakeholder_id')
	        ->where('a.avatar_type','1') //1:Student
	        ->join('users u','a.user_id=u.id')
			->where('sa.id', $studentId)
			->where('sy.acad_year_id', $JoinyearId)
			->get()->row();
		return $studentData;
	}

	public function getFeesStops() {
		return $this->db_readonly->select('*')->get('feev2_stops')->result();
	}

	public function insert_homwork_seendata($id){
		$user = $this->authorization->getAvatarStakeHolderId();
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data=array(
			'homework_id' => $id,
			'stakeholder_id' => $user,
			'student_id' => $studentId,
			'is_read' => 1
		);
		$count = $this->db->query("select * from homework_sent_to where homework_id=$id and stakeholder_id=$user")->result();
		// echo "<pre>";print_r($count);die();
		if(empty($count))
			return $this->db->insert('homework_sent_to',$data);
		else 
			return 0;
	}

	public function getHomeWork($sectionId, $sevenDaysAgo, $date)
	{
		$user = $this->authorization->getAvatarStakeHolderId();
		// echo $this->authorization->getAvatarStakeHolderId();
		$this->db_readonly->select('h.id,image,body, cs.class_name,cs.section_name, date_format(created_date,"%d-%m-%Y") as date,ht.is_read, h.expect_submissions');
		$this->db_readonly->from('homework h');
		//$this->db_readonly->join('class c', 'h.class_id=c.id');
		$this->db_readonly->join('class_section cs', 'h.section_id=cs.id');
		$this->db_readonly->join('homework_sent_to ht',"ht.homework_id=h.id and ht.stakeholder_id=$user",'left');
		//$this->db_readonly->where('h.class_id', $classId);
		$this->db_readonly->where('h.section_id', $sectionId);
		// $this->db_readonly->where('ht.stakeholder_id',$this->authorization->getAvatarStakeHolderId());
		$this->db_readonly->where(array(
			'status' => 1
		));
		$this->db_readonly->where('created_date >=', date('Y-m-d', strtotime($sevenDaysAgo)));
		$this->db_readonly->where('created_date <=', date('Y-m-d', strtotime($date)));
		$this->db_readonly->order_by("h.id", "desc");
		return $this->db_readonly->get()->result();
		/*echo '<pre>';
		print_r($return);
		echo $this->db_readonly->last_query();
		die();*/
	}

	public function getMinimalHomeworkById($homework_id) {
		return $this->db_readonly->select("DATE_FORMAT(h.created_date, '%d-%M') as homework_date, h.staff_id")
		->from('homework h')
		->where('h.id', $homework_id)
		->get()->row();
	}

	public function getHomeworkById($id)
	{
		$data=array('is_read' => '1');
		$this->db->where('stakeholder_id',$this->authorization->getAvatarStakeHolderId());
		$this->db->where('homework_id',$id);
		$this->db->update('homework_sent_to',$data);

		$this->db_readonly->select('h.*, cs.class_name, cs.section_name');
		$this->db_readonly->join("class_section cs", "cs.id=h.section_id", "left");
		$this->db_readonly->where('h.id', $id);
		$this->db_readonly->where('h.status', '1');
		$this->db_readonly->from('homework h');
		$result = $this->db_readonly->get()->row_array();
		$result['paths'] = array();
		if($result['image'] != '') {
			if($result['is_image_json']) {
				$paths = json_decode($result['image']);
				foreach ($paths as $path) {
					array_push($result['paths'], array('name' => $path->name, 'path' => $this->filemanager->getFilePath($path->path)));
				}
			} else {
				array_push($result['paths'], array('name' => 'Homework', 'path' => $this->filemanager->getFilePath($result['image'])));
			}
		}
		return $result;
	}

	public function getHomeworkSubmissions($hw_id, $studentId) {
		$result = $this->db_readonly->select("h.id as homework_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, hs.id hs_id, hs.student_id, hs.file, hs.description, hs.remarks, hs.created_on, DATE_FORMAT(hs.modified_on, '%d-%M %H:%i') as modified_on, hs.is_checked, hs.name")
		->from('homework h')
		->join('homework_submissions hs', 'hs.homework_id=h.id')
		->join('staff_master sm', 'sm.id=hs.checked_by', 'left')
		->where('hs.homework_id', $hw_id)
		->where('hs.student_id', $studentId)
		->order_by('hs.modified_on, hs.created_on')
		->get()->result();

		foreach ($result as $key => $res) {
			$result[$key]->submitted_at = date('d-M h:i a', strtotime($res->created_on));
			$result[$key]->path = '';
			if($res->file != '')
				$result[$key]->path = $this->filemanager->getFilePath($res->file);
		}
		return $result;
	}

	public function deleteSubmission($hs_id) {
		return $this->db->where('id', $hs_id)->delete('homework_submissions');
	}

	public function submit_homework($studentId, $path='') {
		$input = $this->input->post();
		$data = array(
			'homework_id' => $input['homework_id'],
			'student_id' => $studentId,
			'file' => ($path=='')?NULL:$path,
			'name' => $input['submission_name'],
			'description' => $input['description']
		);

		return $this->db->insert('homework_submissions', $data);	
	}
	public function submit_task($taskStudentId, $files_data,$task_id,$submission_comment) {
		
		if(empty($files_data)) {
			return 0;
		}

		// trigger_error(json_encode($files_data));
		// trigger_error($studentId);
		// trigger_error($task_id);
		
		$task_last_date = $this->db->select('TIMESTAMPDIFF(MINUTE, task_last_date, now()) as elapsed_time')
		->from('lp_tasks')
		->where('id', $task_id)
		->get()->row();

		$is_late_submission = ($task_last_date->elapsed_time <= 0)?0:1;

		$this->db->trans_start();
		$this->db->insert_batch('lp_tasks_students_submission_files', $files_data);
		$data = array(
			'submission_status' =>1,
			'submission_on' =>gmdate("Y-m-d H:i:s"),
			'submission_files_path' => '1',
			'is_late_submission' => $is_late_submission,
			'submission_comment' => $submission_comment
		);
		// $this->db->where('lp_tasks_id',$task_id);
		// $this->db->where('student_id',$studentId);
		$this->db->where('id',$taskStudentId);
	 	$this->db->update('lp_tasks_students',$data);
		// echo "<pre>";print_r($data1);die();

		return $this->db->trans_complete();
	}

	public function submit_task_mobile($studentId, $path='',$task_id){
		$task_last_date = $this->db->select('TIMESTAMPDIFF(MINUTE, task_last_date, now()) as elapsed_time')
		->from('lp_tasks')
		->where('id', $task_id)
		->get()->row();

		$is_late_submission = ($task_last_date->elapsed_time <= 0)?0:1;

		$data = array(
			'submission_status' =>1,
			'submission_on' =>gmdate("Y-m-d H:i:s"),
			'submission_files_path' => ($path=='')?NULL:$path,
			'is_late_submission' => $is_late_submission
		);
		$this->db->where('lp_tasks_id',$task_id);
		$this->db->where('student_id',$studentId);
		$result = $this->db->update('lp_tasks_students',$data);
	  return $result;
	}
	public function getAddedFileDetails($task_id, $studentId){

		$lp_task_id = $this->db->select('lp.id as lp_task_student_id')
		->from('lp_tasks_students lp')
		->where('lp.student_id', $studentId)
		->where('lp.lp_tasks_id', $task_id)
		->get()->row();
		// trigger_error(json_encode($lp_task_id));


		$result = $this->db->select('ltf.*')
		->from('lp_tasks_students_submission_files ltf')
		->where('ltf.lp_tasks_students_id', $lp_task_id->lp_task_student_id)
		->where('ltf.type', 'Submission')
		->where('ltf.status', 1)
		->get()->result();
		// trigger_error($this->db->last_query());
		return $result;
	}

	public function getTaskName($task_id){
		$result = $this->db->select('t.task_name')
		->from('lp_tasks t')
		->where('t.id', $task_id)
		->get()->row();
		return $result;
	}

	public function deleteFile($file_id){

		$this->db->where('id', $file_id);
		$result = $this->db->delete('lp_tasks_students_submission_files');
		return $result;
	}

	public function acknowledge_submission($task_student_id, $task_id){
		$task_last_date = $this->db->select('TIMESTAMPDIFF(MINUTE, task_last_date, now()) as elapsed_time')
		->from('lp_tasks')
		->where('id', $task_id)
		->get()->row();

		$is_late_submission = ($task_last_date->elapsed_time <= 0)?0:1;

		$data = array(
			'submission_status' =>1,
			'submission_on' =>gmdate("Y-m-d H:i:s"),
			'is_late_submission' => $is_late_submission
		);

		$this->db->where('id',$task_student_id);
	 	return $this->db->update('lp_tasks_students',$data);

		/*$this->db->where('lp_tasks_id',$task_id);
		$this->db->where('student_id',$studentId);
	 	$this->db->update('lp_tasks_students',$data);*/
	}

	public function getSubmittedFiles($task_student_id){
		$result = $this->db_readonly->select('ltf.*')
		->from('lp_tasks_students_submission_files ltf')
		// ->join('lp_tasks_students lts', 'lts.id=ltf.lp_tasks_students_id', 'left')
		// ->join('lp_tasks lt',"lt.id=lts.lp_tasks_id and lt.id='$task_id'")
		// ->where('lts.lp_tasks_id', $task_id)
		// ->where('lts.student_id', $student_id)
		->where('ltf.lp_tasks_students_id', $task_student_id)
		->where('ltf.type', 'Submission')
		->where('ltf.status', 1)
		->get()->result();
		// echo "<pre>";print_r($result);die();
		return $result;
	}

	public function getEvaluatedFiles($task_student_id){
		$result = $this->db_readonly->select('ltf.*')
		->from('lp_tasks_students_submission_files ltf')
		// ->join('lp_tasks_students lts', 'lts.id=ltf.lp_tasks_students_id', 'left')
		// ->join('lp_tasks lt',"lt.id=lts.lp_tasks_id and lt.id='$task_id'")
		// ->where('lts.lp_tasks_id', $task_id)
		// ->where('lts.student_id', $student_id)
		->where('ltf.lp_tasks_students_id', $task_student_id)
		->where('ltf.type', 'Evaluation')
		->get()->result();
		// echo "<pre>";print_r($result);die();
		return $result;
	}

	public function getFileStudentName($id){
		$result = $this->db->select('ltf.file_name, sa.first_name')
		->from('lp_tasks_students_submission_files ltf')
		->join('lp_tasks_students lts', 'lts.id = ltf.lp_tasks_students_id', 'left')
		->join('student_admission sa', 'sa.id = lts.student_id', 'left')
		->where('ltf.id', $id)
		->get()->row();
		return $result;
	}

	

	public function downloadHomeworkAttachment($id) {
		return $this->db_readonly->select('image, is_image_json')->where('id', $id)->get('homework')->row();
	}
	public function downloadTaskAttachment($id) {
		return $this->db_readonly->select('resource_file')->where('id', $id)->get('resources')->row();
	}
	public function downloadSubmissionAttachment($id) {
		return $this->db_readonly->select('file_path')->where('id', $id)->get('lp_tasks_students_submission_files')->row();
	}
	public function downloadEvaluationAttachment($id) {
		return $this->db_readonly->select('file_path')->where('id', $id)->get('lp_tasks_students_submission_files')->row();
	}
	public function getHomeWorkImage($id) {
		return $this->db_readonly->select('image')->where('id', $id)->get('homework')->row()->image;
	}

	public function getSubmissionData($id) {
		return $this->db_readonly->select('file, name')->where('id', $id)->get('homework_submissions')->row();
	}

	public function getHomeworkMobile($sectionId,$filter)
	{	
		$user = $this->authorization->getAvatarStakeHolderId();
		$this->db_readonly->select('h.id, h.image, h.body, cs.class_name, cs.section_name, date_format(h.created_date,"%d-%m-%Y") as date,ht.is_read, h.expect_submissions');
		$this->db_readonly->from('homework h');
		$this->db_readonly->join('class_section cs', 'h.section_id=cs.id');
		$this->db_readonly->join('homework_sent_to ht',"ht.homework_id=h.id and ht.stakeholder_id=$user",'left');
		// $this->db_readonly->where('ht.stakeholder_id',$this->authorization->getAvatarStakeHolderId());
		$this->db_readonly->where('status', '1');
		$this->db_readonly->where('h.section_id', $sectionId);

		if($filter=="today"){
			$this->db_readonly->where('h.created_date >=',  date('Y-m-d', strtotime("now")));
		}

		if($filter=="yesterday"){
			$this->db_readonly->where('h.created_date >=',  date('Y-m-d', strtotime("-1 days")));
		}

		if($filter=="last_seven_days"){
			$this->db_readonly->where('h.created_date >=',  date('Y-m-d', strtotime('-7 days')));
		}

		if($filter=="last_thirty_days"){
			$this->db_readonly->where('h.created_date >=',  date('Y-m-d', strtotime('-30 days')));
		}
		$this->db_readonly->order_by('h.created_date', 'DESC');
		// $this->db_readonly->limit(10);

		return $this->db_readonly->get()->result();
	}

	public function getStudentTasksMobile($studentId){
		$date = date('Y-m-d');
		$this->db_readonly->select("lt.*,lts.*,lt.id as task_id,lp.subject_name as subject_name, ifnull(lt.lp_assessment_id,0) as assessment_id, la.name as assessment_name, la.description as assessment_description, la.total_points, CONVERT_TZ(lt.task_last_date,'+00:00','+05:30') as local_task_last_date");
		$this->db_readonly->from('lp_tasks_students lts');
		$this->db_readonly->join('lp_tasks lt',"lt.id=lts.lp_tasks_id and date_format(lt.created_on,'%Y-%m-%d')>='$date'");
		$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		$this->db_readonly->join('lp_assessment la','la.id=lt.lp_assessment_id');
		$this->db_readonly->where('lts.student_id',$studentId);
		$this->db_readonly->order_by('lt.created_on', 'DESC');
		return $this->db_readonly->get()->result();
		// echo "<pre>";print_r($return);die();
	}

	public function getStudentTaskMinimumInfo($task_id,$studentId) {
		return $this->db_readonly->select("lt.id, lt.task_name, lts.id as task_student_id, lts.submission_status, created_on, created_by")
		->from('lp_tasks_students lts')
		->join('lp_tasks lt',"lt.id=lts.lp_tasks_id")
		->where('lts.student_id',$studentId)
		->where('lt.id',$task_id)
		->get()->row();
	}

	public function getTaskMinimumInfo($task_id) {
		return $this->db_readonly->select("lt.id, lt.task_name, created_on, created_by")
		->from('lp_tasks lt')
		->where('lt.id',$task_id)
		->get()->row();
	}

	public function getSingleStudentTask($task_id,$studentId){
		/*$this->db_readonly->select("lt.*,lts.*, lts.id as task_student_id, lt.id as task_id,lp.subject_name as subject_name, ifnull(lt.lp_assessment_id,0) as assessment_id, la.name as assessment_name, la.description as assessment_description, la.total_points, TIMESTAMPDIFF(MINUTE, lt.task_last_date, now()) as elapsed_time, CONVERT_TZ(lt.task_last_date,'+00:00','+05:30') as local_task_last_date");*/
		$this->db_readonly->select("lt.consider_this_task_as, lts.id as task_student_id, lt.id as task_id,lp.subject_name as subject_name, TIMESTAMPDIFF(MINUTE, lt.task_last_date, now()) as elapsed_time, CONVERT_TZ(lt.task_last_date,'+00:00','+05:30') as local_task_last_date,CONVERT_TZ(lt.created_on,'+00:00','+05:30') as created_on, lt.task_name, lt.task_description, lt.task_type, lt.created_by, lt.status, lt.require_evaluation, lt.resource_ids, lt.download_status, lt.close_submission, lt.release_evaluation, lts.read_status, lts.submission_status, lts.evaluation_status, lts.submission_on, lts.evaluation_on, lts.evaluation_comments, lts.is_late_submission, lts.resubmission_comment, lts.resubmission_status,lt.task_file_path, ifnull(lt.task_publish_timestamp, CONVERT_TZ(lt.created_on,'+00:00','+05:30')) as task_publish_timestamp_to_display");
		$this->db_readonly->from('lp_tasks_students lts');
		// $this->db_readonly->join('lp_tasks_students_submission_files ltf',"ltf.lp_tasks_students_id=lts.id and ltf.status=1", 'left');

		$this->db_readonly->join('lp_tasks lt',"lt.id=lts.lp_tasks_id");
		// $this->db_readonly->join('lp_tasks lt',"lt.id=lts.lp_tasks_id and lt.id='$task_id'");
		$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		// $this->db_readonly->join('lp_assessment la','la.id=lt.lp_assessment_id', 'left');
		$this->db_readonly->where('lts.student_id',$studentId);
		$this->db_readonly->where('lt.id',$task_id);
		return $this->db_readonly->get()->result();
	}

	public function getStudentTaskData($task_student_id) {
		return $this->db_readonly->select("t.consider_this_task_as, t.id as task_id, TIMESTAMPDIFF(MINUTE, t.task_last_date, now()) as elapsed_time, CONVERT_TZ(t.task_last_date,'+00:00','+05:30') as local_task_last_date, t.task_name, t.task_description, t.require_evaluation, t.task_type, t.status, t.resource_ids, t.download_status, sub.subject_name, ts.id as lp_tasks_students_id, ts.read_status, ts.submission_status, ts.evaluation_status, ts.evaluation_comments, ts.resubmission_status, ts.resubmission_comment, ts.is_late_submission, ts.submission_on, ts.evaluation_on, t.close_submission, t.release_evaluation,t.task_file_path, t.created_on as created_on, ifnull(t.task_publish_timestamp, CONVERT_TZ(t.created_on,'+00:00','+05:30')) as task_publish_timestamp_to_display")
		->from('lp_tasks t')
		->join('lp_tasks_students ts', 'ts.lp_tasks_id=t.id')
		->join('lp_subjects sub', 'sub.id=t.subject_id')
		->where('ts.id', $task_student_id)
		->get()->row();
	}

	public function getSubmittedFilesV2($task_student_id){
		$result = $this->db_readonly->select('ltf.*, ltf2.id as evaluation_file_id, ltf2.file_path as evaluation_file_path, ltf2.file_name as evaluation_file_name')
		->from('lp_tasks_students_submission_files ltf')
		->join('lp_tasks_students_submission_files ltf2', 'ltf2.id=ltf.evaluation_id and ltf2.status=1', 'left')
		->where('ltf.lp_tasks_students_id', $task_student_id)
		->where('ltf.type', 'Submission')
		->where('ltf.status', 1)
		->get()->result();
		return $result;
	}

	public function getEvaluationStatus($task_id, $student_id){
		$result = $this->db_readonly->select('lt.*, lts.*')
		->from('lp_task_students lts')
		->join('lp_tasks lt',"lt.id=lts.lp_tasks_id and lt.id='$task_id'")
		->where('lts.student_id',$student_id)
		->get()->result();
		return $result;
	}

	public function getResourceToPlay(){
		$resource_id = $_POST['resource_id'];
		$this->db_readonly->select('*');
		$this->db_readonly->from('resources');
		$this->db_readonly->where('id',$resource_id);
		return $this->db_readonly->get()->result();
	}

	public function makeTaskRead($task_student_id) {
		$this->db->where('id',$task_student_id);
		$this->db->update('lp_tasks_students', ['read_status' => 'read', 'read_on' => date('Y-m-d H:i:s')]);
		return 1;
	}

	public function updateReadStatus($studentId,$task_id){
		$data=array(
			'read_status' =>'read'
		);
		$this->db->where('lp_tasks_id',$task_id);
		$this->db->where('student_id',$studentId);
		return $this->db->update('lp_tasks_students',$data);
	}

	public function getTodaysHomework($sectionId)
	{
		$today = date('Y-m-d');
		$this->db_readonly->select('count(h.id) as hwCount');
		$this->db_readonly->from('homework h');
		$this->db_readonly->where('status', '1');
		$this->db_readonly->where('h.section_id', $sectionId);
		$this->db_readonly->where("h.created_date",$today);
		return $this->db_readonly->get()->row();
	}

	public function getTodaysStudentTasks($student_id){
		$today = date('Y-m-d');
		$this->db_readonly->select('count(lts.student_id) as task_count');
		$this->db_readonly->from('lp_tasks_students lts');
		$this->db_readonly->join('lp_tasks lt',"lt.id=lts.lp_tasks_id");
		$this->db_readonly->where('lts.student_id',$student_id);
		$this->db_readonly->where("(lt.task_publish_timestamp is not null and date_format(lt.task_publish_timestamp,'%Y-%m-%d')='$today')");
		$this->db_readonly->where('lt.status', 'published');
		$this->db_readonly->where('lts.read_status', 'unread');
		return $this->db_readonly->get()->row();
	}
	public function getMultipleImage($sectionId, $date1, $date2)
	{
		$this->db->select('h.id,image,body,cs.class_name,cs.section_name, created_date as date');
		$this->db->from('homework h');
		//	$this->db->join('class c', 'h.class_id=c.id');
		$this->db->join('class_section cs', 'h.section_id=cs.id');
		// $this->db->where('h.class_id', $classId);
		$this->db->where(array(
			'status' => 1
		));
		$this->db->where('h.section_id', $sectionId);
		$this->db->where("h.created_date >='" . date('Y-m-d', strtotime($date1)) . "' and h.created_date <='" . date('Y-m-d', strtotime($date2)) . "'");
		$this->db->order_by('h.created_date', 'DESC');
		return $this->db->get()->result();

		// echo $this->db->last_query();die();

	}


	public function getFather_Address_Details($pId, $address)
	{
		return $this->db_readonly->select('id as addId, Address_line1, Address_line2, area, district, state, country, pin_code, address_type')
			->from('address_info')
			->where_in('address_type', $address)
			->where('stakeholder_id', $pId)
			->where('avatar_type','2')
			->get()->result();
	}


	public function getStudent_Address_Details($pId, $address)
	{
		return $this->db_readonly->select('id as addId, Address_line1, Address_line2, area, district, state, country, pin_code, address_type')
			->from('address_info')
			->where('address_type', $address)
			->where('stakeholder_id', $pId)
			->where('avatar_type','1')
			->get()->result();
	}


	public function getMother_Address_Details($pId, $address)
	{
		return $this->db_readonly->select('id as addId, Address_line1, Address_line2, area, district, state, country, pin_code, address_type')
			->from('address_info')
			->where_in('address_type', $address)
			->where('stakeholder_id', $pId)
			->where('avatar_type','2')
			->get()->result();
	}

	private function __getRelationDetails($studentId, $relation)
	{
		$fatherData = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*, u.id as userId, p.email as fatherEmail")
			->from('parent p')
			->join('student_relation sr', 'p.id=sr.relation_id', 'left')
		  	->join('avatar a','p.id=a.stakeholder_id')
	        ->where('a.avatar_type','2')
	        ->join('users u','a.user_id=u.id')
			->where('sr.relation_type', $relation)
			->where('sr.std_id', $studentId)
			->get()->row();

		return $fatherData;
	}

	public function getFatherDetails($studentId)
	{
		return $this->__getRelationDetails($studentId, 'Father');
	}

	public function getMotherDetails($studentId, $address = '')
	{
		return $this->__getRelationDetails($studentId, 'Mother');
	}

	public function getGuardianDetails($studentId) {
		$guardian = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*")
			->from('parent p')
			->join('student_relation sr', 'p.id=sr.relation_id', 'left')
			->where('sr.relation_type', 'Guardian')
			->where('sr.std_id', $studentId)
			->get()->row();

		return $guardian;
	}

	public function get_student_detail($std_id)
	{
		$this->db_readonly->select("s.id,s.roll_no,s.admission_no, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) as std_name,c.class_name,cs.section_name,s.gender,s.category,s.boarding,s.is_rte as rte,s.admission_type,s.board,s.medium,s.admission_status,c.id as class");
		$this->db_readonly->from('student s');
		$this->db_readonly->where('admission_status', '2'); // Approved 2
		$this->db_readonly->where('s.id', $std_id);
		$this->db_readonly->join('class c', 'c.id=s.class_id');
		$this->db_readonly->join('class_section cs', 's.class_section_id=cs.id', 'left');
		$data_info =  $this->db_readonly->get()->row();
		$array =  (array)$data_info;  //  use for object convert to array  
		return $array;
	}

	public function get_feemasterFilter($filter)
	{
		$result = $this->db_readonly->select('fm.id,fm.total_fee,fcm.id as fcm_id,fcm.name,fcm.amount')
			->from('fee_master fm')
			->where('fm.filter', $filter)
			->join('fee_component_master fcm', 'fcm.fee_master_id=fm.id')
			->get()->result();
		return $result;
	}

	// Payment History 
	public function getpaymenthistorbystudentwise($stdId)
	{

		$result = $this->db_readonly->select('fti.receipt_number,date_format(fti.paid_date,"%d-%m-%Y") as paid_date,fti.recon_submitted_on,fti.recon_created_on,fcm.name,fcm.amount as feeAmount,ftc.amount as paidAmount, ftc.concession as conAmount')
			->from('fee_transaction_installment fti')
			->join('fee_transaction_components ftc', 'fti.id=ftc.fee_transaction_id')
			->join('fee_component_master fcm', 'ftc.fee_component_id=fcm.id')
			->where('fti.student_id', $stdId)
			->get()->result();
		$fee_no = array();
		foreach ($result as $key => $value) {
			$fee_no[$value->receipt_number][] = $value;
		}
		return $fee_no;
	}

	public function get_transport_history($stdId)
	{
		return $this->db_readonly->select("concat(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) as student_name, ft.amount as amount_paid, ft.payment_type, ft.total_amount as feeAmount,concat(ifnull(c.class_name,''), ' ', ifnull(sc.section_name,'')) as class_name,ts.name as stopName,vd.routeNo as route,ft.receipt_no,ft.usage_mode, date_format(ft.receipt_date,'%d-%m-%Y') as receipt_date")
			->from('fee_transportation ft')
			->join('student s', 'ft.student_id=s.id')
			->join('class c', 's.class_id=c.id')
			->join('transport_stops ts', 'ft.stop_id=ts.id')
			->join('route_allocate ra', 'ft.stop_id=ra.stop_id', 'left')
			->join('vehicle_data vd', 'ra.route_no=vd.id', 'left')
			->join('class_section sc', 's.class_section_id=sc.id', 'left')
			->where('soft_delete', '1')
			->where('s.id', $stdId)
			->get()->result();
	}

	public function get_transport_strucutre($std_id)
	{
		$result = $this->db_readonly->select('*')
			->from('student_to_stops')
			->where('student_id', $std_id)
			->get()->row();
		if (empty($result)) {
			return array();
		}

		$feeData = $this->db_readonly->select('stop_id,pickup_mode,amount,gprs')
			->from('transport_fee_structure tfs')
			->where('stop_id', $result->stop_id)
			->where('pickup_mode', $result->usage_mode)
			->get()->row();
		if (empty($feeData)) {
			return array();
		}
		$stdData = $this->db_readonly->select("s.id as stdId, s.admission_no, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name,c.class_name,cs.section_name,ts.name as stopName")
			->from('student_to_stops sts')
			->join('student s', 's.id=sts.student_id')
			->join('class c', 'c.id=s.class_id')
			->join('class_section cs', 's.class_section_id=cs.id', 'left')
			->join('transport_stops ts', 'sts.stop_id=ts.id')
			->where('s.id', $std_id)
			->get()->row();
		$data = (object)array_merge((array)$feeData, (array)$stdData);
		return $data;
	}

	public function get_facilites_history($std_id)
	{
		return 0;
	}
	// Attendance
	public function getAttendacebyStudentwise($std_id)
	{
		$month = $this->input->post('month');
		$this->db->select('admisission_no');
		$this->db->where('id', $std_id->student_id);
		$result =	$this->db->get('users')->row();
		$this->db->where("date_format(cuurent_date,'%m')", $month);
		$this->db->where('application_no', $result->admisission_no);
		$datewise =	$this->db->get('student_attendence')->result();
		return $datewise;
	}

	public function ptm_notification($classId, $Section)
	{
		$this->db_readonly->distinct();
		$this->db_readonly->select('ptm_master.id,date_of_meeting,description,current_status');
		$this->db_readonly->from('ptm_master');
		$this->db_readonly->join('ptm_cls_section', 'ptm_cls_section.ptm_id=ptm_master.id');
		if ($classId) {
			$this->db_readonly->where('ptm_cls_section.class', $classId);
		}
		if ($Section) {
			$this->db_readonly->where('ptm_cls_section.section', $Section);
		}
		$this->db_readonly->order_by('ptm_master.id', 'desc');
		return $this->db_readonly->get()->result();
	}

	public function parent_feedback($id)
	{
		$parentAvatarId = $this->authorization->getAvatarId();
		$feedback = array(
			'feedback' => $this->input->post('feedback'),
			'parent_avatar_id' => $parentAvatarId,
			'ptm_id' => $id
		);
		$this->db->where('id', $id);
		$query = $this->db->get('parent_feedback')->row();
		if (!empty($query)) {
			$updat_feedback = array(
				'feedback' => $this->input->post('feedback'),
				'parent_avatar_id' => $parentAvatarId,
				'ptm_id' => $id,
			);
			$this->db->where('id', $id);
			return $this->db->update('parent_feedback', $updat_feedback);
		} else {
			return $this->db->insert('parent_feedback', $feedback);
		}
	}
	public function get_feedbackAll()
	{
		$parentAvatarId = $this->authorization->getAvatarId();
		$this->db->where('parent_id', $parentAvatarId);
		return $this->db->get('parent_feedback')->result();
	}

	public function feedback_detailsbyptmId($id)
	{
		$this->db_readonly->select('ptm_master.id,date_of_meeting,description,current_status');
		$this->db_readonly->from('ptm_master');
		$this->db_readonly->join('ptm_cls_section', 'ptm_cls_section.ptm_id=ptm_master.id');
		$this->db_readonly->where('ptm_master.id', $id);
		$this->db_readonly->order_by('ptm_master.id', 'desc');
		return $this->db_readonly->get()->row();
	}

	public function get_feedbackIdwise($id)
	{
		$this->db_readonly->where('ptm_id', $id);
		return $this->db_readonly->get('parent_feedback')->row();
	}

	public function ptm_notificationbydate()
	{
		$current_date = strtotime("+1 day", strtotime($this->Kolkata_datetime()));
		$dateonedayafter = date("d-m-Y", $current_date);
		$this->db->select('*');
		$this->db->where("date_format(date_of_meeting,'%d-%m-%Y')", $dateonedayafter);
		return $this->db->get('ptm_master')->row();
	}

	public function get_smsStudentIdwise($studentId)
	{
		// $date = date('Y-m-d', strtotime('-30 days'));
		$this->db->select("distinct(sms_master.id),date_format(sms_date,'%d-%m-%Y') as date ,sms_date as smsDate, sms_content,student_staff_id as student_id");
		$this->db->from('sms_master');
		$this->db->join('sms_sent_to', 'sms_sent_to.sms_id=sms_master.id');
		$this->db->where('sms_sent_to.student_staff_id', $studentId);
		// $this->db->where('DATE_FORMAT(sms_master.sms_date, "%Y-%m-%d") >',$date);
		$this->db->where('sms_master.user_type', 'Student');
		$this->db->where('sms_master.acad_year_id', $this->yearId);
		$this->db->order_by('sms_master.sms_date', 'desc');
		$this->db->limit(25);
		return $this->db->get()->result();
	}

	public function get_canteenStudentIdwise($stdId)
	{

		$cantee_load = $this->db_readonly->select("sum(ct.amount) as loadmoney, concat(ifnull(s.first_name,''), ' ' ,ifnull(s.last_name,'')) as name")
			->from('student s')
			->join('card_transaction ct', 's.identification_code=ct.identification_code')
			->where('transaction_mode', 'load')
			->where('transcation_type', 'canteen')
			->where('s.id', $stdId)
			->get()->row()->loadmoney;

		$cTrans = $this->db_readonly->select("ct.id as ctId, concat(ifnull(s.first_name,''), ' ' ,ifnull(s.last_name,'')) as name,ct.created_on as date")
			->from('student s')
			->join('canteen_transaction ct', 's.identification_code=ct.identification_code')
			->where('s.id', $stdId)
			->get()->result();
		$transIdArr = array();
		foreach ($cTrans as $key => $trans) {
			$transIdArr[] = $trans->ctId;
		}
		if (empty($transIdArr)) {
			return 0;
		}
		$transItems =  $this->db_readonly->select('cti.canteen_trans_id, cm.menu_name, cti.quantity,cti.price_per_piece')
			->from('canteen_transaction_items cti')
			->join('canteen_menu cm', 'cti.menu_id=cm.id')
			->where_in('cti.canteen_trans_id', $transIdArr)
			->get()->result();
		//echo "<pre>"; print_r($transItems); die();
		foreach ($cTrans as $key => &$c) {
			$items = '';
			$price = 0;
			foreach ($transItems as $key => $t) {
				if ($c->ctId == $t->canteen_trans_id) {
					if (!empty($items)) $items .= ', ';
					$items .= $t->menu_name;
					$price += $t->quantity * $t->price_per_piece;
				}
			}
			$c->items_name = $items;
			$c->price = $price;
		}

		$tPaid = 0;
		foreach ($cTrans as $key => $val) {
			$tPaid += $val->price;
		}
		$balance = $cantee_load - $tPaid;
		return array('balance' => $balance, 'cTrans' => $cTrans);
		// return $cTrans;
	}

	public function getSMSDetail($id)
	{
		$this->db->select("sms_master.id,date_format(sms_date,'%d-%m-%Y') as date ,sms_date as smsDate, sms_content");
		$this->db->where('id', $id);
		return $this->db->get('sms_master')->row();
	}

	public function get_dairyStudentIdwise($studentId)
	{
		$this->db->select("circular_master.id,date_format(circular_date,'%d-%m-%Y %h:%i %p') as date ,circular_title,circular_content,stakeholder_id as student_id");
		$this->db->from('circular_master');
		$this->db->join('circular_sent_to', 'circular_sent_to.circular_id=circular_master.id');
		$this->db->where('circular_sent_to.stakeholder_id', $studentId);
		$this->db->where('circular_sent_to.avatar_type', 1);
		$this->db->where('is_published', 1);
		$this->db->where('acad_year_id', $this->yearId);
		$this->db->order_by('circular_master.circular_date', 'desc');
		return $this->db->get()->result();
		//echo "<pre>";print_r($a);die();
	}

	public function getCircularCount($category, $studentId)
	{
		$this->db_readonly->select("circular_master.id,date_format(circular_date,'%d-%m-%Y %h:%i %p') as date ,circular_title,circular_content,stakeholder_id as student_id");
		$this->db_readonly->from('circular_master');
		$this->db_readonly->join('circular_sent_to', 'circular_sent_to.circular_id=circular_master.id');
		$this->db_readonly->where('circular_sent_to.stakeholder_id', $studentId);
		$this->db_readonly->where('circular_sent_to.avatar_type', 1);
		$this->db_readonly->where('is_published', 1);
		$this->db_readonly->where('acad_year_id', $this->yearId);
		$this->db_readonly->where('category', $category);
		$num_rows = $this->db_readonly->count_all_results();
		return $num_rows;
	}

	public function newCount($category, $studentId)
	{
		$fromDate = date("Y-m-d", strtotime("-7 days"));
		$today = date("Y-m-d");
		$this->db->select("circular_master.id,date_format(circular_date,'%d-%m-%Y %h:%i %p') as date ,circular_title,circular_content,stakeholder_id as student_id");
		$this->db->from('circular_master');
		$this->db->join('circular_sent_to', 'circular_sent_to.circular_id=circular_master.id');
		$this->db->where('circular_sent_to.stakeholder_id', $studentId);
		$this->db->where('circular_sent_to.avatar_type', 1);
		$this->db->where('is_published', 1);
		$this->db->where('acad_year_id', $this->yearId);
		$this->db->where('date_format(circular_date,"%Y-%m-%d") BETWEEN "' . $fromDate . '" and "' . $today . '"');
		$this->db->where('category', $category);
		$new_rows = $this->db->count_all_results();
		return $new_rows;
	}

	public function getCircularCategoryWise($studentId, $category)
	{
		$this->db_readonly->select("circular_master.id,date_format(circular_date,'%d-%m-%Y %h:%i %p') as date ,circular_title,circular_content,stakeholder_id as student_id");
		$this->db_readonly->from('circular_master');
		$this->db_readonly->join('circular_sent_to', 'circular_sent_to.circular_id=circular_master.id');
		$this->db_readonly->where('circular_sent_to.stakeholder_id', $studentId);
		$this->db_readonly->where('circular_sent_to.avatar_type', 1);
		$this->db_readonly->where('is_published', 1);
		$this->db_readonly->where('acad_year_id', $this->yearId);
		$this->db_readonly->where('category', $category);
		$this->db_readonly->order_by('circular_master.circular_date', 'desc');
		return $this->db_readonly->get()->result();
	}

	public function getCircularData($id)
	{
		$this->db_readonly->select("cm.circular_title,cm.circular_content,date_format(cm.circular_date,'%d-%m-%Y %h:%i %p') as date,CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as createdby,cm.file_path");
		$this->db_readonly->from('circular_master cm');
		$this->db_readonly->where('cm.id', $id);
		$this->db_readonly->join('avatar a', 'cm.created_by=a.id', 'left');
		$this->db_readonly->join('staff_master sm', 'a.stakeholder_id=sm.id', 'left');
		return $this->db_readonly->get()->row();
	}

	public function downloadCircularAttachment($id)
	{
		return $this->db_readonly->select("file_path")
			->where('id', $id)
			->get('circular_master')->row()->file_path;
	}

	public function downloadCircularV2Attachment($id)
	{
		return $this->db_readonly->select("file_path")
			->where('id', $id)
			->get('circularv2_master')->row()->file_path;
	}

	public function getTT($csId)
	{
		$ttaSql = "select tta.id as ttaId, tta.sss_id as sssId, tta.period_seq as pSeq, sub.short_name as subName, sub.long_name as subLName, tta.short_name as pName, tta.week_day as weekDay, tta.period_type as pType, tta.class_section_id as csId, sub.long_name as lName from timetable_allocated tta left join staff_subject_section sss on tta.sss_id=sss.id left join subjects sub on sss.subject_id=sub.id where tta.class_section_id='$csId' order by tta.week_day,tta.period_seq";

		$masterData = $this->db->query($ttaSql)->result();
		//echo '<pre>';print_r($masterData);
		return $masterData;
	}

	public function getTTWithStaff($csId) {
		$ttaSql = "select tta.id as ttaId, tta.sss_id as sssId, tta.period_seq as pSeq, sub.short_name as subName, sub.long_name as subLongName, sub.display_color as displayColor, sub.is_multisection as subIsMS, sss.period_span as pSpan, tta.short_name as pName, tta.week_day as weekDay, tta.period_type as pType, tta.class_section_id as csId, sss.allocation_type_staff as alloc_type_staff from timetable_allocated tta left join staff_subject_section sss on tta.sss_id=sss.id left join subjects sub on sss.subject_id=sub.id where tta.class_section_id='$csId' order by tta.week_day,tta.period_seq";
  
		$staffListSql = "select slss.sss_id as sssId, sm.short_name as staffName, concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as lStaffName, tta.period_seq as pSeq, tta.short_name as pName from timetable_allocated tta left join staff_subject_section sss on tta.sss_id=sss.id left join stafflist_subject_section slss on sss.id=slss.sss_id left join staff_master sm on slss.staff_id=sm.id where tta.class_section_id='$csId' order by tta.week_day,tta.period_seq";
  
		$masterData = $this->db_readonly->query($ttaSql)->result();
		//echo '<pre>';print_r($masterData);
	
		$slaveData = $this->db_readonly->query($staffListSql)->result();
		//echo '<pre>';print_r($slaveData);
	
		foreach ($masterData as &$period) {
		  $staffString = '';
		  $lStaffString = '';
		  foreach ($slaveData as $staffList) {
			if (($period->sssId == $staffList->sssId) && ($period->pName == $staffList->pName)) {
			  if (!empty($staffString)) {
				$staffString = $staffString . ', ';
				$lStaffString = $lStaffString . ', ';
			  }
			  $staffString = $staffString . $staffList->staffName;
			  $lStaffString = $lStaffString . $staffList->lStaffName;
			}
		  }
		  $period = (object)array_merge((array)$period, array('staffList' => $staffString));
		  $period = (object)array_merge((array)$period, array('lStaffList' => $lStaffString));
		}
  
		//echo '<pre>';print_r($masterData);die();
		return $masterData;
	}
  
	public function getPeriodHeader($csId)
	{
		$result = $this->db_readonly->select('period_seq as pSeq, TIME_FORMAT(start_time, "%H:%i") as sTime, TIME_FORMAT(end_time, "%H:%i") as eTime, short_name as pName, long_name as pLongName, ttp.period_type as pType')
			->from('timetable_template_periods ttp')
			->join('timetable_template_class_section ttcs', 'ttp.ttt_id=ttcs.timetable_template_id', 'left')
			->where('ttcs.class_section_id', $csId)
			->where('ttp.week_day', '1')
			->order_by('ttp.id')
			->get()->result();

		return $result;
	}

	public function getNumberOfWeekDays($csId)
	{
		$result = $this->db_readonly->select('count(distinct(ttp.week_day)) as wdCount')
			->from('timetable_template_periods ttp')
			->join('timetable_template_class_section ttcs', 'ttp.ttt_id=ttcs.timetable_template_id', 'left')
			->where('ttcs.class_section_id', $csId)
			->get()->row();

		// echo $result; die();

		return $result->wdCount;
	}

	public function getAssessmentData($classId)
	{
		$asses = $this->db->select("a.id as assId, a.long_name as assName")
			->from("assessments a")
			->where("class_id", $classId)
			->where("current_status", '2') //2 => Published
			->get()->result();

		$assSubjects = $this->db->select("as.*, s.long_name as subName")
			->from("assessments_subjects as")
			->join("assessments a", "a.id=as.assessment_id", "left")
			->join("subjects s", "as.subject_id=s.id", "left")
			->order_by("as.date")
			->get()->result();
		//echo '<pre>';print_r($assSubjects);
		foreach ($asses as &$ass) {
			//Construct the Staff-List String
			$assSubs = array();
			foreach ($assSubjects as $assSub) {
				if ($ass->assId == $assSub->assessment_id) {
					$assSubs[] = $assSub;
				}
			}

			$ass = (object)array_merge((array)$ass, array('assSubjects' => $assSubs));
		}
		return $asses;
	}

	// public function get_assessmentparent_time_tableview()
	// {
	// 	$userId = $this->ion_auth->get_user_id();
	// 	$this->db->select('student_id');
	// 	$this->db->from('users');
	// 	$this->db->where('id',$userId);
	// 	$res = $this->db->get()->row();

	// 	$this->db->select('id,class,medium,std_board,std_section');
	// 	$this->db->from('users');
	// 	$this->db->where('id',$res->student_id);
	// 	$student_list = $this->db->get()->row();

	// 	$this->db->select('ASSESSMENTS.ID, ASSESSMENTS.SHORT_NAME,ASSESSMENTS.LONG_NAME,ASSESSMENTS.CLASS_ID,CURRENT_STATUS,DATE_FORMAT(DATE,"%d-%M") as DATE ,START_TIME,END_TIME,SUBJECT_ID,class_tbl.class,SUBJECTS_MAN.LONG_NAME AS SUBJECT_NAME');
	// 	$this->db->from('ASSESSMENTS');
	// 	$this->db->join('ASSESSMENTS_SUBJECTS','ASSESSMENTS_SUBJECTS.ASSESSMENT_ID=ASSESSMENTS.ID');
	// 	$this->db->join('class_tbl','class_tbl.id=ASSESSMENTS.CLASS_ID');
	// 	$this->db->join('SUBJECTS_MAN','SUBJECTS_MAN.ID=ASSESSMENTS_SUBJECTS.SUBJECT_ID');
	// 	$this->db->where('class_tbl.class',$student_list->class);
	// return	$this->db->get()->result();
	// }

	// parent initative

	public function insert_parent_initative_details()
	{
		$parentAvatarId = $this->authorization->getAvatarId();
		$data = array(
			'initiative'         => $this->input->post('initiative'),
			'audience'            => $this->input->post('audience'),
			'duration'           => $this->input->post('duration'),
			'availability'       => $this->input->post('availability'),
			'description'        => $this->input->post('description'),
			'brief_about_parent' => $this->input->post('brief_about_parent'),
			'status'             => 'Created (Not submitted)',
			'created_by'         => $parentAvatarId,
		);
		return $this->db->insert('parent_initiative', $data);
	}
	public function initiative_detailsALl()
	{
		$parentAvatarId = $this->authorization->getAvatarId();
		$this->db_readonly->where('created_by', $parentAvatarId);
		return $this->db_readonly->get('parent_initiative')->result();
	}
	public function edit_parent_initiativebyId($id)
	{
		$this->db->where('id', $id);
		return $this->db->get('parent_initiative')->row();
	}

	public function update_parent_initative_details($id)
	{
		$parentAvatarId = $this->authorization->getAvatarId();
		$data_update = array(
			'initiative'         => $this->input->post('initiative'),
			'audience'            => $this->input->post('audience'),
			'duration'           => $this->input->post('duration'),
			'availability'       => $this->input->post('availability'),
			'description'        => $this->input->post('description'),
			'brief_about_parent' => $this->input->post('brief_about_parent'),
			'status'             => 'Created',
			'created_by'         => $parentAvatarId,
		);
		$this->db->where('id', $id);
		return  $this->db->update('parent_initiative', $data_update);
	}
	public function delete_parent_initativebyId($id)
	{
		$this->db->where('id', $id);
		return $this->db->delete('parent_initiative');
	}
	public function submit_parent_initiative_school($id)
	{
		$parentAvatarId = $this->authorization->getAvatarId();
		$data_submit = array(
			'status'             => 'Submitted to school',
			'created_by'         => $parentAvatarId,
		);
		$this->db->where('id', $id);
		return  $this->db->update('parent_initiative', $data_submit);
	}



	public function get_Allsms_notification()
	{
		$userId = $this->ion_auth->get_user_id();
		$this->db->select('student_id');
		$this->db->from('users');
		$this->db->where('id', $userId);
		$res = $this->db->get()->row();

		$this->db->select('id,class,medium,std_board,std_section');
		$this->db->from('users');
		$this->db->where('id', $res->student_id);
		$student_list = $this->db->get()->row();
		$this->db->select("sms_master.id,date_format(sms_date,'%d-%m-%Y') as date ,sms_content,student_staff_id as student_id");
		$this->db->from('sms_master');
		$this->db->join('sms_sent_to', 'sms_sent_to.sms_id=sms_master.id');
		$this->db->where('sms_sent_to.student_staff_id', $student_list->id);
		$this->db->order_by('sms_master.sms_date', 'desc');
		return $this->db->get()->result();
	}

	public function getSMSInfo($studentId)
	{
		$date = date('Y-m-d', strtotime('-30 days'));
		$this->db->select('sm.id,sm.sms_date,sm.sms_content');
		$this->db->from('sms_master sm');
		$this->db->join('sms_sent_to st', 'st.sms_id=sm.id');
		$this->db->where('st.student_staff_id', $studentId);
		$this->db->where('sm.acad_year_id', $this->yearId);
		// $this->db->where('DATE_FORMAT(sm.sms_date, "%Y-%m-%d") >',$date);
		$this->db->order_by('sm.sms_date', 'desc');
		$this->db->limit(5);
		$result = $this->db->get()->result();
		return $result;
		// echo "<pre>";
		// print_r($result);die();
	}

	public function getCircularInfo($studentId)
	{
		$this->db->select('cm.id,cm.circular_date,cm.circular_title');
		$this->db->from('circular_master cm');
		$this->db->join('circular_sent_to ct', 'ct.circular_id=cm.id');
		$this->db->where('ct.stakeholder_id', $studentId);
		$this->db->where('cm.is_published', 1);
		$this->db->where('ct.avatar_type', 1);
		// $this->db->where('cm.acad_year_id', $this->yearId);
		$this->db->order_by('cm.circular_date', 'desc');
		$this->db->limit(5);
		$result = $this->db->get()->result();
		return $result;
		// echo "<pre>";
		// print_r($result);die();
	}

	public function getAttendanceSessions($class_id, $section_id, $day, $studentId)
	{
		$this->db_readonly->select('DATE_FORMAT(attsess.day, "%d-%m-%Y") as day, attstu.status, ttp.short_name');
		$this->db_readonly->from('attendance_session as attsess');
		$this->db_readonly->join('attendance_student as attstu', 'attstu.attendance_session_id = attsess.id');
		$this->db_readonly->join('attendance_master_group as attmg', 'attmg.id = attstu.attendance_master_group_id');
		$this->db_readonly->join('timetable_template_periods as ttp', 'ttp.id = attmg.timetable_template_periods_id');

		$this->db_readonly->where('attsess.class_id', $class_id);
		$this->db_readonly->where('attsess.class_section_id', $section_id);
		$this->db_readonly->where("attsess.day LIKE '" . $day . "%'");
		$this->db_readonly->where("attstu.student_id", $studentId);

		$pre_result = $this->db_readonly->get()->result_array();
		return $pre_result;
	}

	// Remove code after check 

	// public function getAttendanceSessions_presentday_allyear($class_id, $section_id, $studentId){
	// 	if (empty($this->session->userdata('present')->present_daycount)) {
	// 		$this->db->select('count(attsess.day) as present_daycount');
	//         $this->db->from('attendance_session as attsess');
	//         $this->db->join('attendance_student as attstu','attstu.attendance_session_id = attsess.id');
	//         $this->db->join('attendance_master_group as attmg','attmg.id = attstu.attendance_master_group_id');
	//         $this->db->join('timetable_template_periods as ttp','ttp.id = attmg.timetable_template_periods_id');
	//         $this->db->where('attsess.class_id', $class_id);
	//         $this->db->where('attsess.class_section_id', $section_id);
	//         $this->db->where("attsess.day LIKE '".'2018'."%'");
	//         $this->db->where("attstu.student_id", $studentId);
	//         $this->db->where("attstu.status", 1);
	//         $att_presentDay =  $this->db->get()->row();

	//         $this->session->set_userdata('present', $att_presentDay);
	// 	}
	// }

	// public function getAttendanceSessions_absentday_allyear($class_id, $section_id, $studentId){
	// 	if (empty($this->session->userdata('absent')->absent_daycount)){
	// 		$this->db->select('count(attsess.day) as absent_daycount');
	//         $this->db->from('attendance_session as attsess');
	//         $this->db->join('attendance_student as attstu','attstu.attendance_session_id = attsess.id');
	//         $this->db->join('attendance_master_group as attmg','attmg.id = attstu.attendance_master_group_id');
	//         $this->db->join('timetable_template_periods as ttp','ttp.id = attmg.timetable_template_periods_id');
	//         $this->db->where('attsess.class_id', $class_id);
	//         $this->db->where('attsess.class_section_id', $section_id);
	//         $this->db->where("attsess.day LIKE '".'2018'."%'");
	//         $this->db->where("attstu.student_id", $studentId);
	//         $this->db->where("attstu.status", 0);
	//         $att_absentDay = $this->db->get()->row();

	//       //	print_r($absentDay);die();
	//         $this->session->set_userdata('absent', $att_absentDay);
	// 	} 


	// }

	public function getLateComerDetails($studentId, $day)
	{
		$this->db_readonly->select('DATE_FORMAT(entry_at, "%d-%m-%Y") as entry_at');
		$this->db_readonly->from('student_actions as stuac');
		$this->db_readonly->where("stuac.entry_at LIKE '" . $day . "%'");
		$this->db_readonly->where("stuac.student_id", $studentId);
		$pre_result = $this->db_readonly->get()->result_array();
		return $pre_result;
	}

	// Remove code after check 

	// public function getLateComerDetails_allyear($studentId) {
	// 	if (empty($this->session->userdata('late_comer')->late_comer)){
	// 		$this->db->select('count(entry_at) as late_comer');
	//         $this->db->from('student_actions as stuac');
	//         $this->db->where("stuac.	entry_at LIKE '".'2018'."%'");
	//         $this->db->where("stuac.student_id", $studentId);
	//         $late_days = $this->db->get()->row();
	//          	$this->session->set_userdata('late_comer', $late_days);
	// 	} 


	// }


	public function getAssessmentIds($classId, $sectionId)
	{
		$result = $this->db_readonly->select('assessment_id as assId')->where('class_section_id', $sectionId)->where('class_id', $classId)->get('assessment_sections')->result();
		$assArr = array();
		foreach ($result as $key => $value) {
			array_push($assArr, $value->assId);
		}
		if(empty($assArr)) {
			return array();
		}

		return $this->db_readonly->select('*')->where('publish_status', 'Published')->where_in('id', $assArr)->get('assessments')->result();
		// return $this->db->select('*')->where_in('id',$assArr)->get('assessments')->result();
	}

	public function getPublishedAssessmentsOfStudent($classId, $sectionId)
	{
		$sql = "select a.id as assId,a.short_name,a.long_name,ap.id as portionId,ap.portions from assessments a 
					join assessment_portions ap on ap.assessment_id=a.id 
					where ap.publish_status=1 and a.class_id=$classId";
		return $this->db_readonly->query($sql)->result();
	}

	public function getPortions($pId)
	{
		return $this->db_readonly->query("select * from assessment_portions where id=$pId")->row();
	}

	public function getGroupTT($assId)
	{
		$this->db->select('asg.*');
		$this->db->from('assessment_subject_group asg');
		$this->db->where('asg.assessment_id', $assId);
		$this->db->order_by('asg.date');
		$result = $this->db->get()->result();
		return $result;
	}

	public function getEntityTT($assId)
	{
		$this->db->select('ae.*');
		$this->db->from('assessments_entities ae');
		$this->db->where('ae.assessment_id', $assId);
		$this->db->order_by('ae.date');
		$result = $this->db->get()->result();
		return $result;
	}

	public function getAssessmentTT($assId)
	{
		$this->db_readonly->select('asg.id as asgId, asg.assessment_id as assId, asg.ass_entity_gid assEGid, asg.portions as gPortions, asg.date as gDate, asg.start_time as gStart, asg.end_time as gEnd, asg.portions_at, ae.entity_id, ae.portions, ae.date, ae.start_time, ae.end_time,ae.total_marks');
		$this->db_readonly->from('assessment_subject_group asg');
		$this->db_readonly->join('assessments_entities ae', 'asg.id=ae.ass_subject_gid');
		$this->db_readonly->where('asg.assessment_id', $assId);
		$this->db_readonly->order_by('asg.date, ae.date');
		$result = $this->db_readonly->get()->result();
		return $result;
		// return $this->db->select('ae.*, e.name')->where('assessment_id', $assId)->from('assessments_entities ae')->join('assessment_entity_master e', 'ae.entity_id=e.id')->get()->result();
	}

	public function getOverallTT($assId)
	{
		$minG = $this->db_readonly->query('select `date` from `assessment_subject_group` where `assessment_id`=' . $assId . ' and date is NOT NULL order by `date` asc limit 1')->row();
		$maxG = $this->db_readonly->query('select date from assessment_subject_group where assessment_id=' . $assId . ' and date is NOT NULL order by date desc limit 1')->row();
		$minE = $this->db_readonly->query('select date from assessments_entities where assessment_id=' . $assId . ' and date is NOT NULL order by date asc limit 1')->row();
		$maxE = $this->db_readonly->query('select date from assessments_entities where assessment_id=' . $assId . ' and date is NOT NULL order by date desc limit 1')->row();

		$min = $minG;
		$max = $maxG;
		if (!empty($minE) && $minE < $min)
			$min = $minE;
		if (!empty($maxE) && $maxE > $max)
			$max = $maxE;

		return array('min' => $min, 'max' => $max);
	}

	public function getGroupNames($classId)
	{
		$result = $this->db_readonly->select('id,entity_name')->where('class_id', $classId)->get('assessment_entities_group')->result();
		$arr = array();
		foreach ($result as $key => $value) {
			$arr[$value->id] = $value->entity_name;
		}
		return $arr;
	}

	public function getEntityNames($classId)
	{
		$result = $this->db_readonly->select('id,name')->where('class_id', $classId)->get('assessment_entity_master')->result();
		$arr = array();
		foreach ($result as $key => $value) {
			$arr[$value->id] = $value->name;
		}
		return $arr;
	}
	public function getAssessmentName($assId)
	{
		return $this->db_readonly->select('*')->where('id', $assId)->get('assessments')->row();
	}

	public function getMarksCards($studentId)
	{
		$this->db_readonly->select("a.id as tempId, ah.pdf_link, a.template_name, ah.date, ah.id as hisId, amc.id as cardId, amc.ack_status, CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as parentName, amc.ack_on, a.grading_systems, ay.acad_year");
		$this->db_readonly->from('assessments_marks_cards amc');
		$this->db_readonly->join('assessments_marks_card_history ah', 'amc.active_marks_card_id=ah.id');
		$this->db_readonly->join('assessment_marks_card_templates a', 'amc.marks_card_temp_id=a.id');
		$this->db_readonly->join('avatar av', 'av.id=amc.ack_by and av.avatar_type=2', 'left');
		$this->db_readonly->join('parent p', 'av.stakeholder_id=p.id', 'left');
		$this->db_readonly->join('class c', 'a.class_id=c.id','left');
		$this->db_readonly->join('academic_year ay', 'c.acad_year_id=ay.id','left');
		$this->db_readonly->where('amc.student_id', $studentId);
		$this->db_readonly->where('a.publish_status', 1);
		$this->db_readonly->where('amc.published', 1);
		$this->db_readonly->order_by('ah.date');
		return $this->db_readonly->get()->result();
	}

	public function downloadMarksCard($id)
	{
		return $this->db->select('pdf_link')->where('id', $id)->get('assessments_marks_card_history')->row()->pdf_link;
	}

	public function get_markscard_created_date($id) {
		return $this->db->select('DATE_FORMAT(date, "%d-%m-%Y") as created_date')->where('id', $id)->get('assessments_marks_card_history')->row()->created_date;
	}

	public function download_fee_receipt($id)
	{
		return $this->db->select('receipt_pdf_link')->where('id', $id)->get('feev2_transaction')->row()->receipt_pdf_link;
	}

	public function download_fee_receipt_file_name($id){
		return $this->db->select("concat(sa.first_name,' ',ifnull(sa.last_name,'')) as student_name, ft.receipt_number")
		->from('feev2_transaction ft')
		->join('student_admission sa','ft.student_id = sa.id')
		->where('ft.id', $id)
		->get()->row();
	}

	public function assInTemplate($tempId)
	{
		return $this->db_readonly->select('amt.class_id, amt.assessments, amt.template_name, amc.id as cardId, amt.grading_systems')
        ->from('assessment_marks_card_templates amt')
        ->join('assessments_marks_cards amc', 'amc.marks_card_temp_id = amt.id')
        ->where('amt.id', $tempId)
        ->get()
        ->row();
	}

	public function getGradingSystem($gIds)
	{
		$this->db_readonly->select('*');
		$this->db_readonly->from('assessment_grading_system');
		$this->db_readonly->where_in('id', $gIds);
		return $this->db_readonly->get()->result();

		// $ass = implode(',', $assIds);
		// $this->db->select('ags.id, ags.grades, aeg.entity_name as subName, aeg.id as aegId');
		// $this->db->from('assessment_grading_system ags');
		// $this->db->join('assessment_entities_group aeg', 'aeg.grading_system_id=ags.id');
		// $this->db->where("aeg.id in (select distinct ass_entity_gid from assessment_entity_master where id in (select distinct(entity_id) from assessments_entities where assessment_id in ('$ass')))");
		// return $this->db->get()->result();
	}

	public function getCardDetails($cardId)
	{
		$this->db_readonly->select("ac.id as cardId, ac.active_marks_card_id as linkId, t.id as tempId, t.template_name as tempName, ac.ack_status, CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as parentName, ac.ack_on, t.grading_systems");
		$this->db_readonly->from('assessment_marks_card_templates t');
		$this->db_readonly->join('assessments_marks_cards ac', 'ac.marks_card_temp_id=t.id');
		$this->db_readonly->join('avatar av', 'av.id=ac.ack_by and av.avatar_type=2', 'left');
		$this->db_readonly->join('parent p', 'av.stakeholder_id=p.id', 'left');
		$this->db_readonly->where('ac.id', $cardId);
		return $this->db_readonly->get()->row();
	}

	public function getCardTemplateData($tempId)
	{
		$this->db_readonly->select('id, template_name, assessments');
		$this->db_readonly->from('assessment_marks_card_templates');
		$this->db_readonly->where('id', $tempId);
		return $this->db_readonly->get()->row();
	}

	public function getAllAssessmentsOfTemplate($stdId)
	{
		$this->db_readonly->select('assessments');
		$this->db_readonly->from('assessment_marks_card_templates');
		$this->db_readonly->where("class_id in (select class_id from student_admission where id=$stdId)");
		return $this->db_readonly->get()->result();
	}

	public function getSubjectsUnion($assessment)
	{
		$assIds = implode(",", $assessment);
		$sql = "select distinct(id),name from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id in (" . $assIds . ")) and (ass_type='Internal' or ass_type='External')";
		return $this->db_readonly->query($sql)->result();
		// echo "<pre>"; print_r($return); die();
	}

	public function acknowledge($cardId)
	{
		$avatarId = $this->authorization->getAvatarId();
		$date = date('Y-m-d H:i:s');
		$data = array(
			'ack_status' => 1,
			'ack_by' => $avatarId,
			'ack_on' => $date
		);
		$this->db->where('id', $cardId);
		$ret = $this->db->update('assessments_marks_cards', $data);
		$pName = $this->db->select("CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as parentName")->from('avatar a')->join('parent p', 'p.id=a.stakeholder_id')->where('a.id', $avatarId)->get()->row()->parentName;
		if ($ret)
			return array('name' => $pName, 'date' => $date);
		return array();
	}

	public function getAssDetails($assessment)
	{
		$assIds = implode(",", $assessment);
		return $this->db_readonly->query('select * from assessments where id in (' . $assIds . ')')->result();
	}

	public function getMarks($studentId, $assessments, $entityId)
	{
		$this->db->select('ae.total_marks, a.short_name, a.generation_type, ams.marks, ROUND((ams.marks/ae.total_marks)*100) as percentage');
		$this->db->from('assessments_entities ae');
		$this->db->join('assessments a', 'a.id=ae.assessment_id');
		$this->db->join('assessments_entities_marks_students ams', 'ams.assessments_entities_id=ae.id');
		$this->db->where('ae.entity_id', $entityId);
		$this->db->where('ams.student_id', $studentId);
		$this->db->where_in('ae.assessment_id', $assessments);
		$this->db->order_by('ae.assessment_id');
		return $this->db->get()->result();
	}

	public function fetch_provision_username()
	{
		$avatarId = $this->authorization->getAvatarId();
		return $this->db->select('u.username,u.id,u.active,freeze_username')
			->from('avatar a')
			->where('a.id', $avatarId)
			->join('users u', 'a.user_id=u.id')
			->get()->row();
	}

	public function checkUsernamebyparentwise($username)
	{
		$res = $this->db->query("select * from users where username ='$username'")->num_rows();
		return $res;
	}

	public function update_parent_username_update($id)
	{
		$user_name = $this->input->post('username_new');
		if (empty($user_name)) {
			return 0;
		}
		$res = $this->db->query("select * from users where username ='$user_name'")->num_rows();
		if (empty($res)) {
			$data = array(
				'username' => $user_name,
				'freeze_username' => 1,
			);
			$this->db->where('id', $id);
			$this->db->where('active', 1);
			return $this->db->update('users', $data);
		}
		return 0;
	}

	public function showBox()
	{
		$avatarId = $this->authorization->getAvatarId();
		$this->db->select('u.donot_show');
		$this->db->from('users u');
		$this->db->join('avatar a', 'a.user_id=u.id');
		$this->db->where('a.id', $avatarId);
		return $this->db->get()->row()->donot_show;
	}

	public function getElectives($stdId)
	{
		$this->db->select('aeg.entity_name, ae.friendly_name');
		$this->db->from('assessment_students_elective as');
		$this->db->join('assessment_entities_group aeg', 'as.ass_entity_gid=aeg.id');
		$this->db->join('assessment_elective_group ae', 'as.ass_elective_gid=ae.id');
		$this->db->where('as.student_id', $stdId);
		return $this->db->get()->result();
	}

	public function get_canteen_details($stdId)
	{
		$cantee_load = $this->db_readonly->select("sum(ct.amount) as loadmoney, concat(ifnull(s.first_name,''), ' ' ,ifnull(s.last_name,'')) as name")
			->from('student_admission s')
			->join('card_transaction ct', 's.identification_code=ct.identification_code')
			->where('transaction_mode', 'load')
			->where('transcation_type', 'canteen')
			->where('s.id', $stdId)
			->get()->row();

		$canteen_pay = $this->db_readonly->select("sum(ct.amount) as paymoney")
			->from('student_admission s')
			->join('card_transaction ct', 's.identification_code=ct.identification_code')
			->where('transaction_mode', 'pay')
			->where('transcation_type', 'canteen')
			->where('s.id', $stdId)
			->get()->row();
		$cantee_load->pay = $canteen_pay->paymoney;
		return $cantee_load;
	}

	public function _get_group_assigned_gallery_ids($group_id, $gallery_id, $studentId, $clasSectionId){
		$result = $this->db->select('group_json')
		->from('texting_groups')
		->where('id',$group_id)
		->get()->row();
		$jsnDecode = json_decode($result->group_json);
		$found = 0;
		if (in_array($studentId, $jsnDecode->students) || in_array($clasSectionId->class_section_id, $jsnDecode->class_section)) {
			$found = 1;
		}

		if ($found == 1) {
			return $gallery_id;
		}
	}

	public function load_galleries () {
		$result = $this->db_readonly->select('gl.publish_status, gl.gallery_id, glv.source, glv.source_id')
		->from('gallery_list gl')	 	
		->join('gallery_list_visibility glv','gl.gallery_id=glv.gallery_list_id','left')
		// ->where('glv.source','group')
		->where('gl.publish_status',1)
		->where('gl.gallery_status',1)
		->get()->result();

		if (!empty($result)) {
			$studentId = $this->parent_model->getStudentIdOfLoggedInParent();

			$this->db_readonly->select('sy.class_section_id');
			$this->db_readonly->from('student_year sy');
			$this->db_readonly->where('student_admission_id',$studentId);
			$this->db_readonly->where('sy.acad_year_id',$this->yearId);
			$clasSectionId =  $this->db_readonly->get()->row();

			$galleryids = [];
			foreach ($result as $key => $val) {
				
				if (!empty($val->source)) {
					$gallery_id = '';
					if ($val->source == 'group') {
						if ($val->source_id == 0) continue; //Manjukiran 9-June-2023. Certain group ids are going as 0. Adding a temporary fix.
						$gallery_id = $this->_get_group_assigned_gallery_ids($val->source_id, $val->gallery_id, $studentId, $clasSectionId);
					}
					if ($val->source == 'class_section' && $val->source_id == $clasSectionId->class_section_id) {
						$gallery_id = $val->gallery_id;
					}
					array_push($galleryids, $gallery_id);
				}else{
					array_push($galleryids, $val->gallery_id);
				}
			}

			$noImagePath = "nextelement-common/no_gallery_image.png";
			$this->db_readonly->select("gl.gallery_location,gl.gallery_description,DATE_FORMAT(gl.gallery_date,'%d-%m-%Y') as gallery_date ,gim.is_image,gl.gallery_id, (case when gim.image_name is null then '$noImagePath' else gim.image_name end) as image_name, count(gim.gallery_id) as img_count, gl.gallery_date, gl.gallery_name")
		 	->from('gallery_list gl')
		 	->join('gallery_images_master gim', 'gim.gallery_id=gl.gallery_id and gim.image_status=1','left')
		 	->where('gl.publish_status',1)
		 	// ->where('gim.is_image',1)
		 	->group_by('gl.gallery_id')
			->order_by('gl.gallery_id', 'DESC');
			if ($galleryids) {
				$this->db_readonly->where_in('gl.gallery_id',$galleryids);
			}
		 	$galleries = $this->db_readonly->get()->result();

			$today = date('d-m-Y');
		 	foreach ($galleries as $key => $val) {
		 		$weekPlus = date('d-m-Y', strtotime("+7 day", strtotime($val->gallery_date)));
		 		$val->is_new = 0;
	            if($today >= $val->gallery_date && $today <= $weekPlus) {
	                $val->is_new = 1;
	            }
		 	} 
			
		 	return $galleries;
			
		}
		
	 	
	}

	public function checkNewGalleryAvailable() {
		$gallery_updated_at = $this->session->userdata('gallery_updated_at');
		$today = date('Y-m-d');
		$last_week = date('Y-m-d', strtotime('-7 days', strtotime($today)));
		if(!$gallery_updated_at) {
			//to force update at first call
			$gallery_updated_at = date('Y-m-d', strtotime('-1 days', strtotime($today)));
			$this->session->set_userdata('gallery_updated_at', $today);
		}

		if($today != $gallery_updated_at) {
			$galleries = $this->db_readonly->select("count(gl.gallery_date) as galleries")
			->from('gallery_list gl')
			->where('gl.gallery_status', 1)
			->where('gl.publish_status', 1)
			->where('(gl.gallery_visibility=3 OR gl.gallery_visibility=4)')
			->where("(gl.gallery_date>='$last_week')")
			->get()->row()->galleries;
			
			$this->session->set_userdata('new_gallery_exists', $galleries);
			return $galleries;
		} else {
			return $this->session->userdata('new_gallery_exists');
		}
	}

	public function get_gallery_info($gallery_id)
	{
		$query = $this->db->get_where('gallery_list', array('gallery_id' => $gallery_id))->row();
		return $query;
	}

	public function get_images_info($gallery_id)
	{
		$this->db->order_by('gallery_images_master.image_id', 'DESC');
		$query = $this->db->get_where('gallery_images_master', array('gallery_id' => $gallery_id, 'image_status' => 1));
		return $query->result_array();;
	}

	public function getPrevGallaryId($gallary_id)
	{
		$this->db->select_min('gallery_id');

		$query = $this->db->get('gallery_list')->row_array();
		$Min_Gal_Id = $query['gallery_id'];
		if ($Min_Gal_Id == $gallary_id) {
			$this->session->set_flashdata('flashWarning', 'No previous Galleries found.');
			redirect('parent_controller/view_gallery/' . $gallary_id);
		}
		for ($temp = $gallary_id - 1; $Min_Gal_Id <= $temp; $temp--) {
			if ($Min_Gal_Id == $temp) {
				redirect('parent_controller/view_gallery/' . $temp);
			} else {
				$query = $this->db->get_where('gallery_list', array('gallery_id' => $temp))->row_array();
				if ($query != 0) {
					redirect('parent_controller/view_gallery/' . $temp);
				}
			}
		}
	}

	public function getNextGalleryId($gallary_id)
	{
		$this->db->select_max('gallery_id');
		$query = $this->db->get('gallery_list')->row_array();
		$Max_Gal_Id = $query['gallery_id'];
		if ($Max_Gal_Id == $gallary_id) {
			$this->session->set_flashdata('flashWarning', 'No further Galleries found.');
			redirect('galleries/view_gallery/' . $gallary_id);
		}
		for ($temp = $gallary_id + 1; $Max_Gal_Id >= $temp; $temp++) {
			if ($Max_Gal_Id == $temp) {
				redirect('galleries/view_gallery/' . $temp);
			} else {
				$query = $this->db->get_where('gallery_list', array('gallery_id' => $temp))->row_array();
				if ($query != 0) {
					redirect('galleries/view_gallery/' . $temp);
				}
			}
		}
	}

	// Fees

	public function get_blueprints()
	{
		return $this->db->select('id,name,description')->from('feev2_blueprint')->get()->result();
	}

	public function get_published_blueprints($student_id)
	{
		$zero_date = '0000-00-00';
		$bps = $this->db_readonly->select("fb.id as fb_id, fcs.id as cohort_student_id, fss.id as std_sch_id, fb.name as blueprint_name, fb.description as blueprint_desc, fss.payment_status as payment_status, fcs.online_payment, pre_condition_blueprint,fb.acad_year_id, fb.transport_selection, 'disabled' as enable_status, 'This Fee will be enabled after the other enabled Fees are paid' as 'message', fb.tnc_path, fb.tnc_enabled, fcs.tnc_status, ifnull(fcs.tnc_accepted_by,0) as parent_id, date_format(tnc_accepted_on,'%d-%m-%Y') as accepted_on, (case when pay_date is null then 0 when pay_date = $zero_date then 0 when pay_date < CURDATE() then 1 else 0 end) paydateCheck")
			->from('feev2_cohort_student fcs')
			->join('feev2_blueprint fb', "fcs.blueprint_id=fb.id")
			->join('feev2_student_schedule fss', 'fss.feev2_cohort_student_id=fcs.id')
			->where('fcs.student_id',$student_id)
			->where('fcs.publish_status','PUBLISHED')
			->where('fss.payment_status!=','FULL')
			->order_by('fb.pre_condition_blueprint','desc')
			->get()->result();

		$temp_bp = null;
		foreach ($bps as &$bp) {
			if ($bp->pre_condition_blueprint % 2 == 0) {
				//Check if paid full
				$cond = ($bp->payment_status != 'FULL');
			} else {
				//Check if paid partial
				$cond = ($bp->payment_status == 'NOT_STARTED');
			}
			if ($cond) {
				$temp_bp = $bp;
				break;
			}
		}
		if (sizeof($bps) == 1 ) {
			$bps = $this->__enable_this_order($bps, 0);
		} else {
			if ($temp_bp != null) 
				$bps = $this->__enable_this_order($bps, $temp_bp->pre_condition_blueprint);
		}

		$bpArry = [];
		foreach ($bps as $key => $val) {
			$bpArry[$val->acad_year_id][] = $val;
		}
		return $bpArry;
	}

	private function __enable_this_order($bps, $check_order) {
		foreach ($bps as &$bp) {
			if ($bp->pre_condition_blueprint == 0 || $bp->pre_condition_blueprint >= $check_order) {
				// echo '<pre>order';print_r($bp->pre_condition_blueprint);
				switch ($bp->payment_status) {
					case 'NOT_STARTED':
					case 'PARTIAL':
						$bp->enable_status = 'enabled';
						$bp->message = '';
						break;
					case 'FULL':
						$bp->enable_status = 'disabled';
						break;
				}
			}
		}
		// echo '<pre>';print_r($bps);die();
		return $bps;
	}

	public function get_transport_blueprints_is_assinged($student_id){
		$reuslt = $this->db_readonly->select("fcs.id")
			->from('feev2_cohort_student fcs')
			->where('fcs.student_id',$student_id)
			->join('feev2_blueprint fb','fb.id=fcs.blueprint_id')
			->where('fb.is_transport',1)
			->where('fb.acad_year_id',$this->yearId)
			->get()->row();
		if (!empty($reuslt)) {
			if (!empty($reuslt->id) ) {
				return 1;
			}else{
				return 0;
			}
		}
	}

	// private function __enable_this_order($bps, $check_order) {
	// 	foreach ($bps as &$bp) {
	// 		if ($bp->pre_condition_blueprint == $check_order) {
	// 			switch ($bp->payment_status) {
	// 				case 'NOT_STARTED':
	// 				case 'PARTIAL':
	// 					$bp->enable_status = 'enabled';
	// 					$bp->message = '';
	// 					break;
	// 				case 'FULL':
	// 					$bp->enable_status = 'disabled';
	// 					break;
	// 			}
	// 		}
	// 	}
	// 	return $bps;
	// }

	public function get_published_blueprints_status($student_id, $blueprint_id){
		$bps = $this->db_readonly->select("fcs.id as cohort_student_id, fcs.online_payment, fee_collect_status, fb.message, fb.enable_online_partial_payment, fb.tnc_path, fb.tnc_enabled, fcs.tnc_status, fb.enable_parent_installment_selection,fb.enable_parent_manual_amount,enable_parent_manual_min_amount, fb.acad_year_id, fb.hide_component_details")
			->from('feev2_cohort_student fcs')
			->where('fcs.student_id',$student_id)
			->where('fcs.blueprint_id',$blueprint_id)
			->join('feev2_blueprint fb','fb.id=fcs.blueprint_id')
			->get()->row();
		return $bps;
	}
	public function transaction_status($std_sch_id)
	{

		$result = $this->db_readonly->select('status')
			->from('feev2_transaction')
			->where('fee_student_schedule_id', $std_sch_id)
			->order_by('id desc')
			->limit(1)
			->get()->row();

		if (empty($result)) {
			return '';
		} else {
			return $result->status;
		}
	}

	public function get_concession_remarksby_cohort_std_id($cohortstudentId){
		$result = $this->db_readonly->select('concession_name as concession_remarks')
			->from('feev2_concessions')
			->where('cohort_student_id', $cohortstudentId)
			->where('is_applied',0)
			->get()->row();

		if (empty($result)) {
			return '';
		} else {
			return $result->concession_remarks;
		}
	}

	public function get_adjustment_remarksby_cohort_std_id($cohortstudentId){
		$result = $this->db_readonly->select('adjustment_remarks')
			->from('feev2_adjustment')
			->where('cohort_student_id', $cohortstudentId)
			->where('is_applied',0)
			->get()->row();

		if (empty($result)) {
			return '';
		} else {
			return $result->adjustment_remarks;
		}
	}

	public function get_std_fee_amount_details($std_sch_id)
	{
		$today = date('Y-m-d');

		$sql = "SELECT `fsi`.`id` as `fsInsId`, `fsi`.`feev2_installments_id`, `fsi`.`fee_student_schedule_id`, `fsic`.`id` as `fsicompId`, `fsic`.`blueprint_component_id`, `fsic`.`component_amount`,  ifnull(`fsic`.`component_amount_paid`,0) as  component_amount_paid, `fsic`.`concession_amount_paid`, `fsic`.`concession_amount`, `fbc`.`name` as `component_name`, `fi`.`name` as `insName`, `fbc`.`is_concession_eligible`, `fsi`.`status`, date_format(fi.end_date, '%d-%m-%Y') as end_date, end_date as ins_due_date, `fsic`.`adjustment_amount_paid`, `fsic`.`adjustment_amount`, (ifnull(fsi.total_fine_amount, 0) - ifnull(fsi.total_fine_amount_paid, 0) - ifnull(fsi.total_fine_waived, 0)) as fine_amount, `fi`.`start_date`, `fb`.`open_installment_at_start_date_only`
			FROM `feev2_student_installments` `fsi`
			JOIN `feev2_student_installments_components` `fsic` ON `fsi`.`id`=`fsic`.`fee_student_installment_id`
			JOIN `feev2_installments` `fi` ON `fsi`.`feev2_installments_id`=`fi`.`id`
			JOIN `feev2_blueprint_components` `fbc` ON `fsic`.`blueprint_component_id`=`fbc`.`id`
			JOIN `feev2_blueprint` `fb` ON `fbc`.`feev2_blueprint_id`=`fb`.`id`
			WHERE `fsi`.`fee_student_schedule_id` = $std_sch_id and (case when fb.open_installment_at_start_date_only = 1 then fi.start_date <= '$today' else `fi`.`id` is not null end) order by feev2_installments_id " ;
			//Else condition in CASE added to counter a bug in MySQL
		$result =  $this->db_readonly->query($sql)->result();

		// $this->db_readonly->select('fsi.id as fsInsId, fsi.feev2_installments_id, fsi.fee_student_schedule_id, fsic.id as fsicompId, fsic.blueprint_component_id, fsic.component_amount, fsic.component_amount_paid, fsic.concession_amount_paid, fsic.concession_amount, fbc.name as component_name, fi.name as insName, fbc.is_concession_eligible, fsi.status, date_format(fi.end_date, "%d-%m-%Y") as end_date, fsic.adjustment_amount_paid, fsic.adjustment_amount, (ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_amount_paid,0) - ifnull(fsi.total_fine_waived,0)) as fine_amount,fi.start_date, fb.open_installment_at_start_date_only');
		// $this->db_readonly->from('feev2_student_installments fsi');
		// $this->db_readonly->join('feev2_student_installments_components fsic', 'fsi.id=fsic.fee_student_installment_id');
		// $this->db_readonly->where('fsi.fee_student_schedule_id', $std_sch_id);
		// // $this->db_readonly->where('fsi.status!=','FULL');
		// $this->db_readonly->join('feev2_installments fi', 'fsi.feev2_installments_id=fi.id');
		// $this->db_readonly->join('feev2_blueprint_components fbc', 'fsic.blueprint_component_id=fbc.id');
		// $this->db_readonly->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id');
		// $this->db_readonly->where('case when fb.open_installment_at_start_date_only = 1 then fsi.start_date <= 2021-06-04 then else 0 end)');
		// $result =  $this->db_readonly->get()->result();
		$ins_data = [];
		$installment_name = [];
		foreach ($result as $key => $val) {
			
			$ins_data[$val->feev2_installments_id][] = $val;
			$installment_name[$val->feev2_installments_id] = array('insName' => $val->insName, 'status' => $val->status, 'end_date' => $val->end_date,'fine_amount'=>$val->fine_amount,'ins_due_date'=>$val->ins_due_date);
			
		}
		return array('ins_data' => $ins_data, 'installment_name' => $installment_name);
	}

	public function get_std_fee_amount_confirm($fee_student_schedule_id, $insId)
	{
		$this->db_readonly->select('fsi.id as fsInsId, fsi.feev2_installments_id, fsi.fee_student_schedule_id, fsic.id as fsicompId, fsic.blueprint_component_id,fsic.component_amount, fsic.component_amount_paid, fsic.concession_amount_paid, fsic.concession_amount, fbc.name as component_name, fi.name as insName, fbc.is_concession_eligible, date_format(fi.end_date, "%d-%m-%Y") as end_date, fsic.adjustment_amount_paid, fsic.adjustment_amount, (ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_amount_paid,0) - ifnull(fsi.total_fine_waived,0)) as fine_amount');
		$this->db_readonly->from('feev2_student_installments fsi');
		$this->db_readonly->join('feev2_student_installments_components fsic', 'fsi.id=fsic.fee_student_installment_id');
		$this->db_readonly->where('fsi.fee_student_schedule_id', $fee_student_schedule_id);
		$this->db_readonly->where('fsi.feev2_installments_id', $insId);
		$this->db_readonly->join('feev2_installments fi', 'fsi.feev2_installments_id=fi.id');
		$this->db_readonly->join('feev2_blueprint_components fbc', 'fsic.blueprint_component_id=fbc.id');
		return $this->db_readonly->get()->result();
	}
	// Fee multiple section
	public function get_std_fee_installments_amount_confirm($fee_student_schedule_id, $insId){
  		$insIds = [];
  		foreach ($insId as $insId => $val) {
  			array_push($insIds, $insId);
  		}
		$this->db_readonly->select('fsi.id as fsInsId, fsi.feev2_installments_id, fsi.fee_student_schedule_id, fsic.id as fsicompId, fsic.blueprint_component_id,fsic.component_amount, fsic.component_amount_paid, fsic.concession_amount_paid, fsic.concession_amount, fbc.name as component_name, fi.name as insName, fbc.is_concession_eligible, date_format(fi.end_date, "%d-%m-%Y") as end_date, fsic.adjustment_amount_paid, fsic.adjustment_amount, (ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_amount_paid,0) - ifnull(fsi.total_fine_waived,0)) as fine_amount');
		$this->db_readonly->from('feev2_student_installments fsi');
		$this->db_readonly->join('feev2_student_installments_components fsic', 'fsi.id=fsic.fee_student_installment_id');
		$this->db_readonly->where('fsi.fee_student_schedule_id', $fee_student_schedule_id);
		$this->db_readonly->where_in('fsi.feev2_installments_id', $insIds);
		$this->db_readonly->join('feev2_installments fi', 'fsi.feev2_installments_id=fi.id');
		$this->db_readonly->join('feev2_blueprint_components fbc', 'fsic.blueprint_component_id=fbc.id');
		$result =  $this->db_readonly->get()->result();
		$ins_data = [];
		foreach ($result as $key => $val) {
			$ins_data[$val->feev2_installments_id][] = $val;
		}
		return $ins_data;
	}

	public function check_trans_status($std_sch_id)
	{
		$result = $this->db->select('status')
			->from('feev2_transaction')
			->where('fee_student_schedule_id', $std_sch_id)
			->where('status', 'INITIATED')
			->get()->row();

		if (empty($result)) {
			return 0;
		} else {
			return 1;
		}
	}

	public function transaction_statusby_source_id($transId)
	{
		$result = $this->db->select('status')
			->from('feev2_transaction')
			->where('id', $transId)
			->get()->row();
		return $result;
	}

	public function opm_recon_status_update($fTranss, $trans_status, $response_status)
	{
		$this->db->where('id', $fTrans);
		return $this->db->update(
			'feev2_transaction',
			array(
				'op_recon_status' => $trans_status . 'after' .  $response_status
			)
		);
	}

	public function get_fee_transaction_history($student_id, $acadyearId='')
	{
		
	
		$maxBluprintAcadIds =  $this->db_readonly->select("max(fb.acad_year_id) as acadyearId ")
			->from('feev2_cohort_student fcs')
			->join('feev2_blueprint fb', "fcs.blueprint_id=fb.id")
			->where('fcs.student_id',$student_id)
			->where('fcs.publish_status','PUBLISHED')
			->get()->row();
		// $maxBluprintAcadIds = $this->db->select('max(acad_year_id) as acadyearId')
		// ->from('feev2_blueprint fb')
		// ->get()->row();
			
		if (empty($maxBluprintAcadIds)) {
			$acadyearId = $this->acad_year->getAcadYearId();
		}
		if (empty($acadyearId)) {
			$acadyearId = $maxBluprintAcadIds->acadyearId;
		}
		$blueprints = $this->db->select('fb.name, fss.id as fee_student_schedule_id,fss.payment_status, fss.consolidated_fee_pdf_path, fss.pdf_status as full_pdf_status, fb.consolidated_receipt_html ')
		->from('feev2_cohort_student fcs')
		->join('feev2_blueprint fb', 'fcs.blueprint_id=fb.id')
		->join('feev2_student_schedule fss', 'fcs.id=fss.feev2_cohort_student_id')
		->where('fcs.student_id',$student_id)
		->where('fb.acad_year_id',$acadyearId)
		->where('fcs.publish_status','PUBLISHED')
		->get()->result();

		$result = $this->db->select('ft.id as transId, ft.fee_student_schedule_id, ft.receipt_number, ft.paid_datetime, ft.status, op_recon_status, (ft.amount_paid + ifnull(ft.fine_amount,0) - ifnull(ft.discount_amount,0)) as amount_paid, ft.pdf_status, ft.receipt_pdf_link, ftp.reconciliation_status, opm.tx_response_code, ft.refund_amount')
			->from('feev2_transaction ft')
			->join('feev2_transaction_payment ftp','ftp.fee_transaction_id=ft.id')
			->join('online_payment_master opm','ft.id=opm.source_id','left')
			->where('ft.student_id', $student_id)
			->where('ft.soft_delete!=',1)
			->group_by('ft.id')
			->get()->result();

		if (empty($result)) {
			return array();
		}
		foreach ($blueprints as $key => &$val) {
			foreach ($result as $key => $name) {
				if ($val->fee_student_schedule_id == $name->fee_student_schedule_id) {
					$val->trans[] = $name;
				}
			}
		}
		return $blueprints;
		
	}

	public function get_relation_type($stakeholder_id)
	{
		return $this->db->select('relation_type')
			->where_in('relation_id', $stakeholder_id)
			->get('student_relation')->row()->relation_type;
	}

	public function getTodaysJourney($studentId)
	{
		$journeys = array('PICKING' => [], 'DROPPING' => []);
		$date = date('Y-m-d');
		$day = date('l');
		$stdOverrides = $this->db->select("th.thing_name, th.id as thingId, th.tracking_url, j.id as journeyId, j.journey_name, CONCAT(ifnull(d.first_name,''), ' ', ifnull(d.last_name, '')) as driverName, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as start_time, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as end_time, j.journey_type, d.phone_number, ts.stop_name, ts.id as stopId, j.status, 'no' as journey_change, d.first_name as driverName, 0 as attendance, d.attender_number, d.attender_name")
				->from('tx_things th')
				->join('tx_drivers d', 'd.id=th.driver_id')
				->join('tx_journeys j', 'j.thing_id=th.id')
				->join('tx_student_journeys_override sro', 'sro.journey_id=j.id')
				->join('tx_stops ts', 'ts.id=sro.stop_id')
				->where('sro.student_id', $studentId)
				->where("(sro.type='all' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date') OR (sro.type='single' AND sro.day='$day' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date')")->get()->result();
		$jIds = array();
		foreach ($stdOverrides as $k => $std) {
			$jIds[] = $std->journeyId;
			$journeys[$std->journey_type][] = $std;
		}

		$this->db->select("th.thing_name, th.id as thingId, th.tracking_url, j.id as journeyId, j.journey_name, CONCAT(ifnull(d.first_name,''), ' ', ifnull(d.last_name, '')) as driverName, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as start_time, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as end_time, j.journey_type, d.phone_number, ts.stop_name, ts.id as stopId, j.status, 'no' as journey_change, d.first_name as driverName, 0 as attendance, d.attender_number, d.attender_name")
			->from('tx_things th')
			->join('tx_drivers d', 'd.id=th.driver_id')
			->join('tx_journeys j', 'j.thing_id=th.id')
			->join('tx_student_journeys tsj', 'tsj.journey_id=j.id')
			->join('tx_stops ts', 'ts.id=tsj.stop_id')
			->where('tsj.entity_source_id', $studentId)
			->where('tsj.entity_type', 'Student')
			->where("tsj.day", $day);
		
		if (!empty($jIds))
			$this->db->where_not_in('j.id', $jIds);

		$stdRoute = $this->db->get()->result();

		foreach ($stdRoute as $k => $std) {
			$journeys[$std->journey_type][] = $std;
		}
		return $journeys;
	}

	public function rfidJourneys($rfid) {
		$date = date('Y-m-d');
		$attendance = array();
		$journeys = array('PICKING' => [], 'DROPPING' => []);
		$result = $this->db->select("ta.id as att_id, t.tracking_url, ta.thing_id as thingId, ta.journey_id as journeyId, ta.journey_type, TIME_FORMAT(ta.updated_at, '%h:%i %p') as tap_at, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as start_time, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as end_time, j.journey_name, 0 as stopId, 1 as status, 'Running' as tracking, d.phone_number, d.first_name as driverName, d.attender_number, d.attender_name, 1 as attendance")
			->from('tx_attendance ta')
			->join('tx_things t', 't.id=ta.thing_id')
			->join('tx_drivers d', 't.driver_id=d.id')
			->join('tx_journeys j', 'j.id=ta.journey_id')
			->where("ta.rfid", $rfid)
			->where("DATE_FORMAT(ta.updated_at, '%Y-%m-%d')='$date'")
			// ->order_by('ta.id', 'desc')
			->get()->result();
		foreach ($result as $key => $value) {
			$value->tap_at = date('h:i a',strtotime($this->timezone_setter($value->tap_at)));
			if(!array_key_exists($value->journeyId, $attendance)) {
				$attendance[$value->journeyId] = $value;
				$attendance[$value->journeyId]->taps = array();
			}
			$attendance[$value->journeyId]->taps[] = $value->tap_at;
		}

		foreach ($attendance as $key => $value) {
			$journeys[$value->journey_type][] = $value;
		}
		return $journeys;
		// echo "<pre>"; print_r($journeys); die();
	}

	public function rfidJourney($rfid) {
		$date = date('Y-m-d');
		$attendance = array();
		$result = $this->db->select("ta.id as att_id, t.tracking_url, ta.thing_id, ta.journey_id, ta.journey_type, TIME_FORMAT(ta.updated_at, '%h:%i %p') as tap_at, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as start_time, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as end_time, j.journey_name")
			->from('tx_attendance ta')
			->join('tx_things t', 't.id=ta.thing_id')
			->join('tx_journeys j', 'j.id=ta.journey_id')
			->where("ta.rfid", $rfid)
			->where("DATE_FORMAT(ta.updated_at, '%Y-%m-%d')='$date'")
			->order_by('ta.id', 'desc')
			->get()->result();
		$current_journey = 0;
		foreach ($result as $key => $value) {
			if(empty($attendance)) {
				$attendance[$value->journey_id] = array();
				$current_journey = $value->journey_id;
			}
			$value->tap_at = $this->timezone_setter($value->tap_at);
			if(array_key_exists($value->journey_id, $attendance)) {
				array_push($attendance[$value->journey_id], $value);
			}
		}
		if(!empty($attendance) && $current_journey) {
			$attendance = $attendance[$current_journey];
		}
		usort($attendance, function ($a, $b) {
		    return $a->att_id <=> $b->att_id;
		});
		// echo "<pre>"; print_r($attendance); die();
		return $attendance;
	}

	public function timezone_setter($input) {
      $myDateTime = new DateTime($input, new DateTimeZone('GMT'));
      $myDateTime->setTimezone(new DateTimeZone('Asia/Kolkata'));
      return $myDateTime->format('Y-m-d H:i:s');
    }

	public function getTrackingUrl($studentId)
	{
		$date = date('Y-m-d');
		$day = date('l');
		$current = strtotime(date('H:i:s'));
		$divider = strtotime(date('11:00:00'));
		$journey_type = 'PICKING';
		if ($current > $divider) {
			$journey_type = 'DROPPING';
		}
		$attendance = $this->db->select('th.tracking_url')
			->from('tx_things th')
			->join('tx_journeys j', 'j.thing_id=th.id')
			->join('tx_attendance ta', 'ta.journey_id=j.id')
			->where('ta.student_id', $studentId)
			->where("DATE_FORMAT(ta.time, '%Y-%m-%d')='$date'")
			->get()->row();
		if (empty($attendance)) {
			$stdOverrides = $this->db->select("th.tracking_url")
				->from('tx_things th')
				->join('tx_journeys j', 'j.thing_id=th.id')
				->join('tx_student_journeys_override sro', 'sro.journey_id=j.id')
				->where('sro.student_id', $studentId)
				->where('j.journey_type', $journey_type)
				->where("(sro.type='all' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date') OR (sro.type='single' AND sro.day='$day' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date')")->get()->row();
			if (empty($stdOverrides)) {
				$stdRoute = $this->db->select('th.tracking_url')
					->from('tx_things th')
					->join('tx_journeys j', 'j.thing_id=th.id')
					->join('tx_student_journeys ts', 'ts.journey_id=j.id')
					->where('ts.entity_source_id', $studentId)
					->where('ts.entity_type', 'Student')
					->where('j.journey_type', $journey_type)
					->where("ts.day", $day)
					->get()->row();
				if (empty($stdRoute)) {
					return '';
				}
				return $stdRoute->tracking_url;
			}
			return $stdOverrides->tracking_url;
		}
		return $attendance->tracking_url;
	}

	public function getTrackingUrlByThing($thingId) {
		$result = $this->db->select('tracking_url')->where('id', $thingId)->get('tx_things')->row();
		if(empty($result)) {
			return '';
		}
		return $result->tracking_url;
	}

	public function getStudentBusTrackingUrl($studentId)
	{
		$date = date('Y-m-d');
		return $this->db->select('th.tracking_url')
			->from('things th')
			->join('journeys j', 'j.thing_id=th.id')
			->join('transportation_attendance ta', 'ta.journey_id=j.id')
			->where('ta.student_id', $studentId)
			->where("DATE_FORMAT(ta.time, '%Y-%m-%d')='$date'")
			->get()->row();
	}

	public function get_class_wise_teacher_subj($section_id){
	 	$this->db_readonly->distinct('sss.subject_id');
	    $list = $this->db_readonly->select("s.long_name as sub_name, sss.class_section_id,  concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as teacher_name, sm.id as staff_id")
	    ->from('staff_subject_section sss')
	    ->where('sss.acad_year_id',$this->yearId)
	    ->where('sss.class_section_id', $section_id)
	    ->join('stafflist_subject_section stss', 'stss.sss_id=sss.id')
	    ->join('subjects s','sss.subject_id=s.id')
	    ->join('staff_master sm','stss.staff_id=sm.id')
	    ->order_by('sm.first_name')
	    ->get()->result();
	    // echo "<pre>"; print_r($list); die();
	    $temp = array();
	    foreach ($list as $key => $val) {
    		$temp[trim($val->teacher_name)][] = $val->sub_name;
	    }

	   return $temp;
	}

	public function get_classTeacher_section($section_id){
		return $this->db_readonly->select("concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as teacher_name")
		->from('class_section cs')
		->where('cs.id',$section_id)
		->join('staff_master sm','cs.class_teacher_id=sm.id')
		->get()->row();
	}

	public function getCircularsAndEmails($category, $parentId) {
		$this->db_readonly->select("cm.id,date_format(sent_on,'%d-%m-%Y %h:%i %p') as date ,title,body,category, cm.file_path, cm.mode");
		$this->db_readonly->from('circularv2_master cm');
		$this->db_readonly->where("cm.id in (select circularv2_master_id from circularv2_sent_to where stakeholder_id=$parentId and avatar_type=2)");
		$this->db_readonly->where('cm.category', $category);
		$this->db_readonly->where('cm.visible', 1);
		$this->db_readonly->where('cm.acad_year_id', $this->yearId);
		$this->db_readonly->order_by('cm.sent_on', 'desc');
		return $this->db_readonly->get()->result();
	}

	public function getOldCirculars($category, $studentId) {
		$this->db_readonly->select("cm.id,date_format(circular_date,'%d-%m-%Y %h:%i %p') as date,cm.circular_title as title, cm.circular_content as body,category, cm.file_path, 'circular' as mode");
		$this->db_readonly->from('circular_master cm');
		$this->db_readonly->where("cm.id in (select circular_id from circular_sent_to where stakeholder_id=$studentId and avatar_type=1)");
		$this->db_readonly->where('cm.category', $category);
		$this->db_readonly->where('cm.is_published', 1);
		$this->db_readonly->where('cm.acad_year_id', $this->yearId);
		$this->db_readonly->order_by('cm.circular_date', 'desc');
		return $this->db_readonly->get()->result();
	}

	public function getCountOfCirculars($parentId)
	{
		$this->db_readonly->select("count(ct.id) as circular_count, category");
		$this->db_readonly->from('circularv2_master cm');
		$this->db_readonly->join('circularv2_sent_to ct', 'ct.circularv2_master_id=cm.id');
		$this->db_readonly->where('ct.stakeholder_id', $parentId);
		$this->db_readonly->where('ct.avatar_type', 2);
		$this->db_readonly->where('cm.visible', 1);
		$this->db_readonly->where('cm.acad_year_id', $this->yearId);
		$this->db_readonly->group_by('cm.category');
		$result = $this->db_readonly->get()->result();
		$res = array();
		foreach ($result as $key => $value) {
			$res[$value->category] = $value->circular_count;
		}
		return $res;
	}

	public function getCountOfOldCirculars($studentId)
	{
		$this->db_readonly->select("count(ct.id) as circular_count, category");
		$this->db_readonly->from('circular_master cm');
		$this->db_readonly->join('circular_sent_to ct', 'ct.circular_id=cm.id');
		$this->db_readonly->where('ct.stakeholder_id', $studentId);
		$this->db_readonly->where('ct.avatar_type', 1);
		$this->db_readonly->where('cm.is_published', 1);
		$this->db_readonly->where('cm.acad_year_id', $this->yearId);
		$this->db_readonly->group_by('cm.category');
		$result = $this->db_readonly->get()->result();
		$res = array();
		foreach ($result as $key => $value) {
			$res[$value->category] = $value->circular_count;
		}
		return $res;
	}

	public function latestCirculars($parentId)
	{
		$fromDate = date("Y-m-d", strtotime("-7 days"));
		$today = date("Y-m-d");
		$this->db_readonly->select("count(ct.id) as circular_count, category");
		$this->db_readonly->from('circularv2_master cm');
		$this->db_readonly->join('circularv2_sent_to ct', 'ct.circularv2_master_id=cm.id');
		$this->db_readonly->where('ct.stakeholder_id', $parentId);
		$this->db_readonly->where('ct.avatar_type', 2);
		$this->db_readonly->where('cm.visible', 1);
		$this->db_readonly->where('date_format(sent_on,"%Y-%m-%d") BETWEEN "' . $fromDate . '" and "' . $today . '"');
		$this->db_readonly->where('cm.acad_year_id', $this->yearId);
		$this->db_readonly->group_by('cm.category');
		$result = $this->db_readonly->get()->result();
		$res = array();
		foreach ($result as $key => $value) {
			$res[$value->category] = $value->circular_count;
		}
		return $res;
	}

	public function getCircularDeatils($id)
	{
		$this->db_readonly->select("cm.title,cm.body,date_format(cm.sent_on,'%d-%m-%Y %h:%i %p') as date,CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as createdby,cm.file_path, cm.mode");
		$this->db_readonly->from('circularv2_master cm');
		$this->db_readonly->where('cm.id', $id);
		$this->db_readonly->join('avatar a', 'cm.sent_by=a.id', 'left');
		$this->db_readonly->join('staff_master sm', 'a.stakeholder_id=sm.id', 'left');
		return $this->db_readonly->get()->row();
	}

	public function getOldCircularDeatils($id)
	{
		$this->db_readonly->select("cm.circular_title as title,cm.circular_content as body,date_format(cm.circular_date,'%d-%m-%Y %h:%i %p') as date,CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as createdby,cm.file_path, 'circular' as mode");
		$this->db_readonly->from('circular_master cm');
		$this->db_readonly->where('cm.id', $id);
		$this->db_readonly->join('avatar a', 'cm.created_by=a.id', 'left');
		$this->db_readonly->join('staff_master sm', 'a.stakeholder_id=sm.id', 'left');
		return $this->db_readonly->get()->row();
	}

	public function getCircularNew($parentId)
	{
		$result = $this->db_readonly->select('count(cm.id) as circularCount')
			->from('circularv2_master cm')
			->join("circularv2_sent_to cst", "cm.id=cst.circularv2_master_id")
			->where('cm.visible', 1)
			->where('cst.stakeholder_id', $parentId)
			->where('cst.avatar_type', 2)
			->where('cst.is_read', 0)
			->get()->row();
		return $result;
	}

	public function get_textsStudentIdwise($parentId, $from_date,$to_date)
	{
		$this->db_readonly->select("tm.id,date_format(sent_on,'%M %d %Y') as date ,sent_on as smsDate, message, title, stakeholder_id as student_id, ts.mode, ts.is_read, ts.id as parent_text_id");
		$this->db_readonly->from('texting_master tm');
		$this->db_readonly->where("date_format(tm.sent_on,'%Y-%m-%d') BETWEEN '$from_date' and '$to_date'");
		$this->db_readonly->join('text_sent_to ts', 'ts.texting_master_id=tm.id');
		$this->db_readonly->where("ts.stakeholder_id=$parentId and ts.avatar_type=2");
		$this->db_readonly->order_by('tm.sent_on', 'desc');
		return $this->db_readonly->get()->result();

	}

	public function get_textsStudentIdwise_num_rows($studentId, $parentId, $categoryWise, $academic_year)
	{
		$this->db_readonly->select("tm.id,date_format(sent_on,'%M %d %Y') as date ,sent_on as smsDate, message, title, stakeholder_id as student_id, ts.mode, ts.is_read");
		$this->db_readonly->from('texting_master tm');
		$this->db_readonly->where('tm.acad_year_id',$academic_year);
		if ($categoryWise !='all') {
			$this->db_readonly->where('tm.mode',$categoryWise);
		}
		$this->db_readonly->join('text_sent_to ts', 'ts.texting_master_id=tm.id');
		$this->db_readonly->where("((ts.stakeholder_id=$studentId and ts.avatar_type=1) OR (ts.stakeholder_id=$parentId and ts.avatar_type=2))");
		// $this->db_readonly->where('tm.acad_year_id', $this->yearId);
		$this->db_readonly->order_by('tm.sent_on', 'desc');
		return $this->db_readonly->get()->num_rows();
	}

	public function getTextDetail($id)
	{
		$this->db_readonly->select("tm.id,date_format(tm.sent_on,'%d-%m-%Y') as date ,tm.sent_on as smsDate, message as sms_content");
		$this->db_readonly->where('tm.id', $id);
		return $this->db_readonly->get('texting_master tm')->row();
	}

	public function makeTextRead($parent_text_id)
	{
		return $this->db->where('id', $parent_text_id)->update('text_sent_to', array('is_read' => 1));
	}

	public function getTextsInfo($parentId)
	{
		
		$sql = "SELECT count(id) as textCount 
				from text_sent_to ts 
				where stakeholder_id=$parentId 
				and avatar_type=2 
				and is_read=0";
		return  $this->db_readonly->query($sql)->row();
	}

	public function update_read_by_parent_id($parentId){

		$sql = "update text_sent_to ts set is_read = 1 where stakeholder_id=$parentId and avatar_type=2 and is_read=0";
		return $this->db->query($sql);

	}

	public function updateProfilePhoto($type, $id, $resized, $real) {
		$photos = array();
		if($resized['file_name'] != '') {
            $photos['picture_url'] = $resized['file_name'];
        }
        if($real['file_name'] != '') {
            $photos['high_quality_picture_url'] = $real['file_name'];
        }

        if(!empty($photos)) {
        	if($type == 'student') {
        		$std_year = $this->db->select('id')->where('student_admission_id', $id)->order_by('id', 'desc')->limit(1)->get('student_year')->row();
        		return $this->db->where('id', $std_year->id)->update('student_year', $photos);
        	} else {
        		return $this->db->where('id', $id)->update('parent', $photos);
        	}
		}
		return 1;
	}

	public function update_student_profile_photo($type, $id, $photo, $file_type){
		$photos = array();
        if($file_type == 'HIGH_QUALITY') {
            $photos['high_quality_picture_url'] = $photo;
        } else {
            $photos['picture_url'] = $photo['file_name'];
        }

		if(!empty($photo)){
			if($type == 'family'){
				$data=array('family_picture_url'=> $photo);
				$this->db->where('id',$id)->update('student_admission', $data);
				return 1;
			}
		}
        if(!empty($photos)) {
        	if($type == 'student') {
    		 	// $std_year = $this->db->select('id')->where('student_admission_id', $id)->order_by('id', 'desc')->limit(1)->get('student_year')->row();
        		return $this->db->where('student_admission_id', $id)->where('acad_year_id', $this->yearId)->update('student_year', $photos);
		} else {
        		return $this->db->where('id', $id)->update('parent', $photos);
        	}
		}
		return 1;

       
	}

	private function _updateStudentData($input) {
		$student_admission_fields = $this->db->list_fields('student_admission');
		$student_year_fields = $this->db->list_fields('student_year');
		// $student_health_fields = $this->db->list_fields('student_health');
		// $address_info_fields = $this->db->list_fields('address_info');
		
		$field_name = isset($input['field_name']) ? $input['field_name'] : null;
		$field_value = isset($input['field_value']) ? $input['field_value'] : null;
		$student_id = isset($input['student_id']) ? $input['student_id'] : null;

		// echo "<pre>"; print_r($student_admission_fields); die();
		if(in_array($field_name, $student_admission_fields)) {
			if($field_name == 'dob') {
				$data = array(
					$field_name => ($field_value == '')?null:date('Y-m-d', strtotime($field_value))
				);
			} else {
				$data = array(
					$field_name => ($field_value == '')?null:$field_value
				);
			}
			$this->db->trans_start();
			$this->db->where('id', $student_id)->update('student_admission', $data);
			$stdName = $this->db->select("first_name, last_name")->where('id', $student_id)->get('student_admission')->row();
			if($field_name === 'first_name') {
				$student_name = $field_value.' '.$stdName->last_name;
				$this->db->where('stakeholder_id', $student_id)->where('avatar_type', 1)->update('avatar', array('friendly_name' => $student_name));
				$parents = [$input['father_id'], $input['mother_id']];
				$this->db->where_in('stakeholder_id', $parents)->where('avatar_type', 2)->update('avatar', array('friendly_name' => $student_name));
			}
			if($field_name == 'last_name') {
				$student_name = $stdName->first_name.' '.$field_value;
				$this->db->where('stakeholder_id', $student_id)->where('avatar_type', 1)->update('avatar', array('friendly_name' => $student_name));
				$parents = [$input['father_id'], $input['mother_id']];
				$this->db->where_in('stakeholder_id', $parents)->where('avatar_type', 2)->update('avatar', array('friendly_name' => $student_name));
			}
			return $this->db->trans_complete();
		} else if(in_array($field_name, $student_year_fields)) {
			$data = array(
				$field_name => ($field_value == '')?null:$field_value
			);
			return $this->db->where('student_admission_id',$student_id)->where('acad_year_id',$this->yearId)->update('student_year', $data);
		} 
		// else if(in_array($field_name, $student_health_fields)) {
		// 	$student_health = array(
		// 		$field_name => ($field_value == '')?null:$field_value,
		// 		'student_id' => $student_id
		// 	);
  //       	$std_helath = $this->db->select('id')->where('student_id', $student_id)->get('student_health')->row();
  //       	if(empty($std_helath)) {
  //       		return $this->db->insert('student_health', $student_health);
  //       	} else {
		//  		return $this->db->where('student_id',$student_id)->update('student_health', $student_health);
  //       	}
		// }
	}

	private function _updateParentData($input) {
		$field_name = $input['field_name'];
		$field_value = $input['field_value'];
		$parent_id = $input['parent_id'];
		$data = array(
			$field_name => $field_value
		);
		if($field_name == 'email') {
			$user_id = $this->db_readonly->query("select user_id from avatar where stakeholder_id=$parent_id and avatar_type=2")->row()->user_id;
			$this->db->where("id", $user_id)->update('users', $data);
		}
		if($field_name == 'mobile_no') {
			$user_id = $this->db_readonly->query("select user_id from avatar where stakeholder_id=$parent_id and avatar_type=2")->row()->user_id;
			$this->db->where("id", $user_id)->update('users', array('phone_number' => $field_value));
		}
		return $this->db->where('id',$parent_id)->update('parent', $data);
		// echo '<pre>'; print_r($this->db->last_query()); die();
	}

	private function _updateGuardianData($input) {
		$field_name = $input['field_name'];
		$field_value = $input['field_value'];
		$guardian_id = $input['guardian_id'];
		$data = array(
			$field_name => $field_value
		);
		return $this->db->where('id',$guardian_id)->update('parent', $data);
		 // echo '<pre>'; print_r($this->db->last_query());
	}

	private function _insertGuardianData($input) {
		$modified_by = $this->authorization->getAvatarId();
		$data = array(
			'first_name' => '',
			'last_name' => '',
			'mobile_no' => NULL,
			'email' => NULL,
			'student_id' => $input['student_id'],
			'last_modified_by' => $modified_by
		);
		$field_name = $input['field_name'];
		$field_value = $input['field_value'];
		$data[$field_name] = $field_value;
		
		$this->db->trans_start();
		$this->db->insert('parent', $data);
		$parent_id = $this->db->insert_id();
		$relation = array(
			'std_id' => $input['student_id'],
			'relation_id' => $parent_id,
			'relation_type' => 'Guardian',
			'active' => 1,
			'last_modified_by' => $modified_by
		);
		$this->db->insert('student_relation', $relation);
		$this->db->trans_complete();
		if($this->db->trans_status() === FALSE) {
			$this->db->trans_rollback();
			return 0;
		} else {
			$this->db->trans_commit();
			return $parent_id;
		}
	}

	public function saveProfileData() {
		$input = $this->input->post();
		if($input['type'] == 'student') {
			return $this->_updateStudentData($input);
		} else if($input['type'] == 'father') {
			$input['parent_id'] = $input['father_id'];
			return $this->_updateParentData($input);
		} else if($input['type'] == 'mother') {
			$input['parent_id'] = $input['mother_id'];
			return $this->_updateParentData($input);
		} else if($input['type'] == 'guardian') {
			if($input['guardian_id'] == 0) {
				return $this->_insertGuardianData($input);
			}
			return $this->_updateGuardianData($input);
		}
	}

	public function saveAddressData() {
		$input = $this->input->post('add_data');
		$is_update = $input['update'];
		$address_id = $input['address_id'];
		unset($input['update']);
		unset($input['address_id']);
		if($is_update) {
			return $this->db->where('id', $address_id)->update('address_info', $input);
		} else {
			$input['address_type'] = $address_id;
			return $this->db->insert('address_info', $input);
		}
	}

	private function _student_update_profile_update_by_parent($student, $sMinPath, $sRealPath){
		/* if(!empty($student['student_dob'])) {
            $dob =  date("Y-m-d", strtotime($student['student_dob']));
        } else {
            $dob = null;
        }*/

        /*$student_admission = array (
           'nationality' => (isset($student['nationality']) == '') ? null : $student['nationality'],
           'caste' => (isset($student['s_caste']) == '') ? null : $student['s_caste'],
           'religion' => (isset($student['s_religion']) == '') ? null : $student['s_religion'],
           'aadhar_no' => (isset($student['s_aadhar_no']) == '') ? null : $student['s_aadhar_no'],
           'dob' => $dob

        );*/

        $student_admission = array();

        if(isset($student['gender'])) {
        	$student_admission['gender'] = $student['gender'];
        }
        if(isset($student['s_first_name'])) {
        	$student_admission['first_name'] = $student['s_first_name'];
        }
        if(isset($student['s_last_name'])) {
        	$student_admission['last_name'] = ($student['s_last_name']=='')?NULL:$student['s_last_name'];
        }
        if(isset($student['nationality'])) {
        	$student_admission['nationality'] = ($student['nationality']=='')?NULL:$student['nationality'];
        }
        if(isset($student['s_caste'])) {
        	$student_admission['caste'] = ($student['s_caste']=='')?NULL:$student['s_caste'];
        }
        if(isset($student['s_religion'])) {
        	$student_admission['religion'] = ($student['s_religion']=='')?NULL:$student['s_religion'];
        }
        if(isset($student['s_aadhar_no'])) {
        	$student_admission['aadhar_no'] = ($student['s_aadhar_no']=='')?NULL:$student['s_aadhar_no'];
        }
        if(isset($student['student_dob'])) {
        	$student_admission['aadhar_no'] = ($student['student_dob'] == '')?NULL:date("Y-m-d", strtotime($student['student_dob']));
        }

        if(!empty($student_admission)) {
	 		$this->db->where('id',$student['stdId']);
	        $this->db->update('student_admission', $student_admission);
        }

		$student_year = array ();
		if($sMinPath['file_name'] != '') {
            $student_year['picture_url'] = $sMinPath['file_name'];
        }
        if($sRealPath['file_name'] != '') {
            $student_year['high_quality_picture_url'] = $sRealPath['file_name'];
        }
		if (!empty($student_year)) {
        	$this->db->where('student_admission_id',$student['stdId']);
	        $this->db->where('acad_year_id',$this->yearId);
	        $this->db->update('student_year', $student_year);
        } 

        $student_health = array();
        if(isset($student['blood_group'])) {
        	$student_health['blood_group'] = ($student['blood_group'] == '')?NULL:$student['blood_group'];
        }

        if(!empty($student_health)) {
        	$student_health['student_id'] = $student['stdId'];
        	$std_helath = $this->db->select('id')->where('student_id', $student['stdId'])->get('student_health')->row();
        	if(empty($std_helath)) {
        		$this->db->insert('student_health', $student_health);
        	} else {
		 		$this->db->where('student_id',$student['stdId']);
	        	$this->db->update('student_health', $student_health);
        	}
        }


         if (!empty($student['s_line1'])) {
        	foreach ($student['s_line1'] as $addId => $val) {
	        	$this->db->where('stakeholder_id', $student['stdId']);	
	        	$this->db->where('avatar_type', '1');
	        	$this->db->where('id',$addId);
	        	$query = $this->db->get('address_info')->row();

	        	if (!empty($query)) {
	        		$sAddress_update = array(
		        		'id'=> $addId,
		        		'Address_line1'=>$student['s_line1'][$addId],
		        		'Address_line2'=>$student['s_line2'][$addId],
		        		'area'=>$student['s_area'][$addId],
		        		'district'=>$student['s_district'][$addId],
		        		'state'=>$student['s_state'][$addId],
		        		'country'=>$student['s_country'][$addId],
		        		'pin_code'=>$student['s_pincode'][$addId],
		        		'last_modified_by'=>$student['stdId'],
		        	);
	        		$this->db->where('id',$addId);
	    			$this->db->update('address_info', $sAddress_update);

	        	}else{
	        		$sAddress_insert = array(
		        		'Address_line1'=>$val,
		        		'Address_line2'=>$student['s_line2'][$addId],
		        		'area'=>$student['s_area'][$addId],
		        		'district'=>$student['s_district'][$addId],
		        		'state'=>$student['s_state'][$addId],
		        		'country'=>$student['s_country'][$addId],
		        		'pin_code'=>$student['s_pincode'][$addId],
		        		'address_type'=>$addId,
		        		'stakeholder_id'=>$student['stdId'],
		        		'avatar_type'=>'1',
		        		'last_modified_by'=>$student['stdId']
		        	);
	 				$this->db->insert('address_info', $sAddress_insert);	
	        	}
	        }
        }

		return  $this->db->affected_rows();
	}

	private function _father_update_profile_update_by_parent($father,  $fMinPath, $fRealPath){
        /*$fatherData = array (
           	'qualification' => (isset($father['qualification']) == '') ? null : $father['qualification'],
           	'occupation' => (isset($father['occupation']) == '') ? null : $father['occupation'],
           	'mobile_no' => (isset($father['mobile_no']) == '') ? null : $father['mobile_no'],
            'aadhar_no' => (isset($father['aadhar_no']) == '') ? null : $father['aadhar_no'],
            'annual_income' => (isset($father['annual_income']) == '') ? null : $father['annual_income'],
		);*/

		$fatherData = array();

		if(isset($father['first_name'])) {
        	$fatherData['first_name'] = $father['first_name'];
        }
        if(isset($father['last_name'])) {
        	$fatherData['last_name'] = ($father['last_name'] == '') ? null : $father['last_name'];
        }

        if(isset($father['qualification'])) {
        	$fatherData['qualification'] = ($father['qualification'] == '') ? null : $father['qualification'];
        }

        if(isset($father['occupation'])) {
        	$fatherData['occupation'] = ($father['occupation'] == '') ? null : $father['occupation'];
        }

        if(isset($father['mobile_no'])) {
        	$fatherData['mobile_no'] = ($father['mobile_no'] == '') ? null : $father['mobile_no'];
        }

        if(isset($father['aadhar_no'])) {
        	$fatherData['aadhar_no'] = ($father['aadhar_no'] == '') ? null : $father['aadhar_no'];
        }

        if(isset($father['annual_income'])) {
        	$fatherData['annual_income'] = ($father['annual_income'] == '') ? null : $father['annual_income'];
        }

		/*if($fPath['file_name'] != '') {
            $fatherData = array_merge($fatherData,['picture_url' => $fPath['file_name']]);
        }*/
        if($fMinPath['file_name'] != '') {
            $fatherData = array_merge($fatherData,['picture_url' => $fMinPath['file_name']]);
        }
        if($fRealPath['file_name'] != '') {
        	$fatherData = array_merge($fatherData,['high_quality_picture_url' => $fRealPath['file_name']]);
        }
        if(!empty($fatherData)) {
	 		$this->db->where('id',$father['fatherId']);
	        $this->db->update('parent', $fatherData);
        }

        $userData = array (
           	'email' => (isset($father['email']) == '') ? null : $father['email'],
        );
 		$this->db->where('id',$father['userId']);
        $this->db->update('users', $userData);

        if (!empty($father['line1'])) {
	     	foreach ($father['line1'] as $addId => $val) {
		    	$this->db->where('stakeholder_id', $father['fatherId']);	
		    	$this->db->where('avatar_type', '2');
		    	$this->db->where('id',$addId);
		    	$query = $this->db->get('address_info')->row();

		    	if (!empty($query)) {
		    		$fAddress_update = array(
		        		'id'=> $addId,
		        		'Address_line1'=>$father['line1'][$addId],
		        		'Address_line2'=>$father['line2'][$addId],
		        		'area'=>$father['area'][$addId],
		        		'district'=>$father['district'][$addId],
		        		'state'=>$father['state'][$addId],
		        		'country'=>$father['country'][$addId],
		        		'pin_code'=>$father['pincode'][$addId],
		        		'last_modified_by'=>$father['fatherId'],
		        	);
					$this->db->where('id',$addId);
		    		$this->db->update('address_info', $fAddress_update);
		    	}else{
		    		$fAddress_insert = array(
		        		'Address_line1'=>$val,
		        		'Address_line2'=>$father['line2'][$addId],
		        		'area'=>$father['area'][$addId],
		        		'district'=>$father['district'][$addId],
		        		'state'=>$father['state'][$addId],
		        		'country'=>$father['country'][$addId],
		        		'pin_code'=>$father['pincode'][$addId],
		        		'address_type'=>$addId,
		        		'stakeholder_id'=>$father['fatherId'],
		        		'avatar_type'=>'2',
		        		'last_modified_by'=>$father['fatherId']
		        	);
		 			$this->db->insert('address_info', $fAddress_insert);	
		    	}
		    }
		}
		return $this->db->affected_rows();
	}


	private function _mother_update_profile_update_by_parent($mother, $mMinPath, $mRealPath){
		 $motherData = array (
           	'qualification' => (isset($mother['qualification']) == '') ? null : $mother['qualification'],
           	'occupation' => (isset($mother['occupation']) == '') ? null : $mother['occupation'],
           	'mobile_no' => (isset($mother['mobile_no']) == '') ? null : $mother['mobile_no'],
    	 	'aadhar_no' => (isset($mother['aadhar_no']) == '') ? null : $mother['aadhar_no'],
            'annual_income' => (isset($mother['annual_income']) == '') ? null : $mother['annual_income'],

		);

		$motherData = array();

		if(isset($mother['first_name'])) {
        	$motherData['first_name'] = $mother['first_name'];
        }
        if(isset($mother['last_name'])) {
        	$motherData['last_name'] = ($mother['last_name'] == '') ? null : $mother['last_name'];
        }

        if(isset($mother['qualification'])) {
        	$motherData['qualification'] = ($mother['qualification'] == '') ? null : $mother['qualification'];
        }

        if(isset($mother['occupation'])) {
        	$motherData['occupation'] = ($mother['occupation'] == '') ? null : $mother['occupation'];
        }

        if(isset($mother['mobile_no'])) {
        	$motherData['mobile_no'] = ($mother['mobile_no'] == '') ? null : $mother['mobile_no'];
        }

        if(isset($mother['aadhar_no'])) {
        	$motherData['aadhar_no'] = ($mother['aadhar_no'] == '') ? null : $mother['aadhar_no'];
        }

        if(isset($mother['annual_income'])) {
        	$motherData['annual_income'] = ($mother['annual_income'] == '') ? null : $mother['annual_income'];
        }
		/*if($mPath['file_name'] != '') {
            $motherData = array_merge($motherData,['picture_url' => $mPath['file_name']]);
        }*/
        if($mMinPath['file_name'] != '') {
            $motherData = array_merge($motherData,['picture_url' => $mMinPath['file_name']]);
        }
        if($mRealPath['file_name'] != '') {
        	$motherData = array_merge($motherData,['high_quality_picture_url' => $mRealPath['file_name']]);
        }
        if(!empty($motherData)) {
	 		$this->db->where('id',$mother['motherId']);
	        $this->db->update('parent', $motherData);
        }


         $userData = array (
           	'email' => ($mother['email'] == '') ? null : $mother['email'],
        );
 		$this->db->where('id',$mother['userId']);
        $this->db->update('users', $userData);

        if (!empty($mother['line1'])) {
        	foreach ($mother['line1'] as $addId => $val) {
	        	$this->db->where('stakeholder_id', $mother['motherId']);	
	        	$this->db->where('avatar_type', '2');
	        	$this->db->where('id',$addId);
	        	$query = $this->db->get('address_info')->row();

	        	if (!empty($query)) {
	        		$mAddress_update = array(
		        		'id'=> $addId,
		        		'Address_line1'=>$mother['line1'][$addId],
		        		'Address_line2'=>$mother['line2'][$addId],
		        		'area'=>$mother['area'][$addId],
		        		'district'=>$mother['district'][$addId],
		        		'state'=>$mother['state'][$addId],
		        		'country'=>$mother['country'][$addId],
		        		'pin_code'=>$mother['pincode'][$addId],
		        		'last_modified_by'=>$mother['motherId'],
		        	);
	        		$this->db->where('id',$addId);
	    			$this->db->update('address_info', $mAddress_update);

	        	}else{
	        		$mAddress_insert = array(
		        		'Address_line1'=>$val,
		        		'Address_line2'=>$mother['line2'][$addId],
		        		'area'=>$mother['area'][$addId],
		        		'district'=>$mother['district'][$addId],
		        		'state'=>$mother['state'][$addId],
		        		'country'=>$mother['country'][$addId],
		        		'pin_code'=>$mother['pincode'][$addId],
		        		'address_type'=>$addId,
		        		'stakeholder_id'=>$mother['motherId'],
		        		'avatar_type'=>'2',
		        		'last_modified_by'=>$mother['motherId']
		        	);
	 				$this->db->insert('address_info', $mAddress_insert);
	        	}
	        }
        }
        
	}
	public function update_profile_edit_parents($student, $father, $mother, $sMinPath, $sRealPath, $fMinPath, $fRealPath, $mMinPath, $mRealPath){
		
    	$this->db->trans_begin();
    	$this->_student_update_profile_update_by_parent($student, $sMinPath, $sRealPath);
    	$this->_father_update_profile_update_by_parent($father,  $fMinPath, $fRealPath);
    	$this->_mother_update_profile_update_by_parent($mother, $mMinPath, $mRealPath);

     	if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return 1;
        }

	}

	public function get_status_fee_payment($student_id)
	{
		$status = $this->db_readonly->select("group_concat(fss.payment_status) as payment")
		->from('feev2_cohort_student fcs')
		->where('fcs.student_id',$student_id)
		->where('fcs.publish_status','PUBLISHED')
		->join('feev2_student_schedule fss',"fcs.id=fss.feev2_cohort_student_id")
		// ->group_by('fcs.student_id')
		->get()->row();

		if (substr_count($status->payment, "PARTIAL")) {
			return "PARTIALLY PAID";
		}
		if(substr_count($status->payment, "NOT_STARTED") && substr_count($status->payment, "FULL")) {
			return "PARTIALLY PAID";
		}
		if(substr_count($status->payment, "NOT_STARTED")) {
			return "NOT STARTED";
		}
		return "FULL";
	}
	public function get_published_blueprints_due_dates($student_id){

		$result = $this->db_readonly->select('fb.name as blueprint_name, fi.name as installment_name, (To_days(fi.end_date) - TO_DAYS(curdate())) as difference, date_format(end_date,"%d-%m-%Y") as due_date,fss.payment_status, fcs.publish_status')
		->from('feev2_cohort_student fcs')
		->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
		->join('feev2_student_schedule fss',"fcs.id=fss.feev2_cohort_student_id  and fcs.student_id=$student_id")
		->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id and fsi.status!="FULL"')
		->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
		->group_by('fss.feev2_blueprint_installment_types_id')
		->get()->result();
		return $result;
	}

	public function getRFID($studentId) {
		$result =  $this->db->select("REPLACE(LTRIM(REPLACE(rfid_number,'0',' ')),' ','0') as rfid_number")->where('sa.id', $studentId)->get('student_admission sa')->row();
		if(!empty($result)){
			return $result->rfid_number;
		}else{
			return 0;
		}
	}

	public function getAttendanceJourney($rfid) {
		$today = date('Y-m-d');
		$data = array('PICKING' => [], 'DROPPING' => []);
		$result = $this->db->select('t.tracking_url, ta.journey_id, ta.journey_type')
		->from('tx_attendance ta')
		->join('tx_things t', 't.id=ta.thing_id')
		->where('ta.rfid', $rfid)
		->where("DATE_FORMAT(ta.update, '%Y-%m-%d')='$today'")
		->group_by('ta.journey_id')
		->get()->result();
		foreach ($result as $key => $value) {
			$data[$value->journey_type][$value->journey_id] = array(
				'journey_id' => $value->journey_id,
				'att_tracking_url' => $value->tracking_url
			);
		}
		return $data;
	}

	public function getAssessmentsSubjectsOld($classId) {
		$subjects = $this->db->query("select id as subject_id,subject_name from afl_subjects where class_id=$classId order by subject_name")->result();
		$sql = "select a.*, aa.id as assessment_subject_id, aa.afl_subject_id
				from afl_assessments a 
				join afl_assessment_subjects aa on aa.afl_assessment_id=a.id 
				where a.class_id=$classId and aa.publish_status=1";
		$assessments = $this->db->query($sql)->result();
		$sub = array();
		foreach ($subjects as $key => $subject) {
			$sub[$subject->subject_id] = $subject;
		}

		$data = array();
		foreach ($assessments as $key => $assessment) {
			$subject_id = $assessment->afl_subject_id;
			if(!array_key_exists($assessment->id, $data)) {
				list($month, $year) = explode(" - ", $assessment->schedule);
				$data[$assessment->id] = array();
				$data[$assessment->id]['month'] = $month;
				$data[$assessment->id]['subjects'] = array();
			}
			$data[$assessment->id]['subjects'][$subject_id] = array('subject_name' => $sub[$subject_id]->subject_name, 'assessment_subject_id' => $assessment->assessment_subject_id);
		}
		return $data;
	}

	public function getAssessmentsSubjects($assessment_id, $classId, $stdId) {
		$subjects = $this->db->query("select id as subject_id,subject_name from afl_subjects where class_id=$classId order by subject_name")->result();
		$sql = "select a.*, aa.id as assessment_subject_id, aa.afl_subject_id, aa.publish_status as subject_publish, sp.publish_status as std_publish 
				from afl_assessments a 
				join afl_assessment_subjects aa on aa.afl_assessment_id=a.id 
				join afl_student_perf_pointers sp on sp.afl_assessment_subject_id=aa.id 
				where a.id=$assessment_id and sp.student_id=$stdId";
				// where a.id=$assessment_id and sp.student_id=$stdId and sp.publish_status=1 and aa.publish_status=1";
		$assessments = $this->db->query($sql)->result();

		$sql = "select aa.id as assessment_subject_id
				from afl_assessment_subjects aa 
				join afl_student_perf_pointers sp on aa.id=sp.afl_assessment_subject_id and sp.student_id=$stdId 
				where aa.afl_assessment_id=$assessment_id group by aa.id";
		$stdMarks = $this->db->query($sql)->result();
		$stds = [];
		foreach ($stdMarks as $key => $value) {
			array_push($stds, $value->assessment_subject_id);
		}

		$marsAdded = array();
		foreach ($stdMarks as $key => $value) {
			$marsAdded[] = $value->assessment_subject_id;
		}

		// echo "<pre>"; print_r($assessments);
		// echo "<pre>"; print_r($marsAdded);
		$sub = array();
		foreach ($subjects as $key => $subject) {
			$sub[$subject->subject_id] = $subject;
		}

		$data = array();
		$i = 1;
		foreach ($assessments as $key => $assessment) {
			$subject_id = $assessment->afl_subject_id;
			if($i++ == 1) {
				// list($month, $year) = explode(" - ", $assessment->schedule);
				$data['month'] = $assessment->schedule;
				$data['schedule'] = $assessment->schedule;
				$data['assessment_id'] = $assessment->id;
				$data['assessment_name'] = $assessment->assessment_name;
				$data['publish_status'] = $assessment->subject_publish && $assessment->std_publish;
				$data['subjects'] = array();
			}
			$status = 0; //Marks not added
			if(in_array($assessment->assessment_subject_id, $marsAdded)) {
				$status = 1; //marks addedd
			}
			$data['subjects'][$subject_id] = array('subject_name' => $sub[$subject_id]->subject_name, 'assessment_subject_id' => $assessment->assessment_subject_id, 'status' => $status, 'publish_status' => ($assessment->subject_publish && $assessment->std_publish));
		}
		return $data;
	}

	public function getAssessments($classId) {
		$sql = "select a.*
				from afl_assessments a 
				join afl_assessment_subjects aa on aa.afl_assessment_id=a.id 
				where a.class_id=$classId and aa.publish_status=1 group by a.id";
		return $this->db->query($sql)->result();
	}

	public function getStudentAcadYears($student_id) {
		return $this->db->query("select * from academic_year where id in (select acad_year_id from student_year where student_admission_id=$student_id)")->result();
	}

	public function getYearClassId($studentId, $acad_year_id) {
		return $this->db_readonly->query("select class_id from student_year where student_admission_id=$studentId and acad_year_id=$acad_year_id")->row()->class_id;
	}

	public function getSubjectsAssessments($classId) {
		$subjects = $this->db->query("select id as subject_id,subject_name from afl_subjects where class_id=$classId")->result();
		$sql = "select a.*, aa.id as assessment_subject_id, aa.afl_subject_id
				from afl_assessments a 
				join afl_assessment_subjects aa on aa.afl_assessment_id=a.id 
				where a.class_id=$classId and aa.publish_status=1";
		$assessments = $this->db->query($sql)->result();

		foreach ($subjects as $key => $subject) {
			$subjects[$key]->assessments = array();
			foreach ($assessments as $a => $assessment) {
				if($assessment->afl_subject_id == $subject->subject_id) {
					list($month, $rear) = explode(" - ", $assessment->schedule);
					$assessment->schedule = $month;
					array_push($subjects[$key]->assessments, $assessment);
				}
			}
		}

		return $subjects;
	}

	public function getGradeDescriptions($assessment_subject_id) {
		$sql = "select rpp.id as perf_parameter_id, rg.description as grade_description, gsv.range_name, gsv.start_range, gsv.end_range 
				from afl_rubric_perf_parameters rpp 
				join afl_rubric_perf_parameters_grading_scale_desc rg on rg.afl_rubric_perf_parameters_id=rpp.id 
				join afl_grading_scale_values gsv on gsv.id=rg.afl_grading_scale_values_id  
				where rpp.afl_assessment_subject_id=$assessment_subject_id";
		$desc = $this->db->query($sql)->result();
		$grade_description = array();
		foreach ($desc as $key => $d) {
			if(!array_key_exists($d->perf_parameter_id, $grade_description)) {
				$grade_description[$d->perf_parameter_id] = array();
			}
			array_push($grade_description[$d->perf_parameter_id], $d);
		}
		// echo "<pre>"; print_r($grade_description);
		return $grade_description;
	}

	public function getStudentMarksData($student_id, $assessment_subject_id) {
		$sql = "select sm.self_marks, rpp.id as perf_parameter_id, sm.evaluated_marks, pp.pointer_name, rpp.description 
				from afl_student_marks sm 
				join afl_rubric_perf_parameters rpp on rpp.id=sm.afl_rubric_perf_parameter_id 
				join afl_performance_pointers pp on pp.id=rpp.perf_pointer_id 
				where sm.afl_student_perf_id=(select id from afl_student_perf_pointers where afl_assessment_subject_id=$assessment_subject_id 
				and student_id=$student_id)";
		$marksData = $this->db->query($sql)->result();
		return $marksData;
		// echo "<pre>"; print_r($perf_data); die();
	}

	public function getAssessmentDetailByAssSub($assessment_subject_id) {
		return $this->db->query("select a.*, g.grading_scale_name from afl_assessments a join afl_grading_scale g on g.id=a.afl_grading_scale_id where a.id=(select afl_assessment_id from afl_assessment_subjects where id=$assessment_subject_id)")->row();
	}

	public function getSubjectDetailByAssSub($assessment_subject_id) {
		return $this->db->query("select id, subject_name from afl_subjects where id=(select afl_subject_id from afl_assessment_subjects where id=$assessment_subject_id)")->row();
	}

	public function getSubjectDetails($assessment_subject_id) {
        $result = $this->db->select('s.id as subject_id, s.subject_name, as.afl_topic_ids_json')
        ->from('afl_assessment_subjects as')
        ->join('afl_subjects s', 's.id=as.afl_subject_id')
        ->where('as.id', $assessment_subject_id)
        ->get()->row();

        $subject = array();
        $subject['subject_id'] = $result->subject_id;
        $subject['subject_name'] = $result->subject_name;
        $subject['topics'] = array();
        $topics = json_decode($result->afl_topic_ids_json);
        if(!empty($topics)) {
            $res = $this->db->select('topic, strand_name, sub_strand_name')->join('afl_sub_strands ss', 'ss.id=t.afl_sub_strand_id')->join('afl_strands s', 's.id=ss.afl_strand_id')->where_in('t.id',$topics)->get('afl_topics t')->result();
            foreach ($res as $key => $val) {
                $subject['topics'][] = '<b>'.$val->strand_name.' - '.$val->sub_strand_name.'</b>: '.$val->topic;
            }
        }
        return $subject;
    }

    public function getSubjectName($subject_id) {
    	return $this->db->select("id,subject_name, class_id")->where('id', $subject_id)->get('afl_subjects')->row();
    }

    public function getAflAssessments($subject_id, $std_id) {
    	return $this->db->query("
    		select a.id as assessment_id, a.assessment_name, a.schedule, a.afl_grading_scale_id, sub.id as assessment_subject_id from afl_assessments a join afl_assessment_subjects sub on sub.afl_assessment_id=a.id  join afl_student_perf_pointers sp on sp.afl_assessment_subject_id=sub.id where sub.afl_subject_id=$subject_id and sub.publish_status=1 and sp.publish_status=1 and sp.student_id=$std_id
    		")->result();
    }

    public function getSubjectPerfParameters($assessment_subject_ids, $student_id) {
    	if(empty($assessment_subject_ids)) {
    		return [];
    	}
    	$sub_ids = implode(",", $assessment_subject_ids);

    	$sql1 = "SELECT rp.id as parameterId, rp.perf_pointer_id, rp.description as parameter_description, pp.pointer_name, rp.afl_assessment_subject_id 
    			from afl_rubric_perf_parameters as rp 
    			join afl_performance_pointers as pp on pp.id=rp.perf_pointer_id 
    			where rp.afl_assessment_subject_id in ($sub_ids)";
    	$perf_pointers = $this->db_readonly->query($sql1)->result();
    	// echo '<pre>'; print_r($perf_pointers);
    	$parameter_ids = [];
    	foreach ($perf_pointers as $p) {
    		$parameter_ids[] = $p->parameterId;
    	}
    	$p_ids = implode(",", $parameter_ids);
    	$sql2 = "SELECT sm.afl_rubric_perf_parameter_id as parameterId, sm.self_marks, sm.evaluated_marks 
    			from afl_student_perf_pointers as sp 
    			join afl_student_marks as sm on sm.afl_student_perf_id=sp.id 
    			where sp.afl_assessment_subject_id in ($sub_ids) 
    			and sm.afl_rubric_perf_parameter_id in ($p_ids) 
    			and sp.student_id=$student_id";
		$stdudent_masrks = $this->db_readonly->query($sql2)->result();
		$std_marks = [];
		foreach ($stdudent_masrks as $std) {
			$std_marks[$std->parameterId] = $std;
		}

		$parameters = array();
		foreach ($perf_pointers as $key => $res) {
            if(!array_key_exists($res->parameterId, $parameters) && array_key_exists($res->parameterId, $std_marks)) {
                $parameters[$res->parameterId] = array();
                $parameters[$res->parameterId]['perf_pointer_id'] = $res->perf_pointer_id;
                $parameters[$res->parameterId]['assessment_subject_id'] = $res->afl_assessment_subject_id;
                $parameters[$res->parameterId]['pointer_name'] = $res->pointer_name;
                $parameters[$res->parameterId]['self_marks'] = $std_marks[$res->parameterId]->self_marks;
                $parameters[$res->parameterId]['evaluated_marks'] = $std_marks[$res->parameterId]->evaluated_marks;
            }
        }
        return $parameters;

        /*$result = $this->db->select('rp.id as parameterId, rp.perf_pointer_id, rp.description as parameter_description, pp.pointer_name, rp.afl_assessment_subject_id, sm.self_marks, sm.evaluated_marks')
        ->from('afl_rubric_perf_parameters rp')
        ->join('afl_performance_pointers pp', 'pp.id=rp.perf_pointer_id')
        ->join('afl_student_perf_pointers sp', "sp.afl_assessment_subject_id=rp.afl_assessment_subject_id and sp.student_id=$student_id", 'left')
        ->join('afl_student_marks sm', "sm.afl_rubric_perf_parameter_id=rp.id and sm.afl_student_perf_id=sp.id", 'left')
        ->where_in('rp.afl_assessment_subject_id', $assessment_subject_ids)
        ->get()->result();
        $parameters = array();
        foreach ($result as $key => $res) {
            if(!array_key_exists($res->parameterId, $parameters)) {
                $parameters[$res->parameterId] = array();
                $parameters[$res->parameterId]['perf_pointer_id'] = $res->perf_pointer_id;
                $parameters[$res->parameterId]['assessment_subject_id'] = $res->afl_assessment_subject_id;
                $parameters[$res->parameterId]['pointer_name'] = $res->pointer_name;
                $parameters[$res->parameterId]['self_marks'] = $res->self_marks;
                $parameters[$res->parameterId]['evaluated_marks'] = $res->evaluated_marks;
            }
        }
        return $parameters;*/
    }

    public function getAflSubjects($class_id) {
    	return $this->db->query("select id, subject_name from afl_subjects where class_id=$class_id order by subject_name")->result();
    }

    public function getAflApplicableSubjects($class_id, $student_id) {
    	$sql = "select id, subject_name from afl_subjects 
    			where class_id=$class_id 
    			and id in (select distinct(afl_subject_id) from afl_assessment_subjects where id in (select distinct(afl_assessment_subject_id) from afl_student_perf_pointers where student_id=$student_id)) 
    			order by subject_name";
    	return $this->db->query($sql)->result();
    }

    public function getMonthSummary($assessment_id, $student_id) {
    	$sql1 = "SELECT sub.id as subject_id, sub.subject_name, rp.id as parameterId, rp.perf_pointer_id, rp.description as parameter_description, pp.pointer_name, rp.afl_assessment_subject_id 
    			from afl_subjects as sub 
    			join afl_assessment_subjects as asub on asub.afl_subject_id=sub.id 
    			join afl_rubric_perf_parameters as rp on asub.id=rp.afl_assessment_subject_id 
    			join afl_performance_pointers as pp on pp.id=rp.perf_pointer_id 
    			where asub.afl_assessment_id=$assessment_id 
    			and asub.publish_status=1 
    			order by sub.subject_name";
    	$subjects = $this->db_readonly->query($sql1)->result();
    	if(empty($subjects)) return [];

    	$ass_sub_ids = [];
    	$parameter_ids = [];
    	foreach ($subjects as $sub) {
    		$ass_sub_ids[] = $sub->afl_assessment_subject_id;
    		$parameter_ids[] = $sub->parameterId;
    	}
    	$asub_ids = implode(",", $ass_sub_ids);
    	$p_ids = implode(",", $parameter_ids);

    	$sql2 = "SELECT sm.afl_rubric_perf_parameter_id as parameterId, sm.self_marks, sm.evaluated_marks, sp.publish_status 
    			from afl_student_perf_pointers as sp 
    			join afl_student_marks as sm on sm.afl_student_perf_id=sp.id 
    			where sp.afl_assessment_subject_id in ($asub_ids) 
    			and sm.afl_rubric_perf_parameter_id in ($p_ids) 
    			and sp.student_id=$student_id 
    			and sp.publish_status=1";
		$stdudent_masrks = $this->db_readonly->query($sql2)->result();
		$std_marks = [];
		foreach ($stdudent_masrks as $std) {
			$std_marks[$std->parameterId] = $std;
		}

		foreach ($subjects as $k => $sub) {
			if(array_key_exists($sub->parameterId, $std_marks) && $std_marks[$sub->parameterId]->publish_status == 1) {
				$subjects[$k]->self_marks = $std_marks[$sub->parameterId]->self_marks;
				$subjects[$k]->evaluated_marks = $std_marks[$sub->parameterId]->evaluated_marks;
			}
		}

		$data = array();
        foreach ($subjects as $key => $res) {
        	if(!isset($res->evaluated_marks)) continue;
        	if(!array_key_exists($res->subject_id, $data)) {
        		$data[$res->subject_id] = array();
        		$data[$res->subject_id]['subject_name'] = $res->subject_name;
        		$data[$res->subject_id]['assessment_subject_id'] = $res->afl_assessment_subject_id;
        		$data[$res->subject_id]['parameters'] = array();
        	}
        	array_push($data[$res->subject_id]['parameters'], $res);
        }

        return $data;

    	/*$result = $this->db->select('rp.id as parameterId, rp.perf_pointer_id, rp.description as parameter_description, pp.pointer_name, rp.afl_assessment_subject_id, sm.self_marks, sm.evaluated_marks, sub.id as subject_id, sub.subject_name')
        ->from('afl_rubric_perf_parameters rp')
        ->join('afl_performance_pointers pp', 'pp.id=rp.perf_pointer_id')
        ->join('afl_student_perf_pointers sp', "sp.afl_assessment_subject_id=rp.afl_assessment_subject_id and sp.student_id=$student_id")
        ->join('afl_student_marks sm', "sm.afl_rubric_perf_parameter_id=rp.id and sm.afl_student_perf_id=sp.id", 'left')
        ->join('afl_assessment_subjects asub', "asub.id=rp.afl_assessment_subject_id")
        ->join('afl_subjects sub', "sub.id=asub.afl_subject_id")
        ->where("asub.afl_assessment_id", $assessment_id)
        ->where("asub.publish_status", 1)
        ->where("sp.publish_status", 1)
        ->order_by('sub.subject_name')
        ->get()->result();

        $subjects = array();
        foreach ($result as $key => $res) {
        	if(!array_key_exists($res->subject_id, $subjects)) {
        		$subjects[$res->subject_id] = array();
        		$subjects[$res->subject_id]['subject_name'] = $res->subject_name;
        		$subjects[$res->subject_id]['assessment_subject_id'] = $res->afl_assessment_subject_id;
        		$subjects[$res->subject_id]['parameters'] = array();
        	}
        	array_push($subjects[$res->subject_id]['parameters'], $res);
        }

        return $subjects;*/
    }

    public function getGradingScale($id) {
    	return $this->db->query("select * from afl_grading_scale_values where afl_grading_scale_id=$id")->result();
    }

    public function get_event_detailsOftheStd($stdId){
    	return $this->db->select('emv.*, erd.qr_code, erd.id as erdId')
    	->from('event_master_v2 emv')
    	->where('emv.status',1)
    	->order_by('event_date,start_time')
    	->join('event_registrations erd','emv.id=erd.event_id')
    	->where('erd.student_id',$stdId)
    	->get()->result();
    }

    public function get_wallet_balance_bystudentid($stdId){
    	return $this->db->select('*')->where('student_id',$stdId)->get('student_wallet')->row();
    }

    public function get_event_registrations_details($studentId){
    	$event = $this->db_readonly->select("id, event_name, organizer, max_number_of_allowed, class_sections_id, reg_start_date, reg_end_date, registration_amount, safety_deposit_amount, date_format(reg_start_date,'%d-%b-%Y %h:%i %p') as registration_startdate,date_format(reg_end_date,'%d-%b-%Y') as registration_enddate")
    	->from('event_master_v2')
    	->where('status',1)
    	->order_by('id','desc')
    	->get()->result();

    	foreach ($event as $key => &$val) {
    		$val->event_date = $this->_check_event_dates($val->id);
    		$val->class_true = $this->_class_check($val->class_sections_id, $studentId);
    		$val->registration_obj = $this->_check_already_registered($val->id, $studentId);
    		$val->is_more_than_start_date = $this->_check_start_date_of_event($val->reg_start_date);
    		$val->is_less_than_end_date = $this->_check_end_date_of_event($val->reg_end_date);
    		$val->max_allowed = $this->_check_max_allowed_of_event($val->id, $val->max_number_of_allowed);
    	}
    	// echo "<pre>"; print_r($event); die();
    	return $event;
    }

    public function _check_max_allowed_of_event($event_id,$max_allowed){
    	$register = $this->db->select('count(id) as register_count')->where('event_id',$event_id)->get('event_registrations')->row();
   
    	if ($max_allowed > $register->register_count) {
    		return 1;
    	}else{
    		return 0;
    	}
    }

    public function _check_event_dates($event_id){
	    return $this->db->select("event_id, date_format(event_date,'%d-%b-%Y') as eventdate, time_format(start_time,'%h:%i %p') as starttime, time_format(end_time,'%h:%i %p') as endtime")
	    ->from('event_v2_time_details')
	    ->where('event_id',$event_id)
	    ->get()->result();
    }

    public function _check_start_date_of_event($start_date){

    	$current_date = new DateTime();
 		$startDate    = new DateTime($start_date);
    	if ($current_date >= $startDate) {
    		return 1;
    	}else{
    		return 0;
    	}
    }

 	public function _check_end_date_of_event($end_date){

 		$current_date = new DateTime();
 		$endDate    = new DateTime($end_date);
    	if ($current_date <= $endDate) {
    		return 1;
    	}else{
    		return 0;
    	}
    }

    public function _check_already_registered($eventId, $studentId){

    	$result =  $this->db->select("event_id, er.student_id, registered_on")
    	->from('event_registrations er')
    	->where('event_id',$eventId)
    	->where('er.student_id',$studentId)
    	->get()->row();
    	if (!empty($result)) {
			$result->register_on = local_time($result->registered_on, 'd-m-Y h:i A');
    		return $result;
    	}else{
    		return 0;
    	}
    }

    public function _class_check($selectClassSectionId, $studentId){
    	$sectionIds = json_decode($selectClassSectionId);

    	$result = $this->db_readonly->select('sa.id')
    	->from('student_admission sa')
    	->where('sa.id',$studentId)
	    ->join('student_year sy', 'sa.id=sy.student_admission_id')
	    ->where_in('sy.class_section_id',$sectionIds)
	    // ->where('sy.acad_year_id', $this->yearId)
    	->get()->row();
    	if (!empty($result)) {
    		return 1;
    	}else{
    		return 0;
    	}

    }

    public function get_publish_event_list($studentId){

    	$classId = $this->db->select('c.id')
    	->from('student_admission sa')
    	->where('sa.id',$studentId)
	    ->join('student_year sy', 'sa.id=sy.student_admission_id')
	    ->join('class c', 'c.id=sy.class_id')
	    ->where('sy.acad_year_id', $this->yearId)
    	->get()->row();

    	$eventMaster = $this->db->select("*, date_format(reg_start_date,'%d-%b-%Y') as registration_startdate,date_format(reg_end_date,'%d-%b-%Y') as registration_enddate")
	    ->from('event_master_v2')
	    ->where('status',1)
	    ->order_by('id','desc')
	    ->get()->result();

	    $eventTime = $this->db->select("event_id, date_format(event_date,'%d-%b-%Y') as eventdate, time_format(start_time,'%h:%i %p') as starttime, time_format(end_time,'%h:%i %p') as endtime")
	    ->from('event_v2_time_details')
	    ->get()->result();

	    $eventDates = [];
	    foreach ($eventTime as $key => $val) {
	      $eventDates[$val->event_id][] = $val;
	    }
	   
     	$eventArry = [];
	    foreach ($eventMaster as $key => $val) {
    		$class = json_decode($val->classes);
    		if (in_array($classId->id, $class)) {
    			array_push($eventArry, $val);
    		}
	    }
	    foreach ($eventArry as $key => &$val) {
			if (array_key_exists($val->id,$eventDates)) {
		        $val->event_date = $eventDates[$val->id];
	      	}
	    }
       	return $eventArry;
    }

    public function event_registration_list($stdId){
    	$result =  $this->db->select("event_id,er.student_id, p.first_name, date_format(registered_on,'%d-%b-%Y %h:%i') as register_on")
    	->from('event_registrations er')
    	->where('er.student_id',$stdId)
    	->join('avatar a','er.registered_by=a.id')
    	->join('parent p','p.id=a.stakeholder_id')
    	->get()->result();
    	$eventArry = [];
    	foreach ($result as $key => $val) {
    		$eventArry[$val->event_id] = $val;
    	}
    	return $eventArry;
    }

    public function get_event_registration_count(){
		$register = $this->db->select('event_id, count(id) as register_count')->group_by('event_id')->get('event_registrations')->result();
		$registerCount = [];
		foreach ($register as $key => $val) {
			$registerCount[$val->event_id] = $val->register_count;
		}
		return $registerCount;
    }
    public function get_register_event_detailsbyId($event_id){
    	$event = $this->db->select("*, date_format(reg_start_date,'%d-%b-%Y') as registration_startdate,date_format(reg_end_date,'%d-%b-%Y') as registration_enddate ")
    	->from('event_master_v2')
    	->where('id',$event_id)
    	->get()->row();

	 	$eventTime = $this->db->select("event_id, date_format(event_date,'%d-%b-%Y') as eventdate, time_format(start_time,'%h:%i %p') as starttime, time_format(end_time,'%h:%i %p') as endtime, description")
	    ->from('event_v2_time_details')
	    ->where('event_id',$event_id)
	    ->get()->result();

    	$event->event_date = $eventTime;
	  
    	$registerCount = $this->db->select('count(id) as register_count')->where('event_id',$event_id)->get('event_registrations')->row()->register_count;

    	$subRegister = $this->db->select('sub_event_id')->where('event_id',$event_id)->get('event_registrations')->result();

					$subCount = [];
			foreach ($subRegister as $key => $val) {
				$res = json_decode($val->sub_event_id);
if(!empty($res)){
				foreach ($res as $key => $value) {
					if (!array_key_exists($value, $subCount)) {
						$subCount[$value] = 0;
					}
					$subCount[$value] += count(array($subCount[$value]));
				}
			}
	}
			$event->subRegister = $subCount;
		
    	if ($event->max_number_of_allowed > $registerCount) {
    		return $event;
    	}else{
    		return 0;
    	}
    }

    public function get_success_event_detailsbyId($event_id, $studentId){
    	$event = $this->db->select("*, date_format(reg_start_date,'%d-%b-%Y') as registration_startdate,date_format(reg_end_date,'%d-%b-%Y') as registration_enddate")
    	->from('event_master_v2')
    	->where('id',$event_id)
    	->get()->row();
    	
    	$eventTime = $this->db->select("event_id, date_format(event_date,'%d-%b-%Y') as eventdate, time_format(start_time,'%h:%i %p') as starttime, time_format(end_time,'%h:%i %p') as endtime, description")
	    ->from('event_v2_time_details')
	    ->where('event_id',$event_id)
	    ->get()->result();

	    $eventSubname = $this->db->select("er.sub_event_id")
	    ->from('event_registrations er')
	    ->where('er.event_id',$event_id)
	    ->where('er.student_id',$studentId)
	    ->get()->row();
	    $event->event_date = $eventTime;
	    $event->subname =[];
	    if (!empty($eventSubname->sub_event_id)) {
	    	$res = json_decode($eventSubname->sub_event_id);
			if(!empty($res)){
				foreach ($res as $key => $va) {
					$event->subname[] = $this->db->select("sub_event_name, max_registrations_allowed, sub_event_description, (case when sub_event_date = '1970-01-01' then '' else date_format(sub_event_date,'%d-%m-%Y') end) as  sub_event_date, (case when start_time_of_registration = '00:00:00' then '' else date_format(start_time_of_registration,'%h:%i %p') end) as start_time_of_registration, (case when end_time_of_registration = '00:00:00' then ' ' else date_format(end_time_of_registration,'%h:%i %p') end) as end_time_of_registration")
					->from('eventv2_sub_events')
					->where('id',$va)
					->get()->row();
				}
			}
	    }
	    return $event;
    }

    public function insert_event_registrationbyparent($event_id,$regAmount,$safetyAmount, $sub_event_id){
    	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
    	$tempArry = array(
    		'student_id'=>$studentId,
    		'transaction_mode'=>'offline',
    		'created_by'=>$this->authorization->getAvatarId(),
    		'status'=>'active',
    		'source_id'=>$event_id,
    		'source_type'=>'Event',
    	);

	 	$this->db->trans_begin();
	 	$wallet_tx_id = '';
	 	if ($regAmount !=0) {
    		$wallet_tx_id = $this->_wallet_transactions($tempArry, $regAmount, $studentId);
	 	}
	 	$wallet_safety_tx_id = '';
	 	if ($safetyAmount !=0) {
			$wallet_safety_tx_id = $this->_wallet_safety_transactions($tempArry, $safetyAmount, $studentId);
	 	}
		$result = $this->_event_registrations($event_id, $wallet_tx_id, $wallet_safety_tx_id, $studentId, $sub_event_id);
		if ($result) {
			$this->db->trans_complete();
	      	if ($this->db->trans_status()) {
		        echo 1;
	      	}else{
		        echo 0;
	      	}
		}else{
			echo 0;
		}
		 	
    	

    }

    public function _wallet_transactions($tempArry, $regAmount, $studentId){
    	$this->db->where('student_id', $studentId);
    	$this->db->order_by('id','desc');
      	$query = $this->db->get('student_wallet_transactions')->row();
      	$running_balance_amount = 0;
      	if (!empty($query)) {
      		$running_balance_amount = $query->running_balance_amount;
      	}
    	$txArry = array(
    		'amount'=>$regAmount,
    		'running_balance_amount'=>$running_balance_amount - $regAmount,
    		'transaction_type'=>'Pay'
    	);

    	$mergeArry = array_merge($tempArry, $txArry);
    	$this->db->insert('student_wallet_transactions',$mergeArry);
    	$txId =  $this->db->insert_id();

    	$this->db->where('student_id', $studentId);
      	$query = $this->db->get('student_wallet')->row();

        $update_walletData = array(
          'wallet_amount' => $query->wallet_amount - $regAmount,
        );
        $this->db->where('student_id',$studentId);
        $this->db->update('student_wallet',$update_walletData);
        return $txId;

    }

    public function _wallet_safety_transactions($tempArry, $safetyAmount, $studentId){
    	$this->db->where('student_id', $studentId);
    	$this->db->order_by('id','desc');
      	$query = $this->db->get('student_wallet_transactions')->row();
      	$running_balance_amount = 0;
      	if (!empty($query)) {
      		$running_balance_amount = $query->running_balance_amount;
      	}
    	$txArry = array(
    		'amount'=>$safetyAmount,
    		'running_balance_amount'=>$running_balance_amount - $safetyAmount,
    		'transaction_type'=>'Hold'
    	);
    	$mergeArry = array_merge($tempArry, $txArry);
    	$this->db->insert('student_wallet_transactions',$mergeArry);
    	$seftyTxId =  $this->db->insert_id();

    	$this->db->where('student_id', $studentId);
      	$query = $this->db->get('student_wallet')->row();

        $update_walletData = array(
          'hold_amount' =>  $safetyAmount + $query->hold_amount,
          'wallet_amount' => $query->wallet_amount - $safetyAmount,
        );
        $this->db->where('student_id',$studentId);
        $this->db->update('student_wallet',$update_walletData);
        return $seftyTxId;
    }

    public function _event_registrations($event_id, $wallet_tx_id, $wallet_safety_tx_id, $studentId, $sub_event_id){
    	$query = $this->db->select('id')
    	->from('event_registrations')
    	->where('event_id',$event_id)
    	->where('student_id',$studentId)
    	->get();
    	if ($query->num_rows() > 0) {
    		return 0;
    	}
    	$event = $this->db->select("*")
    	->from('event_master_v2')
    	->where('id',$event_id)
    	->get()->row();
	  
    	$registerCount = $this->db->select('count(id) as register_count')->where('event_id',$event_id)->get('event_registrations')->row()->register_count;

    	if ($event->max_number_of_allowed > $registerCount) {
    		$eventReg = array(
				'event_id'=>$event_id,
				'student_id'=>$studentId,
				'no_of_person '=> '1',
				'status '=> 'active',
				'registered_by '=> $this->authorization->getAvatarId(),
				'registration_wallet_tx_id '=> $wallet_tx_id,
				'safety_depost_amount_wallet_tx_id '=> $wallet_safety_tx_id,
				'safety_deposit_amount_status '=> 'collected',
				'sub_event_id'=>json_encode($sub_event_id)
	    	);
	    	return $this->db->insert('event_registrations',$eventReg);
    	}else{
    		return 0;
    	}
	
    }

    public function get_event_detailsbyeventId($erdId){
    	return $this->db->select('emv.*, erd.qr_code')
    	->from('event_master_v2 emv')
    	->where('erd.id',$erdId)
    	->order_by('event_date,start_time')
    	->join('event_register_details erd','emv.id=erd.event_id')
    	->get()->row();
    }

    public function downloadEventAttachment($id){
		return $this->db->select("event_file")
			->where('id', $id)
			->get('event_master_v2')->row()->event_file;
	}
	public function get_terms_condition_blueprintwise_data($bpId){
		$result = $this->db->where('id',$bpId)->get('feev2_blueprint')->row();

		if ($result->terms_conditions == '') {
			return 0;
		}else{
			return $result->terms_conditions;
		}
	}

	public function check_recon_statusbystdId($stdId, $schId){
		$result = $this->db_readonly->select('reconciliation_status')
		->from('feev2_transaction ft')
		->where('ft.student_id',$stdId)
		->where('ft.fee_student_schedule_id',$schId)
		->join('feev2_transaction_payment ftp',"ft.id=ftp.fee_transaction_id and ftp.reconciliation_status=1")
		->get()->row();
		$status = 0;
		if (!empty($result)) {
			if ($result->reconciliation_status == 1)
			$status = 1;
		}
		return $status;
	}

	public function check_online_payment_progress_status($student_id){
		
		$response_code = array('1030','1088','1006');
		$result = $this->db_readonly->select('opm.tx_response_code')
		->from('feev2_transaction ft')
		->where('ft.student_id',$student_id)
		->join('online_payment_master opm','ft.id=opm.source_id')
		->where_in('opm.tx_response_code',$response_code)
		->where('opm.source','PARENT_FEE')
		->get()->row();

		if (!empty($result)) {
			return $result->tx_response_code;
		}else{
			return 0;
		}
	}

	public function get_blueprint_transport_enabled_id($studentId){

		$this->db_readonly->select('c.branch_id')
		->from('student_admission sa')
		->where('sa.id',$studentId)
		->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
		->join("class c", "sy.class_id=c.id");
        $getBrachId = $this->db_readonly->get()->row();
		$this->db_readonly->select('fb.id, transport_selection');
		$this->db_readonly->from('feev2_blueprint fb');
		$this->db_readonly->where('fb.acad_year_id',$this->yearId);
		$this->db_readonly->where('fb.transport_selection',1);
		if(!empty($getBrachId->branch_id)) {
        	$this->db_readonly->where('fb.branches',$getBrachId->branch_id);
        }
        return  $this->db_readonly->get()->row();

		
	}

	public function getSubjectsList($class_id){
		$this->db_readonly->select('lp.subject_name as subject_name,lp.id as subject_id');
		$this->db_readonly->from('lp_subjects lp');
		$this->db_readonly->join('class c', 'c.class_name = lp.class_name', 'left');
		$this->db_readonly->where('c.id',$class_id);
		$this->db_readonly->where('c.acad_year_id',$this->yearId);
		$this->db_readonly->where('lp.acad_year_id',$this->yearId); // added for acad year to get unique subject id
		return $this->db_readonly->get()->result();
	}

	public function filterTasks($student_id) {
		if(empty($_POST)){
			return [];
		}

		$mode = $_POST['mode'];
		$task_type = $_POST['task_type'];
		$from_date = date('Y-m-d', strtotime($_POST['from_date']));
		$to_date = date('Y-m-d', strtotime($_POST['to_date']));
		$subject_id = $_POST['subject_id'];
		$date_time= date('Y-m-d H:i:s');
		$this->db_readonly->select("t.id as task_id, t.task_name, t.task_type, t.task_description, t.status, ts.id as task_student_id, ts.read_status, ts.submission_status, ts.evaluation_status, ts.is_late_submission, ts.resubmission_status, sub.subject_name, TIMESTAMPDIFF(MINUTE, t.task_last_date, now()) as elapsed_time, CONVERT_TZ(t.task_last_date,'+00:00','+05:30') as local_task_last_date, CONVERT_TZ(t.created_on,'+00:00','+05:30') as created_on, t.release_evaluation, t.close_submission, t.created_on as created_on_new");
		$this->db_readonly->from('lp_tasks t');
		$this->db_readonly->join('lp_tasks_students ts', 't.id=ts.lp_tasks_id');
		$this->db_readonly->join('lp_subjects sub','sub.id=t.subject_id');
		$this->db_readonly->where('t.status', 'published');
		$this->db_readonly->where('ts.student_id', $student_id);
		$this->db_readonly->where('t.acad_year_id', $this->yearId);
		$this->db_readonly->where("(t.task_publish_timestamp is NULL OR t.task_publish_timestamp <= '$date_time')");
		if($subject_id != 'all'){
			$this->db_readonly->where('t.subject_id', $subject_id);
		}
		if($task_type != 'all'){
			$this->db_readonly->where('t.task_type', $task_type);
		}

		if($mode == 'all') {
			$this->db_readonly->where("(DATE_FORMAT(t.created_on,'%Y-%m-%d')>='$from_date' AND DATE_FORMAT(t.created_on,'%Y-%m-%d')<='$to_date')");
		} else if($mode=='not_submitted') {
			$this->db_readonly->where('ts.submission_status',0);
		} else if($mode=='overdue') {
			$today = date('Y-m-d');
			$this->db_readonly->where('ts.submission_status',0);
			$this->db_readonly->where("DATE_FORMAT(t.task_last_date, '%Y-%m-%d')<='$today'");
		}
		$this->db_readonly->order_by('t.id', 'DESC');
		$result = $this->db_readonly->get()->result();
		return $result;
	}

	public function getFilteredStudentsTasks($studentId){
		$from_date = $_POST['from_date'];
		$to_date = $_POST['to_date'];
		$subject_id = $_POST['subject_id'];
		$mode = $_POST['mode'];
		$task_type = $_POST['task_type'];
		// $this->db_readonly->select("lt.*,lts.*,lt.id as task_id,lp.subject_name as subject_name, TIMESTAMPDIFF(MINUTE, lt.task_last_date, now()) as elapsed_time, CONVERT_TZ(lt.task_last_date,'+00:00','+05:30') as local_task_last_date");
		$this->db_readonly->select("lt.consider_this_task_as, lt.id as task_id, lt.task_name, lt.task_type, lt.task_description, lt.status, lts.id as task_student_id, lts.read_status, lts.submission_status, lts.evaluation_status, lts.is_late_submission, lts.resubmission_status, lp.subject_name, TIMESTAMPDIFF(MINUTE, lt.task_last_date, now()) as elapsed_time, CONVERT_TZ(lt.task_last_date,'+00:00','+05:30') as local_task_last_date, CONVERT_TZ(lt.created_on,'+00:00','+05:30') as created_on, lt.release_evaluation, lt.close_submission");
		$this->db_readonly->from('lp_tasks_students lts');
		$this->db_readonly->join('lp_tasks lt',"lt.id=lts.lp_tasks_id");
		if($from_date != 'all'){
			$from_date_org = date('Y-m-d',strtotime($from_date));
			$to_date_org = date('Y-m-d',strtotime($to_date));
			$this->db_readonly->where("date_format(lt.created_on,'%Y-%m-%d')>='$from_date_org' and date_format(lt.created_on,'%Y-%m-%d')<='$to_date_org'");
		}
		if($subject_id=='all'){
			$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		}
		else{
			$this->db_readonly->join('lp_subjects lp',"lp.id=lt.subject_id and lp.id='$subject_id'");
		}
		if($task_type != 'all'){
			$this->db_readonly->where('lt.task_type',$task_type);
		}
		$this->db_readonly->where('lts.student_id',$studentId);
		if($mode=='not_submitted'){
			$this->db_readonly->where('lts.submission_status',0);
		}
		else if($mode=='unread'){
			$this->db_readonly->where('lts.read_status','unread');
		}
		$date_time= date('Y-m-d H:i:s');
		$this->db_readonly->where("(lt.task_publish_timestamp is NULL OR lt.task_publish_timestamp <= '$date_time')");
		$this->db_readonly->order_by('lp.subject_name', 'asc');
		$this->db_readonly->order_by('lt.created_on', 'DESC');
		// $this->db_readonly->group_by('lt.task_name');
		return $this->db_readonly->get()->result();
	}
	public function getFilteredStudentsTasksDesktop($studentId){
		$from_date = $_POST['from_date'];
		$to_date = $_POST['to_date'];
		$subject_id = $_POST['subject_id'];
		$from_date_org = date('Y-m-d',strtotime($from_date));
		$to_date_org = date('Y-m-d',strtotime($to_date));
		$mode = $_POST['mode'];
		$this->db_readonly->select("lt.*,lts.*,lt.id as task_id,lp.subject_name as subject_name,lt.id as task_id, TIMESTAMPDIFF(MINUTE, lt.task_last_date, now()) as elapsed_time, CONVERT_TZ(lt.task_last_date,'+00:00','+05:30') as local_task_last_date");
		$this->db_readonly->from('lp_tasks_students lts');
		$this->db_readonly->join('lp_tasks lt',"lt.id=lts.lp_tasks_id and date_format(lt.created_on,'%Y-%m-%d')>='$from_date_org' and date_format(lt.created_on,'%Y-%m-%d')<='$to_date_org'");
		// $this->db_readonly->where('lt.acad_year_id', $this->yearId);
		if($subject_id=='all'){
			$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		}
		else{
			$this->db_readonly->join('lp_subjects lp',"lp.id=lt.subject_id and lp.id='$subject_id'");
		}
		$this->db_readonly->where('lts.student_id',$studentId);
		//if($mode=='not_submitted'){
			//$this->db_readonly->where('lts.submission_status',0);
		//}
		/*else if($mode=='unread'){
			$this->db_readonly->where('lts.read_status','unread');
		}*/
		$this->db_readonly->order_by('lt.created_on', 'DESC');
		return $this->db_readonly->get()->result();
	}
	public function getFilteredStudentsTasksDesktopOverdue($studentId){
		
		$subject_id = $_POST['subject_id'];
		$today = date('Y-m-d');
		
		$mode = $_POST['mode'];
		$this->db_readonly->select("lt.*,lts.*,lt.id as task_id,lp.subject_name as subject_name,lt.id as task_id");
		$this->db_readonly->from('lp_tasks_students lts');
		$this->db_readonly->join('lp_tasks lt',"lt.id=lts.lp_tasks_id");
		if($subject_id=='all'){
			$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		}
		else{
			$this->db_readonly->join('lp_subjects lp',"lp.id=lt.subject_id and lp.id='$subject_id'");
		}
		$this->db_readonly->where('lts.student_id',$studentId);
		if($mode=='not_submitted'){
			$this->db_readonly->where('lts.submission_status',0);
			
		}
		else if($mode=='overdue'){
			$this->db_readonly->where('lts.submission_status',0);
			$this->db_readonly->where("DATE_FORMAT(lt.task_last_date, '%Y-%m-%d')<='$today'");
		}
		/*else if($mode=='unread'){
			$this->db_readonly->where('lts.read_status','unread');
		}*/
		$this->db_readonly->order_by('lt.created_on', 'DESC');
		return $this->db_readonly->get()->result();
	}

	public function getStudentDetails($studentId){
		$section_id = $this->db_readonly->select('sy.class_section_id')
		->from('student_admission sa')
		->join('student_year sy', 'sy.student_admission_id = sa.id', 'left')
		->where('sa.id', $studentId)
		->where('sy.acad_year_id', $this->yearId)
		->get()->row();

		$result = $this->db_readonly->select("CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name, sa.id, sum(lsr.secured_points) as secured_points, sy.picture_url, sa.gender")
		->from('student_admission sa')
		->join('student_year sy', 'sy.student_admission_id = sa.id', 'left')
		->join('lp_student_rewards lsr', 'lsr.student_id = sa.id', 'left')


		->where('sy.class_section_id', $section_id->class_section_id)
		->group_by('sa.id')
		->order_by('sum(lsr.secured_points)', 'DESC')


		->get()->result();

		// $points =  $this->db->select('sum(lsr.secured_points) as secured_points1')
		// ->from('lp_student_rewards lsr')
		// ->group_by('lsr.student_id')
		// ->order_by('sum(lsr.secured_points)', 'DESC')
		// ->get()->result();

		// foreach($result as $res){
		// 	$found = 0;
		// 	foreach($points as $row){
		// 		if ($res->id == $row->student_id) {
		// 			$found = 1;
		// 		}
		// 	}
		// 	if ($found) {
		// 		$res->secured_points = $points;
		// 	} else {
		// 		$res->secured_points = 0;
		// 	}
		// }
		// echo "<pre>";print_r($result);die();
		return $result;
	}
	public function getAssessmentQuestions($task_id, $assessment_id, $studentId) {
		$sql = "SELECT q.*, ts.id as task_student_id, ts.assessment_status  
				FROM lp_tasks t 
				JOIN lp_tasks_students ts ON ts.lp_tasks_id=t.id 
				JOIN lp_assessment a ON a.id=t.lp_assessment_id 
				JOIN lp_assessment_questions aq ON aq.lp_assessment_id=a.id 
				JOIN lp_questions q ON q.id=aq.lp_question_id 
				WHERE t.id=$task_id 
				AND ts.student_id=$studentId 
				AND t.lp_assessment_id=$assessment_id";
		$result = $this->db->query($sql)->result();

		$sql = "SELECT aa.lp_question_id, ifnull(aa.answer, '') as answer, ifnull(aa.status, '') as answer_status 
				FROM lp_tasks_students ts  
				JOIN lp_assessment_answers aa ON aa.lp_task_student_id=ts.id 
				WHERE ts.lp_tasks_id=$task_id 
				AND ts.student_id=$studentId";
		$given_answers = $this->db->query($sql)->result();
		$answers = [];
		foreach ($given_answers as $key => $ans) {
			$answers[$ans->lp_question_id] = $ans->answer;
		}
		foreach ($result as $key => $res) {
			$res->options = json_decode($res->options);
			if(array_key_exists($res->id, $answers))
				$result[$key]->answer_given = $answers[$res->id];
		}
		// echo '<pre>'; print_r($result); die();
		return $result;
	}

	public function submitAssessment($studentId) {
		$task_student_id = $_POST['task_student_id'];
		$qids = $_POST['questions'];
		$quests = $this->db->select("id, points, answer")->where_in('id', $qids)->get('lp_questions')->result();
		$questions = [];
		$total_points = 0;
		$secured_points = 0;
		foreach ($quests as $key => $quest) {
			$questions[$quest->id] = $quest;
			$total_points += $quest->points;
		}
		$answers = $_POST['answers'];
		$data = [];
		foreach ($answers as $q_id => $answer) {
			$data[] = array(
				'lp_task_student_id' => $task_student_id,
				'lp_question_id' => $q_id,
				'answer' => $answer
			);
			if($answer == $questions[$q_id]->answer) {
				$secured_points += $questions[$q_id]->points;
			}
		}

		if(!empty($data)) {
			$this->db->trans_start();
			$this->db->insert_batch('lp_assessment_answers', $data);
			$this->db->where('id', $task_student_id)->update('lp_tasks_students', ['assessment_status' => 1]);
			$given_by = $this->db->select("created_by")->where('id', $_POST['task_id'])->get('lp_tasks')->row()->created_by;
			$rewards = array(
				'student_id' => $studentId,
				'source' => 'Formative Assessment',
				'secured_points' => $secured_points,
				'total_points' => $total_points,
				'given_by' => $given_by
			);
			$this->db->insert('lp_student_rewards', $rewards);
			$this->db->trans_complete();
		}
		return 1;
	}

	public function check_is_student_deactivated_temporarily($studentId) {
		return $this->db->select('temp_deactivation')->where('id', $studentId)->get('student_admission')->row()->temp_deactivation;
	}

	public function checkStudentPartiallyDeactivated($student_id) {
		return $this->db_readonly->where('id', $student_id)->select('temp_deactivation')->get('student_admission')->row()->temp_deactivation;
	}

	public function removeSubmittedTaskFiles($lp_tasks_students_id) {
		$this->db->trans_start();
		
		$this->db->where('id', $lp_tasks_students_id)->update('lp_tasks_students', ['submission_status' => 0, 'resubmission_status' => 0]);

		$this->db->where('lp_tasks_students_id', $lp_tasks_students_id)->update('lp_tasks_students_submission_files', ['status' => 0]);
		$this->db->trans_complete();
		if($this->db->trans_status() === FALSE) {
			$this->db->trans_rollback();
			return 0;
		} else {
			$this->db->trans_commit();
			return 1;
		}
	}
	public function update_tnc_status($cohort_student_id){
		$avatarId =  $this->authorization->getAvatarId();
		$parentId =  $this->db_readonly->select('stakeholder_id as parent_id')
	  	->from('avatar')
	  	->where('avatar_type',2)
	  	->where('id', $avatarId)
	  	->get()->row();

		$data = array(
			'tnc_status'=>1,
			'tnc_accepted_by'=>$parentId->parent_id,
		);
		$this->db->where('id',$cohort_student_id);
		$this->db->update('feev2_cohort_student',$data);
		return $this->db->affected_rows();
	}

	public function get_day_section_timtable_v2($csId){

		$sql="select id from ttv2_template where status='active'";
		$template_ids_list = $this->db_readonly->query($sql)->result();
		
		$sec_template_id = '';		
		if(!empty($template_ids_list)){
			for($i=0;$i<count($template_ids_list);$i++){
				$template_ids[$i]=$template_ids_list[$i]->id;
			}
	
			$ttt_id = implode(",",$template_ids);
	
			$week_day = date('w');
			//echo '<pre>';print_r($week_day);die();
			if($week_day==0){
				$week_day = 7;
			}
	
			//echo '<pre>';print_r($weekDay);die();
			$sql = "select ttst.id as sec_temp_id from ttv2_section_templates ttst
			join ttv2_template ttt on ttst.ttv2_template_id = ttt.id
			where ttst.class_section_id ='$csId' and ttt.id in ($ttt_id)";
		
			$sec_template_id =  $this->db_readonly->query($sql)->row('sec_temp_id');
			//echo '<pre>';print_r($sec_template_id);die();
		}

		
	
		if($sec_template_id){
		  //echo '<pre>';print_r('1');die();
		  $query = $this->db_readonly->select('ttstp.id as period_id, ttstp.period_type, date_format( ttstp.copy_start_time, "%H:%i") as start_time, date_format( ttstp.copy_end_time, "%H:%i") as end_time, ttstp.copy_long_name as long_name, ttstp.copy_short_name as short_name, ttstp.copy_week_day as week_day, ttstp.copy_pseq as pseq, ttstp.allotted_sw_id, sub.long_name as sub_longname, sub.short_name as sub_short_name, ttsw.staff_id_list, ttsw.staff_shortname_list, ttsw.staff_longname_list, ttsw.id as ttv2_sw_id, ttv2_template_periods_id as tttpid')
		  ->from('ttv2_section_template_periods ttstp')
		  ->join('ttv2_staff_workload ttsw', 'ttstp.allotted_sw_id=ttsw.id','left')
		  ->join('ttv2_subjects sub', 'ttsw.subject_id=sub.id','left')
		  ->where('ttv2_section_template_id', $sec_template_id)
		  ->where('copy_week_day', $week_day)
		  ->order_by('copy_week_day, copy_pseq')
		  ->get()->result();
	
		  $staff_query = $this->db_readonly->select('sm.id as staff_id, sm.first_name as staff_name, sm.oc_platform, oc_additional_info, oc_link')
		  ->from('staff_master sm')
		  ->where('status', 2)
		  ->get()->result();
		  
		  foreach ($query as &$period) {
			$oc_data = array();
			$staff_arr = explode(',', $period->staff_id_list);
			foreach ($staff_query as $staff) {
			  if (in_array($staff->staff_id, $staff_arr)) {
				$oc_temp = new stdClass();
				$oc_temp->oc_platform = $staff->oc_platform;
				$oc_temp->oc_link = $staff->oc_link;
				$oc_temp->oc_additional_info = $staff->oc_additional_info;
				$oc_temp->staff_name = $staff->staff_name;
				$oc_data[] = $oc_temp;
			  }
			}
			$period->oc_data = $oc_data;
		  }
	
		  //echo '<pre>';print_r($query);die();
		  $return_data['timetable_exists'] = 1;
      	  $return_data['section_tt_periods'] = $query;
		  return $return_data;
		}
		else{
			$return_data['timetable_exists'] = 0;
			return $return_data;
		}
	}

	public function get_section_oc_links($csId){
		$sql = "select oc_platform, oc_link, oc_additional_info, CONCAT(ifnull(class_name,''), ifnull(section_name,'')) as class_name 
		from class_section where id='$csId'";
		$oc =  $this->db_readonly->query($sql)->row();
		return $oc;
	}

	public function get_template_by_id($csId) {
		$sql="select ttt.id from ttv2_section_templates ttst
		join ttv2_template ttt on ttst.ttv2_template_id = ttt.id
		where ttst.class_section_id='$csId' and ttt.status='active'";
		$ttv2_id = $this->db_readonly->query($sql)->row('id');

		if (empty($ttv2_id)) {
			return '';
		}

		$sql = "select id, name, description, acad_year_id, show_sunday, show_saturday, show_class_links, show_online_class_info, show_section_links_instead, (select count(*) 
		from ttv2_template_periods tvp where tvp.ttv2_id=tt.id) as is_periods_created from ttv2_template tt where tt.id=$ttv2_id";
	
		$result = $this->db_readonly->query($sql)->row();
				  
		return $result;
	}

	public function get_acad_year_id_from_text(){

		$sql = "SELECT DISTINCT acad_year_id FROM texting_master where acad_year_id !=0 and acad_year_id is not null ORDER BY acad_year_id desc";

		$result = $this->db_readonly->query($sql)->result();
				  
		return $result;
	}

	public function getStudentBoard($student_id) {
		return $this->db_readonly->query("SELECT sy.board from student_year sy where acad_year_id=$this->yearId and sy.student_admission_id=$student_id")->row()->board;
	}

	public function get_certificates_details($student_id){
		$father = $this->db->select("p.id as parent_id, CONCAT(ifnull(p.first_name,''), ifnull(p.last_name,'')) as parentName, vaccination_name as  vaccine_name, vaccination_status, (case when vaccination_supporting_document is null then 'Not-Updated' else 'Updated' end) as upload_status, vaccination_verification_status as verify_status, vaccination_last_submitted_date as last_submitted_date, sr.relation_type")
		->from('student_relation sr')
		->where('sr.relation_type','Father')
		->join('parent p','sr.relation_id=p.id')
		->where('p.student_id',$student_id)
		->get()->row();

		$mother = $this->db->select("p.id as parent_id, CONCAT(ifnull(p.first_name,''), ifnull(p.last_name,'')) as parentName, vaccination_name as  vaccine_name, vaccination_status, (case when vaccination_supporting_document is null then 'Not-Updated' else 'Updated' end) as upload_status, vaccination_verification_status as verify_status, vaccination_last_submitted_date as last_submitted_date, sr.relation_type")
		->from('student_relation sr')
		->where('sr.relation_type','Mother')
		->join('parent p','sr.relation_id=p.id')
		->where('p.student_id',$student_id)
		->get()->row();

		$student = $this->db->select("sa.id as parent_id, CONCAT(ifnull(sa.first_name,''), ifnull(sa.last_name,'')) as studentName, vaccination_name as  vaccine_name, vaccination_status, (case when vaccination_supporting_document is null then 'Not-Updated' else 'Updated' end) as upload_status, vaccination_verification_status as verify_status, vaccination_last_submitted_date as last_submitted_date, 'Student' as relation_type")
		->from('student_admission sa')
		->where('id',$student_id)
		->get()->row();
		return array('father'=>$father, 'mother'=>$mother, 'student'=>$student);

	}

	public function save_vaccination_details_by_id($parent_id, $path, $vaccine_name, $vaccine_status, $relation_type){
		$data = array(
			'vaccination_name' =>$vaccine_name, 
			'vaccination_status' =>$vaccine_status, 
			'vaccination_supporting_document' =>$path,
			'vaccination_last_submitted_date' => gmdate("Y-m-d H:i:s"),
			'vaccination_verification_status'=> 'Not-Verified'
		);
		if ($relation_type =='Student') {
			$this->db->where('id',$parent_id);
			return $this->db->update('student_admission', $data);
		}else{
			$this->db->where('id',$parent_id);
			return $this->db->update('parent', $data);
		}
		
	}

	public function download_vaccination_certificate_by_id($parent_id, $relation_type){
		if ($relation_type == 'Student') {
			return $this->db->select('vaccination_supporting_document')->where('id', $parent_id)->get('student_admission')->row()->vaccination_supporting_document;
		}else{
			return $this->db->select('vaccination_supporting_document')->where('id', $parent_id)->get('parent')->row()->vaccination_supporting_document;
		}
	}

	public function getNonComplianceOfStudent($student_id) {
		$std_nc_data = $this->db_readonly->select("snc.penalty_status, snc.penalty_name, snc_category.color_code, snc.remarks, snc_category.name as category, date_format(snc.snc_date,'%d %M %Y') as snc_date")
			->from("snc_items snc")
			->join("snc_category", "snc.snc_category_id=snc_category.id","left")
			->where('std_admission_id',$student_id)
			->order_by('snc_date',"desc")
			->get()->result();

		return $std_nc_data;
	}

	public function get_multiple_installment_details($cohort_student_id){
		$std_cohort = $this->db_readonly->select('fc.id as cohort_id, fcs.student_id, fc.filter, fc.acad_year_id, fc.blueprint_id')
		->from('feev2_cohort_student fcs')
		->join('feev2_cohorts fc','fcs.feev2_cohort_id=fc.id')
		->where('fcs.id',$cohort_student_id)
		->where('fcs.fee_cohort_status','STANDARD')
		->where('fcs.fee_collect_status','COHORT_CONFIRM')
		->get()->row();
		
		if (!empty($std_cohort)) {
			$result =  $this->db_readonly->select('fc.*, fit.name as ins_type,  GROUP_CONCAT(fi.name) as ins_name')
			->from('feev2_cohorts fc')
			->join('feev2_installment_types fit','fc.default_ins=fit.id')
			->join('feev2_installments fi','fit.id=fi.feev2_installment_type_id')
			->where('filter',$std_cohort->filter)
			->where('acad_year_id',$std_cohort->acad_year_id)
			->where('blueprint_id',$std_cohort->blueprint_id)
			->group_by('fc.id')
			->get()->result();
			foreach ($result as $key => $val) {
				if ($val->id == $std_cohort->cohort_id) {
					$val->selected_cohort = 1;
				}else{
					$val->selected_cohort = 0;
				}
			}
			return $result;
		}else{
			return false;
		}
	}

	public function get_installment_details_by_cohort_id($cohort_id){
		return $this->db_readonly->select('fi.name as ins_name, sum(fcic.amount) as ins_amount')
		->from('feev2_cohort_installment_components fcic')
		->join('feev2_installments fi','fcic.feev2_installment_id=fi.id')
		->where('fcic.feev2_cohort_id',$cohort_id)
		->group_by('fcic.feev2_installment_id')
		->get()->result();
	}

	public function publish_fees_cohort_student_details($cohort_student_id){
	 	$cohort_data = array(
	        'publish_status' => 'PUBLISHED',
	        'online_payment' => 'PUBLISHED'
	    );
	 	$this->db->where('id',$cohort_student_id);
	    return $this->db->update('feev2_cohort_student', $cohort_data);
	}

	public function get_fee_total_acad_year($stdId){
		return $this->db_readonly->select("fb.acad_year_id")
			->from('feev2_cohort_student fcs')
			->join('feev2_blueprint fb', "fcs.blueprint_id=fb.id")
			->where('fcs.student_id',$stdId)
			->where('fcs.publish_status','PUBLISHED')
			->get()->result();
	}
	public function get_fee_detailed_transaction($stdId, $acadyearId){
		return $this->db_readonly->select("ifnull(fss.total_fee,0) as total_fee, (ifnull(fss.total_fee_paid,0) - ifnull(fss.discount,0)) as total_fee_paid, ifnull(fss.discount,0) as discount, sum(ifnull(fsi.total_concession_amount,0)) + sum(ifnull(fsi.total_concession_amount_paid,0)) as total_concession, total_card_charge_amount, sum(ifnull(fsi.refund_amount,0)) as refund_amount,  sum(ifnull(fsi.total_fine_waived,0)) as total_fine_waived, ifnull(loan_provider_charges,0) as loan_charges, sum(ifnull(fsi.total_adjustment_amount,0)) - sum(ifnull(fsi.total_adjustment_amount_paid,0)) as total_adjustment, (ifnull(fss.total_fine_amount,0) - ifnull(fss.total_fine_amount_paid,0) - ifnull(fss.total_fine_waived,0)) as total_fine, sum(ifnull(fsi.total_fine_amount,0)) as totalfineassigned, sum(ifnull(fsi.total_fine_amount_paid,0)) as total_fine_amount_paid,    
        	(ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) - sum(ifnull(fsi.total_concession_amount,0)) - sum(ifnull(fsi.total_concession_amount_paid,0)) ) as balance, fss.payment_status, fb.name as blueprint_name, fb.acad_year_id, ifnull(fss.loan_provider_charges,0) as loan_provider_charges")
			->from('feev2_cohort_student fcs')
			->join('feev2_blueprint fb', "fcs.blueprint_id=fb.id")
			->join('feev2_student_schedule fss', 'fss.feev2_cohort_student_id=fcs.id')
			->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
			->where('fcs.student_id',$stdId)
			->where('fb.acad_year_id',$acadyearId)
			->where('fcs.publish_status','PUBLISHED')
			->group_by('fsi.fee_student_schedule_id')
			->get()->result();
	}

	public function get_student_fee_summary_details_by_id($student_id, $acadyearId){
		

		$this->db_readonly->select("sum(fss.total_fee) as fee_amount, (ifnull(sum(fss.total_concession_amount),0)  + ifnull((sum(fss.total_concession_amount_paid)),0)) as concession, ifnull(sum(fss.discount),0)  as discount, ifnull(sum(fss.total_fine_amount),0) - ifnull(sum(fss.total_fine_amount_paid),0)  as total_fine_amount, ifnull(sum(fss.total_fine_waived),0) as total_fine_waived, sum(ifnull(fss.refund_amount,0)) as refund_amount, (ifnull(sum(fss.total_fee_paid),0) - ifnull(sum(fss.discount),0)) as paid_amount, ifnull(sum(fss.refund_amount),0) as refund_amount, ifnull(sum(fss.refund_amount),0) as refund_amount, (ifnull(sum(fss.total_adjustment_amount),0)  + ifnull((sum(fss.total_adjustment_amount_paid)),0)) as adjustment, ifnull(sum(fss.loan_provider_charges),0) as loan_provider_charges");
	    $this->db_readonly->from('feev2_cohort_student fcs');
	    $this->db_readonly->join('feev2_blueprint fb', 'fcs.blueprint_id=fb.id');
	    // if($this->current_branch) {
	    //   $this->db_readonly->where('fb.branches',$this->current_branch);
	    // }
	    if ($student_id) {
	      $this->db_readonly->where('fcs.student_id', $student_id);
	    }
	    $this->db_readonly->where('fb.acad_year_id', $acadyearId);
	    $this->db_readonly->where('fcs.publish_status','PUBLISHED');
	    $this->db_readonly->join('feev2_student_schedule fss', 'fcs.id=fss.feev2_cohort_student_id');
	    return $this->db_readonly->get()->row_array();
	    // return array('fee_paid' => $total->paid_amount, 'Total_fee' => $total->fee_amount, 'balance' => $total->balance, 'concession' => $total->concession,'refund_amount'=>$total->refund_amount);
	}

	public function update_profile_confirmedbyid($stdYearId){
		// $reqFields = $this->settings->getSetting('parent_profile_mandatory_fields');
		$reqFields= $this->get_requried_fields();
		$acadyearId = $this->acad_year->getAcadYearId();
		// echo "<pre>"; print_r($reqFields); die();
		if (!empty($reqFields)) {
			$result = $this->check_mandatory_field_before($stdYearId, $reqFields);
		}
	
		if (empty($reqFields) || empty($result)) {
			$this->db->where('id',$stdYearId);
			$this->db->where('acad_year_id',$acadyearId);
			return $this->db->update('student_year',array('profile_confirmed'=>'Yes','profile_confirmed_date'=>$this->Kolkata_datetime(),'profile_confirmed_by'=>$this->authorization->getAvatarStakeHolderId(),'profile_status'=>'Lock'));
		}else{
			return $result;
		}
	}

	public function get_requried_fields(){
		$this->db->select('value');
    	$this->db->where('name','profile_mandatory_columns');
    	$mandatoryFields=$this->db->get('config')->result();
		$dbEnabedMandatory = [];
		if (!empty($mandatoryFields)) {
			foreach ($mandatoryFields as $key => $enabled) {
				if (isset($enabled->value)) {
				$dbEnabedMandatory = json_decode($enabled->value);
				}
			}
		}
		return $dbEnabedMandatory;
	}

	public function check_mandatory_field_before($stdYearId, $reqFields){
		$query = $this->db->select('sa.id as stdId, sa.first_name as student_name, sa.dob as student_dob, sy.picture_url as student_photo, sa.gender as student_gender, sa.blood_group as student_blood_group,  sa.nationality as student_nationality, sa.caste as student_caste, sa.religion as student_religion, sa.aadhar_no as student_aadhar, sy.stop as student_stop, sy.pickup_mode as student_pickup_mode, p1.picture_url as father_photo, p2.picture_url as mother_photo, p1.first_name as father_name, p2.first_name as mother_name, p1.qualification as father_qualification, p2.qualification as mother_qualificiation, p1.occupation as father_occupation, p2.occupation as mother_occupation, p1.mobile_no as father_contact_no, p2.mobile_no as mother_contact_no, p1.email as father_email, p2.email as mother_email, p1.annual_income as father_annual_income, p2.annual_income as mother_annual_income, p1.company as father_company, p2.company as mother_company, p1.aadhar_no as father_aadhar, p2.aadhar_no as mother_aadhar, sy.student_house as student_house,sa.email as student_email,sa.mother_tongue as student_mother_tongue,sa.point_of_contact')
		->from('student_admission sa')
		->join('student_year sy','sa.id=sy.student_admission_id')
        ->join('student_relation sr1', "sr1.std_id=sa.id and sr1.relation_type='Father'")
        ->join('parent p1', 'p1.id=sr1.relation_id')
        ->join('student_relation sr2', "sr2.std_id=sa.id and sr2.relation_type='mother'")
        ->join('parent p2', 'p2.id=sr2.relation_id')
		->where('sy.id',$stdYearId)
		->get()->row();

		$this->db->select('sa.id as sId, g.picture_url as guardian_photo, g.first_name as guardian_name, g.mobile_no as guardian_contact_no, g.email as  guardian_email')
		->from('student_admission sa')
		->where('sa.admission_status','2')
		->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
		->where("sy.promotion_status!='JOINED'")
		->join('student_relation grm', 'sa.id=grm.std_id')
		->join('parent g', "g.id=grm.relation_id and grm.relation_type='Guardian'") 
		->where_in('sy.id',$stdYearId);
		$guardianList =  $this->db->get()->row();
		
		$query->guardian_photo = (!empty($guardianList)) ?  $guardianList->guardian_photo : '';
		$query->guardian_name = (!empty($guardianList)) ?  $guardianList->guardian_name : '';
		$query->guardian_contact_no = (!empty($guardianList)) ?  $guardianList->guardian_contact_no : '';
		$query->guardian_email = (!empty($guardianList)) ?  $guardianList->guardian_email : '';

		foreach ($reqFields as $key => $val) {
			if ($val == 'FATHER_ADDRESS') {
				$fatherAddress = $this->__getAddresses_field($query->stdId, 'Father');
			}
			if ($val == 'MOTHER_ADDRESS') {
				$motherAddress = $this->__getAddresses_field($query->stdId, 'Mother');
			}
			if ($val == 'STUDENT_ADDRESS') {
				$studentAddress = $this->__getAddresses_field($query->stdId, 'Student');
			}
		}
		
		$query->father_address = (!empty($fatherAddress)) ?  $fatherAddress->Father_address : '';
		$query->mother_address = (!empty($motherAddress)) ?  $motherAddress->Mother_address : '';
		$query->student_address = (!empty($studentAddress)) ?  $studentAddress->Student_address : '';
		
		$colData = (array)$query;
		$fields = [];
		foreach ($reqFields as $key => $value) {
			$required = strtolower($value);
			if (array_key_exists($required, $colData)) {
				if (empty($colData[$required])) {
					array_push($fields, $value);
				}
			}
		}
		return $fields;
	}

	public function __getAddresses_field($stdId, $addressOf){
		$this->db->select('')
	      ->from('student_admission sd')
	      ->join('student_year sy','sd.id=sy.student_admission_id')
	      ->where('sd.id',$stdId);
		switch ($addressOf) {
	    case 'Father':
	      $this->db->select('sd.id as sId, add.address_type as addType, concat(add.Address_Line1, "", add.Address_Line2,"", add.area,"", add.district,"", add.state,"", add.pin_code) as Father_address')
	      ->join('student_relation srf', 'sd.id=srf.std_id')
	      ->join('parent f', 'f.id=srf.relation_id')
	      ->where('srf.relation_type','Father')
	      ->join('address_info add', 'f.id=add.stakeholder_id')
	      ->where('add.avatar_type', '2')
	      ->where('add.address_type', '0');
	      break;
	    case 'Mother':
	      $this->db->select('sd.id as sId, add.address_type as addType, concat(add.Address_Line1,"", add.Address_Line2,"", add.area,"", add.district,"", add.state,"", add.pin_code) as Mother_address')
	      ->join('student_relation srf', 'sd.id=srf.std_id')
	      ->join('parent f', 'f.id=srf.relation_id')
	      ->where('srf.relation_type','Mother')
	      ->join('address_info add', 'f.id=add.stakeholder_id')
	      ->where('add.avatar_type', '2');
	      break;
	    case 'Student':
	      $this->db->select('sd.id as sId, add.address_type as addType, concat(add.Address_Line1,"", add.Address_Line2,"", add.area,"", add.district,"", add.state,"", add.pin_code) as Student_address')
	      ->join('address_info add', 'sd.id=add.stakeholder_id')
	      ->where('add.avatar_type', '1')
	      ->where('add.address_type', '0');
	      break;
	    }
	    return $this->db->get()->row();
	}

	public function get_sub_events_details($event_id){
		return $this->db->select("id, sub_event_name, max_registrations_allowed, sub_event_description, (case when sub_event_date = '1970-01-01' then '' else date_format(sub_event_date,'%d-%b-%Y') end) as  sub_event_date, (case when start_time_of_registration = '00:00:00' then '' else date_format(start_time_of_registration,'%h:%i %p') end) as start_time_of_registration, (case when end_time_of_registration = '00:00:00' then ' ' else date_format(end_time_of_registration,'%h:%i %p') end) as end_time_of_registration")
		->from('eventv2_sub_events')
		->where('event_id',$event_id)
		->get()->result();
	}
	
	public function get_dairy_text_by_date($student_id, $parentId, $date){
		$this->db_readonly->select("tm.id,date_format(sent_on,'%h-%i-%p') as time ,sent_on as smsDate, message, title, stakeholder_id as student_id, ts.mode, ts.is_read");
		$this->db_readonly->from('texting_master tm');
		$this->db_readonly->join('text_sent_to ts', 'ts.texting_master_id=tm.id');
		$this->db_readonly->where('date_format(tm.sent_on,"%d-%m-%Y")',$date);
		$this->db_readonly->where("((ts.stakeholder_id=$student_id and ts.avatar_type=1) OR (ts.stakeholder_id=$parentId and ts.avatar_type=2))");
		return $this->db_readonly->get()->result();
	}
	
	public function get_dairy_circular_by_date($student_id, $parentId, $date){
		$this->db_readonly->select("cm.id,date_format(sent_on,'%h:%i %p') as date ,title,body,category, cm.file_path, cm.mode");
		$this->db_readonly->from('circularv2_master cm');
		$this->db_readonly->where("cm.id in (select circularv2_master_id from circularv2_sent_to where stakeholder_id=$parentId and avatar_type=2)");
		$this->db_readonly->where('cm.visible', 1);
		$this->db_readonly->where('date_format(cm.sent_on,"%d-%m-%Y")',$date);
		return $this->db_readonly->get()->result();
	}
	public function get_dairy_calendar_by_date($student_id, $parentId, $date){
		$student_board =  $this->db_readonly->query("SELECT sy.board from student_year sy where acad_year_id=$this->yearId and sy.student_admission_id=$student_id")->row()->board;

			// $date = '2018-12';
			$this->db_readonly->select("event_name, event_type, from_date, DATE_FORMAT(from_date, '%b') as fMonth, DATE_FORMAT(to_date, '%b') as tMonth, DATE_FORMAT(from_date, '%d') as fDate, DATE_FORMAT(from_date, '%a') as fDay, DATE_FORMAT(to_date, '%d') as tDate, DATE_FORMAT(to_date, '%a') as tDay, board");
	
			$this->db_readonly->where("(DATE_FORMAT(from_date, '%m-%Y')='$date') AND (applicable_to='2' OR applicable_to=3)");
			if($student_board) {
				$this->db_readonly->where_in('board', ['100', $student_board]); //'100' means for 'All'
			}
			$result = $this->db_readonly->get('school_calender')->result();
			// echo $this->db_readonly->last_query();die();
			return $result;
	}

	public function get_dairy_student_task_by_date($student_id, $parentId, $date){
		
		$this->db_readonly->select("t.task_name, t.task_description, ts.read_status, date_format(t.created_on,'%h:%i %p') as time");
		$this->db_readonly->from('lp_tasks t');
		$this->db_readonly->join('lp_tasks_students ts', 't.id=ts.lp_tasks_id');
		$this->db_readonly->where('t.status', 'published');
		$this->db_readonly->where('ts.student_id', $student_id);
		$this->db_readonly->where('t.acad_year_id', $this->yearId);
		$this->db_readonly->where("date_format(t.created_on,'%d-%m-%Y')='$date'");
		$result = $this->db_readonly->get()->result();
		return $result;
	}


	public function get_student_class_id_by_acadyearwise($student_id,$acad_yearId){
		$result = $this->db_readonly->select('sy.class_id')
		->from('student_year sy')
		->where('student_admission_id',$student_id)
		->where('acad_year_id',$acad_yearId)
		->get()->row();
		if (!empty($result)) {
			return $result->class_id;
		}else{
			return 0;
		}
	}

	public function get_parents_aadhar_data($stu_id){
		$result = $this->db_readonly->select("p.id as pId,CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as parent_Name,sr.relation_type,p.aadhar_document_path,ifnull(p.aadhar_number,'') as aadhar_number,ifnull(p.aadhar_document_remarks,'') as aadhar_document_remarks,p.aadhar_document_status,aadhar_approved_status")
		->from('parent p')
		->join('student_relation sr','p.id=sr.relation_id')
		->where('p.student_id',$stu_id)
		->get()->result();
	
		foreach($result as $key => $val){
		  if (!empty($val->aadhar_document_path)) {
			$val->aadhar_document_path = $this->filemanager->getFilePath($val->aadhar_document_path);
		  }
		}
		return $result;
	}

	public function get_student_documents($student_id) {
		$document_list = $this->db->select("document_name, document_type, for_relation, is_mandatory, document_size_in_mb")
			->from('student_document_types')
			->where('visibile_for_parents', 1)
			->order_by('is_mandatory', 'DESC')
			->get()->result();
	
		$result = $this->db_readonly->select("
				sd.id as sdId, sd.document_type, 
				CONCAT(IFNULL(sa.first_name,''), ' ', IFNULL(sa.last_name,'')) as student_name,
				sd.document_url, IFNULL(sd.aadhar_number,'-') as aadhar_number,
				IFNULL(sd.pancard_number,'-') as pancard_number,
				IFNULL(sd.remarks,'-') as remarks,
				IFNULL(sd.document_status,'Pending') as document_status,
				sd.name_as_per_aadhar, sd.aadhar_number,
				DATE_FORMAT(sd.created_on,'%d-%m-%Y') as uploaded_on
			")
			->from('student_documents sd')
			->join('student_admission sa', 'sd.student_id = sa.id')
			->where('sd.student_id', $student_id)
			->get()->result();
	
		// Add signed URL
		foreach ($result as $val) {
			if (!empty($val->document_url)) {
				$val->document_url = $this->filemanager->getSignedUrlWithExpiry($val->document_url);
			}
		}
	
		$list = [];
		foreach ($document_list as $doc) {
			$list[$doc->document_name] = [
				'doc_type' => $doc->document_type,
				'relation_type' => $doc->for_relation,
				'is_mandatory' => $doc->is_mandatory,
				'document_size_in_mb' => $doc->document_size_in_mb,
				'data_present' => 0
			];
		}
	
		foreach ($result as $val) {
			foreach ($document_list as $doc) {
				if ($doc->document_name == $val->document_type) {
					$list[$doc->document_name] = array_merge((array)$val, [
						'doc_type' => $doc->document_type,
						'relation_type' => $doc->for_relation,
						'is_mandatory' => $doc->is_mandatory,
						'document_size_in_mb' => $doc->document_size_in_mb,
						'data_present' => 1
					]);
					break;
				}
			}
		}
		return $list;
	}

	public function get_panCard_documents($student_id){
		$result = $this->db_readonly->select("p.id as pId,CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as parent_Name,sr.relation_type,p.pan_card_document_path,ifnull(p.pan_card_number,'-') as pan_card_number,ifnull(p.pan_card_document_remarks,'-') as pan_card_document_remarks,p.pan_card_document_status,p.pan_card_approved_status")
		->from('parent p')
		->join('student_relation sr','p.id=sr.relation_id')
		->where('p.student_id',$student_id)
		->get()->result();
	
		foreach($result as $key => $val){
		  if (!empty($val->pan_card_document_path)) {
			$val->pan_card_document_path = $this->filemanager->getFilePath($val->pan_card_document_path);
		  }
		}
		// echo '<pre>';print_r($result);die();
		return $result;
	}

	public function submit_aadhar_details($input,$student_id,$path){
		// echo '<pre>';print_r($input);die();
		if($input['name_in_aadhar'] == '' && $input['aadhar_number'] == '' && $input['aadhar_remarks'] == ''){
			return 0;
		}
		
			$this->db->where('std_id',$student_id);
			$query = $this->db->get('student_relation')->result();
		
			$this->db->where('student_id',$student_id);
			$this->db->where('document_type','Aadhar Card');
			$student_query = $this->db->get('student_documents')->row();
		if($input['type'] == 'Student'){
			$this->db->trans_start();
			$student_data=array(
					'document_url'=>$path['file_name'],
					'student_id'=>$student_id,
					'document_type'=> 'Aadhar Card',
					'aadhar_number'=>$input['aadhar_number'],
					'aadhar_remarks'=>($input['aadhar_remarks'] == '') ? null : $input['aadhar_remarks'],
					'document_status'=>($input['aadhar_remarks'] == '') ? 'Pending' : 'Support document',
					'aadhar_approved_status'=>'Pending',
					'created_by' => $this->authorization->getAvatarStakeHolderId(),
					'created_on' => $this->Kolkata_datetime()
				);
				if(empty($student_query)){
					$this->db->insert('student_documents',$student_data);
				}else{
					$this->db->where('student_id',$student_id);
					$this->db->where('document_type','Aadhar Card');
					$this->db->update('student_documents',$student_data);
				}
				$this->db->where('id',$student_id);
				$this->db->update('student_admission', ['name_as_per_aadhar' => $input['name_in_aadhar']]);
				$this->db->trans_complete();
				return $this->db->trans_status();
		}else {
			$parent_data=array(
				'aadhar_document_remarks' => ($input['aadhar_remarks'] == '') ? null : $input['aadhar_remarks'],
				'aadhar_document_status' => ($input['aadhar_remarks'] == '') ? 'Pending' : 'Support document',
				'aadhar_approved_status'=> 'Pending',
				'aadhar_document_path'=> $path['file_name'],
				'name_as_per_aadhar'=> $input['name_in_aadhar'],
				'aadhar_number'=> $input['aadhar_number']
			  );
			  foreach ($query as $key => $val) {
				if($val->relation_type == $input['type']){
					$this->db->where('id',$val->relation_id);
					return $this->db->update('parent',$parent_data);	
				}
			}
		}
	}

	public function submit_pancard_details($input,$student_id,$path){
		if($input['pan_remarks'] == '' && $input['pancard_number'] == '' ){
			return 0;
		}
		$this->db->where('std_id',$student_id);
			$query = $this->db->get('student_relation')->result();
		
			$pan_data=array(
				'pan_card_document_remarks' => ($input['pan_remarks'] == '') ? null : $input['pan_remarks'],
				'pan_card_document_status' => ($input['pan_remarks'] == '') ? 'Pending' : 'Support document',
				'pan_card_approved_status'=> 'Pending',
				'pan_card_document_path'=> $path['file_name'],
				'pan_card_number'=> $input['pancard_number']
			  );
			  foreach ($query as $key => $val) {
				if($val->relation_type == $input['pan_type']){
					$this->db->where('id',$val->relation_id);
					return $this->db->update('parent',$pan_data);	
				}
			}
		
	}

	public function get_aadhar_details($input,$student_id){
		$this->db->where('std_id',$student_id);
		$query = $this->db->get('student_relation')->result();
		if($input['relation_type'] == 'Student'){
			$student_data = $this->db_readonly->select("sd.document_url,ifnull(sd.aadhar_number,'-') as aadhar_number,ifnull(sd.aadhar_remarks,'-') as aadhar_document_remarks,ifnull(sd.document_status,'-') as document_status,ifnull(sd.aadhar_approved_status,'-') as aadhar_approved_status,ifnull(sa.name_as_per_aadhar,'-') as name_as_per_aadhar")
			->from('student_documents sd')
			->join('student_admission sa','sd.student_id=sa.id')
			->where('sd.document_type','Aadhar Card')
			->where('sd.student_id',$student_id)
			->get()->row();

			if (!empty($student_data->document_url)) {
				$student_data->document_url = $this->filemanager->getFilePath($student_data->document_url);
			}
			
			return $student_data;
		} else {
			$parent_data = $this->db_readonly->select("p.aadhar_document_path as document_url,ifnull(p.aadhar_number,'-') as aadhar_number,ifnull(p.aadhar_document_remarks,'-') as aadhar_document_remarks,ifnull(p.aadhar_document_status,'') as document_status,ifnull(p.aadhar_approved_status,'-') as aadhar_approved_status,ifnull(p.name_as_per_aadhar,'-') as name_as_per_aadhar")
			->from('parent p')
			->join('student_relation sr','p.id=sr.relation_id')
			->where("sr.relation_type",$input['relation_type'])
			->where('p.student_id',$student_id)
			->get()->row();
		
			
			  if (!empty($parent_data->document_url)) {
				$parent_data->document_url = $this->filemanager->getFilePath($parent_data->document_url);
			  }
			
			return $parent_data;
		}
	}

	public function get_pan_details($input,$student_id){
		$parent_data = $this->db_readonly->select("p.pan_card_document_path,ifnull(p.pan_card_number,'-') as pan_card_number,ifnull(p.pan_card_document_remarks,'-') as pan_card_document_remarks,ifnull(p.pan_card_document_status,'') as pan_card_document_status,ifnull(p.pan_card_approved_status,'-') as pan_card_approved_status")
		->from('parent p')
		->join('student_relation sr','p.id=sr.relation_id')
		->where("sr.relation_type",$input['relation_type'])
		->where('p.student_id',$student_id)
		->get()->row();
		
		if (!empty($parent_data->pan_card_document_path)) {
			$parent_data->pan_card_document_path = $this->filemanager->getFilePath($parent_data->pan_card_document_path);
		}
		
		return $parent_data;
	}

	public function get_document_types(){
		return $this->db_readonly->select("document_name,for_relation,document_type, is_mandatory")
		->from('student_document_types')
		->where('visibile_for_parents',1)
		->get()->result();
	}

	public function store_document_details($input,$studentId,$document_path,$acknw_path,$declaration_path){
		$doc_type = $input['doc_name'];
		$sql = "SELECT * from student_documents where document_type = '$doc_type' AND  student_id = '$studentId'";
    	$exist = $this->db->query($sql)->row();
		$file_path = '';
        $attached_document_type = '';
		if(isset($input['has_document'])){
			if($input['has_document'] == 1){
				$file_path = $document_path['file_name'];
				$attached_document_type = 'actual';
			}else if($input['applied_form_document'] == 1){
				$file_path = $acknw_path['file_name'];
				$attached_document_type = 'applied';
			}else{
				$file_path = $declaration_path['file_name'];
				$attached_document_type = 'declaration';
			}
		}else{
			$file_path = $document_path['file_name'];
			$attached_document_type = null;
		}
		if(isset($input['aadhar_number']) && strtolower($input['doc_name']) == 'student aadhar card'){
			$this->db->where('id',$studentId);
			$this->db->update('student_admission',array('aadhar_no'=>$input['aadhar_number'],'name_as_per_aadhar' => $input['name_as_per__aadhar']));

			$previous_data = $this->db->select('aadhar_no, name_as_per_aadhar')
                          ->from('student_admission')
                          ->where('id', $studentId)
                          ->get()
                          ->row();

			$edit_history = array(
				'student_id'=> $studentId,
				'old_data' => 'Student Aadhar Number - '.$previous_data->aadhar_no.' , '.'student Name As Per Aadhar - '.$previous_data->name_as_per_aadhar,
				'new_data' => 'Student Aadhar Number - '.$input['aadhar_number'].' , '.'Student Name As Per Aadhar - '.$input['name_as_per__aadhar'],
				'edited_by' => $this->authorization->getAvatarStakeHolderId(),
				'edited_on' => $this->Kolkata_datetime(),
				'source' =>'Parent'
			);

			$this->db->insert('student_edit_history',$edit_history);
		}

		if(isset($input['aadhar_number']) && strtolower($input['doc_name']) == 'father aadhar card'){
			$father_id = $this->db->select('relation_id')->from('student_relation')->where('std_id',$studentId)->where('relation_type','Father')->get()->row()->relation_id;
			$this->db->where('id',$father_id);
			$this->db->update('parent',array('aadhar_no'=>$input['aadhar_number'],'name_as_per_aadhar' => $input['name_as_per__aadhar']));

			$previous_data = $this->db->select('aadhar_no,name_as_per_aadhar')->from('parent')->where('id',$father_id);

			$edit_history = array(
				'student_id'=> $studentId,
				'old_data' => 'Father Aadhar Number - '.$previous_data->aadhar_no.' , '.'Father Name As Per Aadhar - '.$previous_data->name_as_per_aadhar,
				'new_data' => 'Father Aadhar Number - '.$input['aadhar_number'].' , '.'Father Name As Per Aadhar - '.$input['name_as_per__aadhar'],
				'edited_by' => $this->authorization->getAvatarStakeHolderId(),
				'edited_on' => $this->Kolkata_datetime(),
				'source' =>'Parent'
			);

			$this->db->insert('student_edit_history',$edit_history);
		}
		if(isset($input['aadhar_number']) && strtolower($input['doc_name']) == 'mother aadhar card'){
			$mother_id = $this->db->select('relation_id')->from('student_relation')->where('std_id',$studentId)->where('relation_type','Mother')->get()->row()->relation_id;

			$this->db->where('id',$mother_id);
			$this->db->update('parent',array('aadhar_no'=> $input['aadhar_number'],'name_as_per_aadhar' => $input['name_as_per__aadhar']));

			$previous_data = $this->db->select('aadhar_no,name_as_per_aadhar')->from('parent')->where('id',$mother_id);

			$edit_history = array(
				'student_id'=> $studentId,
				'old_data' => 'Mother Aadhar Number - '.$previous_data->aadhar_no.' , '.'Mother Name As Per Aadhar - '.$previous_data->name_as_per_aadhar,
				'new_data' => 'Mother Aadhar Number - '.$input['aadhar_number'].' , '.'Mother Name As Per Aadhar - '.$input['name_as_per__aadhar'],
				'edited_by' => $this->authorization->getAvatarStakeHolderId(),
				'edited_on' => $this->Kolkata_datetime(),
				'source' =>'Parent'
			);

			$this->db->insert('student_edit_history',$edit_history);

		}
        if(!empty($exist)){
			$data = array(
				'document_type'=>$input['doc_name'],
				'document_url'=>$file_path,
				'name_as_per_aadhar'=>(isset($input['name_as_per__aadhar'])) ? $input['name_as_per__aadhar'] : null,
				'aadhar_number'=>(isset($input['aadhar_number'])) ? $input['aadhar_number'] : null,
				'pancard_number'=>(isset($input['pan_card_number'])) ? $input['pan_card_number'] : null,
				'attached_document_type'=> $attached_document_type,
				'relation_type' => $input['relation'],
				'document_status'=>'Pending',
				'created_by' => $this->authorization->getAvatarId(),
				'created_on' => $this->Kolkata_datetime()
			);
			$this->db->where('id',$exist->id);
			return $this->db->update('student_documents',$data);
		}else{
			$data = array(
				'student_id' => $studentId,
				'document_type'=>$input['doc_name'],
				'document_url'=>$file_path,
				'name_as_per_aadhar'=>(isset($input['name_as_per__aadhar'])) ? $input['name_as_per__aadhar'] : null,
				'aadhar_number'=>(isset($input['aadhar_number'])) ? $input['aadhar_number'] : null,
				'pancard_number'=>(isset($input['pan_card_number'])) ? $input['pan_card_number'] : null,
				'attached_document_type'=> $attached_document_type,
				'relation_type' => $input['relation'],
				'document_status'=>'Pending',
				'created_by' => $this->authorization->getAvatarId(),
				'created_on' => $this->Kolkata_datetime()
			);
			// echo '<pre>';print_r($data);die();
			return $this->db->insert('student_documents',$data);
		}
	}
	
	public function get_student_data_tab_wise($student_id){

        $studentdata= $this->db_readonly->select("CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,c.class_name, cs.section_name, sa.*,ifnull(sa.family_picture_url,'') as family_picture_url,ifnull(sa.admission_year,'') as admission_year,ifnull(sa.attempt,'') as attempt,ifnull(sa.caste_income_certificate_number,'') as caste_income_certificate_number,ifnull(sa.class_admitted_to,,'') as class_admitted_to, sa.admission_no, ifnull(sa.enrollment_number,'') as enrollment_number, sy.roll_no,ifnull(sa.preferred_contact_no,'') as preferred_contact_no,ifnull(sa.caste,'') as caste,sy.picture_url,sa.has_staff as staff_kid,ifnull(date_format(date_of_joining,'%d-%m-%Y'),'') as date_of_joining,
                                                ifnull(sa.vaccination_name, '') as vaccination_name,
                                                ifnull(sa.vaccination_status, '') as vaccination_status,
                                                ifnull(sa.vaccination_last_submitted_date, '') as vaccination_last_submitted_date,
                                                ifnull(sa.vaccination_verification_status, '') as vaccination_verification_status,
                                                ifnull(sa.passport_issued_place, '') as passport_issued_place,
                                                ifnull(sa.passport_number, '') as passport_number,
                                                ifnull(sa.passport_validity, '') as passport_validity,
                                                ifnull(sa.point_of_contact, '') as point_of_contact,
                                                ifnull(sa.student_living_with, '') as student_living_with,
                                                ifnull(sa.vaccination_supporting_document, '') as vaccination_supporting_document,
                                                ifnull(sa.ration_card_number, '') as ration_card_number,
                                                ifnull(sa.ration_card_type, '') as ration_card_type,
                                                ifnull(sa.student_sub_caste, '') as student_sub_caste,
                                                ifnull(sa.student_mobile_no, '') as student_mobile_no,
                                                ifnull(sa.medium_of_instruction, '') as medium_of_instruction,
                                                ifnull(sa.student_remarks, '') as student_remarks,
                                                ifnull(sa.nick_name, '') as nick_name,
                                                ifnull(sa.student_indian_visa_number, '') as student_indian_visa_number,
                                                ifnull(sa.student_indian_visa_expiry_date, '') as student_indian_visa_expiry_date,
                                                ifnull(sa.identification_mark1, '') as identification_mark1,
                                                ifnull(sa.identification_mark2, '') as identification_mark2,
                                                ifnull(sa.sibling1_name, '') as sibling1_name,
                                                ifnull(sa.sibling1_occupation, '') as sibling1_occupation,
                                                ifnull(sa.sibling1_mobile_num, '') as sibling1_mobile_num,

                                                ifnull(sa.sibling2_name, '') as sibling2_name,

                                                ifnull(sa.sibling2_occupation, '') as sibling2_occupation,

                                                ifnull(sa.sibling2_mobile_num, '') as sibling2_mobile_num,

                                                ifnull(sa.sibling3_name, '') as sibling3_name,

                                                ifnull(sa.sibling3_occupation, '') as sibling3_occupation,

                                                ifnull(sa.sibling3_mobile_num, '') as sibling3_mobile_num,

                                                ifnull(sa.student_whatsapp_num, '') as student_whatsapp_num,

                                                ifnull(sa.is_single_child, '') as is_single_child,

                                                ifnull(sa.is_minority, '') as is_minority,

                                                ifnull(sa.current_nearest_location, '') as current_nearest_location,

                                                ifnull(sa.parents_marriage_anniversary, '') as parents_marriage_anniversary,

                                                 ifnull(sa.aadhar_no,'') as aadhar_no,ifnull(sa.birth_district,'') as birth_district,ifnull(sa.mother_tongue,'') as mother_tongue,ifnull(sa.birth_taluk,'') as birth_taluk,ifnull(sa.blood_group,'') as blood_group,ifnull(sa.language_spoken,'') as language_spoken,ifnull(sa.email_password,'') as email_password,ifnull(date_format(dob,'%d-%b-%Y'),'') as dob,
                                                 ifnull(sa.email,'') as email,
                                                 ifnull(sa.emergency_info,'') as emergency_info,
                                                 ifnull(sa.extracurricular_activities,'') as extracurricular_activities, ifnull(sa.vaccination_name, ''), 
                                                 CASE WHEN category = 0 THEN ' '
                                                        ELSE category
                                                   END AS category, 
                                               CASE WHEN religion = 0 THEN ' '
                                                        ELSE religion
                                                   END AS religion,
                                               CASE WHEN caste = 0 THEN ' '
                                                        ELSE caste
                                                   END AS caste,
                                               CASE WHEN life_time_fee_mode = 0 THEN ' '
                                                        ELSE life_time_fee_mode
                                                   END AS life_time_fee_mode,
                                               CASE WHEN joined_helium = 0 THEN ' '
                                                        ELSE joined_helium
                                                   END AS joined_helium,
                                               CASE WHEN blood_group = 0 THEN ' '
                                                    ELSE blood_group
                                                   END AS blood_group,

                                                CASE WHEN gender = 'M' THEN 'Male'
                                                WHEN gender = 'F' THEN 'Female'
                                                    ELSE ''
                                                   END AS gender

                                                 ")
                           ->from('student_admission sa')
                           ->join('student_year sy', ' sa.id=sy.student_admission_id') 
                           ->join('class c', 'sy.class_id=c.id')
                           ->join('class_section cs', 'sy.class_section_id=cs.id','left')
                           ->where('sy.acad_year_id',$this->yearId)
                           ->where('sa.id',$student_id)
                           ->get()->row();
		if(!empty($studentdata->picture_url)){
			$studentdata->picture_url = $this->filemanager->getFilePath($studentdata->picture_url);
		}

        $fatherdata = $this->db_readonly->select("f.*,ifnull(f.picture_url,'') as picture_url,ifnull(company,'') as company,ifnull(annual_income,'') as annual_income,ifnull(qualification,'') as qualification ,ifnull(mobile_no,'') as mobile_no,ifnull(occupation,'') as occupation,ifnull(aadhar_no,'') as aadhar_no,ifnull(language_spoken,'') as language_spoken,ifnull(mother_tongue,'') as mother_tongue,ifnull(designation,'') as designation,ifnull(f.blood_group,'') as blood_group,ifnull(date_format(f.dob,'%d-%b-%Y'),'') as dob,ifnull(f.aadhar_number,'') as aadhar_number,ifnull(f.identification_code,'') as identification_code,ifnull(f.vaccination_name,'') as vaccination_name,ifnull(f.vaccination_status,'') as vaccination_status,ifnull(f.vaccination_last_submitted_date,'') as vaccination_last_submitted_date,ifnull(f.vaccination_verification_status,'') as vaccination_verification_status,ifnull(f.vaccination_supporting_document,'') as vaccination_supporting_document,ifnull(f.pan_number,'') as pan_number,ifnull(f.home_city,'') as home_city,ifnull(f.employee_id,'') as employee_id,ifnull(f.office_landline_number,'') as office_landline_number,ifnull(f.alternate_email_id,'') as alternate_email_id,ifnull(f.whatsapp_num,'') as whatsapp_num,ifnull(f.bank_account_num,'') as bank_account_num,ifnull(f.name_as_per_aadhar,'') as name_as_per_aadhar")
        ->from('parent f')
        ->join('student_relation sr',"f.id=sr.relation_id and sr.relation_type = 'Father'")
        ->where('sr.std_id',$student_id)
        ->get()->row();
		if(!empty($fatherdata->picture_url)){
			$fatherdata->picture_url = $this->filemanager->getFilePath($fatherdata->picture_url);
		}

        $motherdata = $this->db_readonly->select("m.*,ifnull(email,'') as email,ifnull(company,'') as company,ifnull(annual_income,'') as annual_income,ifnull(qualification,'') as qualification,ifnull(mobile_no,'') as mobile_no,ifnull(occupation,'') as occupation,ifnull(aadhar_no,'') as aadhar_no,ifnull(language_spoken,'') as language_spoken,ifnull(mother_tongue,'') as mother_tongue,ifnull(designation,'') as designation, ifnull(m.picture_url,'') as picture_url,ifnull(m.blood_group,'') as blood_group,
            ifnull(date_format(m.dob,'%d-%b-%Y'),'') as dob, ifnull(m.aadhar_number,'') as aadhar_number,ifnull(m.identification_code,'') as identification_code,ifnull(m.vaccination_name,'') as vaccination_name,ifnull(m.vaccination_status,'') as vaccination_status,ifnull(m.vaccination_last_submitted_date,'') as vaccination_last_submitted_date,ifnull(m.vaccination_verification_status,'') as vaccination_verification_status,ifnull(m.vaccination_supporting_document,'') as vaccination_supporting_document,ifnull(m.pan_number,'') as pan_number,ifnull(m.home_city,'') as home_city,ifnull(m.employee_id,'') as employee_id,ifnull(m.office_landline_number,'') as office_landline_number,ifnull(m.alternate_email_id,'') as alternate_email_id,ifnull(m.whatsapp_num,'') as whatsapp_num,ifnull(m.bank_account_num,'') as bank_account_num,ifnull(m.name_as_per_aadhar,'') as name_as_per_aadhar")
        ->from('parent m')
        ->join('student_relation sr',"m.id=sr.relation_id and sr.relation_type = 'Mother'")
        ->where('sr.std_id',$student_id)
        ->get()->row();
		if(!empty($motherdata->picture_url)){
			$motherdata->picture_url = $this->filemanager->getFilePath($motherdata->picture_url);
		}
        $guardiandata = $this->db_readonly->select("m.*,ifnull(email,'') as email,ifnull(company,'') as company,ifnull(annual_income,'') as annual_income,ifnull(qualification,'') as qualification,ifnull(mobile_no,'') as mobile_no,ifnull(occupation,'') as occupation,ifnull(aadhar_no,'') as aadhar_no,ifnull(language_spoken,'') as language_spoken,ifnull(mother_tongue,'') as mother_tongue,ifnull(designation,'') as designation, ifnull(m.picture_url,'') as picture_url,ifnull(m.blood_group,'') as blood_group,ifnull(date_format(m.dob,'%d-%b-%Y'),'') as dob, ifnull(m.aadhar_number,'') as aadhar_number,ifnull(m.identification_code,'') as identification_code,ifnull(m.vaccination_name,'') as vaccination_name,ifnull(m.vaccination_status,'') as vaccination_status,ifnull(m.vaccination_last_submitted_date,'') as vaccination_last_submitted_date,ifnull(m.vaccination_verification_status,'') as vaccination_verification_status,ifnull(m.vaccination_supporting_document,'') as vaccination_supporting_document,ifnull(m.pan_number,'') as pan_number,ifnull(m.home_city,'') as home_city,ifnull(m.employee_id,'') as employee_id,ifnull(m.office_landline_number,'') as office_landline_number,ifnull(m.alternate_email_id,'') as alternate_email_id,ifnull(m.whatsapp_num,'') as whatsapp_num,ifnull(m.bank_account_num,'') as bank_account_num,ifnull(m.name_as_per_aadhar,'') as name_as_per_aadhar")
        ->from('parent m')
        ->join('student_relation sr',"m.id=sr.relation_id and sr.relation_type = 'Guardian'")
        ->where('sr.std_id',$student_id)
        ->get()->row();
		if(!empty($guardiandata->picture_url)){
			$guardiandata->picture_url = $this->filemanager->getFilePath($guardiandata->picture_url);
		}

        $guardian2data = $this->db_readonly->select("m.*, ifnull(email,'') as email,ifnull(company,'') as company,ifnull(annual_income,'') as annual_income,ifnull(qualification,'') as qualification,ifnull(mobile_no,'') as mobile_no,ifnull(occupation,'') as occupation,ifnull(aadhar_no,'') as aadhar_no,ifnull(language_spoken,'') as language_spoken,ifnull(mother_tongue,'') as mother_tongue,ifnull(designation,'') as designation, ifnull(m.picture_url,'') as picture_url,ifnull(m.blood_group,'') as blood_group,
            ifnull(date_format(m.dob,'%d-%b-%Y'),'') as dob, ifnull(m.aadhar_number,'') as aadhar_number,ifnull(m.identification_code,'') as identification_code,ifnull(m.vaccination_name,'') as vaccination_name,ifnull(m.vaccination_status,'') as vaccination_status,ifnull(m.vaccination_last_submitted_date,'') as vaccination_last_submitted_date,ifnull(m.vaccination_verification_status,'') as vaccination_verification_status,ifnull(m.vaccination_supporting_document,'') as vaccination_supporting_document,ifnull(m.pan_number,'') as pan_number,ifnull(m.home_city,'') as home_city,ifnull(m.employee_id,'') as employee_id,ifnull(m.office_landline_number,'') as office_landline_number,ifnull(m.alternate_email_id,'') as alternate_email_id,ifnull(m.whatsapp_num,'') as whatsapp_num,ifnull(m.bank_account_num,'') as bank_account_num,ifnull(m.name_as_per_aadhar,'') as name_as_per_aadhar")
        ->from('parent m')
        ->join('student_relation sr',"m.id=sr.relation_id and sr.relation_type = 'Guardian_2'")
        ->where('sr.std_id',$student_id)
        ->get()->row();
		if(!empty($guardian2data->picture_url)){
			$guardian2data->picture_url = $this->filemanager->getFilePath($guardian2data->picture_url);
		}
        $dariverdata = $this->db_readonly->select("m.*,ifnull(email,'') as email,ifnull(company,'') as company,ifnull(annual_income,'') as annual_income,ifnull(qualification,'') as qualification,ifnull(mobile_no,'') as mobile_no,ifnull(occupation,'') as occupation,ifnull(aadhar_no,'') as aadhar_no,ifnull(language_spoken,'') as language_spoken,ifnull(mother_tongue,'') as mother_tongue,ifnull(designation,'') as designation,ifnull(m.blood_group,'') as blood_group,
            ifnull(date_format(m.dob,'%d-%b-%Y'),'') as dob, ifnull(m.aadhar_number,'') as aadhar_number,ifnull(m.identification_code,'') as identification_code,ifnull(m.vaccination_name,'') as vaccination_name,ifnull(m.vaccination_status,'') as vaccination_status,ifnull(m.vaccination_last_submitted_date,'') as vaccination_last_submitted_date,ifnull(m.vaccination_verification_status,'') as vaccination_verification_status,ifnull(m.vaccination_supporting_document,'') as vaccination_supporting_document,ifnull(m.pan_number,'') as pan_number,ifnull(m.home_city,'') as home_city,ifnull(m.employee_id,'') as employee_id,ifnull(m.office_landline_number,'') as office_landline_number,ifnull(m.alternate_email_id,'') as alternate_email_id,ifnull(m.whatsapp_num,'') as whatsapp_num,ifnull(m.bank_account_num,'') as bank_account_num,ifnull(m.name_as_per_aadhar,'') as name_as_per_aadhar")
        ->from('parent m')
        ->join('student_relation sr',"m.id=sr.relation_id and sr.relation_type = 'Driver'")
        ->where('sr.std_id',$student_id)
        ->get()->row();
		if(!empty($dariverdata->picture_url)){
			$dariverdata->picture_url = $this->filemanager->getFilePath($dariverdata->picture_url);
		}

        $dariver2data = $this->db_readonly->select("m.*,ifnull(email,'') as email,ifnull(company,'') as company,ifnull(annual_income,'') as annual_income,ifnull(qualification,'') as qualification,ifnull(mobile_no,'') as mobile_no,ifnull(occupation,'') as occupation,ifnull(aadhar_no,'') as aadhar_no,ifnull(language_spoken,'') as language_spoken,ifnull(mother_tongue,'') as mother_tongue,ifnull(designation,'') as designation,ifnull(m.blood_group,'') as blood_group,
            ifnull(date_format(m.dob,'%d-%b-%Y'),'') as dob, ifnull(m.aadhar_number,'') as aadhar_number,ifnull(m.identification_code,'') as identification_code,ifnull(m.vaccination_name,'') as vaccination_name,ifnull(m.vaccination_status,'') as vaccination_status,ifnull(m.vaccination_last_submitted_date,'') as vaccination_last_submitted_date,ifnull(m.vaccination_verification_status,'') as vaccination_verification_status,ifnull(m.vaccination_supporting_document,'') as vaccination_supporting_document,ifnull(m.pan_number,'') as pan_number,ifnull(m.home_city,'') as home_city,ifnull(m.employee_id,'') as employee_id,ifnull(m.office_landline_number,'') as office_landline_number,ifnull(m.alternate_email_id,'') as alternate_email_id,ifnull(m.whatsapp_num,'') as whatsapp_num,ifnull(m.bank_account_num,'') as bank_account_num,ifnull(m.name_as_per_aadhar,'') as name_as_per_aadhar")
        ->from('parent m')
        ->join('student_relation sr',"m.id=sr.relation_id and sr.relation_type = 'Driver_2'")
        ->where('sr.std_id',$student_id)
        ->get()->row();
		if(!empty($dariver2data->picture_url)){
			$dariver2data->picture_url = $this->filemanager->getFilePath($dariver2data->picture_url);
		}


        $siblingCount = "select a.user_id,sa.id student_id from avatar a
        join parent p on a.stakeholder_id = p.id
        join student_admission sa on p.student_id = sa.id
        where  avatar_type = 2 and sa.id = $student_id;";
        $sibCountResult = $this->db->query($siblingCount)->result();

        $userIds =[];
        foreach ($sibCountResult as $key => $value) {
            array_push($userIds, $value->user_id);
        }
        $user_ids = implode(',', $userIds);
        $siblings = "select  sa.id as student_id, p.student_id from avatar a
        join parent p on p.id = a.stakeholder_id
        join student_admission sa on p.student_id = sa.id
        where  avatar_type = 2 and a.user_id in ($user_ids)
        group by p.student_id";

        $siblingsid = $this->db->query($siblings)->result();
        $student_ids =[];
        foreach ($siblingsid as $key => $value) {
            array_push($student_ids, $value->student_id);
        }
        $siblings_name = [];
        if (count($student_ids) > 1) {
            $siblings_name = $this->db_readonly->select("sa.id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,c.class_name, cs.id as class_section_id, cs.section_name, c.id as class_id")
           ->from('student_admission sa')
           ->join('student_year sy', ' sa.id=sy.student_admission_id') 
           ->join('class c', 'sy.class_id=c.id')
           ->join('class_section cs', 'sy.class_section_id=cs.id','left')
           ->where_in('sa.id',$student_ids)
           ->where('sy.acad_year_id',$this->yearId)
           ->get()->result();
        }
        
        $studentdata->father = $fatherdata;
        $studentdata->mother = $motherdata;
        $studentdata->guardians = $guardiandata;
        $studentdata->guardians2 = $guardian2data;
        $studentdata->driver = $dariverdata;
        $studentdata->driver2 = $dariver2data;
        $studentdata->siblings_name = $siblings_name;

        return $studentdata;
    }

	public function get_student_school_details($std_id){
		$result = $this->db_readonly->select("*, ifnull(date_format(sa.date_of_joining,'%d-%b-%Y'),'') as date_of_joining")
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            ->join('class c','sy.class_id =c.id')
            ->join('class_section cs', 'cs.id=sy.class_section_id')
            ->where('sa.id', $std_id)
            ->get()->row();
        $admission_type =$this->settings->getSetting('admission_type');
        
        if (!empty($admission_type)) {
            if (!empty($result->admission_type)) {
                $result->admission_type = $admission_type[$result->admission_type];
            } else {
                $result->admission_type = '-';
            }
        }
        $boarding = $this->settings->getSetting('boarding');
        if (!empty($boarding)) {
            if (!empty($result->boarding)) {
                $result->boarding = $boarding[$result->boarding];
            } else {
                $result->boarding = '-';
            }
        }

        $isRte = $this->settings->getSetting('rte');
        if (!empty($isRte)) {
            if (!empty($result->is_rte)) {
                $result->is_rte = $isRte[$result->is_rte];
            } else {
                $result->is_rte = '-';
            }
        }

        return $result;
	}

	public function get_studentDocuments($studentId){
		$document = $this->db_readonly->select("st.*,date_format(st.created_on,'%d-%M-%Y') as created_on")
		->from('student_documents st')
		->where('student_id', $studentId)
		->get()->result();
	
		foreach ($document as $key => &$val) {
			$val->document_view_url = $this->filemanager->getFilePath($val->document_url);
		}

		return $document;
	}

	public function get_prev_schooling_details($student_id) {
        $result = $this->db_readonly->select("id,year_id, school_name, class, board, school_address, medium_of_instruction,report_card,university_name,period,subjects,registration_no,total_marks,total_marks_scored")
            ->where('student_id', $student_id)
            ->from('student_prev_school')
            ->get()->result();

        foreach ($result as $key => &$val) {
            if (!empty($val->report_card)){
                $val->report_card_view_url = $this->filemanager->getFilePath($val->report_card);
            }else{
                $val->report_card_view_url = '';
            }
        }
        return $result;
    }

    public function get_subject_wise_attendance($student_id,$from_date,$to_date) {
        $sql =" SELECT distinct(sub.subject_name),sum(case when s.status =1 then 1 else 0 end ) as present_days,sum(case when s.status =2 then 1 else 0 end ) as absent,sum(case when s.status =3 then 1 else 0 end ) as late
                FROM attendance_v2_master m 
                JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
                JOIN subject_master sub ON m.type_id=sub.id
                where date between '$from_date' and '$to_date'
                AND s.student_admission_id='$student_id' 
                Group By sub.subject_name
                ORDER BY m.taken_on Asc, m.period_no Asc";
        $result1=$this->db->query($sql)->result();
        return $result1;
    }

    public function get_student_circular($studentId)
    {
        $this->db_readonly->select("date_format(cm.sent_on,'%d-%m-%Y') as sent_on,cm.title,cm.body,cm.category,cm.id");
        $this->db_readonly->from("circularv2_master cm");
        $this->db_readonly->join('circularv2_sent_to ct', 'cm.id=ct.circularv2_master_id ');
        $this->db_readonly->where('ct.stakeholder_id', $studentId);
        
        $this->db_readonly->limit('50');
        return $this->db_readonly->get()->result();
    }

     public function get_transposrtation_detailset($student_id)
    {
        $result = $this->db->select("txs.id as stdJourneyId, txj.id as journeyId, txj.journey_name, txj.journey_type, txst.id as stopId, txst.stop_name, txs.day, TIME_FORMAT(txj.tentative_start_time, '%h:%i %p') as startTime, TIME_FORMAT(txj.tentative_end_time, '%h:%i %p') as endTime,th.thing_name,th.thing_reg_number,concat(ttd.first_name,' ',ttd.last_name) as driver_name,ttd.phone_number")
        ->from('tx_student_journeys txs')
        ->join('tx_journeys txj', 'txj.id=txs.journey_id')
        ->join('tx_stops txst', 'txst.id=txs.stop_id')
        ->join('tx_things th', 'txj.thing_id=th.id')
        ->join('tx_drivers ttd', 'th.driver_id=ttd.id')
        ->where('txs.entity_source_id', $student_id)
        ->get()->result();
        return $result;
    }

    public function get_health_disabled_fields(){
    $result = $this->db_readonly->select('*')
      ->from('config')
      ->where('name','health_show_disabled_fields')
      ->get()->row();
      if (!empty($result)) {
         return json_decode($result->value);
      }else{
          return array();
      }
  }

   public function whole_medical_history_360($student_id) {
        $acad_year_id = $this->acad_year->getAcadYearId();
        $result =$this->db_readonly->select('blood_group, physical_disability, learning_disability, physical_disability_reason, learning_disability_reason, allergy, family_history, anaemia, fit_to_participate, height, weight, hair, skin, ear, nose, throat, neck, respiratory, cardio_vascular, abdomen, nervous_system, left_eye, right_eye, extra_oral, bad_breath, tooth_cavity, plaque, gum_inflamation, stains, gum_bleeding, soft_tissue, covid, vitaminb12, vitamind, iron, calcium, vaccines, head_injury, ear_impartement, difficulty_in_breathing, child_has_join_pain, child_past_hsitory_fracture, fracture, foodytpe, child_nervous_breakdown, child_color_blindness, child_diabities, congenital_heart_disease, more_than_month_disease, medicine_name_for_month, any_other_medical_treatment, bld_group, father_bld_group, mother_bld_group, intra_oral, child_past_hsitory_sea')
        ->from('student_health')
        ->where('student_id',$student_id)
        ->order_by('id','desc')
        ->group_by('student_id')
        ->get()->row_array();
        $tempArry =[];
        foreach ($result as $key => $value) {
           $tempArry[$key] = $value;
        }
        return $tempArry;
    }

	public function get_todays_requests($student_id) {
		$siblingCount = "select a.user_id,sa.id student_id from avatar a
      join parent p on a.stakeholder_id = p.id
      join student_admission sa on p.student_id = sa.id
      where  avatar_type = 2 and sa.id = $student_id;";
      $sibCountResult = $this->db->query($siblingCount)->result();
      
      $userIds =[];
      foreach ($sibCountResult as $key => $value) {
          array_push($userIds, $value->user_id);
      }
      $user_ids = implode(',', $userIds);

      $siblings = "select  sa.id as student_id, p.student_id from avatar a
      join parent p on p.id = a.stakeholder_id
      join student_admission sa on p.student_id = sa.id
      where  avatar_type = 2 and a.user_id in ($user_ids)
      group by p.student_id";
      $siblingsid = $this->db->query($siblings)->result();

      $student_ids =[];
      foreach ($siblingsid as $key => $value) {
          array_push($student_ids, $value->student_id);
      }
	

		$unknown= $this->db->select("sa.id as student_id, sa.first_name as student_name, epupa.auth_status, epupa.id as unknown_id, epupa.person_name, epupa.person_phone_number, epupa.photo_url, concat(sm.first_name, ' ', ifnull(sm.last_name, '')) as created_by, 'escort_parent_unknown_person_auth' as table_name")
				->from('escort_parent_unknown_person_auth epupa')
				->join('student_admission sa', "sa.id= epupa.student_admission_id")
				->join('staff_master sm', "sm.id= epupa.created_by_id", 'left')
				->where_in('epupa.student_admission_id', $student_ids)
				->where('epupa.created_by_type', 'staff_master')
				->where("date_format(epupa.auth_from_date, '%Y-%m-%d') >=", date('Y-m-d'))
				->where("date_format(epupa.auth_to_date, '%Y-%m-%d') <=", date('Y-m-d'))
				->get()->result();

		$taxi= $this->db->select("sa.id as student_id, sa.first_name as student_name, epupa.auth_status, epupa.id as unknown_id, epupa.driver_name as person_name, epupa.driver_phone_number as person_phone_number, epupa.driver_photo_url as photo_url, concat(sm.first_name, ' ', ifnull(sm.last_name, '')) as created_by, 'escort_by_taxi' as table_name")
			->from('escort_by_taxi epupa')
			->join('student_admission sa', "sa.id= epupa.student_admission_id")
			->join('staff_master sm', "sm.id= epupa.created_by_id", 'left')
			->where_in('epupa.student_admission_id', $student_ids)
			->where("date_format(epupa.auth_from_to_date, '%Y-%m-%d') =", date('Y-m-d'))
			->get()->result();

		$todays_requests= array_merge($unknown, $taxi);

		foreach($todays_requests as $rk => $rv) {
			if(isset($rv->created_by)) {
				$rv->created_by= $rv->created_by;
			} else {
				$rv->created_by= '';
			}
			if($rv->photo_url) {
				$rv->photo_url= $this->filemanager->getFilePath($rv->photo_url);
			}
		}


		return $todays_requests;

	}

	public function approve() {
		$unknown_ds_arr= $_POST['unknown_ds_arr'];
		$remarks= $_POST['remarks'];
		$table_names_arr= $_POST['table_names_arr'];
		$this->db->trans_start();
		foreach($unknown_ds_arr as $key => $val) {
			if($table_names_arr[$key] == 'escort_parent_unknown_person_auth') {
				$data= array(
					'auth_status' => 1,
					'auth_given_by_type' => 'parent',
					'auth_given_by_id' => $this->authorization->getAvatarStakeHolderId(),
					'auth_given_datetime' => date('Y-m-d H:i:s'),
					'auth_remarks' => $remarks
				);
				$this->db->where('id', $val)->update('escort_parent_unknown_person_auth', $data);
			} else {
				$data= array(
					'auth_status' => 1,
					'auth_given_by_type' => 'parent',
					'auth_given_by_id' => $this->authorization->getAvatarStakeHolderId(),
					'auth_given_timestamp' => date('Y-m-d H:i:s'),
					'auth_remarks' => $remarks
				);
				$this->db->where('id', $val)->update('escort_by_taxi', $data);
			}
		}
		$this->db->trans_complete();
		return $this->db->trans_status();
	}

	public function reject() {
		$unknown_ds_arr= $_POST['unknown_ds_arr'];
		$remarks= $_POST['remarks'];
		$table_names_arr= $_POST['table_names_arr'];
		$this->db->trans_start();
		foreach($unknown_ds_arr as $key => $val) {
			if($table_names_arr[$key] == 'escort_parent_unknown_person_auth') {
				$data= array(
					'auth_status' => -1,
					'auth_given_by_type' => 'parent',
					'auth_given_by_id' => $this->authorization->getAvatarStakeHolderId(),
					'auth_given_datetime' => date('Y-m-d H:i:s'),
					'auth_remarks' => $remarks
				);
				$this->db->where('id', $val)->update('escort_parent_unknown_person_auth', $data);
			} else {
				$data= array(
					'auth_status' => -1,
					'auth_given_by_type' => 'parent',
					'auth_given_by_id' => $this->authorization->getAvatarStakeHolderId(),
					'auth_given_timestamp' => date('Y-m-d H:i:s'),
					'auth_remarks' => $remarks
				);
				$this->db->where('id', $val)->update('escort_by_taxi', $data);
			}
		}
		$this->db->trans_complete();
		return $this->db->trans_status();
	}
	public function save_student_exit_flow(){
		$input = $this->input->post();
		$std_exit = $this->db->select('id')->from('student_terminate')->where('student_id',$input['student_id'])->get()->row();

		$data = array(
			'student_id' => $input['student_id'],
			'student_terminate_reasons_id' => $input['tc_applied_reason_id'],
			'remarks' => $input['brief_description'],
			'avatar_type' => 2,
			'acad_year_id_applied_in' => $input['acad_year_id_applied_in'],
			'created_by_avatar_id' => $this->authorization->getAvatarId()
		);
		
		if(!empty($std_exit)){
			$this->db->where('student_id',$input['student_id']);
			$merge_data=array_merge(["status"=>1],$data);
			$is_updated=$this->db->update('student_terminate', $merge_data);
			$student_terminate_id = $std_exit->id;

			if($is_updated){
				$data = array(
					'student_terminate_id' => $std_exit->id,
					'action' => 'Student Exit Re-applied',
					'avatar_type' => 2,
					'created_by_avatar_id' => $this->authorization->getAvatarId()
				);
				$this->db->insert('student_terminate_history', $data);
			}

			// before assigning new noc approvers we want to remove the previous noc approvers
			$this->db->where("student_exit_flow_id", $student_terminate_id)
				->delete("student_terminate_noc");
		}else{
			$is_inserted=$this->db->insert('student_terminate', $data);
			$student_terminate_id=$this->db->insert_id();
			if($is_inserted){
				$data = array(
					'student_terminate_id' => $student_terminate_id,
					'action' => 'Student Exit Created',
					'avatar_type' => 2,
					'created_by_avatar_id' => $this->authorization->getAvatarId()
				);
				$this->db->insert('student_terminate_history', $data);
			}
		}

		// assigning default approvers
		$get_access_control = $this->db_readonly
			->select('staff_id, type, remarks')
			->from('student_terminate_access_control')
			->get()->result();

		if (!empty($get_access_control)) {
			$dataArry = [];
			foreach ($get_access_control as $access_control) {
				$dataArry[] = array(
					'student_exit_flow_id' => $student_terminate_id,
					'staff_id' => $access_control->staff_id,
					'noc_type' => $access_control->type,
					'admin_remarks' => $access_control->remarks,
					'created_on' => $this->Kolkata_datetime(),
					'created_by' => $this->authorization->getAvatarStakeHolderId(),
					'noc_status' => 0
				);
			}

			$this->db->insert_batch('student_terminate_noc', $dataArry);

			$dataArry = [];
			foreach ($get_access_control as $access_control) {
				$dataArry[] = array(
					'student_terminate_id' => $student_terminate_id,
					'staff_id' => $access_control->staff_id,
					'noc_type' => $access_control->type,
					'avatar_type' => 2,
					'created_by_avatar_id' => $this->authorization->getAvatarId(),
					'action' => 'Assigned for',
				);
			}
			$this->db->insert_batch('student_terminate_history', $dataArry);
		}

		return ["applied"=>$is_inserted,"reApplied"=> $student_terminate_id];
	}

	public function check_student_exit($student_id){
	// created_by_avatar_id
     $result = $this->db_readonly->select("concat(ifnull(sm.first_name,''),ifnull(sm.last_name,'')) as approved_by_name, date_format(st.approve_date,'%d-%M-%Y') as approved_on, a.id as avatar_id, a.avatar_type, a.stakeholder_id, ifnull(st.student_exit_remarks,'-') as student_exit_remarks, sa.admission_no as admission_no, sa.enrollment_number, st.status, ifnull(st.remarks,'-') as applied_remarks, date_format(st.created_on,'%d-%M-%Y') as applied_on, date_format(st.approve_date,'%d-%M-%Y') as approve_date,ifnull(sy.terminate_remarks,'-') as terminate_remarks, str.reason as applied_reason, concat(ifnull(sa.first_name,''),ifnull(sa.last_name,'')) as student_name, st.created_by_avatar_id as applied_by, st.acad_year_id_applied_for as tc_applied_for_year")
    ->from('student_terminate st')
	->join('student_year sy','sy.student_admission_id=st.student_id and sy.acad_year_id='.$this->yearId)
	->join("student_admission sa","sa.id=st.student_id")
	->join("student_terminate_reasons str","str.id=st.student_terminate_reasons_id")
	->join("avatar a","a.id=st.created_by_avatar_id")
    ->join("staff_master sm", "sm.id=st.approved_by", "left")
    ->where('st.student_id', $student_id)
    ->get()->row();

	if(!empty($result)){
		if($result->approved_by_name==""){
			$result->approved_by_name="Super Admin";
		}

		if($result->avatar_type==2){
			// get parent name
			$result->applied_by = $this->db_readonly->select("concat(ifnull(p.first_name,''),ifnull(p.last_name,'')) as parent_name")->from("parent p")->where("id", $result->stakeholder_id)->get()->row()->parent_name;
		}else if($result->avatar_type==3){
			// get staff name
				if ($result->stakeholder_id == 0) {
					$result->applied_by = 'Super Admin';
				} else {
					$result->applied_by = $this->db_readonly
						->select("concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as staff_name")
						->from("staff_master sm")
						->where("id", $result->stakeholder_id)
						->get()->row()->staff_name;
				}
		}
	}
	return $result;
  }

  public function student_status($student_id){
  	return $this->db_readonly->select('status')
    ->from('student_terminate')
    ->where('student_id', $student_id)
    ->get()->row();
  }

  public function get_parent_email($std_id,$template_name){ 
    $email_template = $this->db_readonly->select("et.*,et.content as template_content,ifnull(et.email_subject, 'Email subject not added') as email_subject")
    ->from('email_template et')
    ->where('name',$template_name)
    ->get()->row();
    if (!empty($email_template)) {
      $toEmail = $this->db_readonly->select("sa.id,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,p.email as father_email,p1.email as mother_email,concat(c.class_name, ifnull(cs.section_name,'')) as grade,concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as father_name,concat(ifnull(p1.first_name,''),' ',ifnull(p1.last_name,'')) as mother_name,sy.board, p.id as f_id, p1.id as m_id")
      ->from('student_admission sa')
	  ->join('student_year sy', 'sa.id=sy.student_admission_id')
	  ->join('class c','sy.class_id =c.id')
	  ->join('class_section cs', 'cs.id=sy.class_section_id','left')
      ->join('student_relation sr',"sa.id=sr.std_id")
      ->join('parent p','sr.relation_id=p.id')
      ->join('student_relation sr1',"sa.id=sr1.std_id")
      ->join('parent p1','sr1.relation_id=p1.id')
      ->where('sr.relation_type','Father')
      ->where('sr1.relation_type','Mother')
      ->where('sa.id',$std_id)
	  ->order_by('sy.id','desc')
      ->get()->row();
      $email_template->to_email = $toEmail;

	  $avatar_id = $this->authorization->getAvatarId();
	  $logged_in_as = $this->db_readonly->select('relation_type')
		->from('student_relation sr')
		->join('avatar a', 'a.stakeholder_id = sr.relation_id')
		->where('a.id', $avatar_id)
		->get()->row()->relation_type;
      $email_template->logged_in_as = $logged_in_as;

      $to_mails = [];
	  if(!empty($email_template->members_email)){
		$to_mails = explode(',',$email_template->members_email);
	  }
      if(!empty($toEmail->father_email)){
        array_push($to_mails,$toEmail->father_email);
      }
      if(!empty($toEmail->mother_email)){
        array_push($to_mails,$toEmail->mother_email);
      }
      
      $email_template->to_emails = $to_mails;
      // echo '<pre>';print_r($email_template);die();
      return (array) $email_template;
    }else{
      return 0;
    }
    
  }

  public function get_template_form_path($bp_id){
	return $this->db->select('tnc_path')->from('feev2_blueprint')->where('id',$bp_id)->get()->row()->tnc_path;
  }

  public function check_consent_forms_submitted($student_admission_id){
	$acad_year_id = $this->db->select('sy.acad_year_id')
	            	->from('student_admission sa')
					->join('student_year sy','sa.id=sy.student_admission_id')
                    ->where('sa.id',$student_admission_id)
					->where('promotion_status !=',4)
					->order_by('sy.id','desc')
                    ->get()->row()->acad_year_id;
    if(empty($acad_year_id)){
      return 0;
    }
	$allow_fee_payment_on_consent_agreed = $this->settings->getSetting('allow_fee_payment_on_consent_agreed');
	$consent_form_templates = $this->db->select('id')->from('student_consent_templates')->where('acad_year_id',$acad_year_id)->get()->result();
	$template_ids = [];
	if(!empty($consent_form_templates)){
		foreach($consent_form_templates as $key=>$val){
			array_push($template_ids,$val->id);
		}

		 $this->db->select('id')
		->from('student_consent_submissions')
		->where_in('student_consent_templates_id',$template_ids)
		->where('student_admission_id',$student_admission_id);
		if($allow_fee_payment_on_consent_agreed == 1){
			$this->db->where('consent_provided','Agreed');
		}
		$submitted_consent_forms = $this->db->get()->result();

		if(count($consent_form_templates) == count($submitted_consent_forms)){
			return 1;
		}else{
			return 0;
		}
	}
	return -1;
  }

  public function get_student_details_mandatory_fields_jodo($studentId, $acadyearId){
	$query = $this->db->select("concat(sa.first_name,'',ifnull(sa.last_name,'')) as student_name, date_format(sa.dob,'%d-%m-%Y') as student_dob, concat(p1.first_name,'',ifnull(p1.last_name,'')) as father_name, ifnull(p1.mobile_no,'') as father_contact_no,ifnull(p1.email,'') as father_email, sa.admission_no")
	->from('student_admission sa')
	->join('student_year sy','sa.id=sy.student_admission_id')
	->join('student_relation sr1', "sr1.std_id=sa.id and sr1.relation_type='Father'")
	->join('parent p1', 'p1.id=sr1.relation_id')
	->where('sa.id',$studentId)
	// ->where('sy.acad_year_id',$acadyearId)
	->get()->row();
	if(!empty($query)){
		$query1 = $this->db->select("sa.admission_no")
		->from('student_admission sa')
		->where('sa.admission_no',$query->admission_no)
		->get();
		if($query1->num_rows() > 1){
			$query->admission_no_count = 2;
		}else{
			$query->admission_no_count = 1;
		}
	}
	return $query;
}

public function get_dateWise_tasks($studentId){
	$date= date('Y-m-d H:i:s');
	$formattedDate = date('Y-m-d', strtotime($_POST['formattedDate']));
	$this->db_readonly->select("lt.consider_this_task_as, lt.id as task_id, lt.task_name, lt.task_type, lt.task_description, lt.status, lts.id as task_student_id, lts.read_status, lts.submission_status, lts.evaluation_status, lts.is_late_submission, lts.resubmission_status, lp.subject_name, TIMESTAMPDIFF(MINUTE, lt.task_last_date, now()) as elapsed_time, CONVERT_TZ(lt.task_last_date,'+00:00','+05:30') as local_task_last_date, CONVERT_TZ(lt.created_on,'+00:00','+05:30') as created_on, lt.release_evaluation, lt.close_submission");
	$this->db_readonly->from('lp_tasks_students lts');
	$this->db_readonly->join('lp_tasks lt',"lt.id=lts.lp_tasks_id");
	$this->db_readonly->where("date_format(lt.created_on,'%Y-%m-%d')='$formattedDate'");
	$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
	$this->db_readonly->where('lts.student_id',$studentId);
	// $this->db_readonly->where('lt.task_publish_timestamp <=',date('Y-m-d H:i:s'));
	$this->db_readonly->where("(lt.task_publish_timestamp is NULL OR lt.task_publish_timestamp <= '$date')");
	$this->db_readonly->order_by('lp.subject_name');
	$this->db_readonly->order_by('lt.created_on', 'desc');
	return $this->db_readonly->get()->result();
}

public function getStudentTerminateReasons(){
	$terminate_reasons=$this->db_readonly->select("id,reason")
	->from("student_terminate_reasons")
	->get();

	if($terminate_reasons){
		return $terminate_reasons->result();
	}else{
		return [];
	}
}

public function is_any_un_rejected_tc_exists($data){
	$student_terminate_data=$this->db_readonly->select("*")
	->from("student_terminate st")
	->where("st.student_id",$data["student_id"])
	->where("st.status!=",0)
	->get()->row();

	if(!empty($student_terminate_data)){
		return 1;
	}else{
		return 0;
	}
}

public function getLoggedInStudentInformation($student_id){
	return $student_info=$this->db_readonly->select("sa.admission_no, sa.enrollment_number, concat(ifnull(sa.first_name,''), ifnull(sa.last_name,'')) as student_name, date_format(sa.date_of_joining,'%D %M %Y') as admission_date")
	->from("student_admission sa")
	->where("id",$student_id)
	->get()->row();

	// echo "<pre>"; print_r($student_info); die();
}

	public function getStudentInventoryReport($studentId){
		// echo "<pre>";print_r($studentId);die();
		$transactions = $this->db_readonly->select('psm.id as master_id, DATE_FORMAT(psm.receipt_date, "%d-%b-%Y") as receipt_date, psm.receipt_no as receipt_no, psm.total_amount as total_amount, SUM(pst.quantity) as total_quantity')
									->from('procurement_sales_transactions pst')
									->join('procurement_sales_year psy', 'psy.id = pst.sales_year_id')
									->join('procurement_sales_master psm', 'psm.id = pst.sales_master_id')
									->where('psm.student_id', $studentId)
									->where('psy.is_active', 1)
									->group_by('psm.id')
									->order_by('psm.receipt_date', 'DESC')
									->get()
									->result();
		if(empty($transactions)){
			return false;
		}
		$refunds = $this->db_readonly->select('proc_sales_master_id as master_id, SUM(refund_amount) as refund_amount, SUM(return_quantity) as return_quantity')
									->from('procurement_sales_return')
									->where_in('proc_sales_master_id', array_column($transactions, 'master_id'))
									->group_by('proc_sales_master_id')
									->get()
									->result();

		$refunds_map = [];
		foreach ($refunds as $refund) {
			$refunds_map[$refund->master_id] = [
				'refund_amount' => $refund->refund_amount,
				'return_quantity' => $refund->return_quantity
			];
		}

		foreach ($transactions as $transaction) {
			$master_id = $transaction->master_id;

			$refund_amount = isset($refunds_map[$master_id]['refund_amount']) ? $refunds_map[$master_id]['refund_amount'] : 0;
			$return_quantity = isset($refunds_map[$master_id]['return_quantity']) ? $refunds_map[$master_id]['return_quantity'] : 0;

			$transaction->refund_amount = $refund_amount;
			$transaction->return_quantity = $return_quantity;

			$transaction->amount_after_return = $transaction->total_amount - $refund_amount;
			$transaction->quantity_after_return = $transaction->total_quantity - $return_quantity;

			$transaction->return_amount = $refund_amount;
			$transaction->return_quantity = $return_quantity;
		}
		// echo "<pre>";print_r($transactions);die();
		
		// echo "<pre>";print_r($this->db_readonly->last_query());die(); 
		return $transactions;
	}

	public function get_items_by_master_id($master_id){
		$result = $this->db_readonly->select('pii.id as item_id, pis.id as sub_cat_id ,CONCAT(pis.subcategory_name, ">>", pii.item_name) as item_name, pst.amount as item_amount, pst.quantity as quantity')
									->from('procurement_sales_transactions pst')
									->join('procurement_itemmaster_items pii', 'pii.id = pst.proc_im_items_id')
									->join('procurement_itemmaster_subcategory pis', 'pis.id = pst.proc_im_subcategory_id')
									->where('pst.sales_master_id', $master_id)
									->get()
									->result();
		$return_data = $this->db_readonly->select('psr.proc_im_items_id as item_id, SUM(psr.return_quantity) as return_quantity, SUM(psr.refund_amount) as refund_amount')
										->from('procurement_sales_return psr')
										->where('psr.proc_sales_master_id', $master_id)
										->group_by('psr.proc_im_items_id')
										->get()
										->result();
		$return_map = [];
		foreach ($return_data as $return) {
			$return_map[$return->item_id] = $return;
		}

		foreach ($result as &$item) {
			if (isset($return_map[$item->item_id])) {
				$item->return_quantity = $return_map[$item->item_id]->return_quantity;
				$item->refund_amount = $return_map[$item->item_id]->refund_amount;
			} else {
				$item->return_quantity = 0;
				$item->refund_amount = 0;
			}
		}
		// echo "<pre>";print_r($result);die();
		return $result;

	}

	public function get_student_data_by_id_v2() {
		$parent_avatar_id = $this->authorization->getAvatarId();
		if (!$parent_avatar_id) {
			return null; // Return early if no valid avatar ID
		}
		
		$acad_year_id = $this->acad_year->getAcadYearId();
		if (!$acad_year_id) {
			return null; // Return early if no valid academic year ID
		}

		$isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent)?$this->parentcache->getParentCache()->isNextYearStudent:'0';
		$JoinyearId = $this->yearId;
		if($isNewStudent){
			$JoinyearId =  $this->acad_year->getPromotionAcadYearId();
		}
		//Get the Student Record
			$student_record = $this->db_readonly->select("concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, sy.picture_url, sy.class_section_id, sy.class_id, cs.class_name, cs.section_name, sy.board, p.id as parent_id, sa.id as student_id, if(sa.admission_acad_year_id > $acad_year_id, 1, 0) as is_next_year_student, (case when sy.promotion_status = 4 or sy.promotion_status=5 then 0 else if(sa.admission_status=2, 1, 0) end) as is_approved_student")
				->from('student_admission sa')
			->join('student_year sy',"sa.id=sy.student_admission_id")
				->join('avatar a', "a.id=$parent_avatar_id")
				->join('parent p', "sa.id=p.student_id and p.id=a.stakeholder_id")
			->join('class_section cs','sy.class_section_id=cs.id','left')
			->where('sy.acad_year_id',$JoinyearId)
			->order_by('sy.id','DESC')
			->get()->row();

		if (!$student_record) {
			return null; // Return early if no student record found
		}
		return $student_record;
	}
	
	public function get_num_today_calendar_events_v2($student_board) {
		$today = date('Y-m-d');
		$num_calendar_record = $this->db_readonly->select('count(*) as num_events')
			->from('school_calender')
			->where("((from_date >= '$today') and (to_date <= '$today' OR to_date is null)) and applicable_to != 1 and board in ('$student_board', '100')") //100 => 'All'
			->get()->row();

		return $num_calendar_record->num_events;
	}

	public function get_circular_unread_count($parent_id) {
		$result = $this->db_readonly->select('count(cm.id) as today_circular_count')
			->from('circularv2_master cm')
			->join("circularv2_sent_to cst", "cm.id=cst.circularv2_master_id")
			->where('cm.visible', 1)
			->where('cst.stakeholder_id', $parent_id)
			->where('cst.avatar_type', 2)
			->where('cst.is_read', 0)
			->get()->row();
		return $result->today_circular_count;
	}

	public function get_student_id_by_user_id($stakeholder_id){
		$result =  $this->db_readonly->select('student_id')
		->from('parent')
		->where('id',$stakeholder_id)
		->get()->row();
		if(!empty($result)){
			return $result->student_id;
		}else{
			return 0;
		}
	}

	// private function _get_fees_student_details($stdAdmId){
	// 	return $this->db->select("c.id as class_id")
	// 	->from('student_admission sd')
	// 	->where_in('sy.id',$stdAdmId)
	// 	->join('student_year sy','sy.student_admission_id=sd.id')
	// 	->join('class c','sy.class_id=c.id')
	// 	->get()->row();
	// }
	private function _get_blueprint_wise_discount_alog(){
		
	}
	public function get_all_published_fee_data($stdAdmId){
		$today = date('d-m-Y');
		$zero_date = '0000-00-00';
		// Get master data
		$master = $this->db_readonly->select("
			fcs.id as cohort_student_id,
			fcs.tnc_status,
			COALESCE(fcs.tnc_accepted_by, 0) as parent_id,
			DATE_FORMAT(tnc_accepted_on, '%d-%m-%Y') as accepted_on,
			(CASE WHEN pay_date IS NULL OR pay_date = $zero_date OR pay_date >= CURDATE() THEN 0 ELSE 1 END) as paydateCheck,
			fcs.online_payment,
			fcs.blueprint_id,
			fss.id as schId,
			fb.name as bp_name,
			fb.id as bp_id, fbit.discount_algo, fbit.discount_amount, fbit.staff_discount_amount_algo,date_format(fbit.enabled_discount_end_date,'%d-%m-%Y') as discount_end_date, fb.acad_year_id, sa.has_staff, fss.total_fee, fcs.exclude_dynamic_concession")
		->from('feev2_cohort_student fcs')
		->join('feev2_student_schedule fss', 'fcs.id = fss.feev2_cohort_student_id')
		->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
		->join('feev2_blueprint fb', 'fcs.blueprint_id = fb.id')
		->join('student_admission sa','sa.id=fcs.student_id')
		->join('student_year sy','sy.student_admission_id=sa.id and fb.acad_year_id=sy.acad_year_id')
		->join('class c','sy.class_id=c.id')
		->where('fcs.student_id', $stdAdmId)
		->where('fcs.publish_status', 'PUBLISHED')
		->get()->result();
		if (empty($master)) {
			return 0;
		}
		// Create a map of master data
		$masterMap = array_column($master, null, 'schId');
		$shcIds = array_keys($masterMap);
		foreach ($master as $key => $val) {
			if ($val->discount_algo =='discount_if_full_paid_json') {
				if (!empty($val->discount_amount)) {
					$jsnDecode = (array) json_decode($val->discount_amount, true);
					if (array_key_exists($classId, $jsnDecode)) {
						if($val->has_staff == 1 && $val->staff_discount_amount_algo =='staff_discount'){
							$val->discount_amount = 0;
						}else{
							$val->discount_amount = $jsnDecode[$classId];
						}
					}else{
						$val->discount_amount = 0;
					}
				}
			}
			if($val->has_staff == 1 && $val->staff_discount_amount_algo =='staff_discount'){
				$val->discount_amount = 0;
			}
		}
		// Get installments data
		$result = $this->db_readonly->select("
			fsi.id as schInsId, (fsi.installment_amount - COALESCE(installment_amount_paid, 0) - COALESCE(total_concession_amount, 0) - COALESCE(total_concession_amount_paid, 0)) as installment_amount, fsi.status, (COALESCE(fsi.total_fine_amount, 0) - COALESCE(fsi.total_fine_amount_paid, 0) - COALESCE(fsi.total_fine_waived, 0)) as total_fine, fi.name, DATE_FORMAT(fi.end_date, '%d-%m-%Y') as due_date,
			fi.installment_order, fsi.fee_student_schedule_id, fi.id as installment_id, fi.discount_concession_algo, fi.discount_concession_amount, fsi.installment_amount as total_installment_amount")
		->from('feev2_student_installments fsi')
		->join('feev2_installments fi', 'fsi.feev2_installments_id = fi.id')
		->where_in('fsi.fee_student_schedule_id', $shcIds)
		->where('fsi.status!=','FULL')
		->order_by('fi.installment_order')
		->get()->result();
		$schInsIds = array_column($result, 'schInsId');
		if(empty($schInsIds)){
			return  $result;
		}
		// Get components data
		$components = $this->db_readonly->select("
			fsic.id as schInsCompId,
			(fsic.component_amount - COALESCE(fsic.component_amount_paid, 0) - COALESCE(fsic.concession_amount, 0) - COALESCE(fsic.concession_amount_paid, 0)) as component_amount, (fsic.component_amount - COALESCE(fsic.component_amount_paid, 0)) as total_component_amount,
			fsic.blueprint_component_id,
			fsic.fee_student_installment_id, ifnull(fsic.concession_amount,0) as concession_amount
		")
		->from('feev2_student_installments_components fsic')
		->where_in('fsic.fee_student_installment_id', $schInsIds)
		->get()->result();

		$feestudentInsIds = [];
		foreach ($components as $component) {
			$feestudentInsIds[$component->fee_student_installment_id][] = $component;
		}
		$processedBpIds = [];
		// Merge data into the result set
		foreach ($result as $val) {
			$schId = $val->fee_student_schedule_id;
			if (isset($masterMap[$schId])) {
				$masterData = $masterMap[$schId];
				$val->tnc_status = $masterData->tnc_status;
				$val->paydateCheck = $masterData->paydateCheck;
				$val->bp_name = $masterData->bp_name;
				$val->cohort_student_id = $masterData->cohort_student_id;
				$val->bp_id = $masterData->bp_id;
				$val->full_discount_amount = $masterData->discount_amount;
				$val->discount_algo = $masterData->discount_algo;
				$val->total_fee = $masterData->total_fee;
				$val->discount_end_date = $masterData->discount_end_date;
				$val->online_payment = $masterData->online_payment;
				$val->exclude_concession = $masterData->exclude_dynamic_concession;
			}

			if (isset($feestudentInsIds[$val->schInsId])) {
				$val->components = $feestudentInsIds[$val->schInsId];
			}
			$dueDate = $val->due_date;
			$bpId = $val->bp_id;

			$dueDateObj = DateTime::createFromFormat('d-m-Y', $dueDate);
			$todayObj = DateTime::createFromFormat('d-m-Y', $today);

			$val->checked = 0;
			if ($dueDateObj && $todayObj && $dueDateObj <= $todayObj) {
				$val->checked = 1;
			}
			if (!isset($processedBpIds[$bpId])) {
				$processedBpIds[$bpId] = true;
				$val->checked = 1;
			}
		}
		return $result;
	}

	public function insert_multiple_fee_transactions($pay_amounts, $concession_discounts, $fine_amount, $discount_amount, $studentId, $fees_concession_discount){

		$timezone = new DateTimeZone("Asia/Kolkata");
		$date = new DateTime();
		$time = new DateTime();
		$time->setTimezone($timezone);
		$merge = new DateTime($date->format('Y-m-d') . ' ' . $time->format('H:i:s'));
		$receipt_date = $merge->format('Y-m-d H:i:s');
		$transaction_ids = [];
		foreach ($pay_amounts as $fee_student_schedule_id => $installments) {
			$total_amount_paid = 0;
			$tConceAmount = 0;
			$tConceDiscountAmount = 0;
			$tFineAmount = 0;
			$full_fee_discount = 0;
			foreach ($installments as $installment_id => $components) {	
				$installment_amount = array_sum($components);
				$total_amount_paid += $installment_amount;
				$concession_components = isset($concession_discounts[$fee_student_schedule_id][$installment_id]) ? $concession_discounts[$fee_student_schedule_id][$installment_id] : [];

				$tConceAmount += array_sum(array_map('floatval', $concession_components));

				$concession_discount_components = isset($fees_concession_discount[$fee_student_schedule_id][$installment_id]) ? $fees_concession_discount[$fee_student_schedule_id][$installment_id] : [];

				$tConceDiscountAmount += array_sum(array_map('floatval', $concession_discount_components));

				$fine_components_amont = isset($fine_amount[$fee_student_schedule_id][$installment_id]) ? $fine_amount[$fee_student_schedule_id][$installment_id] : [];
				$tFineAmount += array_sum(array_map('floatval', $fine_components_amont));

				$full_fee_discount = isset($discount_amount[$fee_student_schedule_id]) ? $discount_amount[$fee_student_schedule_id] : 0;
			}
	
			$this->db->insert('feev2_transaction', [
				'student_id' => $studentId,
				'fee_student_schedule_id' => $fee_student_schedule_id,
				'amount_paid' => $total_amount_paid - $tConceDiscountAmount,
				'discount_amount' => $full_fee_discount,
				'paid_datetime' => $receipt_date,
				'acad_year_id' => $this->yearId,
				'status' => 'INITIATED',
				'transaction_mode' => 'ONLINE',
				'collected_by' => $this->authorization->getAvatarId(),
				'concession_amount' => $tConceAmount,
				'fine_amount' =>  $tFineAmount
			]);
			$transaction_ids[$fee_student_schedule_id] = $this->db->insert_id();
		}
		return $transaction_ids;
	}

	public function insert_multiple_installment_transactions_components($component_details, $concession_discounts, $fine_amount, $transaction_ids, $fees_concession_discount){
		foreach ($component_details as $fee_student_schedule_id => $components) {
			foreach ($components as $installment_id_with_component => $comp_details) {
				foreach ($comp_details as $component_key => $amount_paid) {
					list($component_id, $installment_component_id) = explode('_', $component_key);
					list($installment_id, $bpInstallmentId) = explode('_', $installment_id_with_component);

					$fineAmount = isset($fine_amount[$fee_student_schedule_id][$installment_id][$component_id])
						? floatval($fine_amount[$fee_student_schedule_id][$installment_id][$component_id])
						: 0;

					$concessionAmount = isset($concession_discounts[$fee_student_schedule_id][$installment_id][$component_id])
						? floatval($concession_discounts[$fee_student_schedule_id][$installment_id][$component_id])
						: 0;
					$concessionDiscountAmount = isset($fees_concession_discount[$fee_student_schedule_id][$installment_id][$component_id])
						? floatval($fees_concession_discount[$fee_student_schedule_id][$installment_id][$component_id])
						: 0;
					$this->db->insert('feev2_transaction_installment_component', [
						'fee_transaction_id' => $transaction_ids[$fee_student_schedule_id],
						'blueprint_component_id' => $installment_component_id,
						'blueprint_installments_id' => $bpInstallmentId,
						'amount_paid' => $amount_paid - $concessionDiscountAmount,
						'fee_student_installments_components_id' => $component_id,
						'fee_student_installments_id' => $installment_id,
						'fine_amount' => $fineAmount,
						'concession_amount' => $concessionAmount,
					]);
				}
			}
		}
	}

	public function insert_multiple_installment_transactions_paments($transaction_ids){
		foreach ($transaction_ids as $key => $transId) {
			$this->db->insert('feev2_transaction_payment', [
				'fee_transaction_id' => $transId,
    			'payment_type' => '10',
    			'bank_name' => '',
    			'bank_branch' => '',
    			'cheque_or_dd_date' =>'',
    			'card_reference_number' =>'',
    			'reconciliation_status' => 0,
    			'recon_lastmodified_by' => $this->authorization->getAvatarId(),
    			'remarks' => '',
    			'cheque_dd_nb_cc_dd_number' => '',
    			'recon_created_on' => $this->Kolkata_datetime(),
			]);
		}
	}

	public function get_multiple_split_amount($blueprint_id, $split_amount) {
		// echo "<pre>"; print_r($blueprint_id);die();
		// Retrieve components from the database
		$comp_result = $this->db->select('id, vendor_code')
		->from('feev2_blueprint_components')
		->where_in('feev2_blueprint_id', $blueprint_id)
		->get()->result();
		$split_json = array();
		$vendor_map = array();
	
		// Build a map of vendor codes to components
		foreach ($comp_result as $value) {
			$vendor_map[$value->id] = $value->vendor_code;
		}
	
		// Aggregate split amounts by vendor code
		foreach ($split_amount as $fee_student_schedule_id => $installments) {
			foreach ($installments as $installment_id => $components) {
				foreach ($components as $component_id => $amount) {
					$component_id = explode('_', $component_id)[1];
					if (isset($vendor_map[$component_id])) {
						$vendor_code = $vendor_map[$component_id];
	
						// Check if vendor code already exists in the result array
						if (!isset($split_json[$vendor_code])) {
							$split_json[$vendor_code] = new stdClass();
							$split_json[$vendor_code]->vendor_code = $vendor_code;
							$split_json[$vendor_code]->split_amount_fixed = 0;
						}
	
						// Add the amount to the appropriate vendor code
						$split_json[$vendor_code]->split_amount_fixed += $amount;
					}
				}
			}
		}
	
		// Convert associative array to indexed array
		$result = array_values($split_json);
		// Prepare the final result in the format required
		$temp = new stdClass();
		$temp->vendors = $result;
	
		return $temp;
	}

	public function get_fees_amount_payment_after($sourceIds){
		return $this->db->select('sum(ft.amount_paid) as amount_paid')
		->from('feev2_transaction ft')
		->where_in('ft.id',$sourceIds)
		->get()->row();
	}

	public function  get_admission_number($student_id) {
		return $this->db->select('admission_no')->from('student_admission')->where('id',$student_id)->get()->row()->admission_no;
	}

	public function canParentApplyTCForNextYear($stduentId){
		$currentStudentGrade=$this->db_readonly->select("class_id")
		->from("student_year")
		->where('acad_year_id',$this->yearId)
		->where("student_admission_id",$stduentId)
		->get()->row();
		
		$lastGrade=$this->db_readonly->select("id")
		->from("class")
		->where("acad_year_id",$this->yearId)
		->order_by("id","desc")
		->limit(1)
		->get()->row();

		if ($currentStudentGrade->class_id >= $lastGrade->id){
			return 0;
		}else{
			return 1;
		}
	}
	public function submit_student_transport($studentId){
		$transportStageKm = '';
		if(isset( $_POST['kilometer'])){
			if (!empty($this->input->post('fee_applied_for'))) {
				$transportStageKm = $this->input->post('fee_applied_for');
			}else{
				$transportStageKm = $this->input->post('kilometer');
			}
		}
		
		$transportation_mode = isset($_POST['transportation_mode']) ? $this->input->post('transportation_mode') : '';
		$transport_addition_details = isset($_POST['transport_addition_details']) ? $this->input->post('transport_addition_details') : '';
		if($this->input->post('transport') == 1){
			$transportation_mode = 'School Bus';
			$transport_addition_details = ''; 
		}

		$tData = array(
			'stop' => isset($_POST['stops']) ? $this->input->post('stops') : '',
			'has_transport_km' => $transportStageKm,
			'pickup_mode' => isset($_POST['pickup_mode']) ? $this->input->post('pickup_mode') : '',
			'drop_stop' => isset($_POST['drop_stop']) ? $this->input->post('drop_stop') : '',
			'transport_mode' => $transportation_mode,
			'transportation_additional_details' => $transport_addition_details,
			'transport_details_created_on' => $this->Kolkata_datetime(),
			'transport_details_created_by' => $this->authorization->getAvatarId(),
			'transport_required' => isset($_POST['transport']) ? $this->input->post('transport') : '',
			'nearest_land_mark' => isset($_POST['nearest_land_mark']) ? $this->input->post('nearest_land_mark') : '',
			'transport_request_status' => 'Pending',
			'transportation_distance' => isset($_POST['transport_distance']) ? $this->input->post('transport_distance') : '',
			'google_maps_link' => isset($_POST['google_maps_link']) ? $this->input->post('google_maps_link') : ''
		);
		$this->db->where('acad_year_id',$this->input->post('acad_year_id'));
		$this->db->where('student_admission_id', $studentId);
		return $this->db->update('student_year', $tData); 
	}

	public function get_fee_Stop_list(){
		return $this->db->order_by('name','asc')->get('feev2_stops')->result();
	}

	public function get_fee_km_list(){
		return $this->db->get('feev2_km')->result();
	}

	public function get_assigned_transport($studentId,$acad_year_id){
		// echo '<pre>';print_r($acad_year_id);die();
		$this->db->select("ifnull(stop,'') as stop, ifnull(drop_stop,'') as drop_stop, ifnull(pickup_mode,'') pickup_mode, ifnull(has_transport_km,'') as has_transport_km,transport_mode,transportation_additional_details,sy.transport_required,date_format(sy.transport_details_created_on,'%d-%m-%Y') as transport_details_created_on,ifnull(sy.nearest_land_mark,'') as nearest_land_mark,ifnull(sy.transport_request_status,'') as transport_request_status,CASE WHEN a.avatar_type = 2 THEN concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) when avatar_type=4 THEN concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) ELSE '' END AS created_by,ifnull(sy.transportation_distance,'') as transportation_distance,google_maps_link");
		$this->db->from('student_year sy');
		$this->db->join('student_admission sa','sy.student_admission_id=sa.id');
		$this->db->join('avatar a','sy.transport_details_created_by=a.id','left');
		$this->db->join('parent p','a.stakeholder_id=p.id and avatar_type=2','left');
		$this->db->join('staff_master sm','a.stakeholder_id=sm.id and avatar_type=4','left');
		$this->db->where('acad_year_id',$acad_year_id);
		$this->db->where('student_admission_id', $studentId);
		$result =  $this->db->get()->row();

		if(empty($result)){
			return $result;
		}
		$result->pickup = $this->db->select('fs.id,fs.name, fs.route, fk.kilometer')
		->from('feev2_stops fs')
		->where('fs.id',$result->stop)
		->join('feev2_km fk','fs.kilometer=fk.id')
		->get()->row();

		$result->drop = $this->db->select('fs.id,fs.name, fs.route, fk.kilometer')
		->from('feev2_stops fs')
		->where('fs.id',$result->drop_stop)
		->join('feev2_km fk','fs.kilometer=fk.id')
		->get()->row();
		return $result;
	}

	public function check_student_is_promoted($std_id,$promotionAcadYearId){
		$academic_year_id_for_transportation_details = $this->settings->getSetting('academic_year_id_for_transportation_details') ; 
		if(!empty($academic_year_id_for_transportation_details)){
			$promotionAcadYearId = $academic_year_id_for_transportation_details;
		}
		return $this->db->select('id')->from('student_year')->where('acad_year_id',$promotionAcadYearId)->where('student_admission_id',$std_id)->get()->row();
	}

	public function get_transport_email_data($std_id){
		$isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent)?$this->parentcache->getParentCache()->isNextYearStudent:'0';
		$JoinyearId = $this->yearId;
		if($isNewStudent){
			$JoinyearId =  $this->acad_year->getPromotionAcadYearId();
		}

		$email_template = $this->db->select('name,members_email,content,registered_email,email_subject')->from('email_template')->where('name','transportation_request_email_confirmation')->get()->row();
		if(!empty($email_template)){
			$toEmail = $this->db_readonly->select("sa.id,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,p.email as father_email,p1.email as mother_email,concat(cs.class_name, cs.section_name) as class_section_name,admission_no,enrollment_number")
			->from('student_admission sa')
			->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$JoinyearId")
			->join('class c','sy.class_id=c.id')
			->join('class_section cs','sy.class_section_id=cs.id','left')
			->join('student_relation sr',"sa.id=sr.std_id")
			->join('parent p','sr.relation_id=p.id')
			->join('student_relation sr1',"sa.id=sr1.std_id")
			->join('parent p1','sr1.relation_id=p1.id')
			->where('sr.relation_type','Father')
			->where('sr1.relation_type','Mother')
			->where('sa.id',$std_id)
			->get()->row();
      		$email_template->to_email = $toEmail;
		}
		// echo '<pre>';print_r($email_template);die();
		return (array)$email_template;
	}

	public function save_sending_email_data($emails_data){
		return $this->db->insert_batch('email_sent_to', $emails_data);
	}

	public function assinged_fees_for_student($student_id){
		$acad_year_id = $this->input->post('acad_year_id');
		$blueprints = $this->db->select('fb.id, fbit.id as feev2_blueprint_installment_types_id')
        ->from('feev2_blueprint fb')
        ->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id')
        ->where('acad_year_id',$acad_year_id)
		->where('is_transport_request',1)
        ->get()->row();
		if(empty($blueprints)){
			return array();
		}
		$student = $this->get_std_details_move_to_erp_byId($student_id,$acad_year_id);
		$input = [];
		if(!empty($blueprints) && !empty($student)){
			$cohort = $this->_get_cohort_details_move_to_erp($blueprints->id, $student);
			if(!empty($cohort)){
				$input[$blueprints->id] = [];
				$comp_amount[$blueprints->id] = [];
				$concession_amount[$blueprints->id] = [];
				$fine_amount[$blueprints->id] = [];
				$fee_data = $this->_get_fees_sturcutre_data($cohort->id);
				if(!empty($fee_data))
                    foreach ($fee_data as $key => $fee) {
                        $comp_amount[$blueprints->id][$fee->feev2_installment_id][$fee->feev2_blueprint_component_id] = $fee->compAmount;
                        $concession_amount[$blueprints->id][$fee->feev2_installment_id][$fee->feev2_blueprint_component_id] = 0;
                        $fine_amount[$blueprints->id][$fee->feev2_installment_id] = 0;
                    }
                    $input[$blueprints->id]['blueprint_installment_type_id'] = $blueprints->feev2_blueprint_installment_types_id;
                    $input[$blueprints->id]['cohort_id'] = $cohort->id;
                    $input[$blueprints->id]['custom'] = $cohort->id;
                    $input[$blueprints->id]['fine_amount'] = $fine_amount[$blueprints->id];
                    $input[$blueprints->id]['comp_amount'] = $comp_amount[$blueprints->id];
                    $input[$blueprints->id]['concession_amount'] = $concession_amount[$blueprints->id];
                    $input[$blueprints->id]['cohort_student_remarks'] = '';
                    $input[$blueprints->id]['blueprint_id'] = $blueprints->id;
                    $input[$blueprints->id]['student_id'] = $student_id;
                    $input[$blueprints->id]['blue_print_name'] = '';
			}
		}
		return $input;
	}

	public function get_bp_id_by_cohort_student_fee_id($cohort_student_fee_id){
		return $this->db->select('fcs.blueprint_id, fss.total_fee')
		->from('feev2_cohort_student fcs')
		->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
		->where('fcs.id',$cohort_student_fee_id)
		->get()->row();
	}

	public function get_std_details_move_to_erp_byId($stdId, $fee_acad_year_id){
        $school_name = $this->settings->getSetting('school_short_name');
         $this->db->select("sd.id as std_admission_id, sy.id as std_year_id, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as stdName, sd.admission_no, c.class_name as className, sy.board, sy.boarding, sy.medium, sd.category, sy.is_rte, sd.admission_acad_year_id as academic_year_of_joining, sy.admission_type, sy.class_id as class, sd.has_staff as has_staff, sd.sibling_type as has_sibling, c.type as class_type, sy.acad_year_id, donor, has_transport, sd.staff_id, has_transport_km, stop, pickup_mode, sd.gender, sd.life_time_fee_mode as is_lifetime_student, sy.combination, sd.attempt, sd.quota, admission_status")
        ->from('student_year sy')
        ->where('sd.id',$stdId)
        ->where('sy.acad_year_id',$fee_acad_year_id)
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->join('class c','sy.class_id=c.id');
        return $this->db->get()->row();
    }

	private function _get_cohort_details_move_to_erp($blueprint_id, $std_data) {
		$this->load->library('fee_library');
        $std_arr = (array)$std_data;
        $filters = $this->fee_library->construct_filter($blueprint_id, $std_arr);
        $result = $this->db_readonly->select('fc.id, fc.total_fee, fc.friendly_name, default_ins')
          ->from('feev2_cohorts fc')
          ->where('fc.filter',$filters)
          ->where('blueprint_id',$blueprint_id)
          ->get()->row();
        return $result;
    }

	private function _get_fees_sturcutre_data($cohort_id){
        return $this->db->select('fcic.feev2_blueprint_component_id, fcic.amount as compAmount, fcic.feev2_installment_id, fbc.name as compName, fi.name as insName, fbc.is_concession_eligible, feev2_blueprint_installment_types_id, fi.feev2_installment_type_id')
        ->from('feev2_cohort_installment_components fcic')
        ->where('fcic.feev2_cohort_id',$cohort_id)
        ->join('feev2_installments fi','fcic.feev2_installment_id=fi.id')
        ->join('feev2_blueprint_components fbc','fcic.feev2_blueprint_component_id=fbc.id')
        ->order_by('fi.id')
        ->get()->result();
    }

	public function check_non_refund_transport_fees_status($student_id,$acad_year_id) {
		$result = new stdClass();
		$result->fee_enable = 0;
		$result->status = 'Not Paid';
		$result->terms_conditions = '';
		$blueprints = $this->db_readonly->select('fb.id,terms_conditions')
        ->from('feev2_blueprint fb')
        ->where('acad_year_id',$acad_year_id)
		->where('is_transport_request',1)
        ->get()->row();
		if(!empty($blueprints)){
			$result->fee_enable = 1;
			$result->terms_conditions = $blueprints->terms_conditions;
			$result->trans_id = 0;
			$result->trns_status = 0;
			$result->trns_status = 0;
			$result->pdf_status = 0;
			$fee_status = $this->db_readonly->select("(case when fcs.fee_collect_status ='STARTED' then 'Paid' else 'Not Paid' end) as fee_collect_status, ft.id as trans_id, ft.status as trns_status, ft.pdf_status,fcs.id as cohort_student_id,fss.id as fee_schecdule_id")
			->from('feev2_cohort_student fcs')
			->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
			->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id','left')
			->where('fcs.blueprint_id',$blueprints->id)
			->where('fcs.student_id',$student_id)
			->get()->row();
			if(!empty($fee_status)){
				$result->status = $fee_status->fee_collect_status;
				$result->trans_id = !empty($fee_status->trans_id) ? $fee_status->trans_id : 0;
				$result->trns_status = !empty($fee_status->trns_status) ? $fee_status->trns_status : 0;
				$result->pdf_status = !empty($fee_status->pdf_status) ? $fee_status->pdf_status : 0;
				$result->cohort_student_id = !empty($fee_status->cohort_student_id) ? $fee_status->cohort_student_id : 0;
				$result->fee_schecdule_id = !empty($fee_status->fee_schecdule_id) ? $fee_status->fee_schecdule_id : 0;
				$result->student_id = $student_id;
			}
		}
		return $result;
	}

	public function submit_parent_details(){
		$data = array(
			'transport_required' => 1,
			'nearest_land_mark' => $this->input->post('nearest_land_mark'),
			'google_maps_link' => $this->input->post('google_maps_link'),
			'transport_request_status' => 'Pending',
			'transport_mode'=>'School Bus',
			'transport_details_created_on'=>$this->Kolkata_datetime(),
			'transport_details_created_by'=>$this->authorization->getAvatarId()
		);
		$this->db->where('student_admission_id',$this->input->post('loggedInstudentId'));
		$this->db->where('acad_year_id',$this->input->post('acad_year_id'));
		return $this->db->update('student_year',$data);
	}

	public function insert_discount_concession_details_temp($transaction_id, $schedId,  $concessionArry){

		if (!empty($concessionArry)) {
			$totalAmount = 0;
			foreach ($concessionArry as $installment) {
				$totalAmount += array_sum($installment);
			}
			if ($totalAmount > 0) {
				$data = array(
					'feev2_predefined_name' => 'Discount Concession Online Payment',
					'concession_amount' => $totalAmount,
					'fee_student_schedule_id' => $schedId,
					'created_by' => $this->authorization->getAvatarId(),
					'created_on' => $this->Kolkata_datetime(),
					'remarks' => 'Online Payment',
					'transaction_id' => $transaction_id
				);
				return $this->db->insert('feev2_concessiontype2_pre_defined_concession_temp', $data);
			}
		}
	}

	public function store_edit_history($old_data,$new_data){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$edit_history = array(
			'student_id' => $studentId,
			'old_data' => $old_data,
			'new_data' => $new_data,
			'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
			'edited_on' => $this->Kolkata_datetime(),
			'source' => 'Parent'
		);
		$this->db->insert('student_edit_history',$edit_history);
	}

	public function get_fee_transaction_total_amount_paid($sourceIds){
		$totalAmountPaid = $this->db->select('SUM(amount_paid) as totalAmountPaid')
				->from('feev2_transaction')
				->where_in('id', $sourceIds)
				->get()->row();
		if($totalAmountPaid){
			return $totalAmountPaid->totalAmountPaid;
		} else {
			return 0;
		}
	}

	public function store_transport_edit_history($studentId) {
		$transportation_mode = $this->input->post('transport') == 1 ? 'School Bus' : $this->input->post('transportation_mode');
		$transport_addition_details = $this->input->post('transport') == 1 ? '' : $this->input->post('transport_addition_details');
	
		$old = $this->db->select('transport_mode,stop,pickup_mode,drop_stop,transportation_additional_details,transport_required,nearest_land_mark,google_maps_link,transport_request_status')
					   ->from('student_year')->where('student_admission_id', $studentId)->where('acad_year_id',$_POST['acad_year_id'])->get()->row();
	
		$old_val = $new_val = '';
		// Transport Required
		if ($old->transport_required != $this->input->post('transport')) {
			$old_val .= 'Transportation Required: ' . ($old->transport_required ? 'Yes' : 'No') . ' , ';
			$new_val .= 'Transportation Required: ' . ($this->input->post('transport') ? 'Yes' : 'No') . ' , ';
		}
	
		// Stop
		$stop_old = $stop_new = '';
		if (!empty($old->stop)) {
			$stop_old = $this->db->select('name')->from('feev2_stops')->where('id', $old->stop)->get()->row('name');
		}
		if ($this->input->post('stops') && $old->stop != $this->input->post('stops')) {
			$stop_new = $this->db->select('name')->from('feev2_stops')->where('id', $this->input->post('stops'))->get()->row('name');
		}
		if($stop_old != $stop_new){
			$old_val .= 'Stop: ' . $stop_old . ' , ';
			$new_val .= 'Stop: ' . $stop_new . ' , ';
		}
	
		// Pickup Mode
		$pickup_modes = $this->settings->getSetting('transport_mode');
		$pickup_old = $pickup_new = '';
		if (!empty($pickup_modes)) {
			foreach ($pickup_modes as $v) {
				if ($v->value == $old->pickup_mode) $pickup_old = $v->name;
				if ($v->value == $this->input->post('pickup_mode')) $pickup_new = $v->name;
			}
		}
		if ($old->pickup_mode != $this->input->post('pickup_mode')) {
			$old_val .= 'Pickup Mode: ' . $pickup_old . ' , ';
			$new_val .= 'Pickup Mode: ' . $pickup_new . ' , ';
		}
	
		// Nearest Location
		$nearest_old = $old->nearest_land_mark;
		$nearest_new = $this->input->post('nearest_land_mark') ? $this->input->post('nearest_land_mark') : '';
		if($nearest_old != $nearest_new){
			$old_val .= 'Nearest Location: ' . $nearest_old;
			$new_val .= 'Nearest Location: ' . $nearest_new;
		}
		
		// Transport Mode
		if($old->transport_mode != $transportation_mode){
			$old_val .= ' , Transport Mode: ' . $old->transport_mode . ' , ';
			$new_val .= ' , Transport Mode: ' . ($old->transport_mode != $transportation_mode ? $transportation_mode : '');
		}
		
		if($old->transportation_additional_details != $transport_addition_details){
			$old_val .= 'Transport Additional Details: ' . $old->transportation_additional_details;
			$new_val .= 'Transport Additional Details: ' . ($old->transportation_additional_details != $transport_addition_details ? $transport_addition_details : '');
		}
		if(!empty($old_val) && !empty($new_val)){
			$this->db->insert('student_edit_history', [
				'student_id' => $studentId,
				'old_data' => $old_val,
				'new_data' => $new_val,
				'edited_by' => $this->authorization->getAvatarStakeHolderId(),
				'edited_on' => $this->Kolkata_datetime(),
				'source' => 'Parent'
			]);
		}
	}


		public function getProfileCompletionStatus($studentId)
		{
			$checkIdcard = $this->db_readonly->select('itoe.id, itoe.status, ito.id_card_verifier')
			->from('idcard_template_order_entities itoe')
			->join('idcard_template_orders ito','itoe.idcard_template_order_id= ito.id')
			->where("avatar_id", $studentId)
			->where("avatar_type", "student")
			->order_by('idcard_template_order_id', 'DESC')
			->limit(1)
			->get()->row();

			$this->db_readonly->select('id, profile_confirmed, profile_status');
			$this->db_readonly->from('student_year');
			$this->db_readonly->where('student_admission_id', $studentId);
			$this->db_readonly->where('acad_year_id', $this->yearId);
			$status = $this->db_readonly->get()->row();

			// Apply verifier-based filtering logic
			$shouldExclude = false;
			if (!empty($checkIdcard) && !empty($checkIdcard->id_card_verifier)) {
				$verifiers = json_decode($checkIdcard->id_card_verifier, true);
				if (is_array($verifiers) && in_array('by_school_admin', $verifiers) && $checkIdcard->status == 'in review') {
					$shouldExclude = true;
				}
			}

			// Compose the response object
			$result = new stdClass();
			$result->has_idcard = (!empty($checkIdcard) && !$shouldExclude) ? ($checkIdcard->id ? true : false) : false;
			$result->profile_confirmed = $status ? $status->profile_confirmed : null;
			$result->idcard_status = (!empty($checkIdcard) && isset($checkIdcard->status) && !$shouldExclude) ? $checkIdcard->status : null;
			$result->profile_status = $status ? $status->profile_status : null;
			return $result;
		}

		public function get_png_idcards($studentId){
			$checkIdcard = $this->db_readonly->select('id,front_page_img_url,back_page_img_url')
			->from('idcard_template_order_entities')
			->where("avatar_id", $studentId)
			->where("avatar_type", "student")
			->order_by('idcard_template_order_id', 'DESC')
			->limit(1)
			->get()->row();

			$checkIdcard->front_page_img_url= $this->filemanager->getFilePath($checkIdcard->front_page_img_url);
			$checkIdcard->back_page_img_url= $this->filemanager->getFilePath($checkIdcard->back_page_img_url);

			return $checkIdcard;
		}

		public function get_previous_schooling_details($studentId){
			return $this->db_readonly->select("year_id, school_name, class, board, school_address, medium_of_instruction,report_card")
			->where('student_id', $studentId)
			->from('student_prev_school')
			->get()->result();
		}

		public function get_AcadYears(){
			$acad_years = $this->db_readonly->select("id, acad_year")
			->from("academic_year")
			->where("id<=", $this->yearId)
			->get()->result();
			return $acad_years;
		}

		public function add_previous_school_details($studentId){
			$year_id = $this->input->post('year_id');
			$school_name = $this->input->post('school_name');
			$class = $this->input->post('class');
			$board = $this->input->post('board');
			$school_address = $this->input->post('school_address');
			$medium_of_instruction = $this->input->post('medium_of_instruction');
			$report_card = $this->input->post('report_card');
			$university = $this->input->post('university');
			$period = $this->input->post('period');
			$subjects = $this->input->post('subjects');
			$registration_number = $this->input->post('registration_number');
			$total_marks = $this->input->post('total_marks');
			$total_marks_obtained = $this->input->post('total_marks_obtained');
			$prevSchool = array(
				'student_id' => $studentId,
				'year_id' =>$year_id,
				'school_name' =>$school_name,
				'class' =>$class,
				'board' =>$board,
				'school_address' =>$school_address,
				'medium_of_instruction' =>$medium_of_instruction,
				'report_card' =>$report_card,
				'university_name' =>$university,
				'period' =>$period,
				'subjects' =>$subjects,
				'registration_no' =>$registration_number,
				'total_marks' =>$total_marks,
				'total_marks_scored' =>$total_marks_obtained
			);
			return $this->db->insert('student_prev_school', $prevSchool);
		}
		public function get_previous_school_details_by_id($school_id){
			return $this->db_readonly->select("year_id, school_name, class, board, school_address, medium_of_instruction,report_card,university_name,period,subjects,registration_no,total_marks,total_marks_scored")
			->where('id', $school_id)
			->from('student_prev_school')
			->get()->row();
		}
		public function delete_previous_school($school_id){
			return $this->db->where('id', $school_id)->delete('student_prev_school');
		}
		public function update_previous_school(){
			// echo '<pre>';print_r($this->input->post());die();
			$id = $this->input->post('school_id');
			$year_id = $this->input->post('year_id');
			$school_name = $this->input->post('school_name');
			$class = $this->input->post('class');
			$board = $this->input->post('board');
			$school_address = $this->input->post('school_address');
			$medium_of_instruction = $this->input->post('medium_of_instruction');
			$report_card = $this->input->post('report_card');
			$university = $this->input->post('university');
			$period = $this->input->post('period');
			$subjects = $this->input->post('subjects');
			$registration_number = $this->input->post('registration_number');
			$total_marks = $this->input->post('total_marks');
			$total_marks_obtained = $this->input->post('total_marks_scored');
			$prevSchool = array(
				'year_id' =>$year_id,
				'school_name' =>$school_name,
				'class' =>$class,
				'board' =>$board,
				'school_address' =>$school_address,
				'medium_of_instruction' =>$medium_of_instruction,
				'report_card' =>$report_card,
				'university_name' =>$university,
				'period' =>$period,
				'subjects' =>$subjects,
				'registration_no' =>$registration_number,
				'total_marks' =>$total_marks,
				'total_marks_scored' =>$total_marks_obtained
			);
			// echo '<pre>';print_r($prevSchool);die();
			return $this->db->where('id', $id)->update('student_prev_school', $prevSchool);
		}
	
}

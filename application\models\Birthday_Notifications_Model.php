<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Birthday_Notifications_Model extends CI_Model{

	// private $yearId;
	public function __construct()
	{
		parent::__construct();
		// $this->yearId =  $this->acad_year->getAcadYearId();
	}
	
	public function studentDataforBirthdayList(){
		$acadYearId = $this->settings->getSetting('academic_year_id');
	    $this->db_readonly->select("sd.id as student_id,cs.class_name, cs.section_name, CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name,sd.dob as dob, date_format(sd.dob, '%M %d %Y') as dobDisplay,sd.email as std_email, f.id as father_id,f.mobile_no as f_mobile,f.email as f_email,m.id as mother_id,m.mobile_no as m_mobile,m.email as m_email,CONCAT(ifnull(f.first_name,''), ' ', ifnull(f.last_name,'')) AS father_name,CONCAT(ifnull(m.first_name,''), ' ', ifnull(m.last_name,'')) AS mother_name");
	    // $this->db_readonly->select("ss.id as student_id");
	    $this->db_readonly->from('student_admission sd');
	    $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id');
	    $this->db_readonly->where('not isnull(sd.dob)');
	    $this->db_readonly->where('ss.acad_year_id', $acadYearId);
	    $this->db_readonly->where('DATE_FORMAT(sd.dob,"%m-%d") = DATE_FORMAT(CURRENT_DATE,"%m-%d")');
	    $this->db_readonly->where('sd.admission_status', '2'); // Approved 2
		$this->db_readonly->where('ss.promotion_status!=', '4');
    	$this->db_readonly->where('ss.promotion_status!=', '5');
	    $this->db_readonly->join('student_relation as sr1','sr1.std_id=sd.id');
		$this->db_readonly->join('parent as f',"f.id=sr1.relation_id and sr1.relation_type='Father'");
		$this->db_readonly->join('student_relation as sr2','sr2.std_id=sd.id');
		$this->db_readonly->join('parent as m',"m.id=sr2.relation_id and sr2.relation_type='Mother'");
	    $this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id', 'left');
	    $result = $this->db_readonly->get()->result();
	    return $result;
	}
	
	public function fatherDataforBirthdayList(){
		$acadYearId = $this->settings->getSetting('academic_year_id');
		$this->db->select("ss.id as student_id,cs.class_name, cs.section_name, CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name,sd.dob as dob, date_format(sd.dob, '%M %d %Y') as dobDisplay,p.id as parent_id,p.mobile_no as parent_mobile,p.email as parent_email,CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) AS parent_name");
		$this->db->from('student_admission sd');
		$this->db->join('student_year ss', 'sd.id=ss.student_admission_id');
		$this->db->where('not isnull(sd.dob)');
		$this->db->where('ss.acad_year_id', $acadYearId);
		$this->db->where('DATE_FORMAT(sd.dob,"%m-%d") = DATE_FORMAT(CURRENT_DATE,"%m-%d")');
		$this->db->where('sd.admission_status', '2'); // Approved 2
		$this->db->where('ss.promotion_status!=', '4');
    	$this->db->where('ss.promotion_status!=', '5');
		$this->db->join('student_relation as sr','sr.std_id=sd.id');
		$this->db->join('parent as p',"p.id=sr.relation_id and sr.relation_type='Father'");
		$this->db->join('class_section cs', 'ss.class_section_id=cs.id', 'left');
		$result = $this->db->get()->result();
		return $result;
	}
	
	public function motherDataforBirthdayList(){
		$acadYearId = $this->settings->getSetting('academic_year_id');
		$this->db->select("ss.id as student_id,cs.class_name, cs.section_name, CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name,sd.dob as dob, date_format(sd.dob, '%M %d %Y') as dobDisplay,p.id as parent_id,p.mobile_no as parent_mobile,p.email as parent_email,CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) AS parent_name");
		$this->db->from('student_admission sd');
		$this->db->join('student_year ss', 'sd.id=ss.student_admission_id');
		$this->db->where('not isnull(sd.dob)');
		$this->db->where('ss.acad_year_id', $acadYearId);
		$this->db->where('DATE_FORMAT(sd.dob,"%m-%d") = DATE_FORMAT(CURRENT_DATE,"%m-%d")');
		$this->db->where('sd.admission_status', '2'); // Approved 2
		$this->db->where('ss.promotion_status!=', '4');
    	$this->db->where('ss.promotion_status!=', '5');
		$this->db->join('student_relation as sr','sr.std_id=sd.id');
		$this->db->join('parent as p',"p.id=sr.relation_id and sr.relation_type='Mother'");
		$this->db->join('class_section cs', 'ss.class_section_id=cs.id', 'left');
		$result = $this->db->get()->result();
		return $result;
	}

	public function staffDataforBirthdayList(){
		$this->db_readonly->select("sm.id as staff_id,CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) AS staff_name, u.email as stf_email, a.avatar_type as avatar_type");
		$this->db_readonly->from('staff_master sm');
		$this->db_readonly->join('avatar a', 'a.stakeholder_id = sm.id');
		$this->db_readonly->join('users u', 'u.id = a.user_id');
		$this->db_readonly->where('a.avatar_type',4);
		$this->db_readonly->where('sm.status',2);
		$this->db_readonly->where('DATE_FORMAT(sm.dob,"%m-%d") = DATE_FORMAT(CURRENT_DATE,"%m-%d")');
		return $this->db_readonly->get()->result();
	}
	public function getBirthdayEmailTemplate(){
		$this->db_readonly->select('*');
		$this->db_readonly->from('email_template');
		$this->db_readonly->where('name',"birthday wishes email to parents");
		return $this->db_readonly->get()->row();
	}

	public function getBirthdayEmailTemplateForStudents(){
		$this->db_readonly->select('*');
		$this->db_readonly->from('email_template');
		$this->db_readonly->where('name', "birthday student email template");
		return $this->db_readonly->get()->row();
	}

	public function getBirthdayEmailTemplateForStaff(){
		$this->db_readonly->select('*');
		$this->db_readonly->from('email_template');
		$this->db_readonly->where('name', "birthday staff email template");
		return $this->db_readonly->get()->row();
	}

	public function getBirthdayInfoEmailTemplateForMember(){
		$this->db_readonly->select('*');
		$this->db_readonly->from('email_template');
		$this->db_readonly->where('name', "birthday info for members");
		return $this->db_readonly->get()->row();
	}

	public function membersDataForBirthdayInfo($members_email){
		if (strpos($members_email, ', ') != false) {
			$emails = explode(', ', $members_email);
		} else {
			$emails = explode(',', $members_email);
		}

		$emails = array_map('trim', $emails);
		$emails = array_map('strtolower', $emails);

		$this->db->select("sm.id as staff_id, a.avatar_type as avatar_type, u.email as stf_email");
		$this->db->from('staff_master sm');
		$this->db->join('avatar a', 'a.stakeholder_id = sm.id');
		$this->db->join('users u', 'u.id = a.user_id');
		$this->db->where('a.avatar_type', 4);
		$this->db->where('u.active', 1);
		$this->db->where('sm.status', 2);
		$this->db->where_in('LOWER(u.email)', $emails);
		$result = $this->db->get()->result();
		return $result;
	}
}

?>
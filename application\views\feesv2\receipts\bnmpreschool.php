<div class="row">
  <div class="panel panel-default" >
    <div class="panel-heading" style="border-bottom: none;">
      <h3 id="stu_print"  class="panel-title" >Fee Receipt</h3>
        <ul class="panel-controls">
         <?php 
          if ( !empty($cancel)== 1) { ?>
            <a class="btn btn-info" id="stu_print" onclick="close_window()"  href="javascript:void(0)"><i class="fa fa-mail-reply"></i>Close</a>
          <?php }else{ ?>
            <a class="btn btn-info" id="stu_print" href="<?php echo site_url('feesv2/fees_collection/fee_student_blueprints/'.$fee_trans->student_id);?>"><i class="fa fa-mail-reply"></i>Next fee collection</a>
          <?php } ?>
          <button id="stu_print" class="btn btn-danger" onclick="print_receipt()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
        </ul>
    </div>
    <div class="panel-body" id="printArea">
      <div class="col-md-12" >
        <?php for ($m=1; $m <= 1; $m++) {
            switch ($m) {
            case '1':
              $copy = '(Student Copy)';
              break;
            case '2':
              $copy = '(Office Copy)';
              break;
            }
         ?>
            <?php if ($cancel == 1) {
              echo "<h3 class='canceled'>Cancelled</h3>";  
            } ?>
            <table class="table table-border" id="header" style="margin-top:12px;margin-bottom:0px;border-bottom:none">
              <tr>
                <td   width="20%" style="text-align: center;">  
                  <img height="80px" width="110px" style="padding:6px;" class="logo" src="<?php echo base_url().$this->settings->getSetting('school_logo'); ?>">
          </td> 
          <td style="line-height:16px">
                  <p style="font-size:16px;padding-left:190px;padding-top:10px"><b><?php echo $this->settings->getSetting('school_name'); ?></b></p>
                  <p style="padding-left:140px;font-size:12px;"><?php echo $this->settings->getSetting('school_name_line1'); ?></p>
                  <p style="padding-left:115px;font-size:12px;margin:0px;"><?php echo $this->settings->getSetting('school_name_line2'); ?></p>
                </td>
              </tr>
            </table>
            <table class="table table-border" style="margin-bottom:0px;margin-top:0px">
            <thead >
              <tr>
                <td colspan="2" style="text-align: center"><span style="font-size: 18px; "><b>Fee Receipt </b></span></td>
              </tr>
              </thead>
              <tbody >
              
              <tr>
                <td ><b>Student Name: </b> <?= $fee_trans->student->stdName;?></td>
                <td ><b>Academic year paid : </b> <?php echo $this->acad_year->getAcadYearById($fee_trans->no_of_comp->acad_year_id); ?></td>
              </tr>
              <tr>
                <td ><b>Father Name:  </b> <?= $fee_trans->student->fName;?></td>
                <td ><b>Receipt No: </b> <?= $fee_trans->receipt_number;?></td>
              </tr>
              <tr>
                <td ><b>Mother Name: </b> <?= $fee_trans->student->mName;?></td>
                <td ><b>Date : </b> <?php echo date('d-m-Y', strtotime($fee_trans->paid_datetime)) ?> </td>
              </tr>
              <tr>
               <td ></td>
                <td ><b>Admission No: </b> <?= $fee_trans->student->admission_no;?></td>
               
              </tr>
              <tr>
                <td ></td>
                <td ><b>Class: </b>  <?= $fee_trans->student->clsName.''.$fee_trans->student->section_name;?></td>
              </tr>
          </tbody>
          
              
            </table>
            <table class="table table-border " style="border-top:none;margin-top:0px;margin-bottom:0px;border-bottom:none">
                <thead >
                  <tr>
                    <th width="10%" >Sl No</th>
                    <th width="25%">Installment</th>
                    <th >Particulars</th>
                    <th >Amount (Rs.)</th>
                  </tr>
                </thead>
                <tbody >
                <?php 
                $i=1; 
                $cnt= 0; 
                $sl=0;
                $totalAmount = 0; 
                $zeroAmount = 0;

                foreach ($fee_trans->transInsComp as $insName => $comp) {
                  $compCount =  count($comp);
                  foreach ($comp as $key => $val) {  
                    $totalAmount += $val->amount_paid;
                    $zeroAmount = $val->amount_paid; ?>
                    <tr>
                      <?php if(!$sl) { ?>
                      <td  rowspan="<?php echo $compCount ?>"><?php echo $i++ ?></td>
                      <?php $sl = $compCount; }
                      $sl--;
                      ?>
                      <?php if(!$cnt) { ?>
                      <td  rowspan="<?php echo $compCount ?>" ><?= $insName ?></td>
                      <?php $cnt = $compCount; }
                      $cnt--;
                      ?>
                    <?php if ($zeroAmount !=0) { ?>
                      <td ><?php echo $val->compName ?></td>
                      <td ><?php echo number_format($val->amount_paid,2,'.','');  ?></td>
                    <?php } ?>
                    </tr>
                  <?php }
                } ?>
              </tbody>

             <tfoot >
                <tr>
                  <td colspan="3" >Total Fee</td>
                  <td ><?php echo number_format($totalAmount,2,'.',''); ?></td> 
                </tr>
                                
                <?php if ($fee_trans->discount_amount != 0) { ?>
                  <tr>
                    <td colspan="3" >Discount (-)</td>
                    <td ><?php echo $fee_trans->discount_amount ?></td>
                  </tr>
                <?php } ?>

                <?php if ($fee_trans->fine_amount != 0) { ?>
                  <tr>
                    <td colspan="3" >Fine Amount</td>
                    <td ><?php echo $fee_trans->fine_amount ?></td>
                  </tr>
                <?php } ?>

                <?php if ($fee_trans->card_charge_amount != 0) { ?>
                  <tr>
                    <td colspan="3" >Card Charge Amount</td>
                    <td ><?php echo $fee_trans->card_charge_amount ?></td>
                  </tr>
                <?php } ?>
                <tr>
                  <td colspan="3" >Total Amount Paid</td>
                  <td ><?php echo number_format($fee_trans->amount_paid + $fee_trans->fine_amount,2,'.','') ?></td> 
                </tr>
                <tr>
              <td colspan="4"><strong>Rupees in Words: </strong><span id="words_amount<?php echo $m; ?>"></span></td>
            </tr>
              </tfoot>
            </table>
          <table class="table table-border" style="border-top:none;margin-top:0px;margin-bottom:0px;border-bottom:none">
              <?php 
              $payment_mode = '';
              $bankname = '';
              $instrument = 'NA';
              $cheque_dd_net_date = '';
                if ($fee_trans->payment_type == '10') {
                  $payment_mode = 'Online';
                }else{
                  foreach ($payment_modes as $key => $type) {
                    if ($type->value == $fee_trans->payment_type) { 
                      $payment_mode = strtoupper($type->name);
                      $bankname = $fee_trans->bank_name;
                      $instrument = $fee_trans->cheque_dd_nb_cc_dd_number;
                      $cheque_dd_net_date = date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date));
                    }
                  }
                }
              ?>
              <tr>
                <td ><b>Payment Mode  </b> <?php echo $payment_mode ?></td>                
                <td style="text-align:right;"><b>Bank :</b> <?php echo $bankname ?></td>
                
              </tr>
              <tr>
                <td ><strong>Ref No:  </strong> <?php echo $instrument ?></td>
                <td style="text-align:right"><b> Date  </b> <?php echo $cheque_dd_net_date ?></td>                
              </tr>
              <tr>
              <td colspan="2"><strong>Remarks: </strong> <?php  echo($fee_trans->transaction_mode =='ONLINE') ? 'NEFT- AGGREPAY PAYMENTS SOLUTIONS' : $fee_trans->remarks ?></td>
              
              </tr>
              <?php if (!empty($fee_trans->bal_ins)) {
              
                $balance = 0;
               foreach ($fee_trans->bal_ins as $key => $bal) { 
                $balance += $bal->ins_balance;
              } ?>
              <tr>
                <td  colspan="2" ><strong>Balance Amount : </strong> <?=  number_format($balance,2,'.',''); ?></td>
                
              </tr> 
            
          <?php } ?>
          
            </table>
            <table class="table table-border">
              <tr>
                <td colspan="4"><strong>Balance Installment Details</strong></td>
            </tr>
            <tr>
              <th>Particulars</th>
              <th>Amount</th>
              <th>Due Date</th>
            </tr>
            
            <?php

foreach ($fee_trans->bal_ins as $key => $bal) {
    if ($bal->ins_balance == 0) {
       $bal->ins_balance = false;
        continue; 
    }
?>
    <tr>
        <td><?php echo $bal->installment_name ?></td>
        <td><?php echo number_format($bal->ins_balance, 2, '.', '') ?></td>
        <td><?php echo $bal->due_date ?></td>
    </tr>
<?php
}
?>

          </table>
            <table class="table table-border" style="width: 100%;margin-top:0px;border-top:none">
            <tr>
              <td ><strong>Note:</strong> Fees once paid will not be refunded</td>
            </tr>   
            <tr>
              <td style="text-align:center">This is a system generated receipt, No signature is required.</td>
            </tr>          
          </table>
          </div>
      <?php } ?>
    </div>
  </div>
</div>



<script type="text/javascript">
  function close_window() {
    window.close();
  }
</script>

<style type="text/css">
  .canceled{
  position: absolute;
  z-index: 99999;
  top: 155px;
  left:0;
  transform: rotate(332deg);
  font-size: 120px;
  opacity: 0.1;
}

.tr>th>td {
    border:2px solid #000;
  }

</style>
<style type="text/css"> @page { size: auto } </style>
<script type="text/javascript">

  function print_receipt(){
    $('.col-md-6').css('width','50%');
    $('.col-md-6').css('float','right');
    $('.canceled').css('position', 'absolute');
    $('.canceled').css('z-index', '99999');
    $('.canceled').css('font-size','120px');
    $('.canceled').css('top','155px');
    $('.canceled').css('left','0');
    $('.canceled').css('transform','rotate(332deg)');
    $('.canceled').css('opacity','0.1');
    $('.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td').css('font-size', '10px');
    $('.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td').css('border','1px solid #000');
    $('.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td').css('padding','4px');
    
    var restorepage = document.body.innerHTML;
    var printcontent = document.getElementById('printArea').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }

 $(document).ready(function() {
    var amount="<?php echo $fee_trans->amount_paid + $fee_trans->fine_amount?>";
    var words = new Array();
    words[0] = 'Zero';
    words[1] = 'One';
    words[2] = 'Two';
    words[3] = 'Three';
    words[4] = 'Four';
    words[5] = 'Five';
    words[6] = 'Six';
    words[7] = 'Seven';
    words[8] = 'Eight';
    words[9] = 'Nine';
    words[10] = 'Ten';
    words[11] = 'Eleven';
    words[12] = 'Twelve';
    words[13] = 'Thirteen';
    words[14] = 'Fourteen';
    words[15] = 'Fifteen';
    words[16] = 'Sixteen';
    words[17] = 'Seventeen';
    words[18] = 'Eighteen';
    words[19] = 'Nineteen';
    words[20] = 'Twenty';
    words[30] = 'Thirty';
    words[40] = 'Forty';
    words[50] = 'Fifty';
    words[60] = 'Sixty';
    words[70] = 'Seventy';
    words[80] = 'Eighty';
    words[90] = 'Ninety';
    amount = amount.toString();
    var atemp = amount.split(".");
    var number = atemp[0].split(",").join("");
    var n_length = number.length;
    var words_string = "";
    if (n_length <= 9) {
        var n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);
        var received_n_array = new Array();
        for (var i = 0; i < n_length; i++) {
            received_n_array[i] = number.substr(i, 1);
        }
        for (var i = 9 - n_length, j = 0; i < 9; i++, j++) {
            n_array[i] = received_n_array[j];
        }
        for (var i = 0, j = 1; i < 9; i++, j++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                if (n_array[i] == 1) {
                    n_array[j] = 10 + parseInt(n_array[j]);
                    n_array[i] = 0;
                }
            }
        }
        value = "";
        for (var i = 0; i < 9; i++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                value = n_array[i] * 10;
            } else {
                value = n_array[i];
            }
            if (value != 0) {
                words_string += words[value] + " ";
            }
            if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Crores ";
            }
            if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Lakhs ";
            }
            if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Thousand ";
            }
            if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {
                words_string += "Hundred and ";
            } else if (i == 6 && value != 0) {
                words_string += "Hundred " ; 
            }
        }

         words_string = words_string.split(" ").join(" ")  +"Rupees Only";
    }  
    $('#words_amount1').html(words_string);
    $('#words_amount2').html(words_string);
});     
</script>

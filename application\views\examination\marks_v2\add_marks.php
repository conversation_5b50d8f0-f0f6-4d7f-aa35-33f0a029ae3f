<ul class="breadcrumb">
	<li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
	<li><a href="<?php echo site_url('examination/Assessments/index'); ?>">Examination</a></li>
	<li><a href="<?php echo site_url('examination/assessment_marks_v2/index/' . $section->class_id); ?>">Assessments</a>
	</li>
	<li><a
			href="<?php echo site_url('examination/assessment_marks_v2/marks_entry_subjects/' . $assessment->id . '/' . $section->class_id); ?>">Subjects</a>
	</li>
	<li> Add Student Marks</li>
</ul>

<hr>

<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px;">
				<div class="d-flex justify-content-between" style="width:100%;">
					<h3 class="card-title panel_title_new_style_staff" style="">
						<a class="back_anchor"
							href="<?php echo site_url('examination/assessment_marks_v2/marks_entry_subjects/' . $assessment->id . '/' . $section->class_id); ?>">
							<span class="fa fa-arrow-left"></span>
						</a>
						Marks entry for <strong>
							<?php echo $assessment->long_name; ?>
						</strong>of class/section <strong>
							<?php echo $section->class_name . '/' . $section->section_name; ?>
						</strong>
					</h3>
					<!-- <span class="pull-right"><a style="margin-right:3px;" onclick="exportToExcel()" class="btn btn-primary">Export</a></span> -->
					<!-- <ul class="panel-controls">
			<li>
				<a href="" class="new_circleShape_res" style="background-color: #fe970a;" data-toggle="modal" data-target="#sub_event_modal">
				  <span class="fa fa-plus" style="font-size: 19px;"></span>
				</a>
			  </li>
			  <li>
				<a href="" class="new_circleShape_res" data-placement="top" title="Upload CSV file" style="background-color: #1caf9a;" data-toggle="modal" data-target="#upload_sub_event_modal">
				  <span class="fa fa-upload" style="font-size: 19px;"></span>
				</a>
			  </li>
			   <li>
				<button onclick="createAndDownloadCSVForStudentsMarksEntry()" class="new_circleShape_res"
				  data-placement="top" title="Download Uploaded Format file" style="background-color: #6793ca;">
				  <span class="fa fa-download" style="font-size: 19px;"></span>
				</button>
			  </li>
		  </ul> -->

					<div class="col-md-8 d-flex align-items-center justify-content-end" id="exportButtons" style="">
						<div class="" style="">
							<div class="more">
								<button id="more-btn" class="more-btn">
									<span class="more-dot"></span>
									<span class="more-dot"></span>
									<span class="more-dot"></span>
								</button>
								<div class="more-menu" style="right: 2%;">
									<div class="more-menu-caret">
										<div class="more-menu-caret-outer"></div>
										<div class="more-menu-caret-inner"></div>
									</div>
									<!-- <div class="more-menu-item action_btn" data-button-type="download"
										role="presentation">
										<button type="button" class="more-menu-btn" role="menuitem"><i
												class="fa fa-download" aria-hidden="true"></i> Download
											Template</button>
									</div> -->
									<div class="more-menu-item action_btn" data-button-type="download"
										role="presentation">
										<button onclick="downloadTableAsCSV()" type="button" class="more-menu-btn" role="menuitem"><i
												class="fa fa-download" aria-hidden="true"></i> Download
											Template</button>
									</div>

									<div class="more-menu-item action_btn" data-button-type="upload" role="presentation"
										data-toggle="modal" data-target="#upload_sub_event_modal">
										<button type="button" class="more-menu-btn" role="menuitem" data-toggle="modal"
											data-target="#upload_sub_event_modal"><i class="fa fa-upload"
												aria-hidden="true"></i>Upload</button>
									</div>

									<div class="more-menu-item action_btn" data-button-type="export" role="presentation"
										onclick="">
										<button type="button" class="more-menu-btn" role="menuitem"><i
												class="fa fa-file-excel-o" aria-hidden="true"></i>Export</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

		</div>

		<div class="card-body pt-1">
			<?php if (empty($entities)) {
				echo '<h5>You do not have access to add marks. Contact your administrator.</h5>';
			} else {
				?>
				<form method="post" data-parsley-validate="" class="form-horizontal" id="marksForm"
					action="<?php echo site_url('examination/assessment_marks_v2/save_subject_marks'); ?>">
					<div class="d-flex justify-content-between align-items-center">
						<span style="font-weight:bold;color:green;">
							<strong>Notes: </strong><br>
							<ul>
								<li>For Absentees - Enter -1; For Not Applicable - Enter -3; For To Be Done - Enter -2;Enter -5; For Exempt;</li>
								<li>Click 'Save Student Marks' in case you want to 'Save' the entered marks and come back
									later</li>
								<li>Click on 'Lock Student Marks', in case you are ready to finalize marks entry</li>
							</ul>
						</span>

					</div>
					<div class="d-flex">
						<button id="lockBtn" type="button" class="btn action-btn btn-primary pull-right"
							onclick="changeStatus()">Lock Student Marks</button>
						<button type="button" id="saveBtn" class="btn action-btn btn-primary pull-right">Save Student
							Marks</button>

					</div>

					<div class="d-flex justify-content-between align-items-center">
						<div class="d-flex">
						</div>
					</div>

					<div class="mt-3">
						<input type="hidden" name="is_fresh_entry" value="<?php echo $isFreshEntry; ?>">
						<input type="hidden" name="ass_id" value="<?php echo $assessment->id; ?>">
						<input type="hidden" name="class_id" value="<?php echo $section->class_id; ?>">
						<input type="hidden" id="status" name="status" value="1">
						<input type="hidden" id="verifiedStaff" name="verifiedStaff" value="0">
						<?php foreach ($selected_entities as $subId) { ?>
							<input type="hidden" name="subject_id[]" value="<?php echo $subId ?>">
						<?php } ?>
						<input type="hidden" name="section_id" value="<?php echo $section->section_id ?>">

						<?php
						if (empty($entities)) {
							echo "You don't have permissions to read or change marks.";
						} else {
							$len = count($entities);
							if ($len <= 1) {
								$width = "40%";
							} else if ($len <= 2) {
								$width = "50%";
							} else if ($len <= 3) {
								$width = "75%";
							} else {
								$width = "80%";
							}
							echo "<table id='marksTable' class='table table-bordered table-striped' style='width:$width'>";

							?>
							<thead>
								<tr>
									<th width="5%" class="csv_header">#</th>
									<th width="25%" style="min-width:200px;" class="csv_header">Student</th>
									<th   style="min-width:150px;" class="csv_header">Enrollment Number/Admission  Number</th>
									<?php $aeIds = [];
									foreach ($entities as $aeId => $entity) {
										if ($entity->access_level == 'write' || $is_exam_admin) { ?>
											<input type="hidden" name="entity_ids[]" value="<?php echo $entity->entity_id; ?>">
											<input type="hidden" name="aeIds[]" value="<?php echo $aeId; ?>">
											<input type="hidden" name="total[<?php echo $aeId; ?>]"
												value="<?php echo $entity->total_marks; ?>">
											<input type="hidden" name="group_id[]" value="<?php echo $entity->ass_entity_gid ?>">
											<?php
											array_push($aeIds, $aeId);
										}
										?>
										<th  style="word-break:break-word;" class="csv_header">
											<?php echo $entity->name; ?> (
											<?php echo $entity->total_marks; ?>)
										</th>
									<?php } ?>
								</tr>
							</thead>
							<tbody>
								<?php $std_groups = [];
								$marksLocked = 0;
								$sl_no = 1;
								foreach ($students as $key => $student) { ?>
									<tr>
										<td>
											<?php echo $sl_no;
											$sl_no++; 
											$color= '';
											$notice= '';
											if($student->admission_status == '4' || $student->promotion_status == '4') {
												$notice= 'Alumni';
												$color= '#b4a1a1; font-style: italic;';
											} else if($student->admission_status == '5' || $student->promotion_status == '5') {
												$notice= 'Partial-term';
												$color= '#b4a1a1; font-style: italic;';
											}
											?>
										</td>
										<td style="color: <?php echo $color; ?>" class="student_name" data-student-name="<?php echo $student->student_name; ?>">
											<?php echo $student->student_name; if($notice) {echo '('. $notice. ')';} ?>
										</td>
										<td> <?php echo $student->enrollment_number == "NA" ? $student->admission_no : $student->enrollment_number; ?></td>
										<?php foreach ($entities as $aeId => $ent) { ?>
											<td class="subject_class_<?php echo $student->student_id; ?> subject_class">
												<input type="hidden" name="stat" value="<?php echo ($student->{$aeId})->status; ?>">
												<?php
												//If elective, just continue, no more display required
												if (($student->{$aeId})->is_elective == '1' && ($student->{$aeId})->has_elected == '0') {
													echo "<span style='color:lightgrey'>Not Selected</span>";
													continue;
												}

												//Check if the marks/grades is locked
												$disable = '';
												$class = 'marksClass';
												if (($student->{$aeId})->status == 2) {
													$disable = 'disabled';
													$class = "";
													$marksLocked = 1;
												}

												$subjectName = '';

												//Display
												$subjectName = $ent->name . " (" . $ent->total_marks . ")";
												if ($ent->evaluation_type == 'marks') {
													$marks = ($student->{$aeId})->marks;
													// echo $subjectName;
													if($marks == '-2.00') {
														$marks_visile= '';
													} else {
														$marks_visile= $marks;
													}
													echo '<input id="' . $subjectName . '" class="marks_class" ' . $disable . ' data-total_marks="" class="form-control ' . $class . '" type="number" step="0.5" value="' . $marks_visile . '" max="' . $ent->total_marks . '" name="marks_' . $aeId . '[' . $student->student_id . ']" style="font-size: 14px;font-weight: 700;">';
													echo '<input class="form-control" type="hidden" value="' . $marks . '" name="oldmarks_' . $aeId . '[' . $student->student_id . ']" >';
												} else {
													$select = '<select id="' . $subjectName . '" ' .  $disable . ' class="form-control ' . $class . '" name="grades_' . $aeId . '[' . $student->student_id . ']" style="font-size: 14px;font-weight: 700;">';
													$grade = ($student->{$aeId})->grade;
													$tbd = '';
													if ($grade == '' || $grade == NULL) {
														$tbd = 'selected';
														$grade = 'TBD';
													}
													$match = '/^' . $grade . '/';
													$select .= '<option value="" ' . $tbd . '>TBD</option>';
													if ($ent->grades != NULL) {
														foreach ($ent->grades as $key => $gr) {
															
															$sel = '';
															if ($gr->grade == $grade) {
																$sel = 'selected';
															}
															$select .= '<option value="' . $gr->grade . '" ' . $sel . '>' . $gr->grade . '</option>';
														}
													}
													$Ab = '';
													if ($grade == 'AB') {
														$Ab = 'selected';
													}
													$Ex = '';
													if ($grade == 'Ex') {
														$Ex = 'selected';
													}
													$select .= '<option value="AB" ' . $Ab . '>AB</option>';
													$select .= '<option value="Ex" ' . $Ex . '>Ex</option>';
													$select .= '</select>';
													echo $select;
													echo '<input ' . $disable . ' class="form-control" type="hidden" value="' . $grade . '" name="oldgrades_' . $aeId . '[' . $student->student_id . ']" >';
												}
												?>
											</td>
										<?php } ?>
									</tr>
								<?php } ?>
							</tbody>
							</table>
						<?php } ?>
					</div>

				</form>
			<?php } //Empty ?>
		</div>
	</div>

	<div class="modal fade" id="upload_sub_event_modal" tabindex="-1" role="dialog"
		aria-labelledby="upload_sub_event_modalLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content" style="width: 52%;margin: auto;margin-top: 8%;border-radius: .75rem;">
				<div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
					<h4 class="modal-title" id="exampleModalLabel">Add Students Marks</h4>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
					</button>
				</div>
				<div class="modal-body">
					<form enctype="multipart/form-data" method="post" id="students_marks_entry" data-parsley-validate=""
						class="form-horizontal">
						<div class="card-body">
							<div class="form-group">
								<label class="control-label col-md-4">Upload CSV <font color="red">*</font></label>
								<div class="col-sm-6">
									<input type="file" required="" onchange="" name="upload_csv" id="upload_csv"
										class="form-control">
								</div>
								<br>
							</div>
							<center>
								<button type="button" id="sub_event_upload" style="width: 9rem; border-radius: .45rem;"
									class="btn btn-primary" onclick="upload_csv_marks_entry()">Upload</button>
							</center>
							<br>
							<br>
							<div class="content_csv_file">

							</div>
						</div>
					</form>

				</div>
			</div>
		</div>
	</div>
</div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js"
	integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>

<script type="text/javascript">
	var save_lock_button_clicked = false;
	$(document).ready(function () {
		$("#marksForm").trackChanges();
		$(window).bind("beforeunload", function (event) {
			if ($("#marksForm").isChanged() && !save_lock_button_clicked) {
				return 'Form data has changed';
			}
		});
	});

	document.querySelector(".more-menu").addEventListener("click", e => {

		if (e.target.closest(".action_btn")?.dataset) {
			// console.log(e.target.closest(".action_btn").dataset.buttonType);

			const currentActionButton = e.target.closest(".action_btn").dataset.buttonType;
			// if (currentActionButton == "download") {
			// 	return createAndDownloadCSVForStudentsMarksEntry();
			// }

			if (currentActionButton == "upload") {
				// return $("#upload_sub_event_modal").trigger("click");
			}

			if (currentActionButton == "export") {
				return exportToExcel();
			}
		}

	});

	function exportToExcel() {
		$(".subject_class").each(function () {
			var marks = $(this).children('.marks_class').val();
			if (marks != undefined)
				$(this).append(`<font>${marks}</font>`);
		});
		var htmls = "";
		var uri = 'data:application/vnd.ms-excel;base64,';
		var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
		var base64 = function (s) {
			return window.btoa(unescape(encodeURIComponent(s)))
		};

		var format = function (s, c) {
			return s.replace(/{(\w+)}/g, function (m, p) {
				return c[p];
			})
		};

		var className = '<?php echo $section->class_name; ?>';
		var sectionName = '<?php echo $section->section_name; ?>';
		var head = '';
		var title = '';

		var classDeatils = '<table><tr><td><strong>Class: </strong>' + className + '</td><td><strong>Section: </strong>' + sectionName + '</td></tr></table><br>';

		var summaryTable = $("table").html();


		htmls = summaryTable;

		var ctx = {
			worksheet: 'Spreadsheet',
			table: htmls
		}


		var link = document.createElement("a");
		link.download = "export.xls";
		link.href = uri + base64(format(template, ctx));
		link.click();

		$(".subject_class").each(function () {

			$(this).children(`font`).remove();
		});

	}

	function printPerf() {
		// $('tr,th,td').css('border', '1px solid #ccc');
		// $("#footer").css('border-width', '0px !important');
		$("#header").show();
		var restorepage = document.body.innerHTML;
		var printcontent = document.getElementById('printArea').innerHTML;
		document.body.innerHTML = printcontent;
		window.print();
		document.body.innerHTML = restorepage;
		$("#header").hide();
	}

	function unlockEntry() {
		$("#unlockBtn").attr('disabled', true).val('Please wait');
		$(".action-btn").attr('disabled', true);
		var aeIds = JSON.parse('<?php echo json_encode($aeIds); ?>');
		$.ajax({
			url: "<?php echo site_url('examination/assessment_marks_v2/unlockMarksStatus'); ?>",
			data: { 'aeIds': aeIds },
			type: 'post',
			success: function (data) {
				location.reload();
			},
			error: function (err) {
				console.log(err);
			}
		});
	}

	$("#saveBtn").click(function () {
		$('.marksClass').attr('required', false);
		if ($('#marksForm').parsley().validate()) {
			$("#status").val(1);
			$('.marksClass').attr('required', false);
			$("#saveBtn").attr('disabled', 'disabled').html('Please wait...');
			$(".action-btn").attr('disabled', true);
			$("#marksForm").submit();
			save_lock_button_clicked = true;
		}
		// setTimeout(function(){ $("#saveBtn").prop('disabled',false).html('Save'); }, 3000);
	});

	function changeStatus() {
		$('.marksClass').attr('required', true);
		var staff = JSON.parse('<?php echo json_encode($staffList) ?>');
		var staffArr = [];
		staffArr.push({ text: 'Select staff', value: '' });
		staffArr.push({ text: 'Nobody verified', value: '0' });
		for (var i = 0; i < staff.length; i++) {
			staffArr.push({ text: staff[i].staffName, value: staff[i].id });;
		}
		$("#status").val(2);
		if ($("#marksForm").parsley().validate()) {
			bootbox.prompt({
				title: "Select staff who verified the marks",
				inputType: 'select',
				inputOptions: staffArr,
				className: 'widthadjust',
				callback: function (result) {
					if (result) {
						$("#verifiedStaff").val(result);
						$("#lockBtn").attr('disabled', 'disabled').html('Please wait...');
						$(".action-btn").attr('disabled', true);
						$("#marksForm").submit();
						save_lock_button_clicked = true;
					}
				}
			});
		} else {
			alert('To lock, all marks needs to be entered. Enter "-3" in case marks are not applicable. Enter "-1" in case of Absence. You can Save and exit if you need to.');
		}
	}

	function upload_csv_marks_entry() {
		var upload_csv = $('#upload_csv').prop('files')[0];

		var $form = $('#students_marks_entry');
		if ($form.parsley().validate()) {
			var form = $('#students_marks_entry')[0];
			var formData = new FormData(form);
			formData.append('file[]', upload_csv);

			$.ajax({
				url: '<?php echo site_url('examination/assessment_marks_v2/upload_students_marks_csv'); ?>',
				type: 'post',
				data: formData,
				// async: false,
				processData: false,
				contentType: false,
				// cache : false,
				success: function (data) {
					const CSVMarksArray = $.parseJSON(data);

					let isEntryTypeMarks=1;
					
					$("#upload_sub_event_modal").trigger("click");
					if (Object.keys(CSVMarksArray)?.length) {
						$('#upload_csv').val("");
						
						let marksElementArray = document.querySelectorAll(".marks_class");

						if(!marksElementArray.length){
							isEntryTypeMarks=0;
							marksElementArray = document.querySelectorAll(".marksClass");
						}
						
						Object.keys(CSVMarksArray).forEach(student => {
							marksElementArray.forEach(m => {
								const studentName = m.closest("tr").querySelector(".student_name").innerText;

								if (CSVMarksArray[student].name.trim().replaceAll("Â","") == studentName.trim()) {
									// const studentRow = m.closest("td").previousElementSibling.closest("tr");
									const studentRow = m.closest("td").closest("tr");

									let allSubjectColumns = studentRow.querySelectorAll(".marks_class");

									if(!allSubjectColumns.length){
										allSubjectColumns = studentRow.querySelectorAll(".marksClass");
									}
									
									allSubjectColumns.forEach((marksEl, i) => {
										const arr=Object.entries(CSVMarksArray[student].subjects[0]);
										arr.forEach((o,i)=>{
											if(o[0].toString().split("(")[0].trim()===marksEl.id.toString().split("(")[0].trim()){
												if(isEntryTypeMarks){
													// for marks entry
													marksEl.value = o[1] || -2;
												}else{
													// for grades entry
													if(o[1]=="TBD") return;
													marksEl.value = o[1];
												}
											}
										});
									})
								}
							});
						})
					} else {
						Swal.fire({
							icon: "error",
							title: "Oops...",
							text: "Something went wrong!",
							// footer: '<a href="#">Why do I have this issue?</a>'
						});
					}
				}
			});
		}
	}

	// get csv header
	const csvHeaderArr = [];
	const csvHeaders = document.querySelectorAll(".csv_header");
	csvHeaders.forEach(h => {
		csvHeaderArr.push(h.innerText);
	});

	const csvStudentsArr = [];
	const csvStudents = document.querySelectorAll(".student_name");
	csvStudents.forEach(s => {
		csvStudentsArr.push(s.innerText);
	});

	// console.log(csvHeaderArr.join(","));
	// console.log(csvStudentsArr);


	const download = function (data) {

		const blob = new Blob([data], { type: 'text/csv' });

		const url = window.URL.createObjectURL(blob)

		const a = document.createElement('a')

		a.setAttribute('href', url)

		a.setAttribute('download', 'download.csv');
		a.click()
	}

	const csvmaker = function () {

		csvRows = [];

		// const headers = Object.keys(data); 

		csvRows.push(csvHeaderArr.join(","));

		csvStudentsArr.forEach((s, i) => {
			csvRows.push(`${++i},${s}`)
		})

		return csvRows.join('\n')
	}

	const createAndDownloadCSVForStudentsMarksEntry = async function () {

		// JavaScript object 
		const data = {
			id: 1,
			name: "Geeks",
			profession: "developer"
		}

		const csvdata = csvmaker();
		download(csvdata);
	}

	// createAndDownloadCSVForStudentsMarksEntry();

	var el = document.querySelector('.more');
	var btn = el.querySelector('.more-btn');
	var menu = el.querySelector('.more-menu');
	var visible = false;

	function showMenu(e) {
		e.preventDefault();
		if (!visible) {
			visible = true;
			menu.style.opacity=1;
			el.classList.add('show-more-menu');
			menu.setAttribute('aria-hidden', false);
			// document.addEventListener('mousedown', hideMenu);
		}else{
			visible=false;
			menu.style.opacity=0;
		}
	}

	// function hideMenu(e) {
	// 	if (btn.contains(e.target)) {
	// 		return;
	// 	}
	// 	if (visible) {
	// 		visible = false;
	// 		el.classList.remove('show-more-menu');
	// 		menu.setAttribute('aria-hidden', true);
	// 		document.removeEventListener('mousedown', hideMenu);
	// 	}
	// }

	btn.addEventListener('click', showMenu);

</script>

<style type="text/css">
	button {
		margin: 2px;
	}

	ul.panel-controls>li>a {
		border-radius: 50%;
	}

	.widthadjust {
		width: 500px;
		margin: auto;
	}

	ition: relative;
	min-height: 640px;
	}

	.container {
		position: absolute;
		top: 50%;
		left: 50%;
		margin-right: -50%;
		transform: translate(-50%, -50%);
		text-align: center;
	}

	.more-menu {
		width: 100px;
	}

	/* More Button / Dropdown Menu */

	.more-btn,
	.more-menu-btn {
		background: none;
		border: 0 none;
		line-height: normal;
		overflow: visible;
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		width: 100%;
		text-align: left;
		outline: none;
		cursor: pointer;
	}

	.more-dot {
		background-color: #aab8c2;
		margin: 0 auto;
		display: inline-block;
		width: 7px;
		height: 7px;
		margin-right: 1px;
		border-radius: 50%;
		transition: background-color 0.3s;
	}

	.more-menu {
		position: absolute;
		top: 100%;
		z-index: 900;
		float: left;
		padding: 10px 0;
		margin-top: 9px;
		background-color: #fff;
		border: 1px solid #ccd8e0;
		border-radius: 4px;
		box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.25);
		opacity: 0;
		transform: translate(0, 15px) scale(.95);
		transition: transform 0.1s ease-out, opacity 0.1s ease-out;
		/* pointer-events: none; */
	}

	.more-menu-caret {
		position: absolute;
		top: -10px;
		right: 12px;
		width: 18px;
		height: 10px;
		float: right;
		overflow: hidden;
	}

	.more-menu-caret-outer,
	.more-menu-caret-inner {
		position: absolute;
		display: inline-block;
		margin-left: -1px;
		font-size: 0;
		line-height: 1;
	}

	.more-menu {
		right: 0% !important;
		width: 20%;
	}

	.more-menu-caret-outer {
		border-bottom: 10px solid #c1d0da;
		border-left: 10px solid transparent;
		border-right: 10px solid transparent;
		height: auto;
		left: 0;
		top: 0;
		width: auto;
	}

	.more-menu-caret-inner {
		top: 1px;
		left: 1px;
		border-left: 9px solid transparent;
		border-right: 9px solid transparent;
		border-bottom: 9px solid #fff;
	}

	.more-menu-items {
		margin: 0;
		list-style: none;
		padding: 0;
	}

	.more-menu-item {
		display: block;
	}

	.more-menu-btn {
		min-width: 100%;
		color: #66757f;
		cursor: pointer;
		display: block;
		font-size: 13px;
		line-height: 18px;
		padding: 5px 20px;
		position: relative;
		white-space: nowrap;
	}

	.more-menu-item:hover {
		background-color: #489fe5;
	}

	.more-menu-item:hover .more-menu-btn {
		color: #fff;
	}

	.more-btn:hover .more-dot,
	.show-more-menu .more-dot {
		background-color: #516471;
	}

	.show-more-menu .more-menu {
		opacity: 1;
		transform: translate(0, 0) scale(1);
		pointer-events: auto;
	}

	.fa {
		padding: 0 6px;
	}
</style>

<script>
	function downloadTableAsCSV() {
    // Get the table element
    const table = document.querySelector('#marksTable');
    
    // Initialize CSV content
    let csv = [];
    
    // Process headers
    const headerRows = table.querySelectorAll('thead tr');
    for (const row of headerRows) {
        const headerCells = row.querySelectorAll('th');
        const headerRow = [];
        
        for (const cell of headerCells) {
            // Skip hidden input elements
            if (cell.querySelector('input[type="hidden"]')) continue;
            
            let text = cell.textContent.trim();
            
            // Handle subject headers with marks in parentheses
            if (text.includes('(') && text.includes(')')) {
                text = text.replace(/\s+/g, ' '); // Normalize whitespace
            }
            
            // Replace commas and quotes to prevent CSV issues
            text = text.replace(/"/g, '""').replace(/,/g, ';');
            
            // Wrap in quotes if contains special characters
            if (text.includes('"') || text.includes(',') || text.includes('\n')) {
                text = `"${text}"`;
            }
            
            headerRow.push(text);
        }
        
        csv.push(headerRow.join(','));
    }
    
    // Process data rows
    const dataRows = table.querySelectorAll('tbody tr');
    for (const row of dataRows) {
        const dataCells = row.querySelectorAll('td');
        const dataRow = [];
        
        for (const cell of dataCells) {
            // // Skip cells that are part of the student info (handled separately)
            // if (cell.classList.contains('student_name') || 
            //     cell.textContent.trim() === cell.querySelector('input[type="number"]')?.value) {
            //     continue;
            // }
            
            // Handle input elements (marks)
            const input = cell.querySelector('input[type="number"]');
            let text;
            
            if (input) {
                text = input.value.trim();
                // Replace blank or -1 with hyphen
                if (text === '-1.00') {
                    text = '-1.00';
                }
            } else {
                text = cell.textContent.trim();
                // Replace empty with hyphen
                if (text === '') {
                    text = '0';
                }
            }



            // Handle input elements (grades)
            const input_grade = cell.querySelector('select');
            // let text;
            
            if (input_grade) {
                text = input_grade.value.trim();
                // Replace blank or -1 with hyphen
                if (text === '-1.00') {
                    text = '-1.00';
                }
            }
			//  else {
            //     text = cell.textContent.trim();
            //     // Replace empty with hyphen
            //     if (text === '') {
            //         text = '0';
            //     }
            // }


            
            // Replace commas and quotes to prevent CSV issues
            text = text.replace(/"/g, '""').replace(/,/g, ';');
            
            // Wrap in quotes if contains special characters
            if (text.includes('"') || text.includes(',') || text.includes('\n')) {
                text = `"${text}"`;
            }
            
            dataRow.push(text);
        }
        
        csv.push(dataRow.join(','));
    }
    
    // Combine all rows
    const csvContent = csv.join('\n');
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'table_data.csv');
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
<?php
  function admission_is_enabled($disabled_fields, $filter) {
    foreach ($disabled_fields as $f) {
      if ($f == $filter)
        return 0;
    }
    return 1;
  }
?>

<?php 
$admission_ui_colors = [];
$ui_colors_array = $this->settings->getSetting('admissions_ui_theme_color');
$primary_background_color = '#623CE7';
$primary_fontColor = 'white';
$secondary_background_color = '#CBBCFF';
$secondary_fontColor = 'black';
if (!empty($ui_colors_array)) {
    $admission_ui_colors = array_column($ui_colors_array, 'value', 'name');
    $primary_background_color = $admission_ui_colors['primary_background_color'];
    $primary_fontColor = getContrastYIQ($admission_ui_colors['primary_background_color']);
    $secondary_background_color = $admission_ui_colors['secondary_background_color'];
    $secondary_fontColor = getContrastYIQ($admission_ui_colors['secondary_background_color']);
}
function getContrastYIQ(string $hexColor): string {
    // Remove '#' if present
    $hex = ltrim($hexColor, '#');

    // Convert hex to RGB
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));

    // Calculate the brightness
    $yiq = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;

    // Return the best contrasting color
    return ($yiq >= 128) ? '#000000' : '#FFFFFF';
}
?>

<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

<div class="" style="background-color:#F9F7FE;font-style: normal;">

    <div style="background-color: #F9F7FE; padding: 20px 22px;">
        <div style="display: flex; align-items: center; gap: 12px;">
            <!-- Back Icon -->
            <a href="<?php echo site_url('admissions/home') ?>" style="display: flex; align-items: center;">
                <div style="width: 16px; height: 16px;">
                    <?php $this->load->view('svg_icons/back_icon.svg') ?>
                </div>
            </a>

            <!-- Title -->
            <span style="font-weight: 500; font-size: 18px; color: #111;">
                <?= $config_val['form_name'] . ' ' . $config_val['form_year'] ?>
            </span>

            <!-- Info Icon -->
            <a href="javascript:void(0);" onclick="get_instructions('<?= $admission_setting_id ?>')"
                style="display: flex; align-items: center;">
                <div style="width: 18px; height: 18px; cursor: pointer;">
                    <?php $this->load->view('svg_icons/info.svg'); ?>
                </div>
            </a>
        </div>
    </div>

    <div class="container">
        <?php if(!$this->mobile_detect->isTablet() && $this->mobile_detect->isMobile()){ ?>
        <!-- Mobile Stepper Design -->
        <div class="mobile-stepper-container" style="background: white; border-radius: 12px; padding: 0 20px;">
            <div class="d-flex align-items-center gap-3">
                <!-- Circular Progress Icon -->
                <div class="step-icon-circle d-flex align-items-center justify-content-center"
                     style="width: 48px; height: 48px; background: <?= $primary_background_color ?>; border-radius: 50%; flex-shrink: 0;">
                    <div class="step-icon" style="color: white; font-size: 16px; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                        <span id="mobile-step-icon">
                            <?php $this->load->view('svg_icons/admissions_student_form_icon.svg') ?>
                        </span>
                    </div>
                </div>

                <!-- Step Info -->
                <div class="step-info flex-grow-1">
                    <div class="step-counter" style="font-size: 14px; color: #6B7280; font-weight: 500;">
                        <span id="mobile-current-step">Step 1</span>/<span id="mobile-total-steps">6</span>
                    </div>
                    <div class="step-title" style="font-size: 18px; color: #111; font-weight: 600; margin-top: 2px;">
                        <span id="mobile-step-title">Student Details</span>
                    </div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="progress-bar-container" style="margin-top: 16px;">
                <div class="progress" style="height: 6px; background: #E5E7EB; border-radius: 3px; overflow: hidden;">
                    <div class="progress-bar" id="mobile-progress-bar"
                         style="width: 16.67%; background: <?= $primary_background_color ?>; height: 100%; transition: width 0.3s ease; border-radius: 3px;">
                    </div>
                </div>
            </div>
        </div>

        <script>
        // Mobile stepper configuration
        const mobileStepperConfig = {};

        // Build configuration dynamically
        let stepCounter = 1;
        let totalSteps = 0;

        // Student step
        mobileStepperConfig[stepCounter] = {
            title: 'Student Details',
            progress: 0
        };
        stepCounter++;

        // Parent step
        mobileStepperConfig[stepCounter] = {
            title: 'Parent Details',
            progress: 0
        };
        stepCounter++;

        <?php if($config_val['show_guardian_details'] != 0) : ?>
        // Guardian step
        mobileStepperConfig[stepCounter] = {
            title: 'Guardian Details',
            progress: 0
        };
        stepCounter++;
        <?php endif; ?>

        <?php if($this->settings->getSetting('enabled_medical_form_tab_in_admissions') == 1) { ?>
        // Medical step
        mobileStepperConfig[stepCounter] = {
            title: 'Medical Details',
            progress: 0
        };
        stepCounter++;
        <?php } ?>

        <?php if($this->settings->getSetting('disable_document_tab_in_admissions') != 1) { ?>
        // Document step
        mobileStepperConfig[stepCounter] = {
            title: 'Document Upload',
            progress: 0
        };
        stepCounter++;
        <?php } ?>

        // Preview step
        mobileStepperConfig[stepCounter] = {
            title: 'Preview & Submit',
            progress: 0
        };

        // Calculate progress percentages
        totalSteps = Object.keys(mobileStepperConfig).length;
        Object.keys(mobileStepperConfig).forEach((step, index) => {
            mobileStepperConfig[step].progress = ((index + 1) / totalSteps) * 100;
        });

        // Function to update mobile stepper
        function updateMobileStepper(stepNumber) {
            if (!window.matchMedia('(max-width: 768px)').matches) return;

            const config = mobileStepperConfig[stepNumber];
            if (config) {
                document.getElementById('mobile-current-step').textContent = 'Step ' + stepNumber;
                document.getElementById('mobile-step-title').textContent = config.title;
                document.getElementById('mobile-progress-bar').style.width = config.progress + '%';

                // Update total steps
                const totalSteps = Object.keys(mobileStepperConfig).length;
                document.getElementById('mobile-total-steps').textContent = totalSteps;

                // Update icon based on step
                updateMobileStepIcon(stepNumber);
            }
        }

        // Function to update mobile step icon
        function updateMobileStepIcon(stepNumber) {
            const iconElement = document.getElementById('mobile-step-icon');
            if (!iconElement) return;

            let iconSvg = '';
            switch(stepNumber) {
                case 1:
                    iconSvg = `<?php ob_start(); $this->load->view('svg_icons/admissions_student_form_icon.svg'); echo str_replace(array("\n", "\r"), '', ob_get_clean()); ?>`;
                    break;
                case 2:
                    iconSvg = `<?php ob_start(); $this->load->view('svg_icons/admissions_parent_form_icon.svg'); echo str_replace(array("\n", "\r"), '', ob_get_clean()); ?>`;
                    break;
                <?php if($config_val['show_guardian_details'] != 0) : ?>
                case 3:
                    iconSvg = `<?php ob_start(); $this->load->view('svg_icons/admissions_parent_form_icon.svg'); echo str_replace(array("\n", "\r"), '', ob_get_clean()); ?>`;
                    break;
                <?php endif; ?>
                <?php if($this->settings->getSetting('enabled_medical_form_tab_in_admissions') == 1) { ?>
                case 4:
                    iconSvg = `<?php ob_start(); $this->load->view('svg_icons/admissions_medical_form_icon.svg'); echo str_replace(array("\n", "\r"), '', ob_get_clean()); ?>`;
                    break;
                <?php } ?>
                <?php if($this->settings->getSetting('disable_document_tab_in_admissions') != 1) { ?>
                case 5:
                    iconSvg = `<?php ob_start(); $this->load->view('svg_icons/admissions_document_form_icon.svg'); echo str_replace(array("\n", "\r"), '', ob_get_clean()); ?>`;
                    break;
                <?php } ?>
                case 6:
                    iconSvg = `<?php ob_start(); $this->load->view('svg_icons/admissions_preview_form_icon.svg'); echo str_replace(array("\n", "\r"), '', ob_get_clean()); ?>`;
                    break;
                default:
                    iconSvg = `<?php ob_start(); $this->load->view('svg_icons/admissions_student_form_icon.svg'); echo str_replace(array("\n", "\r"), '', ob_get_clean()); ?>`;
            }
            iconElement.innerHTML = iconSvg;
        }

        // Initialize mobile stepper
        document.addEventListener('DOMContentLoaded', function() {
            if (window.matchMedia('(max-width: 768px)').matches) {
                updateMobileStepper(1);

                // Also listen for window resize to handle orientation changes
                window.addEventListener('resize', function() {
                    if (window.matchMedia('(max-width: 768px)').matches) {
                        // Get current active step
                        const activeStep = document.querySelector('.step.active');
                        if (activeStep) {
                            const currentStepNumber = parseInt(activeStep.getAttribute('data-step'));
                            updateMobileStepper(currentStepNumber);
                        }
                    }
                });
            }
        });

        // Override existing step navigation functions to update mobile stepper
        function overrideStepFunctions() {
            if (typeof window.goToStep === 'function') {
                const originalGoToStep = window.goToStep;
                window.goToStep = function(stepNumber) {
                    originalGoToStep(stepNumber);
                    updateMobileStepper(stepNumber);
                };
            }

            if (typeof window.back_to_ToStep === 'function') {
                const originalBackToStep = window.back_to_ToStep;
                window.back_to_ToStep = function(step) {
                    originalBackToStep(step);
                    updateMobileStepper(step);
                };
            }
        }

        // Try to override immediately
        overrideStepFunctions();

        // Also try after a short delay to catch functions defined later
        setTimeout(overrideStepFunctions, 100);

        // Add event listeners for save buttons to update mobile stepper
        document.addEventListener('click', function(e) {
            if (!window.matchMedia('(max-width: 768px)').matches) return;

            // Check if clicked element is a save button that advances to next step
            if (e.target.classList.contains('save-step1') ||
                e.target.classList.contains('save-step2') ||
                e.target.classList.contains('save-step4') ||
                e.target.classList.contains('save-step5')) {

                console.log('Mobile save button clicked:', e.target.className);

                // Add a delay to allow the step change to complete
                setTimeout(function() {
                    const activeStep = document.querySelector('.step.active');
                    if (activeStep) {
                        const currentStepNumber = parseInt(activeStep.getAttribute('data-step'));
                        console.log('Updating mobile stepper to step:', currentStepNumber);
                        updateMobileStepper(currentStepNumber);
                    }
                }, 500);
            }
        });

        // Additional mobile debugging
        if (window.matchMedia('(max-width: 768px)').matches) {
            console.log('Mobile device detected - adding additional event handlers');

            // Add touch event handlers for better mobile responsiveness
            document.addEventListener('touchend', function(e) {
                if (e.target.classList.contains('save-step1') ||
                    e.target.classList.contains('save-step2') ||
                    e.target.classList.contains('save-step4') ||
                    e.target.classList.contains('save-step5')) {

                    console.log('Mobile save button touched:', e.target.className);
                }
            });
        }
        </script>

        <?php }else{ ?>
        <div class="stepper d-flex justify-content-between">
            <div class="step active" data-step="1">
                <div class="circle active-circle" style="background-color: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>;" onclick="back_to_ToStep(1)"><i
                        class="bi bi-person-lines-fill"><?php $this->load->view('svg_icons/admissions_student_form_icon.svg') ?></i>
                </div>
                <div class="label">Student</div>
            </div>
            <div class="step" data-step="2">
                <div class="circle inactive-circle" style="background-color: #E5E7EB; color: #9CA3AF;" onclick="back_to_ToStep(2)"><i
                        class="bi bi-people-fill"><?php $this->load->view('svg_icons/admissions_parent_form_icon.svg') ?></i>
                </div>
                <div class="label">Parent</div>
            </div>
            <?php if($config_val['show_guardian_details'] !=0) :  ?>
            <div class="step" data-step="3">
                <div class="circle inactive-circle" style="background-color: #E5E7EB; color: #9CA3AF;" onclick="back_to_ToStep(3)"><i
                        class="bi bi-person-badge"><?php $this->load->view('svg_icons/admissions_parent_form_icon.svg') ?></i>
                </div>
                <div class="label">Guardian</div>
            </div>
            <?php endif; ?>
            <?php if($this->settings->getSetting('enabled_medical_form_tab_in_admissions') == 1) {?>
            <div class="step" data-step="4">
                <div class="circle inactive-circle" style="background-color: #E5E7EB; color: #9CA3AF;" onclick="back_to_ToStep(4)"><i
                        class="bi bi-plus-square"><?php $this->load->view('svg_icons/admissions_medical_form_icon.svg') ?></i>
                </div>
                <div class="label">Medical</div>
            </div>
            <?php } ?>
            <?php if($this->settings->getSetting('disable_document_tab_in_admissions') != 1) {?>
            <div class="step" data-step="5">
                <div class="circle inactive-circle" style="background-color: #E5E7EB; color: #9CA3AF;" onclick="back_to_ToStep(5)"><i
                        class="bi bi-file-earmark-text"><?php $this->load->view('svg_icons/admissions_document_form_icon.svg') ?></i>
                </div>
                <div class="label">Document</div>
            </div>
            <?php } ?>
            <div class="step" data-step="6">
                <div class="circle inactive-circle" style="background-color: #E5E7EB; color: #9CA3AF;" onclick="back_to_ToStep(6)"><i
                        class="bi bi-eye-fill"><?php $this->load->view('svg_icons/admissions_preview_form_icon.svg') ?></i>
                </div>
                <div class="label">Preview</div>
            </div>
        </div>

        <style>
        .stepper .step.active .circle {
            background-color: <?= $primary_background_color ?> !important;
            color: <?= $primary_fontColor ?> !important;
            border-color: <?= $primary_background_color ?> !important;
        }

        .stepper .step.completed .circle {
            background-color: <?= $primary_background_color ?> !important;
            color: <?= $primary_fontColor ?> !important;
            border-color: <?= $primary_background_color ?> !important;
        }

        .stepper .step.previous .circle {
            background-color: <?= $primary_background_color ?> !important;
            color: <?= $primary_fontColor ?> !important;
            border-color: <?= $primary_background_color ?> !important;
        }

        .stepper .step:not(.active):not(.completed):not(.previous) .circle {
            background-color: #E5E7EB !important;
            color: #9CA3AF !important;
            border-color: #E5E7EB !important;
        }

        .stepper .step.active .label {
            color: <?= $primary_background_color ?> !important;
            font-weight: 600 !important;
        }

        .stepper .step.completed .label,
        .stepper .step.previous .label {
            color: <?= $primary_background_color ?> !important;
            font-weight: 500 !important;
        }

        /* Connecting lines */
        .stepper .step.active:not(:last-child)::after,
        .stepper .step.completed:not(:last-child)::after,
        .stepper .step.previous:not(:last-child)::after {
            background-color: <?= $primary_background_color ?> !important;
        }

        .stepper .step:not(.active):not(.completed):not(.previous):not(:last-child)::after {
            background-color: #E5E7EB !important;
        }
        </style>


        <?php } ?>
        <div id="step-form-1" class="step-form">
            <div class="preview-container">
                <div class="step-form-content">
                    <form enctype="multipart/form-data" id="std-form" class="form-horizontal" data-parsley-validate
                        method="post">
                        <div class="panel-body" id="contentId">
                            <input type="hidden" name="au_id" id="auId" value="<?= $au_id; ?>">
                            <input type="hidden" name="insertId" value="<?= $insert_id; ?>">
                             <input type="hidden" id="admission_setting_id" name="admission_setting_id"
                                    value="<?= $admission_setting_id; ?>">

                            <!-- Student Form Block -->
                            <div class="col-md-12 p-0" style="margin-bottom: 2rem;">
                                <?php $this->load->view('admission/form/new_admission/_blocks/student_form') ?>
                                <?php if(!$this->mobile_detect->isTablet() && $this->mobile_detect->isMobile()) { ?>
                                <div class="row">
                                    <div class="col-12 d-flex flex-column flex-md-row align-items-stretch align-items-md-center justify-content-md-end gap-2 mb-3">
                                        <button type="button" class="btn save-draft1 w-100 w-md-auto"
                                            style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;">
                                            Save as draft
                                        </button>
                                        <button type="button" class="btn save-step1 w-100 w-md-auto mobile-save-btn" id="mobile-paybtn"
                                            style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px; min-height: 44px; touch-action: manipulation;"
                                            onclick="mobileSaveStep1();">
                                            Save & Proceed
                                        </button>
                                    </div>
                                </div>
                                <?php } else { ?>
                                <div style="display: flex; justify-content: flex-end; gap: 10px; margin-bottom: 1rem;">
                                    <button type="button" class="btn save-draft1"
                                        style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px; white-space: nowrap;">Save
                                        as draft</button>
                                    <button type="button" class="btn save-step1" id="paybtn"
                                        style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px; white-space: nowrap;">Save
                                        & Proceed</button>
                                </div>
                                <?php } ?>
                                <p id="dob_error" style="color: red; font-size: 14px;"></p>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div id="step-form-2" class="step-form d-none">
            <div class="step-form-content">
                <div class="preview-container">
                    <form enctype="multipart/form-data" id="parent-form" class="form-horizontal" data-parsley-validate
                        method="post">
                        <div class="panel-body" id="contentId">
                            <div class="col-md-12">
                                <input type="hidden" name="au_id" id="auId" value="<?= $au_id; ?>">
                                <input type="hidden" name="insertId" value="<?= $insert_id; ?>">
                                 <input type="hidden" id="admission_setting_id" name="admission_setting_id"
                                    value="<?= $admission_setting_id; ?>">
                            </div>
                            <?php $this->load->view('admission/form/new_admission/_blocks/parent_form') ?>
                        </div>

                        <?php if(!$this->mobile_detect->isTablet() && $this->mobile_detect->isMobile()) { ?>
                        <div
                            class="d-flex flex-column flex-md-row justify-content-md-end align-items-stretch gap-2 mb-3 mt-3">
                            <button type="button" class="btn prev-step w-100 w-md-auto"
                                style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                Previous Step
                            </button>
                            <button type="button" class="btn save-draft2 w-100 w-md-auto"
                                style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                Save as draft
                            </button>
                            <button type="button" class="btn save-step2 w-100 w-md-auto mobile-save-btn"
                                style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px; min-height: 44px; touch-action: manipulation;"
                                onclick="mobileSaveStep2();">
                                Save & Proceed
                            </button>
                        </div>
                        <?php } else { ?>
                        <div style="gap: 10px;float:right;margin-bottom:2rem;margin-right:4rem">
                            <a class="btn prev-step"
                                style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;margin-right:5px">Previous
                                Step</a>
                            <a class="btn save-draft2"
                                style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;margin-right:5px">Save
                                as draft</a>
                            <button type="button" class="btn save-step2"
                                style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px">Save
                                & Proceed</button>
                        </div>
                        <?php } ?>
                        <p id="dob_error" style="color: red; font-size: 14px;"></p>
                    </form>
                </div>
            </div>
        </div>
        <?php if($config_val['show_guardian_details'] !=0) :  ?>
        <div id="step-form-3" class="step-form d-none">
            <div class="step-form-content">
                <div class="preview-container">
                    <form enctype="multipart/form-data" id="guardian-form" class="form-horizontal" data-parsley-validate
                        method="post">
                        <div class="panel-body" id="contentId">
                            <div class="col-md-12">
                                <input type="hidden" name="au_id" id="auId" value="<?= $au_id; ?>">
                                <input type="hidden" name="insertId" value="<?= $insert_id; ?>">
                                 <input type="hidden" id="admission_setting_id" name="admission_setting_id"
                                    value="<?= $admission_setting_id; ?>">
                            </div>
                            <?php $this->load->view('admission/form/new_admission/_blocks/guardian') ?>

                            <?php if(!$this->mobile_detect->isTablet() && $this->mobile_detect->isMobile()) { ?>
                            <div
                                class="d-flex flex-column flex-md-row justify-content-md-end align-items-stretch gap-2 mb-3 mt-3">
                                <button type="button" class="btn prev-step w-100 w-md-auto"
                                    style="border: 1px solid #6B7280; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                    Previous Step
                                </button>
                                <button type="button" class="btn save-draft4 w-100 w-md-auto"
                                    style="border: 1px solid #6B7280; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                    Save as draft
                                </button>
                                <button type="button" class="btn save-step4 w-100 w-md-auto mobile-save-btn"
                                    style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px; min-height: 44px; touch-action: manipulation;"
                                    onclick="mobileSaveStep4();">
                                    Save & Proceed
                                </button>
                            </div>
                            <?php } else { ?>
                            <div style="gap: 10px;float:right;margin-bottom:2rem;">
                                <a class="btn prev-step"
                                    style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;margin-right:5px">Previous
                                    Step</a>
                                <a class="btn save-draft4"
                                    style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;margin-right:5px">Save
                                    as draft</a>
                                <button type="button" class="btn save-step4"
                                    style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px">Save
                                    & Proceed</button>
                            </div>
                            <?php } ?>
                            <p id="dob_error" style="color: red; font-size: 14px;"></p>

                        </div>

                    </form>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php if($this->settings->getSetting('enabled_medical_form_tab_in_admissions') == 1) {?>
        <div id="step-form-4" class="step-form d-none">
            <div class="step-form-content">
                <div class="preview-container">
                    <form enctype="multipart/form-data" id="medical-form" class="form-horizontal" data-parsley-validate
                        method="post">
                        <div class="panel-body" id="contentId">
                            <div class="col-md-12">
                                <input type="hidden" name="au_id" value="<?= $au_id; ?>">
                                <input type="hidden" id="afid" name="lastId" value="<?= $insert_id; ?>">
                                <input type="hidden" id="admission_setting_id" name="admission_setting_id"
                                    value="<?= $admission_setting_id; ?>">
                            </div>
                            <?php $this->load->view('admission/form/new_admission/_blocks/medical') ?>

                            <?php if(!$this->mobile_detect->isTablet() && $this->mobile_detect->isMobile()) { ?>
                            <div
                                class="d-flex flex-column flex-md-row justify-content-md-end align-items-stretch gap-2 mb-3 mt-3">
                                <button type="button" class="btn medical_prev_button w-100 w-md-auto"
                                    style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                    Previous Step
                                </button>
                                <button type="button" class="btn save-draft5 w-100 w-md-auto"
                                    style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                    Save as draft
                                </button>
                                <button type="button" class="btn save-step5 w-100 w-md-auto mobile-save-btn"
                                    style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px; min-height: 44px; touch-action: manipulation;"
                                    onclick="mobileSaveStep5();">
                                    Save & Proceed
                                </button>
                            </div>
                            <?php } else { ?>
                            <div style="gap: 10px;float:right;margin:3rem 0">
                                <a class="btn medical_prev_button"
                                    style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;margin-right:5px">Previous
                                    Step</a>
                                <a class="btn save-draft5"
                                    style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;margin-right:5px">Save
                                    as draft</a>
                                <button type="button" class="btn save-step5"
                                    style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px">Save
                                    & Proceed</button>
                            </div>
                            <?php } ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php } ?>
        <?php if($this->settings->getSetting('disable_document_tab_in_admissions') == 0) {?>
        <div id="step-form-5" class="step-form d-none">
            <div class="step-form-content">
                <div class="preview-container">
                    <form enctype="multipart/form-data" id="document-form" action="" class="form-horizontal"
                        data-parsley-validate method="post">
                        <div class="panel-body" id="contentId">
                            <div class="col-md-12">
                                <input type="hidden" name="au_id" value="<?= $au_id; ?>">
                                <input type="hidden" id="afid" name="lastId" value="<?= $insert_id; ?>">
                                <input type="hidden" id="admission_setting_id" name="admission_setting_id"
                                    value="<?= $admission_setting_id; ?>">
                            </div>
                            <?php  if ($this->settings->getSetting('application_new_document_tab') == 1) { ?>
                            <?php $this->load->view('admission/form/new_admission/_blocks/document_previous') ?>
                            <?php }else{ ?>
                            <?php $this->load->view('admission/form/new_admission/_blocks/document') ?>
                            <?php } ?>



                        </div>
                        <?php if(!$this->mobile_detect->isTablet() && $this->mobile_detect->isMobile()) { ?>
                        <div
                            class="d-flex flex-column flex-md-row justify-content-md-end align-items-stretch gap-2 mb-3 mt-3">
                            <button type="button" class="btn prev-step w-100 w-md-auto"
                                style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                Previous Step
                            </button>
                            <a class="btn w-100 w-md-auto" id="draft_submit" href="<?php echo site_url('admissions/home') ?>"
                                style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                Save as draft
                            </a>
                            <?php  if ($this->settings->getSetting('application_new_document_tab') == 1) { ?>
                            <button type="button" onclick="mobileSaveStep6()" id="document_submit"
                                class="btn mobile-save-btn w-100 w-md-auto"
                                style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                Save & Preview
                            </button>
                            <?php } else { ?>
                            <button type="button" onclick="mobileSaveStep6()" id="document_submit"
                                class="btn mobile-save-btn w-100 w-md-auto"
                                style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                Save & Preview
                            </button>
                            <?php } ?>
                        </div>
                        <?php } else { ?>
                        <div style="gap: 10px;float:right;margin-bottom:2rem;">
                            <a class="btn prev-step"
                                style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;margin-right:5px">Previous
                                Step</a>
                            <a class="btn" id="draft_submit" href="<?php echo site_url('admissions/home') ?>"
                                style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;margin-right:5px">Save
                                as draft</a>
                            <?php  if ($this->settings->getSetting('application_new_document_tab') == 1) { ?>
                            <button type="button" onclick="documentSubmit_new()"
                                style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px"
                                id="document_submit" class="btn">Save & Preview</button>
                            <?php }else{ ?>
                            <button type="button" onclick="documentSubmit(1)"
                                style="background: <?= $primary_background_color ?>; color: <?= $primary_fontColor ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px"
                                id="document_submit" class="btn">Save & Preview</button>
                            <?php } ?>
                        </div>
                        <?php } ?>

                    </form>
                </div>
            </div>
        </div>
        <?php } ?>

        <div id="step-form-6" class="step-form d-none">
            <div class="step-form-content">
                <div class="preview-container" style="max-width:60%" id="last_container">
                    <form enctype="multipart/form-data" id="final-form" class="form-horizontal" data-parsley-validate
                        method="post">
                        <div class="panel-body" id="contentId">
                            <div class="col-md-12">
                                <input type="hidden" name="au_id" id="au_id" value="<?= $au_id; ?>">
                                <input type="hidden" id="af_id" name="lastId" value="<?= $insert_id; ?>">
                                <input type="hidden" id="admission_setting_id" name="admission_setting_id"
                                    value="<?= $admission_setting_id; ?>">
                            </div>
                            <?php $this->load->view('admission/form/new_admission/_blocks/admissions_final_preview') ?>


                            <?php if(!$this->mobile_detect->isTablet() && $this->mobile_detect->isMobile()) { ?>
                                <div class="d-flex flex-column flex-md-row justify-content-md-end align-items-stretch gap-2 mb-3 mt-3">
                                    <button type="button" class="btn prev-step w-100 w-md-auto"
                                        style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                        Previous Step
                                    </button>
                                    <input type="hidden" name="partial_payement" value="<?php echo $config_val['enable_partial_payment'] ?>">
                                    <?php if($config_val['online_payment'] == 1){ ?>
                                         <button type="button" id="final_submit_button" onclick="terms_check('1')"
                                        class="btn btn-primary w-100 w-md-auto"
                                        style="background: <?= $primary_background_color ?>; color: white; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px;">
                                        Pay & Submit Application
                                    </button>
                                    
                                    <?php }else{ ?>
                                   <button type="button"
                                        style="background: <?= $primary_background_color ?>; color: white; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px;"
                                        id="final_submit_button" class="btn save-step3 w-100 w-md-auto"
                                        onclick="terms_check('0')">Submit Application</button>
                                    <?php } ?>
                                </div>
                            <?php } else {?>
                                <div style="gap: 10px;float:right;margin:5rem 0">
                                    <a class="btn prev-step"
                                        style="border: 1px solid <?= $primary_background_color ?>; background: transparent; color: <?= $primary_background_color ?>; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;margin-right:5px">Previous
                                        Step</a>
                                    <?php if($config_val['online_payment'] == 1){ ?>
                                        <input type="hidden" name="application_fee_amount" value="<?php echo $config_val['admission_fee_amount'] ?>">
                                        <input type="hidden" name="online_enable" value="<?php echo $config_val['online_payment'] ?>">
                                        <button type="button" style="background: <?= $primary_background_color ?>; color: white; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px" id="final_submit_button" onclick="terms_check('1')"
                                        class="btn btn-primary"> Pay & Submit Application</button>
                                    <?php }else{ ?>
                                         <button type="button"
                                        style="background: <?= $primary_background_color ?>; color: white; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px"
                                        id="final_submit_button" class="btn save-step3"
                                        onclick="terms_check('0')">Submit Application</button>
                                    <?php } ?>
                                </div>
                            <?php } ?>
                            
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>

    <div id="upload_document_model" class="modal" role="dialog" style="margin:auto;" data-backdrop="static"
        aria-hidden="true" tabindex="-1">
        <div class="modal-content modal-dialog" style="border-radius: 8px;width:60%">

            <div class="modal-header" style="border-bottom: 2px solid #ccc;">
                <h5 class="modal-title" id="upload_document_header">Upload Details</h5>
            </div>

            <form action="" id="upload_document_form" enctype="multipart/form-data" method="post"
                data-parsley-validate="" class="form-horizontal">
                <div class="modal-body">
                    <input type="hidden" value="" name="document_name" id="document_name">
                    <input type="hidden" value="<?= $insert_id; ?>" name="af_id" id="">
                    <input type="hidden" id="document_sl_no">
                    <div id="upload_aadhar_details"></div>
                </div>
                <div class="modal-footer">
                    <input type="button" id="upload_btn" style="width: 9rem; border-radius: .45rem;"
                        class="btn btn-primary" value="Upload" onclick="upload_admission_documents()">
                    <!-- <button type="button" id="upload_btn" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary"  onclick="upload_admission_documents()">Upload</button>      -->
                    <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;"
                        data-dismiss="modal">Close</button>
                </div>
            </form>
        </div>
    </div>

    <script>
    $(document).ready(function() {
        $("body").removeClass("modal-open");
        get_instructions('<?php echo $admission_setting_id ?>');
    });


    function get_instructions(adm_setting_id, af_id = '') {
        // $("body").addClass("modal1");
        // $(".modal1").css("display", 'contents');
        if (af_id != '' && af_id != '0') {
            return false;
        }
        var id = 'watermark';
        var panelId = 'contentId';
        var srcpath = '<?php echo $this->settings->getSetting('school_logo') ?>';
        var short_name = '<?php echo $this->settings->getSetting('school_short_name') ?>';
        if (short_name == 'sahitya') {
            id = 'watermark1';
            srcpath = '';
            panelId = '';
        }
        var src = '<?php echo base_url() ?>' + srcpath;
        $.ajax({
            url: '<?php echo site_url('Admission_user/instruction_by_setting_id'); ?>',
            type: 'post',
            data: {
                'admission_setting_id': adm_setting_id
            },
            success: function(data) {
                var instruction_obj = $.parseJSON(data);
                var html = '';
                html += `${instruction_obj.guidelines}`;
                html += `${instruction_obj.address.line1}`;
                html += `${instruction_obj.address.line2}`;
                html += `${instruction_obj.address.line3}`;
                html += `${instruction_obj.address.line4}`;
                html += `${instruction_obj.address.phone}`;
                html += `${instruction_obj.address.fax}`;
                html += `${instruction_obj.address.email}`;

                Swal.fire({
                    title: `
                    Instructions
                `,
                    html: html,
                    width: '700px',
                    showCancelButton: false,
                    showConfirmButton: true,
                    confirmButtonText: 'Close',
                    customClass: {
                        popup: 'terms-confirm-popup',
                        confirmButton: 'swal2-submit-btn align-right-btn',
                    },
                    buttonsStyling: false,
                    didOpen: () => {
                         makeSwalDraggable();
                        // Properly align the close button to the right
                        $('.swal2-actions').css({
                            'justify-content': 'flex-end',
                            'margin-top': '20px',
                            'padding': '0 20px 20px 20px'
                        });

                        $('.swal2-submit-btn.align-right-btn').css({
                            'background-color': '<?= $primary_background_color ?>',
                            'color': '<?= $primary_fontColor ?>',
                            'border': 'none',
                            'padding': '12px 24px',
                            'border-radius': '8px',
                            'font-size': '16px',
                            'font-weight': '500',
                            'cursor': 'pointer',
                            'margin': '0'
                        });
                    }
                });
            }
        });
    }
    </script>

        <script>
        // Function to create a lighter version of the primary color
        function lightenColor(color, percent) {
            // Remove # if present
            color = color.replace('#', '');

            // Parse RGB values
            const r = parseInt(color.substr(0, 2), 16);
            const g = parseInt(color.substr(2, 2), 16);
            const b = parseInt(color.substr(4, 2), 16);

            // Lighten each component
            const newR = Math.min(255, Math.floor(r + (255 - r) * percent / 100));
            const newG = Math.min(255, Math.floor(g + (255 - g) * percent / 100));
            const newB = Math.min(255, Math.floor(b + (255 - b) * percent / 100));

            // Convert back to hex
            return '#' +
                   newR.toString(16).padStart(2, '0') +
                   newG.toString(16).padStart(2, '0') +
                   newB.toString(16).padStart(2, '0');
        }
        // Debounce function to prevent excessive calls
        let updateTimeout = null;
        let isUpdating = false;

        // Function to update desktop stepper colors based on theme
        function updateDesktopStepperColors() {
            // Prevent recursive calls
            if (isUpdating) {
                console.log('Update already in progress, skipping');
                return;
            }
            // Don't update colors if SweetAlert is open
            if (document.querySelector('.swal2-container') || document.querySelector('.swal2-popup')) {
                console.log('SweetAlert is open, skipping stepper color update');
                return;
            }
            // Clear any pending timeout
            if (updateTimeout) {
                clearTimeout(updateTimeout);
            }
            // Set flag to prevent recursive calls
            isUpdating = true;

            const primaryColor = '<?= $primary_background_color ?>';
            const primaryFontColor = '<?= $primary_fontColor ?>';
            const secondaryColor = '<?= $secondary_background_color ?>';

            // Create a lighter version of the primary color (40% lighter)
            const lightPrimaryColor = lightenColor(primaryColor, 40);

            // First, determine which step should be active based on visible form
            const visibleForm = document.querySelector('.step-form:not(.d-none)');
            let activeStepNumber = 1;
            if (visibleForm) {
                const formId = visibleForm.id;
                const match = formId.match(/step-form-(\d+)/);
                if (match) {
                    activeStepNumber = parseInt(match[1]);
                }
            }

            console.log('Active step should be:', activeStepNumber);
            // Temporarily disconnect observer to prevent loops
            if (window.stepObserver) {
                window.stepObserver.disconnect();
            }

            // Update step classes to match the active form
            document.querySelectorAll('.stepper .step').forEach(step => {
                const stepNumber = parseInt(step.getAttribute('data-step'));
                step.classList.remove('active', 'previous', 'completed');

                if (stepNumber === activeStepNumber) {
                    step.classList.add('active');
                } else if (stepNumber < activeStepNumber) {
                    step.classList.add('previous', 'completed');
                }
            });

            // Force update all circles with inline styles to override any existing styles
            document.querySelectorAll('.stepper .step .circle').forEach(circle => {
                const step = circle.closest('.step');
                const stepNumber = parseInt(step.getAttribute('data-step'));

                if (stepNumber === activeStepNumber) {
                    // Active step - full primary color
                    circle.style.setProperty('background-color', primaryColor, 'important');
                    circle.style.setProperty('color', primaryFontColor, 'important');
                    circle.style.setProperty('border-color', primaryColor, 'important');
                } else if (stepNumber < activeStepNumber) {
                    // Completed/previous steps - lighter primary color
                    circle.style.setProperty('background-color', lightPrimaryColor, 'important');
                    circle.style.setProperty('color', primaryFontColor, 'important');
                    circle.style.setProperty('border-color', lightPrimaryColor, 'important');
                } else {
                    // Inactive steps - gray
                    circle.style.setProperty('background-color', '#E5E7EB', 'important');
                    circle.style.setProperty('color', '#9CA3AF', 'important');
                    circle.style.setProperty('border-color', '#E5E7EB', 'important');
                }
            });

            // Update labels
            document.querySelectorAll('.stepper .step .label').forEach(label => {
                const step = label.closest('.step');
                const stepNumber = parseInt(step.getAttribute('data-step'));

                if (stepNumber === activeStepNumber) {
                    // Active step label - full primary color
                    label.style.setProperty('color', primaryColor, 'important');
                    label.style.setProperty('font-weight', '600', 'important');
                } else if (stepNumber < activeStepNumber) {
                    // Completed/previous step labels - lighter primary color
                    label.style.setProperty('color', lightPrimaryColor, 'important');
                    label.style.setProperty('font-weight', '500', 'important');
                } else {
                    // Inactive step labels - gray
                    label.style.setProperty('color', '#9EA2AE', 'important');
                    label.style.setProperty('font-weight', '500', 'important');
                }
            });

            console.log('Desktop stepper colors updated for step:', activeStepNumber);
            // Reset the updating flag and reconnect observer after a delay
            setTimeout(() => {
                isUpdating = false;
                if (window.stepObserver && !window.matchMedia('(max-width: 768px)').matches) {
                    const stepElements = document.querySelectorAll('.stepper .step');
                    stepElements.forEach(function(step) {
                        window.stepObserver.observe(step, { attributes: true, attributeFilter: ['class'] });
                    });
                }
            }, 100);
        }

        // Function to override step navigation functions (with loop prevention)
        function overrideStepFunctions() {
            if (typeof window.goToStep === 'function' && !window.goToStep._overridden) {
                const originalGoToStep = window.goToStep;
                window.goToStep = function(stepNumber) {
                    console.log('goToStep called with stepNumber:', stepNumber);
                    originalGoToStep(stepNumber);

                    // Only update colors if no SweetAlert is open and not already updating
                    if (!document.querySelector('.swal2-container') && !isUpdating) {
                        // Clear any existing timeout
                        if (updateTimeout) {
                            clearTimeout(updateTimeout);
                        }
                        // Single delayed update instead of multiple
                        updateTimeout = setTimeout(updateDesktopStepperColors, 300);
                    }
                };
                window.goToStep._overridden = true;
            }

            if (typeof window.back_to_ToStep === 'function' && !window.back_to_ToStep._overridden) {
                const originalBackToStep = window.back_to_ToStep;
                window.back_to_ToStep = function(step) {
                    console.log('back_to_ToStep called with step:', step);
                    originalBackToStep(step);

                    // Only update colors if no SweetAlert is open and not already updating
                    if (!document.querySelector('.swal2-container') && !isUpdating) {
                        // Clear any existing timeout
                        if (updateTimeout) {
                            clearTimeout(updateTimeout);
                        }
                        // Single delayed update instead of multiple
                        updateTimeout = setTimeout(updateDesktopStepperColors, 300);
                    }
                };
                window.back_to_ToStep._overridden = true;
            }
        }

        // Try to override functions immediately
        overrideStepFunctions();

        // Initialize colors on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (!window.matchMedia('(max-width: 768px)').matches && document.querySelector('.stepper')) {
                setTimeout(updateDesktopStepperColors, 100);
                setTimeout(updateDesktopStepperColors, 500);
                setTimeout(overrideStepFunctions, 100);
                setTimeout(overrideStepFunctions, 1000);
            }
        });

        // Also update colors when window loads (after all CSS is loaded)
        window.addEventListener('load', function() {
            if (!window.matchMedia('(max-width: 768px)').matches && document.querySelector('.stepper')) {
                setTimeout(updateDesktopStepperColors, 100);
                setTimeout(updateDesktopStepperColors, 500);
                setTimeout(overrideStepFunctions, 100);
                setTimeout(overrideStepFunctions, 1000);
            }
        });

        // Add mutation observer to watch for step changes (with loop prevention)
        if (!window.matchMedia('(max-width: 768px)').matches) {
            let observerTimeout = null;

            window.stepObserver = new MutationObserver(function(mutations) {
                // Debounce the observer calls
                if (observerTimeout) {
                    clearTimeout(observerTimeout);
                }

                observerTimeout = setTimeout(() => {
                    // Don't process if we're already updating or if SweetAlert is open
                    if (isUpdating ||
                        document.querySelector('.swal2-container') ||
                        document.querySelector('.swal2-popup')) {
                        return;
                    }

                    let shouldUpdate = false;
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            const target = mutation.target;

                            // Only process if it's a stepper step and not inside a SweetAlert
                            if (target.classList.contains('step') &&
                                !target.closest('.swal2-container') &&
                                !target.closest('.swal2-popup')) {
                                console.log('Step class changed:', target.getAttribute('data-step'), target.className);
                                shouldUpdate = true;
                            }
                        }
                    });

                    if (shouldUpdate) {
                        updateDesktopStepperColors();
                    }
                }, 200); // Increased debounce delay
            });

            // Start observing with safeguards
            setTimeout(function() {
                if (!isUpdating) {
                    const stepElements = document.querySelectorAll('.stepper .step');
                    stepElements.forEach(function(step) {
                        window.stepObserver.observe(step, { attributes: true, attributeFilter: ['class'] });
                    });
                }
            }, 1000);
        }
        </script>

    <?php $this->load->view('admission/form/new_admission/inc/css'); ?>
    <?php $this->load->view('admission/form/new_admission/inc/script'); ?>


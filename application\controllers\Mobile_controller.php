<?php

class Mobile_controller extends CI_Controller 
{               
	function __construct() {
		parent::__construct();
	}

  public function getDownloadStrings() {
    $download_strs = [
      "parent_controller/savePdf",
      "parent/circular_inbox/downloadMobileCircularAttachment",
      "parent_controller/downloadMobileCircularAttachment",
      "parent_controller/downloadTasksAttachment",
      "parent_controller/downloadEvaluationAttachment",
      "parent_controller/downloadSubmissionAttachment",
      "parent_controller/receipt_pdf_download"
    ];

    echo json_encode($download_strs);
  }

	public function getURI($slug = '') {
    if (empty($slug)) {
      echo json_encode(['slug' => $slug, 'uri'=>'error']);
      return;
    }
    
    $slug = html_entity_decode(strtolower(trim($slug)));
    
    switch ($slug) {
      case 'vec':
        $uri = 'https://vec.schoolelement.in/auth/login';
        break;
      case 'jet':
        $uri = 'https://jet.schoolelement.in/auth/login';
        break;
      case 'vinayaka':
        $uri = 'https://vinayaka.schoolelement.in/auth/login';
        break;
      case 'pncc':
        $uri = 'https://pncc.schoolelement.in/auth/login';
        break;
      case 'risehighv2':
        $uri = 'https://risehigh.schoolelement.in/auth/login';
        break;
      case 'yashasvi':
        $uri = 'https://yashasvi.schoolelement.in/auth/login';
        break;
      case 'northhill':
        $uri = 'https://northhill.schoolelement.in/auth/login';
        break;
      case 'nurture':
        $uri = 'https://nurture.schoolelement.in/auth/login';
        break;
      case 'npsaga':
        $uri = 'https://npsaga.schoolelement.in/auth/login';
        break;
      case 'demoschool1':
        $uri = 'https://demoschool1.schoolelement.in/auth/login';
        break;
      case 'demoschool':
        $uri = 'https://demoschool.schoolelement.in/auth/login';
        break;
      case 'cambridgeypr':
        $uri = 'https://cambridgeypr.schoolelement.in/auth/login';
        break;
      case 'newcarmel':
        $uri = 'https://newcarmel.schoolelement.in/auth/login';
        break;
      case 'achieve':
        $uri = 'https://achieve.schoolelement.in/auth/login';
        break;
      case 'thelandmark':
        $uri = 'https://thelandmark.schoolelement.in/auth/login';
        break;
      case 'jnanamudra':
        $uri = 'https://jnanamudra.schoolelement.in/auth/login';
        break;
      case 'jnanavikas':
        $uri = 'https://jnanavikas.schoolelement.in/auth/login';
        break;
      case 'ses':
        $uri = 'https://ses.schoolelement.in/auth/login';
        break;
      case 'valiant':
        $uri = 'https://valiant.schoolelement.in/auth/login';
        break;
      case 'grei':
        $uri = 'https://grei.schoolelement.in/auth/login';
        break;
      case 'growdr':
        $uri = 'https://growdr.schoolelement.in/auth/login';
        break;
      case 'maithry':
        $uri = 'https://maithry.schoolelement.in/auth/login';
        break;
      case 'npsjnr':
        $uri = 'https://npsjnr.schoolelement.in/auth/login';
        break;
      case 'bwisgunjur':
        $uri = 'https://bwisgunjur.schoolelement.in/auth/login';
        break;
      case 'divine':
        $uri = 'https://divine.schoolelement.in/auth/login';
        break;
      case 'prarthana':
        $uri = 'https://prarthana.schoolelement.in/auth/login';
        break;
      case 'jeff':
        $uri = 'https://jeff.schoolelement.in/auth/login';
        break;
      case 'sgips':
        $uri = 'https://sgips.schoolelement.in/auth/login';
        break;
      case 'anikethanps':
          $uri = 'https://anikethanps.schoolelement.in/auth/login';
          break;
      case 'brvps':
        $uri = 'https://brvps.schoolelement.in/auth/login';
        break;
      case 'gurukul':
        $uri = 'https://gurukul.schoolelement.in/auth/login';
        break;
      case 'pvv':
        $uri = 'https://pvv.schoolelement.in/auth/login';
        break;
      case 'pate':
        $uri = 'https://pate.schoolelement.in/auth/login';
        break;
      case 'jesps':
        $uri = 'https://jesps.schoolelement.in/auth/login';
        break;
      case 'testserver':
        $uri = 'https://testserver.schoolelement.in/auth/login';
        break;
      case 'englishroots':
        $uri = 'https://englishroots.schoolelement.in/auth/login';
        break;
      case 'apsps':
        $uri = 'https://apsps.schoolelement.in/auth/login';
        break;
      case 'apspt':
        $uri = 'https://apspt.campuselement.in/auth/login';
        break;
      case 'apseng':
        $uri = 'https://apseng.campuselement.in/auth/login';
        break;
      case 'apsasc':
        $uri = 'https://apsasc.campuselement.in/auth/login';
        break;
      case 'apscom':
        $uri = 'https://apscom.campuselement.in/auth/login';
        break;
      case 'apsede':
        $uri = 'https://apsede.campuselement.in/auth/login';
        break;
      case 'apsepu':
        $uri = 'https://apsepu.campuselement.in/auth/login';
        break;
      case 'apspuc':
        $uri = 'https://apspuc.campuselement.in/auth/login';
        break;
      case 'apsemps':
        $uri = 'https://apsemps.schoolelement.in/auth/login';
        break;
      case 'apspusc':
        $uri = 'https://apspusc.campuselement.in/auth/login';
        break;
      case 'surabhi':
        $uri = 'https://surabhi.schoolelement.in/auth/login';
        break;
      case 'rosemount':
        $uri = 'https://rosemount.schoolelement.in/auth/login';
        break;
      case 'jspuc':
        $uri = 'https://jspuc.campuselement.in/auth/login';
      break;
      case 'mmvs':
        $uri = 'https://mmvs.schoolelement.in/auth/login';
      break;
      case 'skalvi':
        $uri = 'https://skalvi.schoolelement.in/auth/login';
      break;
      case 'bbul':
        $uri = 'https://bbul.schoolelement.in/auth/login';
      break;
      case 'mles':
        $uri = 'https://mles.schoolelement.in/auth/login';
      break;
      case 'mcs':
        $uri = 'https://mcs.schoolelement.in/auth/login';
      break;
      case 'metpuc':
        $uri = 'https://metpuc.schoolelement.in/auth/login';
      break;
      case 'jsvm':
        $uri = 'https://jsvm.schoolelement.in/auth/login';
      break;
      case 'vasishta':
        $uri = 'https://vasishta.schoolelement.in/auth/login';
        break;
      case 'transcendcollege':
        $uri = 'https://transcendcollege.campuselement.in/auth/login';
        break;
      case 'transcendschool':
        $uri = 'https://transcendschool.campuselement.in/auth/login';
        break;
      case 'transcenddegree':
        $uri = 'https://transcenddegree.campuselement.in/auth/login';
        break;
      case 'dsct':
        $uri = 'https://dsct.schoolelement.in/auth/login';
        break;
      case 'wonderkids':
        $uri = 'https://wonderkids.schoolelement.in/auth/login';
        break;
      case 'vsips':
        $uri = 'https://vsips.schoolelement.in/auth/login';
        break;
      case 'vsiscm':
        $uri = 'https://vsiscm.schoolelement.in/auth/login';
        break;
      case 'vspuc':
        $uri = 'https://vspuc.schoolelement.in/auth/login';
        break;
      case 'smvn':
        $uri = 'https://smvn.schoolelement.in/auth/login';
      break;
      case 'wpl':
        $uri = 'https://wpl.schoolelement.in/auth/login';
      break;
      case 'advitya':
        $uri = 'https://advitya.campuselement.in/auth/login';
      break;
      case 'mis':
        $uri = 'https://mis.schoolelement.in/auth/login';
      break;
      case 'vts':
        $uri = 'https://vts.schoolelement.in/auth/login';
      break;
      case 'omshree':
        $uri = 'https://omshree.schoolelement.in/auth/login';
      break;
      case 'ekpahal':
        $uri = 'https://ekpahal.schoolelement.in/auth/login';
      break;
      case 'vetvvpuram':
        $uri = 'https://vetvvpuram.schoolelement.in/auth/login';
      break;
      case 'vetjpnagar':
        $uri = 'https://vetjpnagar.schoolelement.in/auth/login';
      break;
      case 'vetpuc':
        $uri = 'https://vetpuc.schoolelement.in/auth/login';
      break;
      case 'stmary':
        $uri = 'https://stmary.schoolelement.in/auth/login';
      break;
      case 'heliumacademy':
        $uri = 'https://heliumacademy.schoolelement.in/auth/login';
      break;
      case 'vetfgc':
        $uri = 'https://vetfgc.campuselement.in/auth/login';
        break;
      case 'vetbvlpolytechnic':
        $uri = 'https://vetbvlpolytechnic.campuselement.in/auth/login';
        break;
      case 'wgs':
        $uri = 'https://wgs.schoolelement.in/auth/login';
      break;
      case 'valistus':
        $uri = 'https://valistus.schoolelement.in/auth/login';
      break;
      case 'grips':
        $uri = 'https://grips.schoolelement.in/auth/login';
      break;
      case 'spruha':
        $uri = 'https://spruha.schoolelement.in/auth/login';
      break;
      case 'itutor':
        $uri = 'https://itutor.schoolelement.in/auth/login';
      break;
      case 'apsrhs':
        $uri = 'https://apsrhs.schoolelement.in/auth/login';
      break;
      case 'apshs':
        $uri = 'https://apshs.schoolelement.in/auth/login';
      break;
      case 'apstrust':
        $uri = 'https://apstrust.schoolelement.in/auth/login';
      break;
      case 'vvnedify':
        $uri = 'https://vvnedify.schoolelement.in/auth/login';
      break;
      case 'vvnenglish':
        $uri = 'https://vvnenglish.schoolelement.in/auth/login';
      break;
      case 'vvnpu':
        $uri = 'https://vvnpu.schoolelement.in/auth/login';
      break;
      case 'avin':
        $uri = 'https://avin.schoolelement.in/auth/login';
      break;
      case 'kle':
        $uri = 'https://kle.schoolelement.in/auth/login';
      break;
      case 'vvntrust':
        $uri = 'https://vvntrust.schoolelement.in/auth/login';
      break;
      case 'vettrust':
        $uri = 'https://vettrust.schoolelement.in/auth/login';
      break;
      case 'vvnahs':
        $uri = 'https://vvnahs.schoolelement.in/auth/login';
      break;
      case 'vvndc':
        $uri = 'https://vvndc.campuselement.in/auth/login';
      break;
      case 'flomont':
        $uri = 'https://flomont.schoolelement.in/auth/login';
      break;
      case 'vetarena':
        $uri = 'https://vetarena.schoolelement.in/auth/login';
      break;
      case 'invictustheschool':
        $uri = 'https://invictustheschool.schoolelement.in/auth/login';
      break;
      case 'bomis':
        $uri = 'https://bomis.schoolelement.in/auth/login';
      break;
      case 'stpeter':
        $uri = 'https://stpeter.schoolelement.in/auth/login';
      break;
      case 'ektaschool':
        $uri = 'https://ektaschool.schoolelement.in/auth/login';
      break;
      case 'noblepu':
        $uri = 'https://noblepu.campuselement.in/auth/login';
      break;
      case 'nobledegree':
        $uri = 'https://nobledegree.campuselement.in/auth/login';
      break;
      case 'bwpshyd':
        $uri = 'https://bwpshyd.schoolelement.in/auth/login';
      break;
      case 'vaheglobal':
        $uri = 'https://vaheglobal.schoolelement.in/auth/login';
      break;
      case 'advityadegree':
        $uri = 'https://advityadegree.campuselement.in/auth/login';
      break;
      case 'bwns':
        $uri = 'https://bwns.schoolelement.in/auth/login';
      break;
      case 'ttv':
        $uri = 'https://ttv.schoolelement.in/auth/login';
      break;
      case 'ssvips':
        $uri = 'https://ssvips.schoolelement.in/auth/login';
      break;
      case 'srndegree':
        $uri = 'https://srndegree.campuselement.in/auth/login';
      break;
      case 'srnpu':
        $uri = 'https://srnpu.campuselement.in/auth/login';
      break;
      case 'mbasket':
        $uri = 'https://mbasket.campuselement.in/auth/login';
      break;
      case 'bmnps':
        $uri = 'https://bmnps.schoolelement.in/auth/login';
      break;
      case 'sv':
        $uri = 'https://sv.schoolelement.in/auth/login';
      break;
      case 'suman':
        $uri = 'https://suman.schoolelement.in/auth/login';
      break;
      case 'sgi':
        $uri = 'https://sgi.schoolelement.in/auth/login';
      break;
      case 'sumanlondonkids':
        $uri = 'https://sumanlondonkids.schoolelement.in/auth/login';
      break;
      case 'mvmpuc':
        $uri = 'https://mvmpuc.campuselement.in/auth/login';
      break;
      case 'mvmpucollege':
        $uri = 'https://mvmpucollege.campuselement.in/auth/login';
      break;
      case 'mvmpharmacy':
        $uri = 'https://mvmpharmacy.campuselement.in/auth/login';
      break;
      case 'mvmahs':
        $uri = 'https://mvmahs.campuselement.in/auth/login';
      break;
      case 'mvmasm':
        $uri = 'https://mvmasm.campuselement.in/auth/login';
      break;
      case 'mvmphysio':
        $uri = 'https://mvmphysio.campuselement.in/auth/login';
      break;
      case 'itari':
        $uri = 'https://itari.schoolelement.in/auth/login';
      break;
      case 'isl':
        $uri = 'https://isl.schoolelement.in/auth/login';
      break;
      case 'erl':
        $uri = 'https://erl.schoolelement.in/auth/login';
      break;
      case 'iistest':
        $uri = 'https://iistest.schoolelement.in/auth/login';
      break;
      case 'iish':
        $uri = 'https://iish.schoolelement.in/auth/login';
      break;
      case 'iisb':
        $uri = 'https://iisb.schoolelement.in/auth/login';
      break;
      case 'iisp':
        $uri = 'https://iisp.schoolelement.in/auth/login';
      break;
      case 'iais':
        $uri = 'https://iais.schoolelement.in/auth/login';
      break;
      case 'sof':
        $uri = 'https://sof.schoolelement.in/auth/login';
      break;
      case 'suy':
        $uri = 'https://suy.schoolelement.in/auth/login';
      break;
      case 'ielcw':
        $uri = 'https://ielcw.schoolelement.in/auth/login';
      break;
      case 'ielck':
        $uri = 'https://ielck.schoolelement.in/auth/login';
      break;
      case 'ielcjh':
        $uri = 'https://ielcjh.schoolelement.in/auth/login';
      break;
      case 'ielca':
        $uri = 'https://ielca.schoolelement.in/auth/login';
      break;
      case 'ielcrcn':
        $uri = 'https://ielcrcn.schoolelement.in/auth/login';
      break;
      case 'kriyative':
        $uri = 'https://kriyative.schoolelement.in/auth/login';
      break;
      case 'bbkv':
        $uri = 'https://bbkv.schoolelement.in/auth/login';
      break;
      case 'jetrural':
        $uri = 'https://jetrural.schoolelement.in/auth/login';
        break;
      case 'pnccicse':
        $uri = 'https://pnccicse.schoolelement.in/auth/login';
        break;
      case 'nhws':
        $uri = 'https://nhws.schoolelement.in/auth/login';
        break;
      case 'gcu':
        $uri = 'https://gcu.campuselement.in/auth/login';
        break;
      case 'sneha':
        $uri = 'https://sneha.schoolelement.in/auth/login';
        break;
      case 'anps':
        $uri = 'https://anps.schoolelement.in/auth/login';
        break;
      case 'diya':
        $uri = 'https://diya.schoolelement.in/auth/login';
        break;
      case 'ncjdegree':
        $uri = 'https://ncjdegree.campuselement.in/auth/login';
        break;
      case 'ncjpuc':
        $uri = 'https://ncjpuc.campuselement.in/auth/login';
        break;
      case 'ncbdegree':
        $uri = 'https://ncbdegree.campuselement.in/auth/login';
        break;
      case 'ncbpuc':
        $uri = 'https://ncbpuc.campuselement.in/auth/login';
        break;
      case 'aurinkoacademy':
        $uri = 'https://aurinkoacademy.schoolelement.in/auth/login';
        break;
      case 'ssgs':
        $uri = 'https://ssgs.schoolelement.in/auth/login';
        break;
      case 'kautilya':
        $uri = 'https://kautilya.schoolelement.in/auth/login';
        break;
      case 'bnmps':
        $uri = 'https://bnmps.schoolelement.in/auth/login';
        break;
      case 'snis':
        $uri = 'https://snis.schoolelement.in/auth/login';
        break;
      case 'jadevalley':
        $uri = 'https://jadevalley.schoolelement.in/auth/login';
        break;
      case 'bmsicse':
        $uri = 'https://bmsicse.schoolelement.in/auth/login';
        break;
      case 'bmenglishschool':
        $uri = 'https://bmenglishschool.schoolelement.in/auth/login';
        break;
      case 'bgsvst':
        $uri = 'https://bgsvst.schoolelement.in/auth/login';
        break;
      case 'ssb':
        $uri = 'https://ssb.schoolelement.in/auth/login';
        break;
      case 'ne':
        $uri = 'https://ne.schoolelement.in/auth/login';
        break;
      case 'sspb':
        $uri = 'https://sspb.schoolelement.in/auth/login';
        break;
      case 'sspk':
        $uri = 'https://sspk.schoolelement.in/auth/login';
        break;
      case 'wrps':
        $uri = 'https://wrps.schoolelement.in/auth/login';
        break;
      case 'iisb':
        $uri = 'https://iisb.schoolelement.in/auth/login';
        break;
      case 'iish':
        $uri = 'https://iish.schoolelement.in/auth/login';
        break;
      case 'iisp':
        $uri = 'https://iisp.schoolelement.in/auth/login';
        break;
      case 'iais':
        $uri = 'https://iais.schoolelement.in/auth/login';
        break;
      case 'ielck':
        $uri = 'https://ielck.schoolelement.in/auth/login';
        break;
      case 'ielcjh':
        $uri = 'https://ielcjh.schoolelement.in/auth/login';
        break;
      case 'ielca':
        $uri = 'https://ielca.schoolelement.in/auth/login';
        break;
      case 'ielcw':
        $uri = 'https://ielcw.schoolelement.in/auth/login';
        break;
      case 'ielcrcn':
        $uri = 'https://ielcrcn.schoolelement.in/auth/login';
        break;
      case 'sof':
        $uri = 'https://sof.schoolelement.in/auth/login';
        break;
      case 'suy':
        $uri = 'https://suy.schoolelement.in/auth/login';
        break;
      case 'itari':
        $uri = 'https://itari.schoolelement.in/auth/login';
        break;
      case 'svc':
        $uri = 'https://svc.schoolelement.in/auth/login';
        break;
      case 'ekyabyrathi':
        $uri = 'https://ekyabyrathi.campuselement.in/auth/login';
        break;
      case 'ekyanice':
        $uri = 'https://ekyanice.campuselement.in/auth/login';
        break;
      case 'cmrnps':
        $uri = 'https://cmrnps.campuselement.in/auth/login';
        break;
      case 'manchesterglobal':
        $uri = 'https://manchesterglobal.schoolelement.in/auth/login';
        break;
      case 'gjs':
        $uri = 'https://gjs.schoolelement.in/auth/login';
        break;
      case 'iics':
        $uri = 'https://iics.schoolelement.in/auth/login';
        break;
      case 'vks':
        $uri = 'https://vks.schoolelement.in/auth/login';
        break;
      case 'vkh':
        $uri = 'https://vkh.schoolelement.in/auth/login';
        break;
      case 'grace':
        $uri = 'https://grace.schoolelement.in/auth/login';
        break;
      case 'jicm':
        $uri = 'https://jicm.campuselement.in/auth/login';
        break;
      case 'mws':
        $uri = 'https://mws.schoolelement.in/auth/login';
        break;
      case 'nhsbgudi':
        $uri = 'https://nhsbgudi.schoolelement.in/auth/login';
        break;
      case 'mavk':
        $uri = 'https://mavk.schoolelement.in/auth/login';
        break;
      case 'kga':
        $uri = 'https://kga.schoolelement.in/auth/login';
        break;
      case 'mvmadh':
        $uri = 'https://mvmadh.schoolelement.in/auth/login';
        break;
      case 'mvmkml':
        $uri = 'https://mvmkml.schoolelement.in/auth/login';
        break;
      case 'kesarhosahalli':
        $uri = 'https://kesarhosahalli.schoolelement.in/auth/login';
        break;
      case 'testmysql8':
        $uri = 'https://testmysql8.schoolelement.in/auth/login';
        break;
      case 'neps':
        $uri = 'https://neps.schoolelement.in/auth/login';
        break;
      case 'aikhyathapublicschool':
        $uri = 'https://aikhyathapublicschool.schoolelement.in/auth/login';
        break;
      case 'smespp':
        $uri = 'https://smespp.schoolelement.in/auth/login';
        break;
      case 'smescn':
        $uri = 'https://smescn.schoolelement.in/auth/login';
        break;
      case 'smesdj':
        $uri = 'https://smesdj.schoolelement.in/auth/login';
        break;
      case 'stthresaschool':
        $uri = 'https://stthresaschool.schoolelement.in/auth/login';
        break;
      case 'spusouthend':
        $uri = 'https://spusouthend.campuselement.in/auth/login';
        break;
      case 'pws':
        $uri = 'https://pws.schoolelement.in/auth/login';
        break;
      case 'npsomr':
        $uri = 'https://npsomr.schoolelement.in/auth/login';
        break;
      case 'dmps':
        $uri = 'https://dmps.schoolelement.in/auth/login';
        break;
      case 'dns':
        $uri = 'https://dns.schoolelement.in/auth/login';
        break;
      case 'sesask':
        $uri = 'https://sesask.schoolelement.in/auth/login';
        break;
      case 'idfs':
        $uri = 'https://idfs.schoolelement.in/auth/login';
        break;
      case 'sharada':
        $uri = 'https://sharada.schoolelement.in/auth/login';
        break;
      case 'hkint':
        $uri = 'https://hkint.schoolelement.in/auth/login';
        break;
      case 'demoschool2024':
        $uri = 'https://demoschool2024.schoolelement.in/auth/login';
        break;
      case 'srneve':
        $uri = 'https://srneve.campuselement.in/auth/login';
        break;
      case 'aimit':
        $uri = 'https://aimit.campuselement.in/auth/login';
        break;
      case 'bnmpu':
        $uri = 'https://bnmpu.campuselement.in/auth/login';
        break;
      case 'bnmdegree':
        $uri = 'https://bnmdegree.campuselement.in/auth/login';
        break;
      case 'raya':
        $uri = 'https://raya.schoolelement.in/auth/login';
        break;
      case 'samruddhi':
        $uri = 'https://samruddhi.campuselement.in/auth/login';
        break;
      case 'apsdegree':
        $uri = 'https://apsdegree.campuselement.in/auth/login';
        break;
      case 'samruddhidegree':
        $uri = 'https://samruddhidegree.campuselement.in/auth/login';
        break;
      case 'npskr':
        $uri = 'https://npskr.schoolelement.in/auth/login';
        break;
      case '10xism':
        $uri = 'https://10xism.schoolelement.in/auth/login';
        break;
      case 'adarshschool':
        $uri = 'https://adarshschool.schoolelement.in/auth/login';
        break;
      case 'mpuc':
        $uri = 'https://mpuc.campuselement.in/auth/login';
        break;
      case 'nesb':
        $uri = 'https://nesb.campuselement.in/auth/login';
        break;
      case 'sis':
        $uri = 'https://sis.schoolelement.in/auth/login';
        break;
      case 'tpueastblr':
        $uri = 'https://tpueastblr.campuselement.in/auth/login';
        break;
      case 'tdceastblr':
        $uri = 'https://tdceastblr.campuselement.in/auth/login';
        break;
      case 'ttt':
        $uri = 'https://ttt.schoolelement.in/auth/login';
        break;
      case 'tta':
        $uri = 'https://tta.schoolelement.in/auth/login';
        break;
      case 'newhardwick':
        $uri = 'https://newhardwick.schoolelement.in/auth/login';
        break;
      case 'hisb':
        $uri = 'https://hisb.schoolelement.in/auth/login';
        break;
      case 'sojseg':
        $uri = 'https://sojseg.schoolelement.in/auth/login';
        break;
      case 'ifs':
        $uri = 'https://ifs.schoolelement.in/auth/login';
        break;
      case 'pncctoddlers':
        $uri = 'https://pncctoddlers.schoolelement.in/auth/login';
        break;
      case 'bnmpreschool':
        $uri = 'https://bnmpreschool.schoolelement.in/auth/login';
        break;
      case 'smartstart':
        $uri = 'https://smartstart.schoolelement.in/auth/login';
        break;
      case 'vatican':
        $uri = 'https://vatican.schoolelement.in/auth/login';
        break;
      case 'rcis':
        $uri = 'https://rcis.schoolelement.in/auth/login';
        break;
      case 'ekyanava':
        $uri = 'https://ekyanava.campuselement.in/auth/login';
        break;
      default:
        $uri = 'error';
  }
    echo json_encode(['slug' => $slug, 'uri'=>$uri]);
  }
}

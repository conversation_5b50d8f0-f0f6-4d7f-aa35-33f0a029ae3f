<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON>
 *          <EMAIL>
 *
 * Created:  9 May 2023
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */
class Visitors_v2_controller extends CI_Controller {
  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('VISITOR')) {
        redirect('dashboard', 'refresh');
    }
    $this->load->model('Visitor_v2_model');
    $this->load->library('upload');
    $this->load->library('filemanager');
    $this->config->load('form_elements');
    $this->load->helper('sms_helper');

  }
  public function visitor_dashboard() {
    $data['visitor_security_permit'] = $this->authorization->isAuthorized('VISITOR.SECURITY_REGISTRATION');
    $data['register_permit_staff'] = $this->authorization->isAuthorized('VISITOR.STAFF_PREREGISTRATION');
    $data['staff_approval_permit'] = $this->authorization->isAuthorized('VISITOR.STAFF_APPROVAL');
    $data['vistor_view_permit'] = $this->authorization->isAuthorized('VISITOR.VIEW_REPORTS');
    $data['vistor_switch_school'] = $this->authorization->isAuthorized('VISITOR.SWITCH_SCHOOL');
    $site_url = site_url();
    $data['tiles'] = array(
      [
        'title' => 'Visitor Registration (By Security)',
        'sub_title' => 'Register a vistor',
        'icon' => 'svg_icons/reportcard.svg',
        'url' => $site_url.'Visitors_v2_controller/visitor_list',
        'permission' => $data['visitor_security_permit']
      ],
      [
        'title' => 'Approve Visit',
        'sub_title' => 'Approve Visit',
        'icon' => 'svg_icons/visitorreport.svg',
        'url' => $site_url.'Visitors_v2_controller/approve_visit_by_host',
        'permission' => $data['staff_approval_permit']
      ],
      [
        'title' => 'Visitor Registration (By Staff)',
        'sub_title' => 'Register a vistor',
        'icon' => 'svg_icons/reportcard.svg',
        'url' => $site_url.'Visitors_v2_controller/add_new_visitor_by_host',
        'permission' => $data['register_permit_staff']
      ],
      [
        'title' => 'Switch School',
        'sub_title' => 'school name switch',
        'icon' => 'svg_icons/reportcard.svg',
        'url' => $site_url.'Visitors_v2_controller/switch_school_details',
        'permission' => $data['vistor_switch_school']
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);
   
    $data['report_tiles'] = array(
      [
        'title' => 'Visitor Report',
        'sub_title' => 'View Visitors',
        'icon' => 'svg_icons/visitorreport.svg',
        'url' => $site_url.'Visitors_v2_controller/visitor_detail',
        'permission' => $data['vistor_view_permit']
      ],
      // [
      //   'title' => 'Daily Visitor Report',
      //   'sub_title' => 'View Visitors',
      //   'icon' => 'svg_icons/visitorreport.svg',
      //   'url' => $site_url.'Visitors_v2_controller/daily_visitor_report',
      //   'permission' => $data['vistor_view_permit']
      // ],
      [
        'title' => 'Report By Visitor',
        'sub_title' => 'View Visitors',
        'icon' => 'svg_icons/visitorreport.svg',
        'url' => $site_url.'Visitors_v2_controller/report_by_visitor',
        'permission' => $data['vistor_view_permit']
      ],
      [
        'title' => 'Monthly Summary',
        'sub_title' => 'View Visitors',
        'icon' => 'svg_icons/visitorreport.svg',
        'url' => $site_url.'Visitors_v2_controller/summary_report',
        'permission' => $data['vistor_view_permit']
      ],
      [
        'title' => 'Visitor Frequency Report',
        'sub_title' => 'View Visitors',
        'icon' => 'svg_icons/visitorreport.svg',
        'url' => $site_url.'Visitors_v2_controller/visitor_frequency_report',
        'permission' => $data['vistor_view_permit']
      ],
      [
        'title' => 'Not Checked-out Visitors',
        'sub_title' => 'Not checked-out visitors',
        'icon' => 'svg_icons/reportcard.svg',
        'url' => $site_url.'Visitors_v2_controller/not_checked_out_visitors',
        'permission' => $data['vistor_view_permit']
      ]

    );
    
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
   
    $data['main_content'] = 'visitors_v2/visitor_v2_dashboard';     	
    $this->load->view('inc/template', $data);
  }
  public function visitor_list() {
    $data['visitor_security_permit'] = $this->authorization->isAuthorized('VISITOR.SECURITY_REGISTRATION');
    $date = $this->input->post('date');
    if(empty($date)){
      $date = date('Y-m-d');
    }
    $data['staff_list'] = $this->Visitor_v2_model->staff_details();
    $data['selected'] = $date;
    $date = date('Y-m-d', strtotime($date));
    $data['visitors_list'] = $this->Visitor_v2_model->visitor_data_by_date($date,  $data['visitor_security_permit']);

    
    foreach($data['visitors_list'] as $key => $val) {
      $img_arr= [];
      if($val->visitor_img) {
        $arr= explode(',', $val->visitor_img);
        foreach($arr as $k => $v) {
          array_push($img_arr, $this->filemanager->getFilePath($v));
        }
      }
      $val->images= $img_arr;
    }
    
    $data['total'] = $this->Visitor_v2_model->get_counts($date, 1);
    $data['in'] = $this->Visitor_v2_model->get_counts($date, 2);
    $data['out'] = $this->Visitor_v2_model->get_counts($date, 3);
    $data['campus'] = $this->Visitor_v2_model->get_counts($date, 4);
    $data['otherDateOut'] = $this->Visitor_v2_model->get_counts($date, 5);
   
    // echo '<pre>'; print_r($data); die();

    if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'visitors_v2/index_tablet';
    } else if ($this->mobile_detect->isMobile()) {
          $data['main_content'] = 'visitors_v2/index_mobile';
    } else {
          $data['main_content']    = 'visitors_v2/index';
    }
    $this->load->view('inc/template', $data);
  }
   public function visitor_index($clone_id ='') {
    $data['staffs']= $this->Visitor_v2_model->get_all_staffs();
    $data['clone_data'] = array();
    if ($clone_id != '') {
      $data['clone_data'] =$this->Visitor_v2_model->get_clone_data($clone_id);
      foreach($data['clone_data'] as $key => $val) {
        $img_arr= [];
        if($val->visitor_img) {
          $arr= explode(',', $val->visitor_img);
          foreach($arr as $k => $v) {
            array_push($img_arr, $this->filemanager->getFilePath($v));
          }
        }
        $val->images= $img_arr;
      }
    }
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'visitors_v2/add_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'visitors_v2/add_mobile';
    } else {
      $data['main_content']    = 'visitors_v2/add';
    }
    
    $this->load->view('inc/template', $data);
  }

  public function not_checked_out_visitors() {
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'visitors_v2/not_checked_out_visitors_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'visitors_v2/not_checked_out_visitors_mobile';
    } else {
      $data['main_content']    = 'visitors_v2/not_checked_out_visitors';
    }
    $this->load->view('inc/template', $data);
  }

  public function get_un_checkedout_visitor_data(){
    $result=$this->Visitor_v2_model->get_un_checkedout_visitor_data($_POST);
    foreach($result as $key => $val) {
      $img_arr= [];
      if($val->visitor_img) {
        $arr= explode(',', $val->visitor_img);
        foreach($arr as $k => $v) {
          array_push($img_arr, $this->filemanager->getFilePath($v));
        }
      }
      $val->images= $img_arr;
    }
    echo json_encode($result);
  }

  public function make_checkedout_visitor(){
    $result=$this->Visitor_v2_model->make_checkedout_visitor($_POST);
    echo json_encode($result);
  }

   public function get_old_not_checkedout_remark(){
    $result=$this->Visitor_v2_model->get_old_not_checkedout_remark($_POST);
    echo json_encode($result);
  }

  public function add_visitor_info() { 
    $visitors_details = $this->Visitor_v2_model->add_visitor_list($_GET['page']);

    $email= isset($_POST['email']) ? $_POST['email'] : '';
         // Importing additional libraries
      $this->load->model('communication/emails_model');
      $this->load->model('communication/texting_model', 'texting_model');
      $this->load->helper('email_helper');
    if($visitors_details && $email) {

      
      $school_name= $this->settings->getSetting('school_name');
      $to_meet_id= isset($_POST['to_meet']) ? $_POST['to_meet'] : 'NA';
      if($to_meet_id != 'NA') {
        $to_meet_name= $this->Visitor_v2_model->to_meet_staff_name($to_meet_id);
      } else {
        $to_meet_name= '-';
      }

      $this->load->model('email_model');
      $email_template = $this->email_model->get_email_template_to_send_visitor('meeting confirmation email to parent');

      if (!empty($email_template)) {
        $emailBody =  $email_template->content;

        $memberEmail = [];
        $memberEmail[]['email'] = $email;
        $emailBody = str_replace('%%name%%',$_POST['visitor_name'], $emailBody);
        $emailBody = str_replace('%%staff_to_meet%%', $to_meet_name, $emailBody);
        $emailBody = str_replace('%%date%%',date('Y-m-d', strtotime($_POST['visiting_date'])), $emailBody);
        $emailBody = str_replace('%%school%%',$school_name, $emailBody);

        // Email V2 server different

        $memberEmailStr= implode(',', $memberEmail[0]); // Return str
          
          $email_master_data = array(
            'subject' => $email_template->email_subject,
            'body' => $emailBody,
            'source' => 'Email Template',
            'sent_by' => $this->authorization->getAvatarId(),
            'recievers' => $memberEmailStr,
            'from_email' => $email_template->registered_email,
            'files' => NULL,
            'acad_year_id' => $this->acad_year->getAcadYearId(),
            'visible' => 1,
            'sender_list' =>NULL,
            'sending_status' => 'Completed'
          );
          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          $sent_data = [];
          if(!empty($email_master_id)){
              $obj = new stdClass();
              $obj->email_master_id = $email_master_id;
              $obj->id = $to_meet_id;
              $obj->email = $memberEmailStr;
              $obj->avatar_type = 2;
              $obj->status = ($memberEmail) ? 'Awaited' : 'No Email';
              $sent_data[] = $obj;
          }

          $this->emails_model->save_sending_data($sent_data, $email_master_id);
          sendEmail($emailBody, $email_template->email_subject, $email_master_id, [$memberEmailStr], $email_template->registered_email); // Attachements not required to send

      }
      
    }

    $visitor_v2_info_id= $visitors_details;
    $to_meet_id= isset($_POST['to_meet']) ? $_POST['to_meet'] : 'NA';
    $to_meet_emal= $this->Visitor_v2_model->to_meet_email($to_meet_id);
    
    if($_POST['filled_by'] != 'Staff' && $visitor_v2_info_id && $to_meet_emal) {

      $school_name= $this->settings->getSetting('school_name');
      $to_meet_id= isset($_POST['to_meet']) ? $_POST['to_meet'] : 'NA';
      if($to_meet_id != 'NA') {
        $to_meet_name= $this->Visitor_v2_model->to_meet_staff_name($to_meet_id);
      } else {
        $to_meet_name= '-';
      }

      $this->load->model('email_model');
      $email_template = $this->email_model->get_email_template_to_send_visitor('approval email to meet staff');
      if (!empty($email_template)) {
        $emailBody =  $email_template->content;
        $memberEmail = [];
        $memberEmail[]['email'] = $to_meet_emal;
        $emailBody = str_replace('%%staff_to_meet%%',$_POST['visitor_name'], $emailBody);
        $emailBody = str_replace('%%name%%', $to_meet_name, $emailBody);
        $emailBody = str_replace('%%date%%',date('Y-m-d', strtotime($_POST['visiting_date'])), $emailBody);
        $emailBody = str_replace('%%school%%',$school_name, $emailBody);

        // Email V2 server different
          $memberEmailStr= implode(',', $memberEmail[0]);
          $email_master_data = array(
            'subject' => $email_template->email_subject,
            'body' => $emailBody,
            'source' => 'Email Template',
            'sent_by' => $this->authorization->getAvatarId(),
            'recievers' => $memberEmailStr,
            'from_email' => $email_template->registered_email,
            'files' => NULL,
            'acad_year_id' => $this->acad_year->getAcadYearId(),
            'visible' => 1,
            'sender_list' =>NULL,
            'sending_status' => 'Completed'
          );
          $email_master_id = $this->emails_model->saveEmail($email_master_data);

          $sent_data = [];
          if(!empty($email_master_id)){
              $obj = new stdClass();
              $obj->email_master_id = $email_master_id;
              $obj->id = $to_meet_id;
              $obj->email = $memberEmailStr;
              $obj->avatar_type = 2;
              $obj->status = ($memberEmailStr) ? 'Awaited' : 'No Email';
              $sent_data[] = $obj;
          }

          $this->emails_model->save_sending_data($sent_data, $email_master_id);
          $x= sendEmail($emailBody, $email_template->email_subject, $email_master_id, [$memberEmailStr], $email_template->registered_email); // Attachements not required to send

      }
    }

    $to_meet= $_POST['to_meet'];
    $filled_by= $_POST['filled_by'];
    if(isset($to_meet) && $to_meet != 0 && $filled_by != 'Staff') {

      if($this->settings->getSetting('staff_visitor_approval_notiffication')) {
          $this->load->helper('texting_helper');
          $input_arr = array();
          $input_arr['staff_ids'] = [$to_meet];
          $input_arr['mode'] = 'notification';
          $input_arr['source'] = 'Security Guard';
          $input_arr['message'] = "You got a visitor for aproval. Please go to 'visitor'-> 'Approve' and approve/reject it.";
          $input_arr['title'] = 'Visitor Approval';
          $status= sendText($input_arr);
      }

    }

    $numbers = $this->config->item('numbers');
    if($_GET['page']=='security') {
      if($visitors_details){
        $this->session->set_flashdata('flashSuccess', 'Visitor Info added successfully');
        redirect('Visitors_v2_controller/visitor_list');
      }
      else {
        $this->session->set_flashdata('flashError', 'Something Wrong..');
        redirect('Visitors_v2_controller/visitor_index');
      }
    }
    if($_GET['page']=='staff') {
      if($visitors_details){
        $this->session->set_flashdata('flashSuccess', 'Visitor Info added successfully');
        redirect('Visitors_v2_controller/visitor_dashboard');
      }
      else {
        $this->session->set_flashdata('flashError', 'Something Wrong..');
        redirect('Visitors_v2_controller/visitor_index');
      }
    }
  }  
  public function capture_image(){
    $filename = '';
    if(isset($_FILES['webcam']['name'])){
      $filepath = $this->s3FileUpload($_FILES['webcam']['name']);
      echo $filepath['file_name'];
    }
    
  }

  // private function _resize_image($file, $max_resolution, $type)
  // {
  //   if (file_exists($file)) {
  //     if ($type == 'image/jpeg')
  //       $original_image = imagecreatefromjpeg($file);
  //     else
  //       $original_image = imagecreatefrompng($file);

  //     //check orientation 
  //     // $exif = exif_read_data($file);

  //     try {
  //       $exif = exif_read_data($file);
  //     } catch (Exception $exp) {
  //       $exif = false;
  //     }

  //     if ($exif) {
  //       if (!empty($exif['Orientation'])) {
  //         switch ($exif['Orientation']) {
  //           case 3:
  //             $original_image = imagerotate($original_image, 180, 0);
  //             break;

  //           case 6:
  //             $original_image = imagerotate($original_image, -90, 0);
  //             break;

  //           case 8:
  //             $original_image = imagerotate($original_image, 90, 0);
  //             break;
  //         }
  //       }
  //     }

  //     //resolution
  //     $original_width = imagesx($original_image);
  //     $original_height = imagesy($original_image);

  //     //try width first
  //     $ratio = $max_resolution / $original_width;
  //     $new_width = $max_resolution;
  //     $new_height = $original_height * $ratio;

  //     //if that dosn't work
  //     if ($new_height > $max_resolution) {
  //       $ratio = $max_resolution / $original_height;
  //       $new_height = $max_resolution;
  //       $new_width = $original_width * $ratio;
  //     }
      
  //     if ($original_image) {
  //       $new_image = imagecreatetruecolor($new_width, $new_height);
  //       imagecopyresampled($new_image, $original_image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
  //       if ($type == 'image/jpeg')
  //         imagejpeg($new_image, $file);
  //       else
  //         imagepng($new_image, $file);
  //     }

  //     return $file;
  //     // echo '<br>Resized: ';
  //     // echo filesize($file); 

  //     // echo '<pre>'; print_r($file); die();
  //   }
  // }

  public function upload_catured_pic(){

    $filename = '';
    if(isset($_FILES['visitor_photo']['name'])){
      $filepath = $this->s3FileUpload_2($_FILES['visitor_photo']['name']);
      echo json_encode($filepath['file_name']) ;
    }

    // $filename = '';
    // if(isset($_FILES['visitor_photo'])){

    //   $min_size = $this->_resize_image($_FILES['visitor_photo']['tmp_name'], 1200, $_FILES['visitor_photo']['type']);
    //   $picture = array('tmp_name' => $min_size, 'name' => $min_size);


    //   $filepath = $this->s3FileUpload_2($picture);
    //   echo json_encode($filepath['file_name']);
    // }
    
  }

  public function s3FileUpload_2($file) {

    if($_FILES['visitor_photo']['tmp_name'] == '' || $_FILES['visitor_photo']['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
      }        
      return $this->filemanager->uploadFile($_FILES['visitor_photo']['tmp_name'],$_FILES['visitor_photo']['name'],'visitor');

    // if($_FILES['visitor_photo']['tmp_name'] == '' || $_FILES['visitor_photo']['name'] == '') {
    // return ['status' => 'empty', 'file_name' => ''];
    // }        
    // return $this->filemanager->uploadFile($_FILES['visitor_photo']['tmp_name'],$_FILES['visitor_photo']['name'],'visitor');
  }

  public function s3FileUpload($file) {
    if($_FILES['webcam']['tmp_name'] == '' || $_FILES['webcam']['name'] == '') {
    return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($_FILES['webcam']['tmp_name'],$_FILES['webcam']['name'],'visitor');
  }
  public function get_visitor_data_by_number() {
    $number = $_POST['mobileNumber'];
    $data = $this->Visitor_v2_model->get_visitor_data_by_number($number);
    echo json_encode($data);
  }
 

  public function approve_visit_by_host () {
    $data['visitors_list'] = $this->Visitor_v2_model->visitor_data_for_particular_host();

    foreach($data['visitors_list'] as $key => $val) {
      $img_arr= [];
      if($val->visitor_img) {
        $arr= explode(',', $val->visitor_img);
        foreach($arr as $k => $v) {
          array_push($img_arr, $this->filemanager->getFilePath($v));
        }
      } else {
        array_push($img_arr, 'https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg');
      }
      $val->images= $img_arr;
    }

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'visitors_v2/approve_visit_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'visitors_v2/approve_visit_mobile';
    } else {
      $data['main_content']    = 'visitors_v2/approve_visit';
    }
    $this->load->view('inc/template', $data);
  } 
  public function check_in_visitor() {
    $id = $_POST['id'];
    $date = $_POST['date'];
    $visitor_img = $_POST['visitor_img'];
    $status = $this->Visitor_v2_model->vistor_check_in_insert($id, $date, $visitor_img);
    echo $status;
  } 
  public function save_rfid() {
    $id = $_POST['id'];
    $rfid = $_POST['rfid'];
    $status = $this->Visitor_v2_model->vistor_check_in_rfid_insert($id, $rfid);
    echo $status;
  }
  public function save_photo() {
    $id = $_POST['id'];
    $visitor_img = $_POST['visitor_img'];
    $status = $this->Visitor_v2_model->vistor_photo_insert($id, $visitor_img);
    echo $status;
  }
  public function check_out_visitor() {
    $id = $_POST['id'];
    $date = $_POST['date'];
    $status = $this->Visitor_v2_model->vistor_check_out_insert($id, $date);
    echo $status;
  } 
  public function approve_visit() {
    $visitor_id=$_POST['visitor_id'];
    $approve_status =$_POST['approve_status'];
    $appoved_reject_comments =$_POST['appoved_reject_comments'];
    $echo= $this->Visitor_v2_model->approve_visit($visitor_id,$approve_status,$appoved_reject_comments);

      if($this->settings->getSetting('staff_visitor_approval_notiffication')) {
        $name= $echo['name'];

          $this->load->helper('texting_helper');
          $input_arr = array();
          $input_arr['staff_ids'] = [$visitor_id];
          $input_arr['mode'] = 'notification';
          $input_arr['source'] = 'Security Guard';
          $input_arr['message'] = "Received aproval for $name.";
          $input_arr['title'] = 'Visitor Approval';
          $status= sendText($input_arr);
      }

    echo $echo['status'];

  }
    
  public function whom_to_meet() {
    $visitor_id=$_POST['visitor_id'];
    $whom_to_meet=$_POST['whom_to_meet'];
    $is_approver_changed = $this->Visitor_v2_model->is_approver_present($visitor_id);
    if($is_approver_changed){
      $input = array(
        'title' => 'Notification for Visitor',
        'message' => 'Approval has been moved to another staff.',
        'source' => 'Your source',
        'mode' => 'notification', // or 'notification_sms' if you want to send both notification and SMS
        'send_to' => 'Staff', // Send notification only to staff
        'staff_ids' => array($is_approver_changed) // An array of staff IDs to receive the notification
      );
      $this->load->helper('texting_helper');
      // Call the sendText function
      $response = sendText($input);
  
      // Check the response
      if (!empty($response['error'])) {
          // Error occurred while sending the notification
          echo "Error: " . $response['error'];
      } else {
          // Notification sent successfully to staff
          echo "Notification sent successfully to staff.";
      }
    }
    $result = $this->Visitor_v2_model->whom_to_meet($visitor_id,$whom_to_meet);
    $input = array(
      'title' => 'Notification for Visitor',
      'message' => 'A visitor needs Approval To meet',
      'source' => 'Your source',
      'mode' => 'notification', // or 'notification_sms' if you want to send both notification and SMS
      'send_to' => 'Staff', // Send notification only to staff
      'staff_ids' => array($whom_to_meet) // An array of staff IDs to receive the notification
    );
    $this->load->helper('texting_helper');
    // Call the sendText function
    $response = sendText($input);

    // Check the response
    if (!empty($response['error'])) {
        // Error occurred while sending the notification
        echo "Error: " . $response['error'];
    } else {
        // Notification sent successfully to staff
        echo "Notification sent successfully to staff.";
    }
    echo json_encode($result);
  }
  public function visitor_detail() {
    $fdate = $this->input->post('fdate');
    $todate = $this->input->post('todate');
      if(empty($fdate)){
        $fdate = date('Y-m-d');
      }
      if(empty($todate)){
        $todate = date('Y-m-d');
      }
    $data['selectedFrom'] = $fdate;
    $data['selectedTo'] = $todate;
    $data['visitor_security_permit'] = $this->authorization->isAuthorized('VISITOR.SECURITY_REGISTRATION');
    $data['visitors_det'] = $this->Visitor_v2_model->get_visitor_detail($fdate, $todate, $data['visitor_security_permit']);

    foreach($data['visitors_det'] as $key => $val) {
      $img_arr= [];
      if($val->visitor_img) {
        $arr= explode(',', $val->visitor_img);
        foreach($arr as $k => $v) {
          array_push($img_arr, $this->filemanager->getFilePath($v));
        }
      }
      $val->images= $img_arr;
    }
    $data['total'] = $this->Visitor_v2_model->get_counts_by_range(date('d-m-Y', strtotime($fdate)), date('d-m-Y', strtotime($todate)), 1);
    $data['in'] = $this->Visitor_v2_model->get_counts_by_range(date('d-m-Y', strtotime($fdate)), date('d-m-Y', strtotime($todate)), 2);
    $data['out'] = $this->Visitor_v2_model->get_counts_by_range(date('d-m-Y', strtotime($fdate)), date('d-m-Y', strtotime($todate)), 3);
    $data['campus'] = $this->Visitor_v2_model->get_counts_by_range(date('d-m-Y', strtotime($fdate)), date('d-m-Y', strtotime($todate)), 4);
    $data['otherDateOut'] = $this->Visitor_v2_model->get_counts_by_range(date('d-m-Y', strtotime($fdate)), date('d-m-Y', strtotime($todate)), 5);

    if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
      $data['main_content'] = 'visitors_v2/visitor_report_mobile';
    } else {
      $data['main_content'] = 'visitors_v2/visitor_report';
    }
    $this->load->view('inc/template',$data);

  }
  public function add_new_visitor_by_host($clone_id ='') {
    $data['visitor_security_permit'] = $this->authorization->isAuthorized('VISITOR.SECURITY_REGISTRATION');
    $data['register_permit_staff'] = $this->authorization->isAuthorized('VISITOR.STAFF_PREREGISTRATION');
    $data['logged_in_staff_id'] = $this->authorization->getAvatarStakeHolderId();
    // echo '<pre>A: '; print_r( $data['visitor_security_permit'] );
    // echo '<pre>B: '; print_r( $data['register_permit_staff'] ); die();
    $data['visitor_by_host_data'] = $this->Visitor_v2_model->get_vsitor_data_by_host( $data['logged_in_staff_id']);
    foreach($data['visitor_by_host_data'] as $key => $val) {
      $img_arr= [];
      if($val->visitor_img) {
        $arr= explode(',', $val->visitor_img);
        foreach($arr as $k => $v) {
          array_push($img_arr, $this->filemanager->getFilePath($v));
        }
      }
      $val->images= $img_arr;
    }

    $data['clone_data'] =$this->Visitor_v2_model->get_clone_data($clone_id);
    $data['staffs']= $this->Visitor_v2_model->get_all_staffs();

    if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
      $data['main_content'] = 'visitors_v2/add_new_by_host_mobile';
    } else {
      $data['main_content'] = 'visitors_v2/add_new_by_host';
    }
    
    $this->load->view('inc/template',$data);
  }
  public function daily_visitor_report() {
    $data['daily_visitor_data'] = $this->Visitor_v2_model->daily_visitor_data();
    $data['main_content'] = 'visitors_v2/daily_visitor_report';
    $this->load->view('inc/template',$data);
  }
  public function report_by_visitor() {
    $data['visitor_list'] = $this->Visitor_v2_model->get_visitor_list();
    $data['main_content'] = 'visitors_v2/report_by_visitor';
    $this->load->view('inc/template',$data);
  }
  public function get_data_by_visitor_selection(){
    $visitor_name=$_POST['visitor_name'];
    $result =$this->Visitor_v2_model->get_data_by_visitor_selection($visitor_name);
    echo json_encode($result);
  }
  public function summary_report() {
    $data['acad_year']= $this->Visitor_v2_model->get_acad_year_name_by_id();
    $data['main_content'] = 'visitors_v2/summary_report';
    $this->load->view('inc/template',$data);
  }
  public function get_month_wise_summary() {
    $dates= $_POST['dates'];
    $month_wise= $this->Visitor_v2_model->summary_report($dates);
    echo json_encode($month_wise);
   
  }
  public function visitor_frequency_report() {
    $data['frequency_data'] = $this->Visitor_v2_model->get_frequency_data_by_name();
    $data['main_content'] = 'visitors_v2/visitor_frequency_report';
    $this->load->view('inc/template',$data);
  }

  public function switch_school_details(){
    $data['main_content'] = 'visitors_v2/switch_school';
    $this->load->view('inc/template',$data);
  }
 
  public function details_of_visitor_if_checkin_and_checkout_date_are_different() {
    $res= $this->Visitor_v2_model->details_of_visitor_if_checkin_and_checkout_date_are_different();

    foreach($res as $key => $val) {
      $img_arr= [];
      if($val->visitor_img) {
        $arr= explode(',', $val->visitor_img);
        foreach($arr as $k => $v) {
          array_push($img_arr, $this->filemanager->getFilePath($v));
        }
      }
      $val->images= $img_arr;
    }

    echo json_encode($res);
   
  }

  public function get_and_fill_details() {
    $rfid= $_POST['rfid'];
    $res= $this->Visitor_v2_model->get_and_fill_details($rfid);
    echo json_encode($res);
   
  }

  public function add_details_and_checkin() {
    $rfid= $_POST['rfid'];
    $pics_str= $_POST['pics_str'];
    $visitor_id= $_POST['visitor_id'];
    $res= $this->Visitor_v2_model->add_details_and_checkin($rfid, $pics_str, $visitor_id);
    echo json_encode($res);
   
  }

  public function student_qr_code_scan_get_rfidnumber(){
    $input_qr_code = $_POST['content'];
    echo json_encode($this->Visitor_v2_model->get_student_details_from_qr_code($input_qr_code));
  }

}
?>     
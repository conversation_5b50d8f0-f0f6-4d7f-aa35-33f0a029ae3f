<script type="text/javascript">
var gYears = '<?= json_encode($yearinfo)?>';
 var gYear = $.parseJSON(gYears);
 // console.log(gYear);
 // var yearLength = gYear.length;
 var yearLength = Object.keys(gYear).length;
 // alert(yearLength);
$(document).ready(function () {
    var dob_maxdate = new Date();
    dob_maxdate.setFullYear( dob_maxdate.getFullYear() + 0 );

    var dob_mindate = new Date();
    dob_mindate.setFullYear( dob_mindate.getFullYear() - 30 );

    var doj_maxdate = new Date();
    doj_maxdate.setFullYear( dob_maxdate.getFullYear() + 1 );

    var doj_mindate = new Date();
    doj_mindate.setFullYear( dob_mindate.getFullYear() - 40 );

    $('.datepick').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    });

     $('.datepick1').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
    });

function readURL(input,photo_type) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e) {
            if(photo_type == 'student_photo'){
                $('#previewing').attr('src', e.target.result);
            }else if(photo_type == 'family'){
                $('#family_previewing').attr('src', e.target.result);
            }else if(photo_type == 'stud_sign'){
                $('#stud_sig_previewing').attr('src', e.target.result);
            }
        }

        reader.readAsDataURL(input.files[0]);
    }
}
$('#fileupload').change(function(){
    var src = $(this).val();

    if(src && validate_photo_size(this.files[0],'fileupload','fileuploadError')){

        completed_promises = 0;
        current_percentage = 0;
        total_promises = 1;
        in_progress_promises = total_promises;
        saveFileToStorage(this.files[0],'student');
        $('#previewing').css('opacity','0.3');
        $("#fileuploadError").html("");
        readURL(this, 'student_photo');
    } else{
        this.value = null;
    }
});

$('#family_fileupload').change(function(){
    var src = $(this).val();

    if(src && validate_photo_size(this.files[0], 'family_fileupload','family_fileuploadError')){

        completed_promises = 0;
        current_percentage = 0;
        total_promises = 1;
        in_progress_promises = total_promises;
        saveFileToStorage(this.files[0], 'family');
        $('#family_previewing').css('opacity','0.3');
        $("#family_fileuploadError").html("");
        readURL(this,'family');
    } else{
        this.value = null;
    }
});

$('#stud_sign_fileupload').change(function(){
    var src = $(this).val();
    if(src && validate_photo_size(this.files[0],'stud_sign_fileupload','stud_sign_fileuploadError')){

        completed_promises = 0;
        current_percentage = 0;
        total_promises = 1;
        in_progress_promises = total_promises;
        saveFileToStorage(this.files[0], 'stud_sign');
        $('#stud_sig_previewing').css('opacity','0.3');
        $("#stud_sign_fileuploadError").html("");
        readURL(this,'stud_sign');
    } else{
        this.value = null;
    }
});

$('#documentId').change(function(){
    var src = $(this).val();
    if(src && validate_document(this.files[0], 'documentId')){
        $("#error1").html("");
        readURL(this);
    } else{
        $("#error1").html("Allowed file size exceeded. (Max. 1 Mb) / Allowed file types are jpeg, jpg and pdf");
        this.value = null;
    }
});

var other = '<?php echo $final_preview->nationality ?>';
if (other == 'Other') {
    $('#nationality_other').show();
  }else{
    $('#nationality_other').hide();

  }

var other = '<?php if (!empty($admission_prev_schools->board_other))  echo $admission_prev_schools->board_other ?>';
if (other == 'Other') {
    $('#board_other').show();
}else{
    $('#board_other').hide();

}

var other = '<?php echo $final_preview->religion ?>';
if (other == 'Other') {
    $('#religion_other').show();
  }else{
    $('#religion_other').hide();
  }

var other = '<?php echo $final_preview->std_mother_tongue ?>';
if (other == 'Other') {
    $('#mother_tongue_name').show();
  }else{
    $('#mother_tongue_name').hide();
  }


var other = '<?php echo $final_preview->father_mother_tongue ?>';
if (other == 'Other') {
    $('#f_mother_tongue_name').show();
  }else{
    $('#f_mother_tongue_name').hide();
  }

var other = '<?php echo $final_preview->mother_mother_tongue ?>';
if (other == 'Other') {
    $('#m_mother_tongue_name').show();
  }else{
    $('#m_mother_tongue_name').hide();
  }


});



function validatePhoto(file,errorId){
    if (file.size > 2097152 || file.fileSize > 2097152)
    {
       $("#"+errorId+"Error").html("Allowed file size exceeded. (Max. 2MB)")
       return false;
    }
    if(file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#"+errorId+"Error").html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}


function validate_document(file,error){
    if (file.size > 1000000 || file.fileSize > 1000000){
       $("#"+error+"Error").html("Allowed file size exceeded. (Max. 1 Mb)")
       return false;
    }
    if(file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'application/pdf') {
        $("#"+error+"Error").html("Allowed file types are jpeg, jpg and pdf");
        return false;
    }
    return true;
}



function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}


$(document).ready(function () {
    $('.next').on('click', function () {
        var current = $(this).data('currentBlock'),
        next = $(this).data('nextBlock');
    // only validate going forward. If current group is invalid, do not go further
    // .parsley().validate() returns validation result AND show errors
    if (next > current)
        if (false === $('#std-form').parsley().validate('block' + current))
            return;

    // validation was ok. We can go on next step.
    $('.block' + current)
    .removeClass('show')
    .addClass('hidden');

    $('.block' + next)
    .removeClass('hidden')
    .addClass('show');
    });

    $('.nav-tabs > li a[title]').tooltip();
    
    //Wizard
    $('a[data-toggle="tab"]').on('show.bs.tab', function (e) {

        var $target = $(e.target);
    
        if ($target.parent().hasClass('disabled')) {
            return false;
        }
    });

    $(".next-step").click(function (e) {
        var $active = $('.wizard1 .nav-tabs li.active');
        $active.next().removeClass('disabled');
        nextTab($active);

    });
    $(".prev-step").click(function (e) {

        var $active = $('.wizard1 .nav-tabs li.active');
        prevTab($active);

    });

    $(".student_save").click(function (e){
        // var formData =$("#std-form").serialize();
        var selectedClass = $("#class").val();
        var $form = $('#std-form');
        if ($form.parsley().validate()){
            var form = $('#std-form')[0];
            var formData = new FormData(form);
            $('.student_save').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_staff_controller/updateStudentDetails_erp'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                // cache : false,
                success: function(data) {
                console.log(data);
                $('.student_save').val('Save & Continue').removeAttr('disabled');
                    if(data !='') {
                        // $('#adId').val(data);
                        // $('#afid').val(data);
                        var $active = $('.wizard1 .nav-tabs li.active');
                        $active.next().removeClass('disabled');
                        nextTab($active);
                        get_language_selection(selectedClass);
                        get_subject_details(selectedClass);
                        return 1;
                    } else {
                        alert("Something went wrong in Student data, try again.");
                        return 0;
                    }
                }
            });
        }
    });

    function get_language_selection(selectedClass){
        var admsettingId = '<?php echo $admission_setting_id  ?>';
        $.ajax({
        url: '<?php echo site_url('admission_staff_controller/get_language_class_wise_data'); ?>',
        type: 'post',
        data: {'selectedClass':selectedClass,'admsettingId':admsettingId},
        success: function(data) {
        var res_data = JSON.parse(data);
        if(res_data != ''){
            $('#language_selection').html(construct_language_selection(res_data));
        }
        }
    });
        
    }

    function get_subject_details(selectedClass){
        var admsettingId = '<?php echo $admission_setting_id  ?>';
        $.ajax({
        url: '<?php echo site_url('admission_staff_controller/get_subject_details'); ?>',
        type: 'post',
        data: {'selectedClass':selectedClass,'admsettingId':admsettingId},
        success: function(data) {
        var res_data = JSON.parse(data);
        if(res_data == 1){
           $('.subject_btn').show();
        }else{
            $('.subject_btn').hide();
        }
        }
    });
    }

    function construct_language_selection(res_data){
        var langChoices = {
            'lang_1_choice': '<?php echo $final_preview->lang_1_choice; ?>',
            'lang_2_choice': '<?php echo $final_preview->lang_2_choice; ?>',
            'lang_3_choice': '<?php echo $final_preview->lang_3_choice; ?>'
        };

        var html = '';
        html += `<div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Language selection</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-5" id="">`;
                        for(i in res_data){
                            html += '<div class="form-group">';
                            html += `<label class="control-label col-md-4">${i}</label>`;
                            html += `<div class="col-md-8">`;
                            for(j in res_data[i]){
                                console.log(res_data[i]['required']);
                                if(j != 'required'){
                                    html += `<select class="form-control" name="${j}" ${res_data[i]['required']}>`;
                                    html += '<option value="">Select Language</option>';
                                    for(k in res_data[i][j]){
                                        var selected = '';
                                        if (k == langChoices[j]) {
                                            selected = 'selected';
                                        }
                                        html += `<option ${selected} value="${k}">${res_data[i][j][k]}</option>`;
                                    }
                                    html += `</select>`;
                                }
                            }
                            html += `</div>`;
                            html += `</div>`;
                        }
                        html += `</div>
                            </div>
                        </div>
                    </div>`;
        return html;
    }

    $(".save-step2").click(function (e){
        var f_mobile_no = $("#f_mobile_no").val();
        var m_mobile_no = $("#m_mobile_no").val();
        if (f_mobile_no != undefined && m_mobile_no != undefined  && f_mobile_no != '' && m_mobile_no != '' && f_mobile_no === m_mobile_no) {
            alert("Father's and Mother's Mobile Numbers should not be the same.");
            return false;
        }
        var father_email_id = $("#f_email").val();
        var mother_email_id = $("#m_email").val();
        if (father_email_id != undefined && mother_email_id != undefined && father_email_id != '' && mother_email_id != '' && father_email_id === mother_email_id) {
            alert("Father's and Mother's Email Id's should not be the same.");
            return false;
        }
        //var formData =$("#health-form").serialize();
        var document_form = '<?php echo $this->settings->getSetting('disable_document_tab_in_admissions') ?>';
        var gaurdian_form = '<?php echo $config_val['show_guardian_details'] ?>';
        var medical_form = '<?php echo $this->settings->getSetting('enabled_medical_form_tab_in_admissions') ?>';
        var $form = $('#parent-form');
        if ($form.parsley().validate()){
            var form = $('#parent-form')[0];
            var formData = new FormData(form);
            $('.save-step2').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_staff_controller/update_parent_details_erp'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // console.log(data);
                    $('.save-step2').val('Save & Continue').removeAttr('disabled');
                    if(data == 1) {
                        if(gaurdian_form == 0 && document_form == 1 && medical_form == 0){
                            url = '<?php echo site_url('admission_staff_controller/preview_data_erp') ?>';
                            $('#parent-form').attr('action',url);
                            $('#parent-form').submit();
                        }else{
                            var $active = $('.wizard1 .nav-tabs li.active');
                            $active.next().removeClass('disabled');
                            nextTab($active);
                            get_parent_data();
                            return 1;
                        }
                    } else {
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                }
            });
        }
    });


    function get_parent_data(){
        var insert_id = '<?php echo $insert_id ?>';

        var documenttypes = '<?php echo json_encode($documents) ?>';
        var docsArry = $.parseJSON(documenttypes);

        var documents_required_fields = '<?php echo json_encode($documents_required_fields) ?>';
        var required_fields_arr = $.parseJSON(documents_required_fields);

        var admission_form = '<?php echo json_encode($admission_form) ?>';
        var admission_form_arr = $.parseJSON(admission_form);

        var new_document_type = '<?php echo $config_val['document_input_version'] ?>';
        $.ajax({
                url: '<?php echo site_url('admission_staff_controller/get_parent_data'); ?>',
                type: 'post',
                data: {'af_id':insert_id},
                success: function(data) {
                    var resdata = $.parseJSON(data);
                    if(new_document_type == 'V1'){
                        $('#doc_id').html(_construct_document_table(resdata,docsArry,required_fields_arr,admission_form_arr));
                    }else{
                        $('#doc_id').html(_construct_document_table_new(resdata,docsArry,required_fields_arr,admission_form_arr));
                    }
                }
            });
    }

    function _construct_document_table(resdata,docsArry,required_fields_arr,admission_form_arr){
        var html = '';
        for(var i=0;i<docsArry.length;i++){
            var requiredColor = '';
            var required = '';
            if($.inArray(docsArry[i], required_fields_arr) !== -1){
                requiredColor = '<font color="red">*</font>';
                required = 'required';
            }
            filespath = 0;
            doc_rowId = 0;
            doc_disabledUpload = '';
            $.each(admission_form_arr, function(key,val) {       
                if (val.document_type == docsArry[i]) {
                    filespath = 1;
                    doc_rowId = val.id;
                    doc_disabledUpload = 'disabled';
                    required ='';
                }   
            });

            html += '<tr>';
            html += `<td>${i+1}</td>`;
            html += `<td style="width: 30%;" >${docsArry[i]} ${requiredColor}<input type="hidden" name="document_for" id="document_for${i+1}" value="${docsArry[i]}" ></td>`;
            html += `<td style="border-right: none; width:10%">
                    <input type="file" onchange="upload_document_file_path(${i+1},this)" name="document_file_path" ${required} class="documentUpload" ${doc_disabledUpload} id="doc-upload${i+1}" accept="image/jpeg, application/pdf"/>
                    <div id="afterSuccessUploadShow${i+1}">
                    </div>`;
            html += `<span style="color:red;" id="doc_error${i+1}"></span>`;
                    if(filespath == 1){
            html += `<a style="margin-top: 1rem;" id="successmessageId${i+1}" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>
                    <a style="margin-top: 1rem;" onclick="deletedocument_row_new(${doc_rowId},${i+1})" id="removeButtonId${i+1}" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>`;
                    }
            html += `</td>
                    <td style="border-left: none;" ><span id="percentage_doc_completed${i+1}" style="font-size: 20px; display: none;">0 %</span></td>
                    </tr>`;

        }
        return html; 
    }

    function _construct_document_table_new(resdata,docsArry,required_fields_arr,admission_form_arr){
        var documents_arr = [];
        // console.log(docsArry);
       for(var i = 0;i<docsArry.length;i++){
            if($.type(docsArry[i].condition) === 'undefined'){
                documents_arr.push(docsArry[i]);
                // break;
            }
            if(resdata.nationality == 'Indian'){
                if(docsArry[i].relation == 'student' && docsArry[i].condition == 'if_nationality_indian'){
                     documents_arr.push(docsArry[i]);
                    //  break;
                }
            }else{
                if(docsArry[i].relation == 'student' && docsArry[i].condition == 'if_nationality_other'){
                     documents_arr.push(docsArry[i]);
                    //  break;
                }
            }
            if(resdata.f_nationality == 'Indian'){
                if(docsArry[i].relation == 'father' && docsArry[i].condition == 'if_nationality_indian'){
                     documents_arr.push(docsArry[i]);
                    //  break;
                }
            }else{
                if(docsArry[i].relation == 'father' && docsArry[i].condition == 'if_nationality_other'){
                     documents_arr.push(docsArry[i]);
                    //  break;
                }
            }

            if(resdata.m_nationality == 'Indian'){
                if(docsArry[i].relation == 'mother' && docsArry[i].condition == 'if_nationality_indian'){
                     documents_arr.push(docsArry[i]);
                    //  break;
                }
            }else{
                if(docsArry[i].relation == 'mother' && docsArry[i].condition == 'if_nationality_other'){
                     documents_arr.push(docsArry[i]);
                    //  break;
                }
            }
       }
        var html = '';
        for(var k=0;k<documents_arr.length;k++){
            var requiredColor = '';
            var required = '';
            if($.type(documents_arr[k].required) !== 'undefined' && documents_arr[k].required){
                requiredColor = '<font color="red">*</font>';
                required = 'required';
            }
            var instruction = '';
            if($.type(docsArry[k].instruction)  !== 'undefined'){
                instruction = documents_arr[k].instruction;
            }
            filespath = 0;
            doc_rowId = 0;
            doc_disabledUpload = '';
            $.each(admission_form_arr, function(key,val) {   
                if (val.document_type == documents_arr[k].name) {
                    console.log(val.document_type);    
                    filespath = 1;
                    doc_rowId = val.id;
                    doc_disabledUpload = 'disabled';
                    required ='';
                }
            });
            html += '<tr>';
            html += `<td>${k+1}</td>`;
            html += `<td style="width: 30%;" >${documents_arr[k].name} ${requiredColor}<input type="hidden" name="document_for" id="document_for${k+1}" value="${documents_arr[k].name}" ><span class="help-block">${instruction}</span></td>`;
            html += `<td style="border-right: none; width:10%">
                    <input type="file" onchange="upload_document_file_path(${k+1},this)" name="document_file_path" ${required} class="documentUpload" ${doc_disabledUpload} id="doc-upload${k+1}" accept="image/jpeg, application/pdf"/>
                    <div id="afterSuccessUploadShow${k+1}">
                    </div>`;
            html += `<span style="color:red;" id="doc_error${k+1}"></span>`;
                    if(filespath == 1){
            html += `<a style="margin-top: 1rem;" id="successmessageId${k+1}" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>
                    <a style="margin-top: 1rem;" onclick="deletedocument_row_new(${doc_rowId},${k+1})" id="removeButtonId${k+1}" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>`;
                    }
            html += `</td>
                    <td style="border-left: none;" ><span id="percentage_doc_completed${k+1}" style="font-size: 20px; display: none;">0 %</span></td>
                    </tr>`;
                   
        }
        return html;
    }

    $(".save-step4").click(function (e){
        //var formData =$("#health-form").serialize();
        var document_form = '<?php echo $this->settings->getSetting('disable_document_tab_in_admissions') ?>';
        var medical_form = '<?php echo $this->settings->getSetting('enabled_medical_form_tab_in_admissions') ?>';
        var $form = $('#guardian-form');
        if ($form.parsley().validate()){
            var form = $('#guardian-form')[0];
            var formData = new FormData(form);
            $('.save-step4').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_staff_controller/update_guardian_details_erp'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // console.log(data);
                    $('.save-step4').val('Save & Continue').removeAttr('disabled');
                    if(data == 1) {
                        if(document_form == 1 && medical_form == 0){
                            url = '<?php echo site_url('admission_staff_controller/preview_data_erp') ?>';
                            $('#guardian-form').attr('action',url);
                            $('#guardian-form').submit();
                        }else{
                            var $active = $('.wizard1 .nav-tabs li.active');
                            $active.next().removeClass('disabled');
                            nextTab($active);
                            return 1;
                        }
                    } else {
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                }
            });
        }
    });

    
});

$(".save-step5").click(function (e){
        var document_form = '<?php echo $this->settings->getSetting('disable_document_tab_in_admissions') ?>';
        var $form = $('#medical-form');
        if ($form.parsley().validate()){
            var form = $('#medical-form')[0];
            var formData = new FormData(form);
            $('.save-step5').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_staff_controller/update_medical_form_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // console.log(data);
                    $('.save-step5').val('Save & Continue').removeAttr('disabled');
                    if(data == 1) {
                        if(document_form == 1){
                            url = '<?php  echo site_url('admission_staff_controller/preview_data_erp') ?>';
                            $('#medical-form').attr('action',url);
                            $('#medical-form').submit();
                        }else{
                            var $active = $('.wizard1 .nav-tabs li.active');
                            $active.next().removeClass('disabled');
                            nextTab($active);
                            return 1;
                        }
                    } else {
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                }
            });
        }
    });

function nextTab(elem) {
    $(elem).next().find('a[data-toggle="tab"]').click();
}
function prevTab(elem) {
    $(elem).prev().find('a[data-toggle="tab"]').click();
}



function check_sibling_ad_no() {
   var sb_ad =  $('#sb_admission_number').val();
   $.ajax({
        url: '<?php echo site_url('admission_controller/check_sibling_ad_nobyinput'); ?>',
        type: 'post',
        data: {'sb_ad':sb_ad},
        success: function(data) {
            console.log(data);
            if (data) {
                $('#exit_error').html(data);
            }else{
                $('#exit_error').html("");
            }
        }
    });
}


$('#document_add').on('click',function(){
    var file_doc = $('#documentId').val();
    if(file_doc ==''){
        bootbox.alert({
            title:'Error',
            message: "Upload document and then click Add",
            size: 'small'
        });
        return false;
    }
    var $form = $('#document-form');
    var afid = $('#afid').val();
        var form = $('#document-form')[0];
        var formData = new FormData(form);
        $('#document_add').val('Please wait ...').attr('disabled','disabled');
        $("#loader1").show();
        $.ajax({
            url: '<?php echo site_url('admission_staff_controller/update_documents_erp'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache : false,
            success: function(data) {
                console.log(data);
            $("#loader1").hide();
            $('#document_add').val('Add').removeAttr('disabled');
                if (data != '') {
                    $('#documentId').val('');
                    $.post("<?php echo site_url('admission_staff_controller/get_admission_form_document_erp');?>",{afid:afid},function(data){
                        // console.log(data);
                        var details = $.parseJSON(data);
                        if (details !='') {
                            $('#document_submit').removeAttr('disabled');
                            $('#draft_submit').removeAttr('disabled');
                            var m=1;
                            var html = '';
                            html += '<table class="table">';
                            html += '<thead>';
                            html += '<tr>';
                            html += '<th>#</th>';
                            html += '<th>Name</th>';
                            html += '<th>Action</th>';
                            html += '</tr>';
                            html += '</thead>';
                            html += '<tbody>';
                            for (var i = 0; i < details.length; i ++) {
                                html += '<tr>';
                                html +='<td>'+m+'</td>'
                                html +='<td>'+details[i].document_type+'</td>'
                                html +='<td><a href="javascript:void(0)" onclick="deletedocument_row('+details[i].id+')" class="remove btn btn-danger "><i class="fa fa-trash-o"></i></a></td>'
                                html += '</tr>';
                                m++;
                            }
                            html += '</tbody>';
                            html += '</table>';
                            $('#display_document').html(html);
                        }  
                    });
                }else{
                    alert('Something went wrong');
                }
            }
        });
});

function deletedocument_row(d_id) {
    var afid = $('#afid').val();
    $.ajax({
        url: '<?php echo site_url('admission_staff_controller/delete_documentbyId_erp'); ?>',
        type: 'post',
        data:{'d_id': d_id},
        success: function(data) {
            if(data !='') {
                $.post("<?php echo site_url('admission_staff_controller/get_admission_form_document_erp');?>",{afid:afid},function(data){
                    var details = $.parseJSON(data);
                    var m=1;
                    var html = '';
                    html += '<table class="table">';
                    html += '<thead>';
                    html += '<tr>';
                    html += '<th>#</th>';
                    html += '<th>Name</th>';
                    html += '<th>Action</th>';
                    html += '</tr>';
                    html += '</thead>';
                    html += '<tbody>';
                    for (var i = 0; i < details.length; i ++) {
                        html += '<tr>';
                        html +='<td>'+m+'</td>'
                        html +='<td>'+details[i].document_type+'</td>'
                        html +='<td><a href="javascript:void(0)" onclick="deletedocument_row('+details[i].id+')" class="remove btn btn-danger "><i class="fa fa-trash-o"></i></a></td>'
                        html += '</tr>';
                        m++;
                    }
                    html += '</tbody>';
                    html += '</table>';
                    $('#display_document').html(html);
                });
            } else {
                alert("Something went wrong in Student data, try again.");
                return 0;
            }
        }
    });
}

$('#nationality').on('change',function(){
    var others = $('#nationality').val();
    if(others == 'Other'){
        $('#nationality_other').show();
    }else{
        $('#nationality_other').hide();
    }
});

$('#schooling_board').on('change',function(){
    var others = $('#schooling_board').val();
    if(others == 'Other'){
        $('#board_other').show();
    }else{
        $('#board_other').hide();
    }
});

$('#religion').on('change',function(){
    var others = $('#religion').val();
    if(others == 'Other'){
        $('#religion_other').show();
    }else{
        $('#religion_other').hide();
    }
});


$('#document_for').on('change',function(){
    var others = $('#document_for').val();
    if(others == 'Others'){
        $('#documentName').show();
    }else{
        $('#documentName').hide();
    }
});

$('#mother_tongue').on('change',function(){
    var others = $('#mother_tongue').val();
    if(others == 'Other'){
        $('#mother_tongue_name').show();
    }else{
        $('#mother_tongue_name').hide();
    }
});
 

$('#father_mother_tongue').on('change',function(){
    var others = $('#father_mother_tongue').val();
    if(others == 'Other'){
        $('#f_mother_tongue_name').show();
    }else{
        $('#f_mother_tongue_name').hide();
    }
});

$('#mother_mother_tongue').on('change',function(){
    var others = $('#mother_mother_tongue').val();
    if(others == 'Other'){
        $('#m_mother_tongue_name').show();
    }else{
        $('#m_mother_tongue_name').hide();
    }
});

function breadcrumb_click_confirm(e) {
    bootbox.confirm({
    title : "Confirm",  
    message: "Are you sure you want to exit? <br> <span class='help-block'>On exit, You have to start from the beginning.</span>",
    buttons: {
        confirm: {
            label: 'Yes',
            className: 'btn-success'
        },
        cancel: {
            label: 'No',
            className: 'btn-danger'
        }
    },
    callback: function (result) {
      if(result) { 
        var url = '';
        switch (e){
            case '1':
            url = '<?php echo site_url('dashboard') ?>';
            break;
            case '2':
            url = '<?php echo site_url('admission_process') ?>';
            break;
            case '3':
            url = '<?php echo site_url('admission_staff_controller/mobile_registration') ?>';
            break;
        }
        $('#breadcrumb-form').attr('action',url);
        $('#breadcrumb-form').submit();
        return false;         
      }
    }
  });
}



$('#prev_add').on('click',function(){
    var file_doc = $('#schooling_school').val();
    if(file_doc ==''){
        bootbox.alert({
            title:'Error',
            message: "Enter school name and then click add button",
            size: 'small'
        });
        return false;
    }
    var $form = $('#document-form');
   $('#combinationId').removeAttr('required');
    var afid = $('#afid').val();
    if ($form.parsley().validate()){
        var form = $('#document-form')[0];
        var formData = new FormData(form);
        $('#prev_add').val('Please wait ...').attr('disabled','disabled');
        $.ajax({
            url: '<?php echo site_url('admission_staff_controller/update_previous_school_details_erp'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache : false,
            success: function(data) {
            console.log(data);
            $('#prev_add').val('Add >>').removeAttr('disabled');
                if (data != '') {
                   $('#combinationId').attr('required','required');
                    // $('#schooling_year').val('');
                    $.post("<?php echo site_url('admission_staff_controller/get_previous_school_details_erp');?>",{afid:afid},function(data){
                        var details = $.parseJSON(data);
                        // console.log(details);
                        if (details !='') {
                            var html = '';
                            html += '<table class="table table-bordered">';
                            html += '<thead>';
                            html += '<tr>';
                            html += '<th>Year</th>';
                            html += '<th>Grades and marks obtained in Final Exam</th>';
                            html += '</tr>';
                            html += '</thead>';
                            html += '<tbody>';
                            for (var i = 0; i < details.length; i++) {
                                html += '<tr>';
                                html +='<td>'+details[i].year+'</td>'
                                html +='<td>';
                                html +='<lable>School Name : '+details[i].school_name+'</label><br>';
                                html +='<lable>Class : '+details[i].class+'</label><br>';
                                if (details[i].board =='Other' ) {
                                    html +='<lable>Board : '+details[i].board+', '+details[i].board_other+'</label><br>';
                                }else{
                                     html +='<lable>Board : '+details[i].board+'</label><br>';
                                }
                                html +='<lable>Class : '+details[i].class+'</label><br>';
                                // var json = $.parseJSON(details[i].marks);
                                // console.log(json);
                                html +='<lable>Subject : '
                                for (var j = 0; j < details[i].marks.length; j++) {
                                    var json = $.parseJSON(details[i].marks[j].sub_name);
                                    // console.log(json);
                                    html += '<strong>'+json.name+'</strong> - ' +details[i].marks[j].marks_scored+ ' '+'( '+details[i].marks[j].grade+') '
                                }
                                html +='<a href="javascript:void(0)" onclick="deletepreviou_school_row('+details[i].apsid+')" class="remove btn btn-danger pull-right "><i class="fa fa-trash-o"></i></a>'
                                html +='</lable>';
                                html += '</tr>';
                            }                        
                            html += '</tbody>';
                            html += '</table>';
                            // console.log(html);
                            $('#display_prev').html(html);
                            $('#schooling_class').val('');
                            $('.per').val('');
                            $('.grd').val('');
                            yearLength--;
                            $.ajax({
                                url: '<?php echo site_url('admission_staff_controller/get_remaing_years_selecting_erp'); ?>',
                                type: 'post',
                                data: formData,
                                processData: false,
                                contentType: false,
                                cache : false,
                                success: function(data) {
                                    var years = $.parseJSON(data);
                                    if (years.length== 0) {

                                    }
                                    // console.log(years);
                                    // alert(years.length);
                                    var yearinfo = '<option value="">Select Year</option>';
                                    for (var i = 0; i < years.length; i ++) {
                                      yearinfo +='<option value="'+years[i]+'">'+years[i]+'</option>';
                                    }
                                    // console.log(yearinfo);
                                    $('#schooling_year').html(yearinfo);
                                }
                            });

                            $('#schooling_year').val('');
                            $('.displayshow').hide();
                            $('#prev_add').attr('disabled','disabled');
                        }  
                    });
                }else{
                    alert('Something went wrong');
                }
            }
        });
    }
});

function deletepreviou_school_row(apsid) {
    var $form = $('#document-form');
    var form = $('#document-form')[0];
    var formData = new FormData(form);
    var afid = $('#afid').val();
    $.ajax({
        url: '<?php echo site_url('admission_staff_controller/delete_details_schooling_byId_erp'); ?>',
        type: 'post',
        data:{'apsid': apsid},
        success: function(data) {
            // console.log(data);
            if (data != '') {
                $.post("<?php echo site_url('admission_staff_controller/get_previous_school_details_erp');?>",{afid:afid},function(data){
                    var details = $.parseJSON(data);
                    // console.log(data);
                    if (details !='') {
                        var html = '';
                        html += '<table class="table table-bordered">';
                        html += '<thead>';
                        html += '<tr>';
                        html += '<th>Year</th>';
                        html += '<th>Grades and marks obtained in Final Exam</th>';
                        html += '</tr>';
                        html += '</thead>';
                        html += '<tbody>';
                        for (var i = 0; i < details.length; i++) {
                            html += '<tr>';
                            html +='<td>'+details[i].year+'</td>'
                            html +='<td>';
                            html +='<lable>School Name : '+details[i].school_name+'</label><br>';
                            html +='<lable>Class : '+details[i].class+'</label><br>';
                            if (details[i].board =='Other' ) {
                                html +='<lable>Board : '+details[i].board+', '+details[i].board_other+'</label><br>';
                            }else{
                                 html +='<lable>Board : '+details[i].board+'</label><br>';
                            }
                            html +='<lable>Subject : '

                            for (var j = 0; j < details[i].marks.length; j++) {
                                var json = $.parseJSON(details[i].marks[j].sub_name);
                                // console.log(json);
                                html += '<strong>'+json.name+'</strong> - ' +details[i].marks[j].percentage+ '% '+'( '+details[i].marks[j].grade+') '
                            }

                            html +='<a href="javascript:void(0)" onclick="deletepreviou_school_row('+details[i].apsid+')" class="remove btn btn-danger pull-right "><i class="fa fa-trash-o"></i></a>'
                            html +='</lable>';
                            html += '</tr>';

                        }                        
                        html += '</tbody>';
                        html += '</table>';
                        $('#display_prev').html(html);
                        yearLength++;

                         $.ajax({
                            url: '<?php echo site_url('admission_staff_controller/get_remaing_years_selecting_erp'); ?>',
                            type: 'post',
                            data: formData,
                            processData: false,
                            contentType: false,
                            cache : false,
                            success: function(data) {
                                // console.log(data);
                                var years = $.parseJSON(data);
                                // console.log(years);
                                // alert(years.length);
                                var yearinfo = '<option value="">Select Year</option>';
                                for (var i = 0; i < years.length; i ++) {
                                  yearinfo +='<option value="'+years[i]+'">'+years[i]+'</option>';
                                }
                                // console.log(yearinfo);
                                $('#schooling_year').html(yearinfo);
                            }
                        });

                        return true; 
                    }
                    $.ajax({
                        url: '<?php echo site_url('admission_staff_controller/get_remaing_years_selecting_erp'); ?>',
                        type: 'post',
                        data: formData,
                        processData: false,
                        contentType: false,
                        cache : false,
                        success: function(data) {
                            // console.log(data);
                            var years = $.parseJSON(data);
                            // console.log(years);
                            if (years.length == 0) {
                                documentSubmit(0);
                            }
                            var yearinfo = '<option value="">Select Year</option>';
                            for (var i = 0; i < years.length; i ++) {
                              yearinfo +='<option value="'+years[i]+'">'+years[i]+'</option>';
                            }
                            // console.log(yearinfo);
                            $('#schooling_year').html(yearinfo);
                        }
                    });
                    $('#display_prev').html('');
                    $('#dispalyempty').show();
                    $('#empty').html('Details of Schooling not added');
                    // alert('Something went wrong');
                });
            }else{
               
                alert('Something went wrong');
            }
        }
    });
}

function documentSubmit(val) {
    var required = '<?php echo $required_fields['year']['required'] ?>';
    if (required == 'required') {
        if (yearLength == 0) {
            $('#schooling_year').removeAttr('required');
            $('#schooling_school').removeAttr('required');
            $('#schooling_class').removeAttr('required');
            $('#schooling_board').removeAttr('required');
            $('#school_address').removeAttr('required');
            $('#registration_no').removeAttr('required');
            $('#medium_of_instruction').removeAttr('required');
            url = '<?php  echo site_url('admission_staff_controller/preview_data_erp') ?>';
            $('#document-form').attr('action',url);
            $('#document-form').submit();
            return true;
        }else{
           alert("Enter previous details click to add>>, then you can 'Save and Preview'.");
        }
    }else{
        $('#schooling_year').removeAttr('required');
        $('#schooling_school').removeAttr('required');
        $('#schooling_class').removeAttr('required');
        $('#schooling_board').removeAttr('required');
        $('#school_address').removeAttr('required');
        url = '<?php  echo site_url('admission_staff_controller/preview_data_erp') ?>';
        $('#document-form').attr('action',url);
        $('#document-form').submit();
        return true;
    }
   

    // if (yearLength == 0) {
        // $('#schooling_school').removeAttr('required');
        // $('#schooling_class').removeAttr('required');
        // $('#schooling_board').removeAttr('required');
        // url = '<?php // echo site_url('admission_staff_controller/preview_data_erp') ?>';
        // $('#document-form').attr('action',url);
        // $('#document-form').submit();
        // return true;
    // }else{
    //    alert("Enter previous 3-years' schooling details, then you can 'Save and Preview'.");
    // }
}

function max_marks_total(m) {
    var maxmarks = $('#maxMarks_'+m).val();
    if(maxmarks == ''){
        $('#maxMarks_'+m).val('');
    }
    var tmaxMarks = 0;
    $('.maxMarks').each(function() {
      tmaxMarks += parseFloat($(this).val());
    });

    $('#total_max_marks_entry').val(tmaxMarks);
    var total_max_marks_entry = $('#total_max_marks_entry').val();
    if (total_max_marks_entry == '') {
        total_max_marks_entry = 0;
    }
    var total_marks_scored = $('#total_marks_scored').val();
    if (total_marks_scored == '') {
        total_marks_scored = 0;
    }
    var percentage = parseFloat(total_marks_scored)/parseFloat(total_max_marks_entry)*100;
    $('#total_percentage').val(percentage.toFixed(2));
}
function max_marks_scored_total(m) {
    var maxMarkScored = $('#maxMarkScored_'+m).val();
    if(maxMarkScored == ''){
        $('#maxMarkScored_'+m).val('');
    }
    var tmaxMakrsScored = 0;
    $('.maxMakrsScored').each(function() {
      tmaxMakrsScored += parseFloat($(this).val());
    });
    $('#total_marks_scored').val(tmaxMakrsScored);

    var total_max_marks_entry = $('#total_max_marks_entry').val();
    if (total_max_marks_entry == '') {
        total_max_marks_entry = 0;
    }
    var total_marks_scored = $('#total_marks_scored').val();
    if (total_marks_scored == '') {
        total_marks_scored = 0;
    }
    var percentage = parseFloat(total_marks_scored)/parseFloat(total_max_marks_entry)*100;
    $('#total_percentage').val(percentage.toFixed(2));
}

function check_is_value_not_empty(e, sl) {
    if(e.value !=''){
        $('#maxMarks_'+sl).attr({
           "max" : 125,       
           "min" : 100 
        });

        $('#maxMarkScored_'+sl).attr({
           "max" : 125,       
           "min" : 0
        });
    }else{
        $('#maxMarks_'+sl).attr({
           "max" : 0,       
           "min" : 0 
        });

        $('#maxMarkScored_'+sl).attr({
           "max" : 0,       
           "min" : 0
        });
    }
}



function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function (e) {
            $('#previewing').attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}

 function saveFileToStorage(file, fileType = 'student') {
    // Set up UI elements based on file type
    var progressElement, fileInputElement, previewElement, hiddenInputElement;

    switch(fileType) {
        case 'family':
            progressElement = '#percentage_family_completed';
            fileInputElement = '#family_fileupload';
            previewElement = '#family_previewing';
            hiddenInputElement = '#family_photo_path';
            break;
        case 'stud_sign':
            progressElement = '#percentage_stud_sign_completed';
            fileInputElement = '#stud_sign_fileupload';
            previewElement = '#stud_sig_previewing';
            hiddenInputElement = '#signature_img_path';
            break;
        default: // student photo
            progressElement = '#percentage_student_completed';
            fileInputElement = '#fileupload';
            previewElement = '#previewing';
            hiddenInputElement = '#student_high_quality_url';
    }

    $(progressElement).show();
    $(fileInputElement).attr('disabled','disabled');
    $(".student_save").prop('disabled',true);

    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {'filename':file.name, 'file_type':file.type, 'folder':'profile'},
        success: function(response) {
            single_file_progress(0, fileType);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl":"public-read"
                },
                processData: false,
                data: file,
                xhr: function () {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function (e) {
                        if (e.lengthComputable) {
                            single_file_progress(e.loaded / e.total *100|0, fileType);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    console.log('File uploaded successfully:', path);
                    $(hiddenInputElement).val(path);
                    $(progressElement).hide();
                    $(fileInputElement).removeAttr('disabled');
                    $(previewElement).css('opacity','1');
                    $(".student_save").prop('disabled',false);
                },
                error: function(err) {
                    console.log('Upload error:', err);
                    $(progressElement).hide();
                    $(fileInputElement).removeAttr('disabled');
                    $(previewElement).css('opacity','1');
                    $(".student_save").prop('disabled',false);
                    alert('Upload failed. Please try again.');
                }
            });
        },
        error: function (err) {
            console.log('Signed URL error:', err);
            $(progressElement).hide();
            $(fileInputElement).removeAttr('disabled');
            $(previewElement).css('opacity','1');
            $(".student_save").prop('disabled',false);
            alert('Upload failed. Please try again.');
        }
    });
}

function single_file_progress(percentage, fileType = 'student') {
  if(percentage == 100) {
      in_progress_promises--;
      if(in_progress_promises == 0) {
          current_percentage = percentage;
      }
  } else {
      if(current_percentage<percentage) {
          current_percentage = percentage;
      }
  }

  // Update progress display based on file type
  var progressElement;
  switch(fileType) {
      case 'family':
          progressElement = '#percentage_family_completed';
          break;
      case 'stud_sign':
          progressElement = '#percentage_stud_sign_completed';
          break;
      default:
          progressElement = '#percentage_student_completed';
  }

  $(progressElement).html(`${current_percentage} %`);
  return false;
}

function validate_documents_config_based(files){
    var max_size_string = '<?php echo $documents_size_in_admissions ;?>';
    var file_size = parseFloat(files.size/1024/1024);
    var max_file_size = parseInt(max_size_string);
    if(file_size > max_file_size) {
        return false;
    } else {
        return true;
    }
}
</script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
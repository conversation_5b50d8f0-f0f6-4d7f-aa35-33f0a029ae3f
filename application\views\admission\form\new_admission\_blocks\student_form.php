<div id="loader1" style="display: none;">
    <img src="<?php echo base_url('assets/img/loader_img.gif');?>" width="100" height="100"
        style="position:absolute; top:40%;left:40%;">
</div>

<!-- <h4 style="margin-bottom: 14px; background-color: #f5f5f5; height: 50px; width: 100%; padding: 15px 5px;" class=""><span style="padding: auto;">Student Details</span></h4> -->
<?php $student_name_label = "First Name";
if($config_val['student_first_name_label'])  { $student_name_label = $config_val['student_first_name_label']; } ?>
<?php $freez_name = '' ; if(in_array('student_name',$freez_primary_fields)) $freez_name = 'readonly'; ?>

<div class="row d-flex justify-content-between">
    <div class="col-md-6 mb-4">
        <label class="form-label" for="student_firstname"><?= $student_name_label; ?> &nbsp;<font color="red">*</font>
        </label>
        <input <?= $freez_name ?> placeholder="Enter First Name"
            <?php if(!empty($final_preview->std_name)) echo 'value="'.$final_preview->std_name.'"' ?>
            id="student_firstname" name="student_firstname" type="text" required="" class="form-control input-md"
            data-parsley-error-message="First name is required and should contain only alphabets and spaces."
            data-parsley-pattern="^[a-zA-Z. ]+$" data-parsley-pattern-message="Only alphabets and spaces are allowed."
            data-parsley-minlength="2" data-parsley-minlength-message="First name must be at least 2 characters long">
        <?php if($this->settings->getSetting('enquiry_help_block_parent_name')) { ?>
        <span class="help-block"><?php echo $this->settings->getSetting('enquiry_help_block_parent_name')?></span>
        <?php } else{ ?>
        <span class="help-block">As per official records</span>
        <?php }?>
    </div>

    <?php if (admission_is_enabled($disabled_fields, 'student_middle_name')) : ?>
    <div class="col-md-6 mb-4">
        <label for="student_middlename" class="form-label">
            Middle Name &nbsp;
            <?php if ($required_fields['student_middle_name']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
        </label>
        <input type="text" class="form-control <?= (!empty(form_error('student_middle_name'))) ? 'is-invalid' : '' ?>"
            id="student_middlename" name="student_middle_name" placeholder="Enter Middle Name"
            <?= $required_fields['student_middle_name']['required'] ?>
            data-parsley-error-message="Middle name is required and should contain only alphabets and spaces."
            data-parsley-pattern="^[a-zA-Z. ]+$" data-parsley-pattern-message="Only alphabets and spaces are allowed."
            data-parsley-minlength="2" data-parsley-minlength-message="Middle name must be at least 2 characters long"
            <?php if (!empty($final_preview->student_middle_name)) echo 'value="' . $final_preview->student_middle_name . '"'; ?>>
        <?php if (!empty(form_error('student_middle_name'))) : ?>
        <div class="invalid-feedback"><?= form_error('student_middle_name'); ?></div>
        <?php else : ?>
        <span class="help-block">
            <?= $this->settings->getSetting('enquiry_help_block_parent_name') ?: 'As per official records' ?>
        </span>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if (admission_is_enabled($disabled_fields, 'student_last_name')) : ?>
    <div class="col-md-6 mb-4">
        <label for="student_lastname" class="form-label">
            Last Name &nbsp;
            <?php if ($required_fields['student_last_name']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
        </label>
        <input type="text" class="form-control <?= (!empty(form_error('student_last_name'))) ? 'is-invalid' : '' ?>"
            id="student_lastname" name="student_last_name" placeholder="Enter Last Name"
            <?= $required_fields['student_last_name']['required'] ?>
            data-parsley-error-message="Last name is required and should contain only alphabets and spaces."
            data-parsley-pattern="^[a-zA-Z. ]+$"
            <?php if (!empty($final_preview->student_last_name)) echo 'value="' . $final_preview->student_last_name . '"'; ?>>
        <?php if (!empty(form_error('student_last_name'))) : ?>
        <div class="invalid-feedback"><?= form_error('student_last_name'); ?></div>
        <?php else : ?>
        <span class="help-block">
            <?= $this->settings->getSetting('enquiry_help_block_parent_name') ?: 'As per official records' ?>
        </span>
        <?php endif; ?>
    </div>
    <?php endif; ?>


    <!-- GENDER FIELD -->
    <div class="col-md-6 mb-4">
        <label class="form-label d-block">
            Gender &nbsp;<span class="text-danger">*</span>
        </label>
        <div class="position-relative">
            <select class="form-control" name="gender" id="gender_select" data-parsley-group="block1" required
                data-parsley-error-message="Please select gender.">
                <option value="" disabled selected>Select Gender</option>
                <option value="M"
                    <?= (!empty($final_preview->gender) && $final_preview->gender == 'M') ? 'selected' : '' ?>>Male
                </option>
                <option value="F"
                    <?= (!empty($final_preview->gender) && $final_preview->gender == 'F') ? 'selected' : '' ?>>Female
                </option>
                <option value="O"
                    <?= (!empty($final_preview->gender) && $final_preview->gender == 'O') ? 'selected' : '' ?>>Other
                </option>
            </select>
            <div class="arrow-icon position-absolute"
                style="position: absolute; right: 22px; top: 50%; transform: translateY(-50%);">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>
    </div>

    <!-- DATE OF BIRTH FIELD -->
    <?php $freez_dob = in_array('dob', $freez_primary_fields) ? 'readonly' : ''; ?>
    <div class="col-md-6 mb-4">
        <label for="dob_dtpicker_input" class="form-label">
            Date of Birth &nbsp;<span class="text-danger">*</span>
        </label>

        <div class="position-relative">
            <input type="text" id="dob_dtpicker_input" name="student_dob" class="form-control datepick pe-5"
                placeholder="Enter Date Of Birth" autocomplete="off" required <?= $freez_dob ?>
                data-parsley-error-message="Date of birth is required and must be in DD-MM-YYYY format."
                data-parsley-pattern="^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$"
                data-parsley-pattern-message="Please enter date in DD-MM-YYYY format."
                <?php if (!empty($final_preview->dob) && $final_preview->dob != '00-00-0000') echo 'value="' . date('d-m-Y', strtotime($final_preview->dob)) . '"'; ?>>

            <!-- Bootstrap icon positioned absolutely -->
            <div
                style="position: absolute; right: 1px; top: 50%; transform: translateY(-50%); z-index: 10;padding-right:20px">
                <?php $this->load->view('svg_icons/calendar_icon.svg'); ?>
            </div>
        </div>

        <!-- Help text -->
        <?php if ($this->settings->getSetting('admission_help_block_for_dob')) : ?>
        <span class="help-block"><?= $this->settings->getSetting('admission_help_block_for_dob') ?></span>
        <?php else : ?>
        <span class="help-block">As per official records, format should be
            dd-mm-yyyy</span>
        <?php endif; ?>

        <?php if (!empty($config_val['dob_instructions'])) : ?>
        <span class="help-block"><?= $config_val['dob_instructions'] ?></span>
        <?php endif; ?>

        <?php if ($this->settings->getSetting('admission_age_guidelines_message')) : ?>
        <div class="mt-1">
            <a href="#" data-bs-toggle="modal" data-bs-target="#age_guidelines_message">Read Age Guidelines</a>
        </div>
        <?php endif; ?>

        <label class="form-label mt-1" id="age_cal"></label>
    </div>


    <?php $label = $this->settings->getSetting('your_word_for_class'); ?>
    <?php $freez_class = '' ; if(in_array('grade', $freez_primary_fields)) $freez_class = "style='pointer-events: none;' readonly"; ?>

    <div class="col-md-6 mb-4">
        <label for="class" class="form-label">
            <?= $label ? $label : 'Grade' ?> &nbsp;<span class="text-danger">*</span>
        </label>

        <?php 
    $count = sizeof($class_applied_for); 
    if ($count == 1) {
        echo '<input type="text" class="form-control" name="class" id="class" readonly value="'.$class_applied_for[0].'">';
    } else {
        $grade = !empty($final_preview->grade_applied_for) ? $final_preview->grade_applied_for : '';
        ?>
        <select name="class" id="class" class="form-control" required <?= $freez_class ?>
            data-parsley-error-message="Please select grade it is required">
            <option value="">Select Grade</option>
            <?php foreach ($class_applied_for as $cls): 
                $friendlyName = isset($class_friendly_name[$cls]) ? $class_friendly_name[$cls]->friendly_name : '';
                $selected = ($grade == $cls) ? 'selected' : '';
            ?>
            <option value="<?= $cls ?>" <?= $selected ?>>
                <?= $cls . ' ' . $friendlyName ?>
            </option>
            <?php endforeach; ?>
        </select>
        <div style="position: absolute; right: 50px; top: 45%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
        <?php } ?>

        <span class="help-block">
            <?= $label ? $label : 'Grade' ?> for which admission is being sought
        </span>

        <label class="control-label text-danger" id="age_cal_dob_error"></label>
    </div>

    <?php if (!empty($streams)) : ?>
    <div class="col-md-6 mb-4">
        <label for="combinationId" class="form-label">
            Select Stream &nbsp;<span class="text-danger">*</span>
        </label>
        <?php 
            $combin = !empty($admission_stream->combination) ? $admission_stream->combination : '';
            $combId = !empty($admission_stream->combination_id) ? $admission_stream->combination_id : '';
        ?>

        <select name="stream" id="combinationId" required class="form-control custom-select-arrow"
            data-parsley-error-message="Please select a stream.">
            <option value="">Select Stream</option>
            <?php if(!empty($streams)) foreach ($streams as $comb => $stream): ?>
            <option value="<?= $comb ?>" <?= set_value("stream", $combin) == $comb ? 'selected' : '' ?>>
                <?= $comb ?>
            </option>
            <?php endforeach; ?>
        </select>
        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>

    <?php 
        $checkComb = 0;
        if(!empty($streams)) {
        foreach ($streams as $comb => $value) {
            if (count($value) > 0) {
                $checkComb = 1;
            }
        }
    }
    ?>

    <?php if ($checkComb): ?>
    <div class="col-md-6 mb-4" id="streamCombination">
        <label for="stream_combination_id" class="form-label">
            Combination &nbsp;<span class="text-danger">*</span>
        </label>
        <select class="form-control custom-select-arrow" name="combination" required
            id="stream_combination_id" data-parsley-error-message="Please select a combination."></select>
        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>

        <?php if ($config_val['combination_help_block']) : ?>
        <div class="form-text">
            <a href="#" class="btn p-0 btn-link" onclick="show_combination_description()">Click here</a>
            to get the details regarding combinations
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>



    <input type="hidden" name="combination_id" id="selected_combination_id" value="<?= $combId ?>">
    <script type="text/javascript">
    $(document).ready(function() {
        var admission_setting_id = '<?php echo $admission_setting_id ?>';
        var combination = $('#combinationId').val();
        var selected_combination_id = $('#selected_combination_id').val();
        $.ajax({
            url: '<?php echo site_url('admission_controller/fetch_stream_based_on_combinations'); ?>',
            type: 'post',
            data: {
                'admission_setting_id': admission_setting_id,
                'combination': combination
            },
            success: function(data) {
                var details = $.parseJSON(data);
                var options = '<option value="">Select Combination</option>';
                for (var i = 0; i < details.length; i++) {
                    var selected = '';
                    if (details[i].id == selected_combination_id) {
                        selected = 'selected';
                    }
                    options += '<option ' + selected + ' value="' + details[i].id + '">' + details[
                        i].name + '</option>';
                }
                $('#stream_combination_id').html(options);
            }
        });
        $('#streamCombination').show();
    });


    $('#combinationId').on('change', function() {
        if (this.value == '') {
            $('#streamCombination').hide();
        } else {
            var admission_setting_id = '<?php echo $admission_setting_id ?>';
            var combination = $('#combinationId').val();
            $.ajax({
                url: '<?php echo site_url('admission_controller/fetch_stream_based_on_combinations'); ?>',
                type: 'post',
                data: {
                    'admission_setting_id': admission_setting_id,
                    'combination': combination
                },
                success: function(data) {
                    var details = $.parseJSON(data);
                    var options = '<option value="">Select Combination</option>';
                    for (var i = 0; i < details.length; i++) {
                        options += '<option value="' + details[i].id + '">' + details[i].name +
                            '</option>';
                    }
                    $('#stream_combination_id').html(options);
                }
            });
            $('#streamCombination').show();
        }
    });
    </script>
    <?php endif ?>

    <script type="text/javascript">
    $('#class').on('change', function() {
        var classname = $('#class').val();
        if (classname == '9') {
            $('#langOption').show();
            $('#lang_choiceId').attr('required', 'required');
        } else {
            $('#langOption').hide();
            $('#lang_choiceId').removeAttr('required');
        }
    });

    $(document).ready(function() {
        var classname =
            '<?php if(!empty($final_preview->grade_applied_for)) echo $final_preview->grade_applied_for ?>';
        if (classname == '9') {
            $('#langOption').show();
            $('#lang_choiceId').attr('required', 'required');
        } else {
            $('#langOption').hide();
            $('#lang_choiceId').removeAttr('required');
        }
    });
    </script>


    <?php if (!empty($config_val['custom_field'])): ?>
    <?php foreach ($config_val['custom_field'] as $key => $val): ?>
    <div class="col-md-6 mb-4">
        <label for="<?= $val->label ?>" class="form-label">
            <?= ucwords(str_replace('_', ' ', $val->label)) ?>
            <?php if ($required_fields['custom_field']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
        </label>
        <input type="text" class="form-control" id="<?= $val->label ?>" name="custom_fileds[<?= $val->label ?>]"
            placeholder="Enter <?= ucwords(str_replace('_', ' ', $val->label)) ?>"
            <?= $required_fields['custom_field']['required'] ?>
            data-parsley-error-message="<?= ucwords(str_replace('_', ' ', $val->label)) ?> is required."
            data-parsley-minlength="2" data-parsley-minlength-message="<?= ucwords(str_replace('_', ' ', $val->label)) ?> must be at least 2 characters long."
            <?php if (!empty($final_preview->custom_field[$val->label])) echo 'value="' . $final_preview->custom_field[$val->label] . '"'; ?>>
        <small class="form-text text-muted"><?php echo isset($val->help_text) ? $val->help_text : ''; ?></small>
    </div>
    <?php endforeach; ?>
    <?php endif; ?>

    <?php if(admission_is_enabled($disabled_fields, 'sats_number')): ?>
    <div class="col-md-6 mb-4">
        <label for="sats_number" class="form-label">
            SATS Number &nbsp;
            <?php if ($required_fields['sats_number']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
        </label>
        <input type="text" class="form-control" id="sats_number" name="sats_number" placeholder="Enter SATS Number"
            <?= $required_fields['sats_number']['required'] ?>
            <?php if (!empty($final_preview->sats_number)) echo 'value="' . $final_preview->sats_number . '"'; ?>
            data-parsley-error-message="SATS number is required."
            data-parsley-pattern="^[A-Za-z0-9]+$" data-parsley-pattern-message="SATS number should contain only letters and numbers."
            data-parsley-minlength="3" data-parsley-minlength-message="SATS number must be at least 3 characters long.">
    </div>
    <?php endif; ?>

    <?php if(admission_is_enabled($disabled_fields, 'birth_taluk')): ?>
    <div class="col-md-6 mb-4">
        <div class="row">
            <div class="col-12 col-sm-6 mb-3" style="padding-right:10px !important">
                <label for="birth_taluk" class="form-label">
                    Birth Place (Region/District)
                    <?php if ($required_fields['birth_taluk']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
                </label>
                <input type="text" class="form-control" id="birth_taluk" name="birth_taluk" placeholder="Region/District"
                    data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$"
                    <?= $required_fields['birth_taluk']['required'] ?>
                    <?php if (!empty($final_preview->birth_taluk)) echo 'value="' . $final_preview->birth_taluk . '"'; ?>>
            </div>
            <div class="col-12 col-sm-6 mb-3" style="padding-left:5px !important">
                <label for="birth_district" class="form-label">
                    Birth Place (State) &nbsp;
                    <?php if ($required_fields['birth_district']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
                </label>
                <input type="text" class="form-control" id="birth_district" name="birth_district" placeholder="State"
                    data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$"
                    <?= $required_fields['birth_district']['required'] ?>
                    <?php if (!empty($final_preview->birth_district)) echo 'value="' . $final_preview->birth_district . '"'; ?>>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if(admission_is_enabled($disabled_fields, 'student_aadhar')): ?>
    <div class="col-md-6 mb-4">
        <label for="student_aadhar" class="form-label">
            Aadhar card No &nbsp;
            <?php if ($required_fields['student_aadhar']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
        </label>
        <input type="text" class="form-control" id="student_aadhar" name="student_aadhar"
            placeholder="Enter Aadhar Number" data-parsley-type="digits"
            data-parsley-type-message="Aadhar number should contain only digits." data-parsley-length="[12, 12]"
            data-parsley-error-message="Aadhar number must be exactly 12 digits" <?= $required_fields['student_aadhar']['required'] ?>
            <?php if (!empty($final_preview->student_aadhar)) echo 'value="' . $final_preview->student_aadhar . '"'; ?>>
    </div>
    <?php endif; ?>

    <?php if(admission_is_enabled($disabled_fields, 'student_quota')): ?>
    <div class="col-md-6 mb-4">
        <label for="student_quota" class="form-label">
            Quota &nbsp;
            <?php if ($required_fields['student_quota']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
        </label>
        <select id="student_quota" name="student_quota" class="form-control custom-select-arrow"
            <?= $required_fields['student_quota']['required'] ?> data-parsley-error-message="Please select quota">
            <option value="">Select Quota</option>
            <?php if (!empty($this->settings->getSetting('quota'))): ?>
            <?php foreach ($this->settings->getSetting('quota') as $key => $value): ?>
            <option value="<?= $key ?>" <?= ($final_preview->student_quota == $key) ? 'selected' : '' ?>>
                <?= $value ?>
            </option>
            <?php endforeach; ?>
            <?php endif; ?>
        </select>
        <div style="position: absolute; right: 50px; top: 67%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif; ?>


    <?php if(admission_is_enabled($disabled_fields, 'student_blood_group')) :  ?>
    <div class="col-md-6 mb-4">
        <label for="student_blood_group" class="form-label">
            Blood Group &nbsp;
            <?php if ($required_fields['student_blood_group']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
        </label>

        <?php
            $student_blood_group = !empty($final_preview->student_blood_group) ? $final_preview->student_blood_group : '';
            $array = ['' => 'Select Blood Group'];
            foreach ($this->config->item('blood_groups') as $key => $bloodgroup) {
                $array[$bloodgroup] = $bloodgroup;
            }
        ?>

        <select name="student_blood_group" id="student_blood_group" class="form-control custom-select-arrow"
            <?= $required_fields['student_blood_group']['required'] ?>
            data-parsley-error-message="Please select blood group">
            <?php foreach ($array as $key => $label): ?>
            <option value="<?= $key ?>"
                <?= set_value("student_blood_group", $student_blood_group) == $key ? 'selected' : '' ?>>
                <?= $label ?>
            </option>
            <?php endforeach; ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>

    <?php if (admission_is_enabled($disabled_fields, 'nationality')) : ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="nationality">
            Nationality &nbsp;
            <?php if ($required_fields['nationality']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php
        $nationality = !empty($final_preview->nationality) ? $final_preview->nationality : '';
        $nationality_list = $this->config->item('nationality');
        $is_required = $required_fields['nationality']['required'];
        ?>

        <select name="nationality" id="nationality" class="form-control" <?= $is_required ?>
            onchange="add_required_aadhar_number()" data-parsley-error-message="Please select nationality.">
            <?php foreach ($nationality_list as $nation): ?>
            <option value="<?= $nation ?>" <?= ($nationality == $nation) ? 'selected' : '' ?>>
                <?= $nation ?>
            </option>
            <?php endforeach; ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>

    <div id="nationality_other" class="col-md-6 mb-4" style="display: none;">
        <label class="form-label" for="others">Others</label>
        <input placeholder="Please Specify"
            <?php if (!empty($final_preview->nationality_other)) echo 'value="' . $final_preview->nationality_other . '"'; ?>
            id="others" name="nationality_other" type="text" class="form-control input-md"
            data-parsley-error-message="Please specify your nationality."
            data-parsley-pattern="^[a-zA-Z. ]+$" data-parsley-pattern-message="Only alphabets, dots and spaces are allowed."
            data-parsley-minlength="2" data-parsley-minlength-message="Nationality must be at least 2 characters long.">
        <span class="help-block">Please Specify</span>
    </div>
    <?php endif; ?>





    <?php if(admission_is_enabled($disabled_fields, 's_present_addr')) : ?>
    <div class="row">
        <!-- Label takes full width -->
        <div class="col-12">
            <label class="form-label" for="s_present_addr">
                Present/Communication Address &nbsp;
                <?php if($required_fields['s_present_addr']['required']=='required') echo'<font color="red">*</font>' ?>
            </label>
        </div>
        <!-- Fields row: textarea left, other fields right -->
        <div class="col-md-6 mb-4 d-flex flex-column">
            <textarea class="form-control mb-2 flex-grow-1" <?php echo $required_fields['s_present_addr']['required'] ?>
                id="s_present_addr" name="s_present_addr" rows="7" style="min-height: 160px; height:100%;"
                placeholder="Enter Present Address" data-parsley-maxlength="200"
                data-parsley-maxlength-message="Present address cannot exceed 200 characters"
                data-parsley-error-message="Enter Present address"><?php if(!empty($final_preview->s_present_addr)) echo $final_preview->s_present_addr ?></textarea>
            <span class="help-block"> Write the complete address</span>
        </div>
        <div class="col-md-6 mb-4 d-flex flex-column" style="padding-left:15px;padding-right:0">
            <div class="row flex-grow-1">
                <div class="col-12 mb-3">
                    <input type="text" id="s_present_area" <?php echo $required_fields['s_present_area']['required'] ?>
                        placeholder="Enter Area" class="form-control"
                        <?php if(!empty($final_preview->s_present_area)) echo 'value="'.$final_preview->s_present_area.'"' ?>
                        name="s_present_area" data-parsley-error-message="Area is required."
                        data-parsley-minlength="2" data-parsley-minlength-message="Area must be at least 2 characters long." data-parsley-maxlength="60"
                        data-parsley-pattern="^[a-zA-Z0-9\s.,\-()]+$" data-parsley-pattern-message="Only letters, numbers, spaces, and common punctuation allowed.">
                </div>
                <div class="col-12 mb-3">
                    <input type="text" id="s_present_district" placeholder="Enter District"
                        <?php echo $required_fields['s_present_district']['required'] ?> class="form-control"
                        <?php if(!empty($final_preview->s_present_district)) echo 'value="'.$final_preview->s_present_district.'"' ?>
                        name="s_present_district"
                        data-parsley-error-message="District is required and should contain alphabets and spaces only."
                        data-parsley-pattern="^[a-zA-Z ]+$">
                </div>
                <div class="row g-0">
                    <div class="col-md-4  mb-3 position-relative">
                        <?php
                        $array = array(''=>'Country');
                        foreach ($this->config->item('country') as $key => $nation) {
                            $array[$nation] =  $nation;
                        }
                        echo form_dropdown("s_present_country", $array, set_value("s_present_country",$final_preview->s_present_country), "id='s_present_country' ".$required_fields['s_present_country']['required']." class='form-control custom-select-arrow' data-parsley-error-message='Please select country.'");
                        ?>
                        <div
                            style="position: absolute; right: 15px; top: 45%; transform: translateY(-50%); pointer-events: none; z-index: 2; display: flex; align-items: center; justify-content: center;">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3 position-relative" id="state_select">
                        <select class="form-control" id="s_present_state" name="s_present_state">
                            <option value="">Select State</option>
                            <?php foreach ($this->config->item('states') as $state) { 
                            $selected = ($final_preview->s_present_state == $state) ? 'selected' : ''; ?>
                            <option value="<?= $state ?>" <?= $selected ?>><?= $state ?></option>
                            <?php } ?>
                        </select>
                        <div
                            style="position: absolute; right: 25px; top: 45%; transform: translateY(-50%); pointer-events: none;">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                    <div class="col-md-4  mb-3" id="state_input" style="display:none;">
                        <input type="text" id="s_present_state1" placeholder="Enter State" class="form-control"
                            <?php if(!empty($final_preview->s_present_state)) echo 'value="'.$final_preview->s_present_state.'"' ?>
                            name="s_present_state1" data-parsley-error-message="State is required only alphabets and spaces are allowed."
                            data-parsley-pattern="^[a-zA-Z ]+$">
                    </div>
                    <div class="col-md-4 mb-3">
                        <input id="s_present_pincode" name="s_present_pincode"
                            <?php echo $required_fields['s_present_pincode']['required'] ?> placeholder="Pincode"
                            type="text"
                            <?php if(!empty($final_preview->s_present_pincode)) echo 'value="'.$final_preview->s_present_pincode.'"' ?>
                            class="form-control" data-parsley-type="digits" data-parsley-length="[3, 10]"
                            data-parsley-type-message="Pincode should contain only digits."
                            data-parsley-length-message="Pincode must be between 3 and 10 digits."
                            data-parsley-error-message="Please enter a valid pincode.">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <?php if(!in_array('s_permanent_addr',$disabled_fields) && !in_array('s_present_addr',$disabled_fields)) { ?>
    <div class="col-md-12 mb-4" style="display: block;">
        <div class="col-md-8 p-0">
            <label for="copy_present_address" class="mobile-checkbox-label" style="cursor: pointer; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: flex; align-items: center; padding: 15px 10px; margin: 0; border-radius: 8px; transition: background-color 0.2s ease; -webkit-tap-highlight-color: rgba(0,0,0,0.1); position: relative;">
                <input type="checkbox" id="copy_present_address" value="" style="margin-right: 12px; transform: scale(1.5); cursor: pointer; min-width: 20px; min-height: 20px; z-index: 10; position: relative;">
                <span style="margin-left: 8px; font-size: 16px; line-height: 1.4; pointer-events: none;">Same As Present Address</span>
            </label>
        </div>
    </div>
    <?php } ?>

    <?php if(admission_is_enabled($disabled_fields, 's_permanent_addr')) : ?>
    <div class="row">
        <!-- Label takes full width -->
        <div class="col-12">
            <label class="form-label" for="s_permanent_addr">
                Permanent Address &nbsp;
                <?php if($required_fields['s_permanent_addr']['required']=='required') echo'<font color="red">*</font>' ?>
            </label>
        </div>
        <!-- Fields row: textarea left, other fields right -->
        <div class="col-md-6 mb-4 d-flex flex-column">
            <textarea class="form-control mb-2 flex-grow-1"
                <?php echo $required_fields['s_permanent_addr']['required'] ?> id="s_permanent_addr"
                name="s_permanent_addr" rows="7" style="min-height: 160px; height:100%;"
                placeholder="Enter Permanent Address" data-parsley-maxlength="100"
                data-parsley-maxlength-message="Present address cannot exceed 100 characters"
                data-parsley-error-message="Address is required"><?php if(!empty($final_preview->s_permanent_addr)) echo $final_preview->s_permanent_addr ?></textarea>
            <span class="help-block">Write the complete address</span>
        </div>
        <div class="col-md-6 mb-4 d-flex flex-column">
            <div class="row flex-grow-1">
                <div class="col-12 mb-3">
                    <input type="text" id="s_permanent_area"
                        <?php echo $required_fields['s_permanent_area']['required'] ?> placeholder="Enter Area"
                        class="form-control"
                        <?php if(!empty($final_preview->s_permanent_area)) echo 'value="'.$final_preview->s_permanent_area.'"' ?>
                        name="s_permanent_area" data-parsley-error-message="Area is required">
                </div>
                <div class="col-12 mb-3">
                    <input type="text" id="s_permanent_district" placeholder="Enter District"
                        <?php echo $required_fields['s_permanent_district']['required'] ?> class="form-control"
                        <?php if(!empty($final_preview->s_permanent_district)) echo 'value="'.$final_preview->s_permanent_district.'"' ?>
                        name="s_permanent_district" data-parsley-error-message="Only alphabets and spaces are allowed."
                        data-parsley-pattern="^[a-zA-Z ]+$">
                </div>
                <div class="row g-0">
                    <div class="col-md-4  mb-3 position-relative">
                        <?php 
                        $array = array(''=>'Country');
                        foreach ($this->config->item('country') as $nation) {
                            $array[$nation] =  $nation;
                        }
                        echo form_dropdown("s_permanent_country", $array, set_value("s_permanent_country",$final_preview->s_permanent_country), "id='s_permanent_country' ".$required_fields['s_permanent_country']['required']." class='form-control'");
                        ?>
                        <div
                            style="position: absolute; right: 20px; top: 45%; transform: translateY(-50%); pointer-events: none;">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                    <div class="col-md-4  mb-3 position-relative" id="per_state_select">
                        <select name="s_permanent_state" id="s_permanent_state" class="form-control"
                            data-parsley-error-message="">
                            <option value="">Select State</option>
                            <?php foreach ($this->config->item('states') as $state) {
                            $selected = ($final_preview->s_permanent_state == $state) ? 'selected' : ''; ?>
                            <option value="<?= $state ?>" <?= $selected ?>><?= $state ?></option>
                            <?php } ?>
                        </select>
                        <div
                            style="position: absolute; right: 25px; top: 45%; transform: translateY(-50%); pointer-events: none;">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                    <div class="col-md-4  mb-3" style="display: none;" id="per_state_input">
                        <input type="text" id="s_permanent_state1" placeholder="Enter State" class="form-control"
                            <?php if(!empty($final_preview->s_permanent_state)) echo 'value="'.$final_preview->s_permanent_state.'"' ?>
                            name="s_permanent_state1"
                            data-parsley-error-message="Only alphabets and spaces are allowed."
                            data-parsley-pattern="^[a-zA-Z ]+$">
                    </div>
                    <div class="col-md-4 mb-3">
                        <input id="s_permanent_pincode" name="s_permanent_pincode"
                            <?php echo $required_fields['s_permanent_pincode']['required'] ?> placeholder="Pincode"
                            type="text"
                            <?php if(!empty($final_preview->s_permanent_pincode)) echo 'value="'.$final_preview->s_permanent_pincode.'"' ?>
                            class="form-control" data-parsley-type="digits" data-parsley-length="[3, 10]"
                            data-parsley-type-message="Pincode should contain only digits."
                            data-parsley-length-message="Pincode must be between 3 and 10 digits."
                            data-parsley-error-message="Please enter a valid pincode.">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if (admission_is_enabled($disabled_fields, 'std_mother_tongue')) : ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="mother_tongue">
            Mother Tongue &nbsp;
            <?php if ($required_fields['std_mother_tongue']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php
        $mother_tongue = !empty($final_preview->std_mother_tongue) ? $final_preview->std_mother_tongue : '';
        $mother_tongue_list = $this->config->item('languages');
        $is_required = $required_fields['std_mother_tongue']['required'];
        ?>

        <select name="std_mother_tongue" id="mother_tongue" class="form-control custom-select-arrow"
            <?= $is_required ?> data-parsley-error-message="Please select mother tongue.">
            <option value="">Select Mother Tongue</option>
            <?php foreach ($mother_tongue_list as $lang): ?>
            <option value="<?= $lang ?>" <?= ($mother_tongue == $lang) ? 'selected' : '' ?>>
                <?= $lang ?>
            </option>
            <?php endforeach; ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>

    <div id="mother_tongue_name" class="col-md-6 mb-4" style="display: none;">
        <label class="form-label" for="mother_tongue_others">Others</label>
        <input placeholder="Enter Please Specify"
            <?php if (!empty($final_preview->mother_tongue_other)) echo 'value="' . $final_preview->mother_tongue_other . '"'; ?>
            id="mother_tongue_other" name="mother_tongue_other" type="text" class="form-control input-md"
            data-parsley-error-message="Please specify your mother tongue."
            data-parsley-pattern="^[a-zA-Z. ]+$" data-parsley-pattern-message="Only alphabets, dots and spaces are allowed."
            data-parsley-minlength="2" data-parsley-minlength-message="Mother tongue must be at least 2 characters long.">
        <span class="help-block">Please Specify</span>
    </div>
    <?php endif; ?>

   <?php if(admission_is_enabled($disabled_fields, 'ration_card_number')) :  ?>
        <div class="col-md-6 mb-4">
            <label class="form-label" for="ration_card_number">
                Ration Card Number
                &nbsp;
                <?php if($required_fields['ration_card_number']['required']=='required') echo '<font color="red">*</font>'; ?>
            </label>

            <input type="text"
                id="ration_card_number"
                name="ration_card_number"
                class="form-control input-md"
                placeholder="Enter Ration Card Number"
                <?php echo $required_fields['ration_card_number']['required']; ?>
                data-parsley-type="alphanum"
                data-parsley-pattern="^[A-Za-z0-9]{8,14}$"
                data-parsley-pattern-message="Enter a valid Ration Card Number (8–14 alphanumeric characters)"
                data-parsley-trigger="blur"
                data-parsley-error-message="Enter a valid Ration Card Number"
                <?php if (!empty($final_preview->ration_card_number)) echo 'value="' . $final_preview->ration_card_number . '"'; ?>
            >
        </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'ration_card_type')) :  ?>

    <div class="col-md-6 mb-4">
        <label class="form-label fw-semibold mb-2">Ration Card Type &nbsp;
            <?php if($required_fields['ration_card_type']['required']=='required') echo'<font color="red">*</font>' ?>
        </label>
        <select class="form-control" name="ration_card_type" id="ration_card_type_select" required
            data-parsley-error-message="Please select ration card type.">
            <option value="" selected>Select Ration Card Type</option>
            <option value="APL"
                <?= (!empty($final_preview->ration_card_type) && $final_preview->ration_card_type == 'APL') ? 'selected' : '' ?>>
                APL</option>
            <option value="BPL"
                <?= (!empty($final_preview->ration_card_type) && $final_preview->ration_card_type == 'BPL') ? 'selected' : '' ?>>
                BPL</option>
        </select>
        <div style="position: absolute; right: 50px; top: 60%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>

    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'curriculum_currently_studying')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="curriculum_currently_studying">
            Curriculum Currently Studying &nbsp;
            <?php if ($required_fields['curriculum_currently_studying']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php
            $currentlyBoard = !empty($final_preview->curriculum_currently_studying) ? $final_preview->curriculum_currently_studying : '';
            $currentlyBoardConfig = ['IB', 'CBSE', 'ICSE', 'State', 'Home School', 'IGCSE', 'IBDP', 'NIOS', 'Montessori', 'NA'];
            $is_required = $required_fields['curriculum_currently_studying']['required'];
            ?>

        <select name="curriculum_currently_studying" id="curriculum_currently_studying"
            class="form-control custom-select-arrow" <?= $is_required ?>
            data-parsley-error-message="Please select current curriculum/board.">
            <option value="">Select Board</option>
            <?php foreach ($currentlyBoardConfig as $board): ?>
            <option value="<?= $board ?>" <?= ($currentlyBoard == $board) ? 'selected' : '' ?>>
                <?= $board ?>
            </option>
            <?php endforeach; ?>
        </select>
        <div style="position: absolute; right: 50px; top: 60%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>

    <?php if (admission_is_enabled($disabled_fields, 'curriculum_interested_in')) : ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="curriculum_interested_in">
            Board/Curriculum Interested in &nbsp;
            <?php if ($required_fields['curriculum_interested_in']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php
        $Board = !empty($final_preview->curriculum_interested_in) ? $final_preview->curriculum_interested_in : '';
        $BoardConfig = json_decode($this->settings->getSetting('curriculum_interested'));
        $is_required = $required_fields['curriculum_interested_in']['required'];
        ?>

        <select name="curriculum_interested_in" id="curriculum_interested_in" class="form-control custom-select-arrow"
            <?= $is_required ?> data-parsley-error-message="Please select curriculum you are interested in.">
            <option value="">Select Board</option>
            <?php
            if (!empty($BoardConfig)) {
                foreach ($BoardConfig as $board) {
                    $selected = ($Board == $board) ? 'selected' : '';
                    echo "<option value=\"$board\" $selected>$board</option>";
                }
            }
            ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif; ?>

    <?php if (admission_is_enabled($disabled_fields, 'boarding')) : ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="boarding">
            Boarding Preference &nbsp;
            <?php if ($required_fields['boarding']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php
        $boardingOptions = $this->settings->getSetting('boarding');
        $boardingValue = isset($final_preview->boarding) ? $final_preview->boarding : '';
        $is_required = $required_fields['boarding']['required'];
        ?>

        <select name="boarding" id="boarding" class="form-control custom-select-arrow" <?= $is_required ?>
            data-parsley-error-message="Please select preferred boarding type.">
            <option value="">Select Preferred Boarding Type</option>
            <?php foreach ($boardingOptions as $key => $boarding): ?>
            <option value="<?= $key ?>" <?= ($boardingValue == $key) ? 'selected' : '' ?>>
                <?= $boarding ?>
            </option>
            <?php endforeach; ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif; ?>


    <?php if (admission_is_enabled($disabled_fields, 'primary_language_spoken')) : ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="primary_language_spoken">
            Primary Language Spoken &nbsp;
            <?php if ($required_fields['primary_language_spoken']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php
        $primaryLang = $this->config->item('languages');
        $primaryLangValue = isset($final_preview->primary_language_spoken) ? $final_preview->primary_language_spoken : '';
        $is_required = $required_fields['primary_language_spoken']['required'];
        ?>

        <select name="primary_language_spoken" id="primary_language_spoken" class="form-control custom-select-arrow"
            <?= $is_required ?> data-parsley-error-message="Please select primary language spoken.">
            <option value="">Select</option>
            <?php foreach ($primaryLang as $lang): ?>
            <option value="<?= $lang ?>" <?= ($primaryLangValue == $lang) ? 'selected' : '' ?>>
                <?= $lang ?>
            </option>
            <?php endforeach; ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif; ?>


    <?php if (admission_is_enabled($disabled_fields, 'second_language_currently_studying')) : ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="second_language_currently_studying">
            Second Language Currently Studying &nbsp;
            <?php if ($required_fields['second_language_currently_studying']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php
        $secondLanguages = ['Kannada','Telugu','English','Hindi','German','French','Spanish','Sanskrit'];
        $secondLangValue = isset($final_preview->second_language_currently_studying) ? $final_preview->second_language_currently_studying : '';
        $is_required = $required_fields['second_language_currently_studying']['required'];
        ?>

        <select name="second_language_currently_studying" id="second_language_currently_studying"
            class="form-control custom-select-arrow" <?= $is_required ?>
            data-parsley-error-message="Please select second language currently studying.">
            <option value=""> Select </option>
            <?php foreach ($secondLanguages as $lang): ?>
            <option value="<?= $lang ?>" <?= ($secondLangValue == $lang) ? 'selected' : '' ?>>
                <?= $lang ?>
            </option>
            <?php endforeach; ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif; ?>


    <?php if(admission_is_enabled($disabled_fields, 'esl_english_as_second_language')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="esl_english_as_second_language">ESL (English as Second Language) &nbsp;
            <?php if($required_fields['esl_english_as_second_language']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select class="form-control custom-select-arrow" data-parsley-error-message="Enter English as Second Language"
            <?php echo $required_fields['esl_english_as_second_language']['required'] ?>
            name="esl_english_as_second_language">
            <option value=""> Select </option>
            <option <?php if ($final_preview->esl_english_as_second_language == 'Yes') echo 'selected' ?> value="Yes">
                Yes</option>
            <option <?php if ($final_preview->esl_english_as_second_language == 'No') echo 'selected' ?> value="No">
                No</option>
        </select>
        <div style="position: absolute; right: 50px; top: 60%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'sibling_student_name')) : ?>
    <div class="col-md-6 mb-4">
        <label class="form-label">Does Student Have Sibling? &nbsp;
            <?php if($required_fields['has_sibling']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>
        <select class="form-control" name="has_sibling" id="has_sibling_select" onchange="if_yes_sibling_data()"
            required data-parsley-error-message="Please select if you have a sibling.">
            <option value="" selected>Select Option</option>
            <option value="1"
                <?= (!empty($final_preview->has_sibling) && $final_preview->has_sibling == 1) ? 'selected' : '' ?>>Yes
            </option>
            <option value="0"
                <?= (isset($final_preview->has_sibling) && $final_preview->has_sibling == 0) ? 'selected' : '' ?>>No
            </option>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    
    <div class="col-md-6 mb-4" id="in_school">
        <label class="form-label"> Sibling Studying In? &nbsp;
            <?php if($required_fields['has_sibling']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>
        <div class="position-relative">
            <select class="form-control" name="sibling_in" id="sibling_in_select" onchange="change_sibling_studying_in()"
                required data-parsley-error-message="Please select where your sibling is studying.">
                <option value="" selected>Select Option</option>
                <option value="same_school"
                    <?= (!empty($final_preview->sibling_inschool_other) && $final_preview->sibling_inschool_other == 'same_school') ? 'selected' : '' ?>>
                    <?= $institute_group_name ?></option>
                <option value="other"
                    <?= (isset($final_preview->sibling_inschool_other) && $final_preview->sibling_inschool_other == 'other') ? 'selected' : '' ?>>
                    Other</option>
            </select>
            <div class="arrow-icon position-absolute" style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>
    </div>
    

    <div class="col-md-6 mb-4 sibling_data" style="display:none;">
        <label class="form-label" for="sb_admission_number"> Sibling Name &nbsp;
            <?php if($required_fields['sibling_student_name']['required']=='required') echo'<font color="red">*</font>' ?></label>
       <input
            placeholder="Enter Sibling's Name"
            id="sibling_student_name"
            name="sibling_student_name"
            type="text"
            class="form-control input-md"
            data-parsley-pattern="^[A-Za-z\s]+$"
            data-parsley-pattern-message="Should contain only alphabets or spaces"
            data-parsley-trigger="blur"
            data-parsley-error-message="Should contain only alphabets or spaces"
            <?php if(!empty($final_preview->sibling_student_name)) echo 'value="'.$final_preview->sibling_student_name.'"'; ?>
        >
        <span class="help-block">Enter sibling's name as per school record</span>
    </div>
    <!-- Sibling Grade -->
    <div class="col-md-6 mb-4 sibling_data" style="display:none;">
        <label class="form-label" for="sibling_student_class">
            Sibling Grade &nbsp;
            <?php if($required_fields['sibling_student_class']['required']=='required') echo'<font color="red">*</font>' ?>
        </label>
        <input
            type="text"
            placeholder="Enter Grade"
            name="sibling_student_class"
            id="sibling_student_class"
            class="form-control"
            data-parsley-pattern="^[A-Za-z0-9\s]+$"
            data-parsley-pattern-message="Only letters, numbers, or spaces allowed"
            data-parsley-trigger="blur"
            <?= $required_fields['sibling_student_class']['required'] ?>
            <?php if(!empty($final_preview->sibling_student_class)) echo 'value="'.$final_preview->sibling_student_class.'"' ?>
        >
        <span class="help-block">If studying, enter name of the grade</span>
    </div>

    <!-- Sibling School Name -->
    <div class="col-md-6 mb-4" id="sibling_school_name" style="display:none;">
        <label class="form-label" for="siblingSchool_name">
            Sibling's school/college &nbsp;
            <?php if($required_fields['sibling_school_name']['required']=='required') echo'<font color="red">*</font>' ?>
        </label>
        <input
            placeholder="Enter School name and address"
            id="siblingSchool_name"
            name="sibling_school_name"
            type="text"
            class="form-control input-md"
            data-parsley-pattern="^[A-Za-z0-9\s.,\-()]+$"
            data-parsley-pattern-message="Only alphabets, numbers, commas, dots, dashes, or parentheses allowed"
            data-parsley-trigger="blur"
            <?= $required_fields['sibling_school_name']['required'] ?>
            <?php if(!empty($final_preview->sibling_school_name)) echo 'value="'.$final_preview->sibling_school_name.'"' ?>
        >
    </div>

    <?php endif ?>

    <?php if (admission_is_enabled($disabled_fields, 'religion')) : ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="religion">
            Religion &nbsp;
            <?php if ($required_fields['religion']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php
        $relig = !empty($final_preview->religion) ? $final_preview->religion : '';
        $religionConfig = json_decode($this->settings->getSetting('religion'));
        $fallbackReligionList = $this->config->item('religions');
        $is_required = $required_fields['religion']['required'];
        ?>
        <div class="position-relative">
        <select name="religion" id="religion" class="form-control custom-select-arrow" <?= $is_required ?>>
            <option value="">Select Religion</option>
            <?php
            if (!empty($religionConfig)) {
                foreach ($religionConfig as $data) {
                    $selected = ($relig == $data) ? 'selected' : '';
                    echo "<option value=\"$data\" $selected>$data</option>";
                }
            } else {
                foreach ($fallbackReligionList as $religion) {
                    $selected = ($relig == $religion) ? 'selected' : '';
                    echo "<option value=\"$religion\" $selected>$religion</option>";
                }
            }
            ?>
        </select>
        <div class="arrow-icon position-absolute" style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
        </div>
    </div>

    <div id="religion_other" class="col-md-6 mb-4" style="display: none;">
        <label class="form-label" for="religion_other">Others</label>
        <input placeholder="Please Specify" id="religion_other" name="religion_other" type="text"
            class="form-control input-md" data-parsley-error-message="Cannot be empty"
            data-parsley-pattern="^[a-zA-Z. ]+$" data-parsley-minlength="2"
            <?php if (!empty($final_preview->religion_other)) echo 'value="' . $final_preview->religion_other . '"'; ?>>
        <span class="help-block">Please Specify</span>
    </div>
    <?php endif; ?>


    <!-- new filters starts -->
    <?php if ($student_caste_present_in_db == 1) { ?>
    <?php if (admission_is_enabled($disabled_fields, 'category')): ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="category">Category &nbsp;
            <?php if ($required_fields['category']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
        </label>
        <select class="form-control custom-select-arrow  select2" name="category" title='Select category' id="category"
            onchange="getCaste()" <?php echo $required_fields['category']['required'] ?>>
            <?php echo '<option value="">Select category</option>' ?>

            <?php foreach ($categoryOptions as $category) {
                            $selected = '';
                            if ($category->value == $final_preview->category) {
                                $selected = 'selected';
                            } ?>
            <option value="<?php echo $category->value ?>" <?= $selected ?>>
                <?php echo $category->category ?>
            </option>
            <?php } ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>


    <?php if (admission_is_enabled($disabled_fields, 'student_caste')): ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="caste">Caste &nbsp;
            <?php if ($required_fields['student_caste']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
        </label>
        <select class="form-control custom-select-arrow select2" name="student_caste" title='Select caste'
            id="student_caste" onchange="backFillCategory()"
            <?php echo $required_fields['student_caste']['required'] ?>>
            <?php echo '<option value="">Select caste</option>' ?>
            <?php foreach ($casteOptions as $c) { 
                            $selected = '';
                            if ($c->caste == $final_preview->student_caste) {
                                $selected = 'selected';
                            }?>
            <option data-cate-id="<?php echo $c->category ?>" value="<?php echo $c->caste ?>" <?= $selected ?>>
                <?php echo $c->caste ?>
            </option>
            <?php } ?>
        </select>
        <div style="position: absolute; right: 35px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>

    <?php if (admission_is_enabled($disabled_fields, 'student_sub_caste')): ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="student_sub_caste">Sub Caste &nbsp;
            <?php if ($required_fields['student_sub_caste']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
        </label>
        <select class="form-control custom-select-arrow select2" name="student_sub_caste" title='Select sub caste'
            id="student_sub_caste">
            <?php echo '<option value="0">Select sub caste</option>' ?>
            <?php foreach ($subCasteOptions as $c) { ?>
            <option data-caste-id="<?php echo $c->caste ?>" value="<?php echo $c->sub_caste ?>">
                <?php echo $c->sub_caste ?>
            </option>
            <?php } ?>
        </select>
        <div style="position: absolute; right: 35px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php } ?>
    <!-- new filters ends -->

    <!-- old filters starts -->
    <?php if ($student_caste_present_in_db == 0) { ?>
    <?php if (admission_is_enabled($disabled_fields, 'category')): ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="category">
            Category &nbsp;
            <?php if ($required_fields['category']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php
            $cate = !empty($final_preview->category) ? $final_preview->category : '';
            $categoryList = $this->settings->getSetting('category');
            $is_required = $required_fields['category']['required'];
            ?>

        <select name="category" id="category" class="form-control custom-select-arrow" <?= $is_required ?>
            data-parsley-error-message="Please select category.">
            <option value="">Select Category</option>
            <?php foreach ($categoryList as $key => $category): ?>
            <option value="<?= $key ?>" <?= ($cate == $key) ? 'selected' : '' ?>>
                <?= $category ?>
            </option>
            <?php endforeach; ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif; ?>
    <?php if (admission_is_enabled($disabled_fields, 'student_caste')): ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="student_caste">Caste &nbsp;
            <?php if ($required_fields['student_caste']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
        </label>
        <input type="text" placeholder="Enter Caste" <?php if (!empty ($final_preview->student_caste))
                        echo 'value="' . $final_preview->student_caste . '"' ?> id="student_caste"
            <?php echo $required_fields['student_caste']['required'] ?> name="student_caste"
            class="form-control input-md" data-parsley-error-message="only alphabets and spaces are allowed."
            data-parsley-pattern="^[a-zA-Z ]+$">
    </div>
    <?php endif ?>

    <?php if (admission_is_enabled($disabled_fields, 'student_sub_caste')): ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="student_sub_caste">Sub Caste &nbsp;
            <?php if ($required_fields['student_sub_caste']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
        </label>
        <input type="text" placeholder="Enter Sub Caste" <?php if (!empty ($final_preview->student_sub_caste))
                        echo 'value="' . $final_preview->student_sub_caste . '"' ?> id="student_sub_caste"
            <?php echo $required_fields['student_sub_caste']['required'] ?> name="student_sub_caste"
            class="form-control input-md" data-parsley-error-message="only alphabets and spaces are allowed."
            data-parsley-pattern="^[a-zA-Z ]+$">
    </div>
    <?php endif ?>
    <?php } ?>
    <!-- old caste filters end -->

    <?php if(admission_is_enabled($disabled_fields, 'caste_income_certificate_number')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="caste_income_certificate_number">Caste and Income Certificate
            Number
            &nbsp;<?php if($required_fields['caste_income_certificate_number']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" data-parsley-error-message="Enter Valid Number"
            <?php echo $required_fields['caste_income_certificate_number']['required'] ?>
            placeholder="Enter Caste and Income Certificate Number"
            <?php if(!empty($final_preview->caste_income_certificate_number)) echo 'value="'.$final_preview->caste_income_certificate_number.'"' ?>
            id="caste_income_certificate_number" name="caste_income_certificate_number" class="form-control input-md">
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'student_email_id')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="student_email_id"> Email ID &nbsp;
            <?php if($required_fields['student_email_id']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="email" data-parsley-error-message="Enter valid email-id"
            <?php echo $required_fields['student_email_id']['required'] ?> placeholder="Enter Valid Email Id"
            <?php if(!empty($final_preview->student_email_id)) echo 'value="'.$final_preview->student_email_id.'"' ?>
            id="student_email_id" name="student_email_id" class="form-control input-md" data-parsley-type="email"
            data-parsley-type-message="Please enter a valid email address."
            data-parsley-error-message="Email is required.">
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'student_mobile_no')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="student_mobile_no">
            Mobile Number &nbsp;
            <?php if($required_fields['student_mobile_no']['required']=='required') echo '<span class="text-danger">*</span>'; ?>
        </label>

        <div class="row g-2">
            <div class="col-4 position-relative">
                <?php 
                    $array = array();
                    foreach ($this->config->item('country_codes') as $key => $code) {
                        $array[$code] = $code;
                    }
                    echo form_dropdown("s_country_code", $array, set_value("s_country_code", $final_preview->s_country_code), 
                        "id='s_country_code' " . $required_fields['s_country_code']['required'] . " class='form-control' ");
                ?>
                <div class="arrow-icon position-absolute"
                    style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%);">
                    <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                </div>
            </div>

            <div class="col-8">
                <input type="text" name="student_mobile_no" id="student_mobile_no" class="form-control"
                    placeholder="Enter Valid Number" data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]"
                    data-parsley-error-message="Please enter a valid mobile number."
                    data-parsley-pattern-message="Mobile number should contain only digits, spaces, hyphens, parentheses and plus sign."
                    data-parsley-length-message="Mobile number must be between 8 and 20 characters."
                    <?= $required_fields['student_mobile_no']['required']; ?>
                    value="<?= !empty($final_preview->student_mobile_no) ? $final_preview->student_mobile_no : '' ?>">
            </div>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'family_annual_income')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="family_annual_income">Family's Annual Income &nbsp;
            <?php if($required_fields['family_annual_income']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input id="family_annual_income"
            <?php if(!empty($final_preview->family_annual_income)) echo 'value="'.$final_preview->family_annual_income.'"' ?>
            name="family_annual_income" type="text" placeholder="Enter Annual Income" class="form-control input-md"
            <?php echo $required_fields['family_annual_income']['required'] ?>
            data-parsley-error-message="Enter valid currency value" data-parsley-pattern="^[0-9]\d*(\.\d+)?$">
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'extracurricular_activities')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="extracurricular_activities">Extra-curricular Activities, if any &nbsp;
            <?php if($required_fields['extracurricular_activities']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" data-parsley-error-message="Enter Activities"
            <?php echo $required_fields['extracurricular_activities']['required'] ?> placeholder="Enter Activities"
            <?php if(!empty($final_preview->extracurricular_activities)) echo 'value="'.$final_preview->extracurricular_activities.'"' ?>
            id="extracurricular_activities" name="extracurricular_activities" class="form-control input-md">
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'emergency_contact')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="emergency_contact"> Emergency Contact Name and Number &nbsp;
            <?php if($required_fields['emergency_contact']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea type="text" name="emergency_contact" rows="3" id="emergency_contact" class="form-control"
            placeholder="Enter the emergency contact person name,contact number and relationShip with student."
            data-parsley-error-message="Emergency contact details are required." <?= $required_fields['emergency_contact']['required'] ?>
            data-parsley-minlength="10" data-parsley-minlength-message="Please provide complete emergency contact details (at least 10 characters)."><?php if(!empty($final_preview->emergency_contact)) echo $final_preview->emergency_contact; ?></textarea>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'prefered_contact_number')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="prefered_contact_number"> Prefered Contact Number &nbsp;
            <?php if($required_fields['prefered_contact_number']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" name="prefered_contact_number"
            value="<?php if(!empty($final_preview->prefered_contact_number)) echo $final_preview->prefered_contact_number; ?>"
            id="prefered_contact_number" class="form-control"
            <?php echo $required_fields['prefered_contact_number']['required']; ?>
            placeholder="Enter Prefered Contact Number" data-parsley-pattern="^[0-9 -()+]+$"
            data-parsley-length="[8, 20]" data-parsley-error-message="Please enter a valid contact number."
            data-parsley-pattern-message="Contact number should contain only digits, spaces, hyphens, parentheses and plus sign."
            data-parsley-length-message="Contact number must be between 8 and 20 characters.">
    </div>
    <?php endif ?>

   <?php if(admission_is_enabled($disabled_fields, 'school_to_home_distance_in_km')) : ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="school_to_home_distance_in_km">
            Distance from school to home in km &nbsp;
            <?php if($required_fields['school_to_home_distance_in_km']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>
        <input
            type="text"
            name="school_to_home_distance_in_km"
            id="school_to_home_distance_in_km"
            class="form-control"
            placeholder="Enter Distance From School To Home"
            data-parsley-type="number"
            data-parsley-type-message="Please enter a valid number"
            data-parsley-min="0"
            data-parsley-error-message="This field is required"
            data-parsley-trigger="blur"
            <?= $required_fields['school_to_home_distance_in_km']['required']; ?>
            value="<?php if(!empty($final_preview->school_to_home_distance_in_km)) echo $final_preview->school_to_home_distance_in_km; ?>"
        >
    </div>
    <?php endif ?>


    <?php if(admission_is_enabled($disabled_fields, 'medical_concerns')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="medical_concerns">Medical Concerns if any &nbsp;
            <?php if($required_fields['medical_concerns']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select class="form-control custom-select-arrow" id="has_medical_concerns" name="has_medical_concerns"
            <?php echo $required_fields['medical_concerns']['required'] ?>>
            <option value="">Select</option>
            <option <?php if ($final_preview->has_medical_concerns == 'Yes') echo 'selected' ?> value="Yes">Yes
            </option>
            <option <?php if ($final_preview->has_medical_concerns == 'No') echo 'selected' ?> value="No">No
            </option>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <div class="col-md-6 mb-4"
        style="<?php if ($final_preview->has_medical_concerns == 'Yes') { echo 'display: block;' ; } else{ echo 'display: none;'; } ?>"
        id="medical_concerns">
        <label class="form-label" for="medical_concerns">Specify the medical concerns
            <?php if($required_fields['medical_concerns']['required']=='required') echo ' <font color="red">*</font>'; ?>
        </label>

        <textarea id="medical_concern_text" name="medical_concerns" class="form-control input-md" rows="3"
            data-parsley-error-message="Enter Medical Concerns"
            placeholder="Enter Medical Concerns"><?php if(!empty($final_preview->medical_concerns)) echo $final_preview->medical_concerns ; ?></textarea>
    </div>
    <?php endif ?>

   <?php if(admission_is_enabled($disabled_fields, 'passport_number')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="passport_number">
            Passport Number &nbsp;
            <?php if($required_fields['passport_number']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>
        <input
            type="text"
            id="passport_number"
            name="passport_number"
            class="form-control input-md"
            placeholder="Enter Passport Number"
            data-parsley-pattern="^[A-Za-z0-9]{6,9}$"
            data-parsley-pattern-message="Enter a valid passport number (6-9 alphanumeric characters)"
            data-parsley-trigger="blur"
            <?= $required_fields['passport_number']['required']; ?>
            <?php if(!empty($final_preview->passport_number)) echo 'value="'.$final_preview->passport_number.'"'; ?>
        >
    </div>
    <?php endif ?>



    <?php if(admission_is_enabled($disabled_fields, 'passport_issued_place')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="passport_issued_place">Passport issued place &nbsp;
            <?php if($required_fields['passport_issued_place']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" data-parsley-error-message="Enter passport issued place"
            <?php echo $required_fields['passport_issued_place']['required'] ?>
            placeholder="Enter Passport Issued Place"
            <?php if(!empty($final_preview->passport_issued_place)) echo 'value="'.$final_preview->passport_issued_place.'"' ?>
            id="passport_issued_place" name="passport_issued_place" class="form-control input-md">
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'passport_expiry_date')) : ?>
    <div class="col-md-6 mb-4">
        <label for="passport_expiry_date" class="form-label">
            Passport Expiry Date &nbsp;
            <?php if($required_fields['passport_expiry_date']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
        </label>

        <div class="position-relative">
            <input type="text" id="passport_expiry_date" name="passport_expiry_date" class="form-control datepick1 pe-5"
                placeholder="Enter Passport Expiry Date" autocomplete="off"
                <?= $required_fields['passport_expiry_date']['required'] ?>
                data-parsley-error-message="Enter passport expiry date"
                <?php if (!empty($final_preview->passport_expiry_date)) echo 'value="' . date('d-m-Y', strtotime($final_preview->passport_expiry_date)) . '"'; ?>>

            <!-- Bootstrap-style calendar icon -->
            <div
                style="position: absolute; right: 1px; top: 50%; transform: translateY(-50%); z-index: 10; padding-right: 20px;">
                <?php $this->load->view('svg_icons/calendar_icon.svg'); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>


    <?php if(admission_is_enabled($disabled_fields, 'joining_period')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="joining_period">Joining Period &nbsp;
            <?php if($required_fields['joining_period']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select class="form-control custom-select-arrow" data-parsley-error-message="Enter joining period"
            <?php echo $required_fields['joining_period']['required'] ?> name="joining_period">
            <option value="">Select Joining Period</option>
            <?php if(!empty($this->settings->getSetting('admission_joining_period'))) {
                 foreach (json_decode($this->settings->getSetting('admission_joining_period')) as $key => $val) { ?>
            <option <?php if ($final_preview->joining_period == $val) echo 'selected' ?> value="<?php echo $val ?>">
                <?php echo $val ?></option>
            <?php } } ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'transport')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="transport">Transportation required? &nbsp;
            <?php if($required_fields['transport']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select class="form-control custom-select-arrow" data-parsley-error-message="Select transport"
            <?php echo $required_fields['transport']['required'] ?> name="transport" id="transportation">
            <option value="">Select</option>
            <option <?php if ($final_preview->transport == 'Yes') echo 'selected' ?> value="Yes">Yes</option>
            <option <?php if ($final_preview->transport == 'No') echo 'selected' ?> value="No">No</option>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <div id="transportation_details" style="display: none;">
        <div class="col-md-6 mb-4 p-0">
            <select name="transportation_mode" id="transportation_mode" class="form-control"
                style="margin-top:15px;" data-parsley-error-message="Please select mode of transport.">
                <option value="">Select Mode of Transport</option>
                <option <?php if ($final_preview->transportation_mode == 'Cycle / Walker') echo 'selected' ?>
                    value="Cycle / Walker">Cycle / Walker</option>
                <option <?php if ($final_preview->transportation_mode == 'Private Transport') echo 'selected' ?>
                    value="Private Transport">Private Transport</option>
                <option <?php if ($final_preview->transportation_mode == 'Personal pickup / Drop') echo 'selected' ?>
                    value="Personal pickup / Drop">Personal pickup / Drop</option>
            </select>
            <div style="position: absolute; right: 50px; top: 60%; transform: translateY(-50%);">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>
        <div class="col-md-6 mb-4 p-0">
            <textarea name="transport_addition_details" id="transport_addition_details" class="form-control"
                style="margin-top:15px;"
                placeholder="Enter Additional Details"><?php if(!empty($final_preview->transport_addition_details)) echo $final_preview->transport_addition_details ?></textarea>
            <span class="help-block">Addition details like pickup/drop person name and contact number</span>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'pen_number')) :  ?>
<div class="col-md-6 mb-4">
    <label class="form-label" for="pen_number">PEN Number &nbsp;
        <?php if($required_fields['pen_number']['required']=='required') echo '<font color="red">*</font>'; ?>
    </label>
    <input type="text" class="form-control" name="pen_number" id="pen_number" placeholder="Enter PEN number"
        pattern="^[A-Za-z0-9]{6,15}$"
        data-parsley-pattern-message="Only alphanumeric characters allowed (6–15 characters)"
        data-parsley-trigger="blur"
        <?= $required_fields['pen_number']['required']; ?>
        <?php if(!empty($final_preview->pen_number)) echo 'value="'.$final_preview->pen_number.'"'; ?>>
</div>
<?php endif; ?>

<?php if(admission_is_enabled($disabled_fields, 'udise_number')) :  ?>
<div class="col-md-6 mb-4">
    <label class="form-label" for="udise_number">UDISE Number &nbsp;
        <?php if($required_fields['udise_number']['required']=='required') echo '<font color="red">*</font>'; ?>
    </label>
    <input type="text" class="form-control" name="udise_number" id="udise_number" placeholder="Enter UDISE number"
        pattern="^[0-9]{11}$"
        data-parsley-pattern-message="UDISE number must be 11 digits"
        data-parsley-trigger="blur"
        <?= $required_fields['udise_number']['required']; ?>
        <?php if(!empty($final_preview->udise_number)) echo 'value="'.$final_preview->udise_number.'"'; ?>>
</div>
<?php endif; ?>

<?php if(admission_is_enabled($disabled_fields, 'apaar_id')) :  ?>
<div class="col-md-6 mb-4">
    <label class="form-label" for="apaar_id">APAAR ID &nbsp;
        <?php if($required_fields['apaar_id']['required']=='required') echo '<font color="red">*</font>'; ?>
    </label>
    <input type="text" class="form-control" name="apaar_id" id="apaar_id" placeholder="Enter APAAR ID"
        pattern="^[A-Za-z0-9]{12}$"
        data-parsley-pattern-message="APAAR ID must be 12 alphanumeric characters"
        data-parsley-trigger="blur"
        <?= $required_fields['apaar_id']['required']; ?>
        <?php if(!empty($final_preview->apaar_id)) echo 'value="'.$final_preview->apaar_id.'"'; ?>>
</div>
<?php endif; ?>

    <?php if (admission_is_enabled($disabled_fields, 'physical_disability')): ?>
    <?php $disability = $final_preview->physical_disability ?? 'N'; ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="disability">
            Is the Student physically challenged? &nbsp;
            <?= ($required_fields['physical_disability']['required'] == 'required') ? '<span class="text-danger">*</span>' : '' ?>
        </label>
        <div class="position-relative">
        <select class="form-control" name="disability" id="disability_select" onchange="is_physically_challenged()"
            <?= ($required_fields['physical_disability']['required'] == 'required') ? 'required' : '' ?>
            data-parsley-error-message="Please select if you have any physical disability.">
            <option value="" selected>Select Option</option>
            <option value="Y" <?= ($disability == 'Yes' || $disability == 'Y') ? 'selected' : '' ?>>Yes</option>
            <option value="N" <?= ($disability == 'No' || $disability == 'N') ? 'selected' : '' ?>>No</option>
        </select>
        <div class="arrow-icon position-absolute" style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
        </div>
    </div>

    <div class="col-md-6 mb-4" style="display:none;" id="physical_desription">
        <label class="form-label" for="physical_disability_description">Disability Description &nbsp;</label>
        <textarea name="physical_disability_desription" id="physical_disability_description" rows="3"
            class="form-control"><?= $final_preview->physically_challenged_discription ?? '' ?></textarea>
    </div>
    <?php endif; ?>

    <?php if(admission_is_enabled($disabled_fields, 'learning_disability')) : ?>
    <?php $learning_val = !empty($final_preview->learning_disability) ? $final_preview->learning_disability : ''; ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="learning">
            Does the Student have special needs/learning challenges? &nbsp;
            <?php if($required_fields['learning_disability']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>
        <div class="position-relative">
            <select class="form-control" name="learning" id="learning_select" onchange="if_yes_descriptions()"
                <?= ($required_fields['learning_disability']['required']=='required') ? 'required' : '' ?>
                data-parsley-error-message="Please select if you have any learning disability.">
                <option value="" selected>Select Option</option>
                <option value="Y" <?= ($learning_val == 'Yes' || $learning_val == 'Y') ? 'selected' : '' ?>>Yes</option>
                <option value="N"
                    <?= ($learning_val == 'No' || $learning_val == 'N' || empty($learning_val)) ? 'selected' : '' ?>>No
                </option>
            </select>
            <div class="arrow-icon position-absolute"
                style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4" id="special_needs_description_id" style="display:none">
        <label class="form-label" for="special_needs_description">Select special needs/learning challenges &nbsp;<font
                color="red">*</font></label>
        <?php 
            $options = ['' => ' Select '] + array_combine(
                ['ADHD','Dyslexia','Speech delay','Autism','Others'],
                ['ADHD','Dyslexia','Speech delay','Autism','Others']
            );
            echo form_dropdown("special_needs_description", $options, set_value("special_needs_description", $final_preview->special_needs_description ?? ''), "id='special_needs_description' class='form-control'");
        ?>
    </div>
    <?php endif; ?>


    <?php if(!in_array('know_about_us',$disabled_fields)) :?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="know_about_us">How did you know about
            us? &nbsp;
            <?php if($required_fields['know_about_us']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" name="know_about_us" class="form-control" <?php echo $required_fields['know_about_us']['required'] ?> placeholder="How Did You Know About Us"
            value="<?php if(!empty($final_preview->know_about_us)) echo $final_preview->know_about_us ?>"
            data-parsley-error-message="Please tell us how you came to know about us."
            data-parsley-minlength="3" data-parsley-minlength-message="Please provide at least 3 characters." >
    </div>
    <?php endif ?>

    <?php if(!in_array('reason_for_joining_this_institute',$disabled_fields)) :?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="reason_for_joining_this_institute">Why do you want to join
            <?= $school_name; ?> ?&nbsp;
            <?php if($required_fields['reason_for_joining_this_institute']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea name="reason_for_joining_this_institute" class="form-control" id="reason_for_joining_this_institute"
            <?php echo $required_fields['reason_for_joining_this_institute']['required'] ?> rows="3"
            placeholder="Enter Reason"><?php if(!empty($final_preview->reason_for_joining_this_institute)) echo $final_preview->reason_for_joining_this_institute ?></textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('student_area_of_strength',$disabled_fields)) :?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="student_area_of_strength">Areas of Strength in order of
            biggest strength first &nbsp;
            <?php if($required_fields['student_area_of_strength']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea name="student_area_of_strength" class="form-control" id="student_area_of_strength"
            <?php echo $required_fields['student_area_of_strength']['required'] ?> rows="3"
            placeholder="Enter Areas Of Strength"><?php if(!empty($final_preview->student_area_of_strength)) echo $final_preview->student_area_of_strength ?></textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('student_area_of_improvement',$disabled_fields)) :?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="student_area_of_improvement">Improvement areas in
            order of most concerning area first &nbsp;
            <?php if($required_fields['student_area_of_improvement']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea name="student_area_of_improvement" class="form-control" id="student_area_of_improvement"
            <?php echo $required_fields['student_area_of_improvement']['required'] ?> rows="3"
            placeholder="Enter Areas Of Improvement"><?php if(!empty($final_preview->student_area_of_improvement)) echo $final_preview->student_area_of_improvement ?></textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('student_hobbies',$disabled_fields)) :?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="student_hobbies">3 Hobbies in order of most favorite first &nbsp;
            <?php if($required_fields['student_hobbies']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea name="student_hobbies" class="form-control" id="student_hobbies"
            <?php echo $required_fields['student_hobbies']['required'] ?> rows="3"
            placeholder="Enter Hobbies"><?php if(!empty($final_preview->student_hobbies)) echo $final_preview->student_hobbies ?></textarea>
    </div>
    <?php endif ?>

    <?php $institute_type = $this->settings->getSetting('institute_type');
    $label_name = 'Have you enrolled for 1st PU / 11th Std  in a different college earlier? &nbsp;'; 
    if($institute_type == 'degree') { $label_name = 'Have you enrolled for any other UG program earlier? &nbsp;'; }?>
    <?php if(!in_array('did_they_enrolled_in_different_institute_earlier',$disabled_fields)) :?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="did_they_enrolled_in_different_institute_earlier">
            <?= $label_name; ?>
            <?php if($required_fields['did_they_enrolled_in_different_institute_earlier']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="position-relative">
            <select name="did_they_enrolled_in_different_institute_earlier" class="form-control custom-select-arrow"
                id="did_they_enrolled_in_different_institute_earlier"
                <?php echo $required_fields['did_they_enrolled_in_different_institute_earlier']['required'] ?> rows="3">
                <option value="">Select</option>
                <option value="Yes"
                    <?php if($final_preview->did_they_enrolled_in_different_institute_earlier == 'Yes') echo 'selected' ?>>
                    Yes</option>
                <option value="No"
                    <?php if($final_preview->did_they_enrolled_in_different_institute_earlier == 'No') echo 'selected' ?>>
                    No</option>
            </select>
            <div class="arrow-icon position-absolute"
                style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>
    </div>
    <?php endif ?>
    <div class="row mb-4">
        <?php if (!in_array('std_photo_uri', $disabled_fields)) : ?>
        <div class="col-md-6 mb-4">
            <label class="form-label fw-semibold mb-2">Recent Photo &nbsp;
                <?php if($required_fields['std_photo_uri']['required']=='required') echo '<font color="red">*</font>'; ?>
            </label>

            <div class="border border-dashed rounded p-4 text-center position-relative"
                style="cursor: pointer; background-color: #fafafa;"
                onclick="document.getElementById('fileupload').click();">
                <?php if(!empty($final_preview->std_photo_uri)) { ?>
                <img src="<?= $this->filemanager->getFilePath($final_preview->std_photo_uri) ?>" alt="Preview"
                    id="previewing" class="mb-2" style="max-height: 80px; max-width: 150px;margin:0 auto" />
                <?php }else{ ?>
                <img src="" alt="Preview" id="previewing" class="mb-2"
                    style="max-height: 100px; max-width: 150px;margin:0 auto;display:none" />
                <div id="photo_placeholder"
                    style="max-height: 100px; max-width: 150px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                    <?php $this->load->view('svg_icons/empty_image.svg'); ?>
                </div>
                <?php } ?>

                <div class="d-flex flex-column align-items-center">
                    <i class="bi bi-upload" style="font-size: 24px;"></i>
                    <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                    <small class="text-muted">JPEG, PNG, JPG Max size: <?= $image_size_in_admissions ?>MB</small>
                </div>
                <?php if($final_preview->std_photo_uri){ ?>
                <input type="file" name="student_photo" id="fileupload"
                    accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;" />
                <?php }else{ ?>
                <input type="file" name="student_photo" id="fileupload"
                    accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;"
                    <?= $required_fields['std_photo_uri']['required'] ?? '' ?> />
                <?php } ?>

                <input type="hidden" name="high_quality_url" id="student_high_quality_url">
                <span id="fileuploadError" class="text-danger d-block mt-2"></span>
            </div>

            <div class="mt-2">
                <span class="help-block">
                    <?= $this->settings->getSetting('student_photo_note_display') ?: 'Upload recent passport size photograph' ?>
                </span>
            </div>
        </div>
        <?php endif; ?>
        <?php if (!in_array('student_signature', $disabled_fields)) : ?>
        <div class="col-md-6 mb-4">
            <label class="form-label fw-semibold mb-2">Signature &nbsp;
                <?php if ($required_fields['student_signature']['required'] == 'required') echo '<font color="red">*</font>'; ?>
            </label>

            <div class="border border-dashed rounded p-4 text-center position-relative"
                style="cursor: pointer; background-color: #fafafa;"
                onclick="document.getElementById('stud_sign_fileupload').click();">
                <?php if(!empty($final_preview->student_signature)) { ?>
                <img src="<?= $this->filemanager->getFilePath($final_preview->student_signature) ?>"
                    alt="Signature Preview" id="stud_sig_previewing" class="mb-2"
                    style="max-height: 80px; max-width: 150px;margin:0 auto" />
                <?php }else{ ?>
                <img src="" alt="Signature Preview" id="stud_sig_previewing" class="mb-2"
                    style="max-height: 80px; max-width: 150px;display:none;margin:0 auto" />
                <div id="stud_sig_placeholder"
                    style="max-height: 100px; max-width: 150px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                    <?php $this->load->view('svg_icons/empty_image.svg'); ?>
                </div>
                <?php } ?>
                <div class="d-flex flex-column align-items-center">
                    <i class="bi bi-upload" style="font-size: 24px;"></i>
                    <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                    <small class="text-muted">PNG, JPG or JPEG Max size: <?= $image_size_in_admissions ?>MB</small>
                </div>

                <!-- Progress indicator for signature upload -->
                <div id="percentage_stud_sign_completed" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,255,255,0.9); padding: 5px 10px; border-radius: 5px; font-weight: bold; color: #007bff;">0%</div>

                <?php if($final_preview->student_signature){ ?>
                <input type="file" name="student_signature" id="stud_sign_fileupload"
                    accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;" />
                <?php }else{ ?>
                <input type="file" name="student_signature" id="stud_sign_fileupload"
                    accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;"
                    <?= $required_fields['student_signature']['required'] ?? '' ?> />
                <?php } ?>
                <input type="hidden" name="signature_img_path" id="signature_img_path">
                <span id="stud_sign_fileuploadError" class="text-danger d-block mt-2"></span>
            </div>

        </div>
        <?php endif; ?>
        <?php if (!in_array('family_photo', $disabled_fields)) : ?>
        <div class="col-md-6 mb-4">
            <label class="form-label fw-semibold mb-2">Family Photo &nbsp;
                <?php if ($required_fields['family_photo']['required'] == 'required') echo '<font color="red">*</font>'; ?>
            </label>

            <div class="border border-dashed rounded p-4 text-center position-relative"
                style="cursor: pointer; background-color: #fafafa;"
                onclick="document.getElementById('family_fileupload').click();">
                <?php if(!empty($final_preview->family_photo)) { ?>
                <img src="<?= $this->filemanager->getFilePath($final_preview->family_photo) ?>"
                    alt="Family Photo Preview" id="family_previewing" class="mb-2"
                    style="max-height: 80px; max-width: 150px;margin:0 auto" />
                <?php }else{ ?>
                <img src="" alt="Family Photo Preview" id="family_previewing" class="mb-2"
                    style="max-height: 80px; max-width: 150px;display:none;margin:0 auto" />
                <div id="family_placeholder"
                    style="max-height: 100px; max-width: 150px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                    <?php $this->load->view('svg_icons/empty_image.svg'); ?>
                </div>
                <?php } ?>
                <div class="d-flex flex-column align-items-center">
                    <i class="bi bi-upload" style="font-size: 24px;"></i>
                    <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                    <small class="text-muted">JPEG, PNG or JPG Max size: <?= $image_size_in_admissions ?>MB</small>
                </div>

                <!-- Progress indicator for family photo upload -->
                <div id="percentage_family_completed" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,255,255,0.9); padding: 5px 10px; border-radius: 5px; font-weight: bold; color: #007bff;">0%</div>

                <?php if($final_preview->family_photo){ ?>
                <input type="file" name="family_photo" id="family_fileupload"
                    accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;" />
                <?php }else{ ?>
                <input type="file" name="family_photo" id="family_fileupload"
                    accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;"
                    <?= $required_fields['family_photo']['required'] ? 'required' : '' ?> />
                <?php } ?>
                <input type="hidden" name="family_photo_path" id="family_photo_path">
                <span id="family_fileuploadError" class="text-danger d-block mt-2"></span>
            </div>

            <div class="mt-2">
                <span class="help-block">
                    <?= $this->settings->getSetting('student_photo_note_display') ?: 'Upload recent photograph' ?>
                </span>
            </div>
        </div>
        <?php endif; ?>
    </div>

</div>


<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

<script type="text/javascript">
$(document).ready(function() {
    var transport = '<?php echo $final_preview->transport ?>';
    if (transport == 'Yes') {
        $('#transportation_details').hide();
    } else if (transport == 'No') {
        $('#transportation_details').show();
    }

    $(".select2").select2();
    // Also for has_sibling_select
    $(document).on('change', '#has_sibling_select', function() {
        if_yes_sibling_data();
    });
    if_yes_sibling_data();
    if_yes_descriptions();
    if_yes_sibling_data_edit();
    is_physically_challenged();

})

$('#transportation').on('change', function() {
    if ($(this).val() == 'Yes') {
        $('#transportation_details').hide();
    } else if ($(this).val() == 'No') {
        $('#transportation_details').show();
    }
})

$('#has_medical_concerns').on('change', function() {
    var has_medical_concerns = $('#has_medical_concerns').val();
    if (has_medical_concerns == 'Yes') {
        $('#medical_concerns').show();
        $('#medical_concern_text').attr('required', 'required');
    } else if (has_medical_concerns == 'No') {
        $('#medical_concerns').hide();
        $('#medical_concern_text').val('');
        $('#medical_concern_text').removeAttr('required');
    }
});

// Enhanced mobile-friendly checkbox handler for copy present address
$('#copy_present_address').on('click change touchend', function(e) {
    e.stopPropagation();
    setTimeout(() => {
    if ($(this).prop("checked") == true) {
        var s_present_addr = $('#s_present_addr').val();
        var s_present_area = $('#s_present_area').val();
        var s_present_district = $('#s_present_district').val();
        var s_present_country = $('#s_present_country').val();
        if (s_present_country == 'India') {
            var s_present_state = $('#s_present_state').val();
            $('#per_state_select').show();
            $('#per_state_input').hide();
            $('input[type=text][name=s_permanent_state1]').removeAttr("required");
            $('#s_permanent_state').val(s_present_state);
            $('#s_permanent_state1').val('');
        } else {
            var s_present_state = $('#s_present_state1').val();
            $('#per_state_select').hide();
            $('#per_state_input').show();
            $('select[name=s_permanent_state]').removeAttr("required");
            $('#s_permanent_state1').val(s_present_state);
            $('#s_permanent_state').val('');

        }
        var s_present_pincode = $('#s_present_pincode').val();
        $('#s_permanent_addr').val(s_present_addr);
        $('#s_permanent_area').val(s_present_area);
        $('#s_permanent_district').val(s_present_district);

        $('#s_permanent_country').val(s_present_country);
        $('#s_permanent_pincode').val(s_present_pincode);
    } else {
        $('#s_permanent_addr').val('');
        $('#s_permanent_area').val('');
        $('#s_permanent_district').val('');
        $('#s_permanent_state').val('');
        $('#s_permanent_state1').val('');
        $('#s_permanent_country').val('');
        $('#s_permanent_pincode').val('');
    }
    }, 50); // Small delay to ensure checkbox state is properly updated
});


function add_required_aadhar_number() {
    var student_aadhar = '<?php echo $required_fields['student_aadhar']['required'] ?>';
    var nationality = $('#nationality').val();
    if (nationality == 'Indian' && student_aadhar) {
        $('#student_aadhar').attr('required', 'required');
    } else {
        $('#student_aadhar').removeAttr('required');
    }
    var document_version = '<?php echo $config_val['document_input_version'] ?>';
    if (document_version == 'V2') {
        check_uploaded_documents()
    }
}

function check_uploaded_documents() {
    var nationality = $('#nationality').val();
    var old_nationality = '<?php echo $final_preview->nationality ?>';
    var af_id = '<?php echo $final_preview->id ?>';
    var adm_setting_id = '<?php echo $final_preview->admission_setting_id ?>';
    if(old_nationality == '' || old_nationality == null) old_nationality = 'Indian';
    if (nationality != old_nationality) {
        $.ajax({
            url: '<?php echo site_url('admission_controller/get_admission_documents'); ?>',
            type: 'post',
            data: {
                'af_id': af_id,
                'adm_setting_id': adm_setting_id,
                'nationality': nationality,
                'relation': 'student'
            },
            success: function(data) {
                parsed_data = $.parseJSON(data);
                if (parsed_data != '') {
                    delete_uploaded_documents(parsed_data, old_nationality, 'student');
                }
            },
            error: function(err) {
                console.log(err);
            }

        });
    }
}

// Custom SweetAlert styling
const swalCustomStyles = `
<style>
.swal-medium-width {
    width: 400px !important;
    max-width: 90vw !important;
}
.swal2-popup {
    border-radius: 12px !important;
}
.swal2-title {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
}
.swal2-content {
    font-size: 1rem !important;
    line-height: 1.5 !important;
}
</style>
`;

// Inject custom styles if not already present
if (!document.querySelector('#swal-custom-styles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'swal-custom-styles';
    styleElement.innerHTML = swalCustomStyles;
    document.head.appendChild(styleElement);
}

function delete_uploaded_documents(parsed_data, p_old_nationality, relation) {
    console.log(p_old_nationality);
    Swal.fire({
        title: 'Delete the Documents',
        text: 'If you change the nationality uploaded documents will be deleted. Do you want to continue?',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#dc3545',
        confirmButtonText: 'Yes, Delete',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        customClass: {
            popup: 'swal-medium-width'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('admission_controller/delete_uploaded_documents'); ?>',
                type: 'post',
                data: {
                    'document_ids': parsed_data
                },
                success: function(data) {
                    if (data) {
                        Swal.fire({
                            title: 'Success!',
                            text: 'Documents deleted successfully',
                            icon: 'success',
                            confirmButtonColor: '#28a745',
                            timer: 3000,
                            timerProgressBar: true
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Something went wrong while deleting documents',
                            icon: 'error',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Network error occurred. Please try again.',
                        icon: 'error',
                        confirmButtonColor: '#dc3545'
                    });
                }
            });
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            // User cancelled, revert nationality selection
            if (relation == 'student') {
                $('#nationality').val(p_old_nationality);
            } else if (relation == 'father') {
                $('#f_nationality').val(p_old_nationality);
            } else if (relation == 'mother') {
                $('#m_nationality').val(p_old_nationality);
            }
        }
    });
}
// var casteData = <?php //echo json_encode($caste); ?>;
// var categoryOptions = <?php //echo json_encode($categoryOptions); ?>;
var casteOptions = <?php echo json_encode($casteOptions); ?>;
casteOptions = Object.entries(casteOptions)

function getCaste() {
    const casteCategory = $("#category option:selected").text();
    let castHtml = '<option value="">Select caste</option>'
    casteOptions.forEach(c => {
        if (c[1].category.trim() == casteCategory.trim()) {
            castHtml += `<option data-cate-id="${c[1].category}" value="${c[1].caste}">${c[1].caste}</option>`;
        }
    })
    $("#student_caste").html(castHtml);
}

function backFillCategory() {
    const categoryName = $("#student_caste :selected")[0].dataset.cateId;
    const allCategories = document.querySelectorAll("#category")[0].querySelectorAll("option")

    allCategories.forEach(category => {
        if (category.value.trim() == categoryName.trim()) {
            category.selected = "Selected";
        }
    });

    $("#category").html(allCategories);
}

function is_physically_challenged() {
    var physical_disability = $('#disability_select').val();
    if (physical_disability == 'Y') {
        $('#physical_desription').show();
    } else {
        $('#physical_desription').hide();
    }
}

function if_yes_sibling_data_edit() {
    var hasSibling = $('#has_sibling_select').val();
    if (hasSibling == "1") {
        $("#in_school").show();
        $(".sibling_data").show();
    } else {
        $("#in_school").hide();
        $(".sibling_data").hide();
    }
    var sibling_in = $('#sibling_in_select').val();
    if (sibling_in == 'other') {
        $("#sibling_school_name").show();
    } else {
        $("#sibling_school_name").hide();
    }
}

function if_yes_sibling_data() {
    var sibling_name_required = '<?php echo $required_fields['sibling_student_name']['required']?>';
    var sibling_school_name = '<?php echo $required_fields['sibling_school_name']['required']?>';
    var sibling_student_class = '<?php echo $required_fields['sibling_student_class']['required']?>';

    // Get the value from the dropdown
    var hasSibling = $('#has_sibling_select').val();
    if (hasSibling == '1' || hasSibling == 1) {
        $("#in_school").show();
        $(".sibling_data").show();
        $('#siblingSchool_name').val('');
        $('#sibling_in_select').val('same_school'); // Default to same_school
        if (sibling_name_required) {
            $('#sibling_student_name').attr('required', 'required');
        }
        if (sibling_student_class) {
            $('#sibling_student_class').attr('required', 'required');
        }
    } else {
        $('#sibling_in_select').removeAttr('required');
        $('#sibling_student_name').removeAttr('required');
        $('#sibling_student_class').removeAttr('required');
        $('#siblingSchool_name').removeAttr('required');
        $('#sibling_in_select').val('');
        $("#in_school").hide();
        $(".sibling_data").hide();
        $('#sibling_school_name').hide();
    }
    change_sibling_studying_in();

}

function change_sibling_studying_in() {
    var sibling_in = $('#sibling_in_select').val();
    var sibling_school_name = '<?php echo $required_fields['sibling_school_name']['required']?>';
    if (sibling_in == 'other') {
        $("#sibling_school_name").show();
        $('#siblingSchool_name').val('');
        if (sibling_school_name) {
            $('#siblingSchool_name').attr('required', 'required');
        }
    } else {
        $('#siblingSchool_name').removeAttr('required');
        $("#sibling_school_name").hide();
    }
}

$(document).ready(function() {
    present_state_validate_required_field();
    permanent_state_validate_required_field();
});

function present_state_validate_required_field() {
    var present_state_required = '<?php echo $required_fields['s_present_state']['required'] ?>';
    var country = $('#s_present_country').val();
    if (country == 'India') {
        $('#state_select').show();
        $('input[type=text][name=s_present_state1]').removeAttr("required");
        $('#state_input').hide();
        if (present_state_required) {
            $('select[name=s_present_state]').attr('required', 'required');
        }
    } else {
        $('#state_input').show();
        $('select[name=s_present_state]').removeAttr("required");
        $('#state_select').hide();
        if (present_state_required) {
            $('input[type=text][name=s_present_state1]').attr('required', 'required');
        }
    }
}

$("#s_present_country").change(function() {
    present_state_validate_required_field();

    //     var required_fields = '<?php // echo json_encode($required_fields) ?>';
    //     var required_fields_array = $.parseJSON(required_fields);
    //     var present_state_required = required_fields_array['s_present_state']['required'];
    //     var country = $('#s_present_country').val();
    //   if(country == 'India'){
    //     $('#state_select').show();
    //     $('input[type=text][name=s_present_state1]').removeAttr("required");
    //     $('#state_input').hide();
    //     if(present_state_required){
    //         $('select[name=s_present_state]').attr('required','required');
    //     }
    //   }else{
    //     $('#state_input').show();
    //     $('select[name=s_present_state]').removeAttr("required");
    //     $('#state_select').hide();
    //     if(present_state_required){
    //         $('input[type=text][name=s_present_state1]').attr('required','required');
    //     }
    //   }
});

function permanent_state_validate_required_field() {
    var permanent_state_required = '<?php echo $required_fields['s_permanent_state']['required'] ?>';
    var country = $('#s_permanent_country').val();
    if (country == 'India') {
        $('#per_state_select').show();
        $('input[type=text][name=s_permanent_state1]').removeAttr("required");
        $('#per_state_input').hide();
        if (permanent_state_required) {
            $('select[name=s_permanent_state]').attr('required', 'required');
        }
    } else {
        $('#per_state_select').hide();
        $('#per_state_input').show();
        $('select[name=s_permanent_state]').removeAttr("required");
        if (permanent_state_required) {
            $('input[type=text][name=s_permanent_state1]').attr('required', 'required');
        }
    }
}

$("#s_permanent_country").change(function() {
    permanent_state_validate_required_field();
});

function if_yes_descriptions() {
    if ($('#learning_select').val() == "Y") {
        $('#special_needs_description_id').show();
        $('#special_needs_description').attr('required', 'required');
    } else {
        $('#special_needs_description_id').hide();
        $('#special_needs_description').removeAttr('required');
        $('#special_needs_description').val('');
    }
}

function show_combination_description() {
    var content = `<?php echo $config_val['combination_help_block'] ; ?>`;
    Swal.fire({
        title: `<div class="swal2-header-custom" style="text-align: center;margin-bottom: 20px;">
                    <span>Description for combination</span>
                </div>`,
        html: content,
        showDenyButton: false,
        //   showCancelButton: true,
        confirmButtonText: "OK",
    })
}

// Fix for checkbox clickability issues in student form
$(document).ready(function() {
    // Ensure copy present address checkbox is clickable
    $('#copy_present_address').css({
        'z-index': '999',
        'position': 'relative',
        'pointer-events': 'auto',
        'opacity': '1',
        '-webkit-appearance': 'checkbox',
        'appearance': 'checkbox'
    });

    // Add click handler with debugging
    $('#copy_present_address').off('click').on('click', function(e) {
        console.log('Copy present address checkbox clicked:', $(this).is(':checked'));
        // Let the default behavior happen
    });

    // Label click handler for mobile - ensure it triggers the main function
    $('label[for="copy_present_address"]').off('click').on('click', function(e) {
        var checkbox = $('#copy_present_address');
        if (e.target !== checkbox[0]) {
            e.preventDefault();
            // Toggle checkbox state
            checkbox.prop('checked', !checkbox.prop('checked'));
            // Trigger all the events that the main function listens to
            checkbox.trigger('click').trigger('change').trigger('touchend');
            console.log('Label clicked for copy_present_address, checkbox now:', checkbox.is(':checked'));
        }
    });

    // Add mobile-specific styles for student form checkbox
    $('<style>').prop('type', 'text/css').html(`
        #copy_present_address {
            z-index: 999 !important;
            position: relative !important;
            pointer-events: auto !important;
            opacity: 1 !important;
            -webkit-appearance: checkbox !important;
            appearance: checkbox !important;
            transform: scale(1.5) !important;
            margin-right: 12px !important;
        }

        @media (max-width: 768px) {
            .mobile-checkbox-label {
                min-height: 48px !important;
                padding: 15px !important;
                background-color: transparent !important;
                border: none !important;
                border-radius: 8px !important;
                margin-bottom: 10px !important;
                display: flex !important;
                align-items: center !important;
            }
            .mobile-checkbox-label:active {
                background-color: rgba(0,0,0,0.05) !important;
                transform: scale(0.98) !important;
            }

            /* Ensure checkbox is properly sized on mobile */
            #copy_present_address {
                min-width: 20px !important;
                min-height: 20px !important;
                transform: scale(1.6) !important;
            }
        }
    `).appendTo('head');
});
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div id="age_guidelines_message" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?= $school_name ?></h4>
            </div>
            <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:500px;">
                <div id="modal-loader">
                    <?php echo $this->settings->getSetting('admission_age_guidelines_message'); ?>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

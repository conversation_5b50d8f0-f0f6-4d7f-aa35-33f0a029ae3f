<?php $institute = $this->settings->getSetting('your_word_for_institute'); ?>
<?php if(!empty($prev_eduction_info)) : ?>
<div class="row mb-5">
    <div class="modern-table-container col-md-12">
        <h3 style="font-weight:600; margin-bottom:5px;font-size: 16px;">Details of Previous Education</h3>
        <span class="modern-help-block">
            Provide your past education info and click "Fill the details."
        </span>
        <hr style="margin: 16px 0 24px 0; border-top: 1px E8E8E8;">
        <form id="demo-form" action="" class="form-horizontal" data-parsley-validate method="post">
            <table class="modern-table">
                <thead>
                    <tr style="color: #878787;font-size: 12px;font-weight: 600;">
                        <th style="width: 15%;">Year</th>
                        <th style="width: 45%;">School Name</th>
                        <th style="width: 20%;">Grade</th>
                        <th style="width: 20%;"></th>
                    </tr>
                </thead>
                <tbody id="pre_school_table">
                    <?php foreach (
                        $previous_year_settings as $key => $year) { ?>
                    <tr>
                        <td><?php echo $year; ?>
                            <?php if($required_fields['year']['required']=='required') echo '<font color="red">*</font>' ?>
                        </td>
                        <td class="required_checkYear" id="displayschoolName<?php echo $year ?>"></td>
                        <td id="displayclassName<?php echo $year ?>">-</td>
                        <td>
                            <button type="button" class="add-details-btn"
                                onclick="showPreviousEducationSwal('<?php echo $year ?>')"
                                id="detail_popup_<?php echo $year; ?>">
                                <span class="plus-icon">+</span>
                                Add Details
                            </button>
                            <span id="successErrorMessage<?php echo $year ?>" class="success-message"></span>
                            <button type="button" class="add-details-btn subject_btn"
                                onclick="showPreviousSubjectsSwal('<?php echo $year ?>')"
                                id="subject_popup_<?php echo $year; ?>"
                                disabled
                                title="Please add school details first">
                                <span class="plus-icon">+</span>
                                Add Subjects
                            </button>
                        </td>
                    </tr>
                    <?php } ?>
                </tbody>
            </table>
        </form>
    </div>
</div>
<?php endif ?>

<div class="row" id="language_selection">
</div>

<?php $document_size = $this->settings->getSetting("documents_size_in_admissions"); if(empty($document_size)) { $document_size = 2 ;} ?>
<?php if(!empty($documents)){ ?>
<div class="row mt-5">
    <div class="modern-table-container col-md-12">
        <h3 style="font-weight:600; margin-bottom:5px;font-size: 16px;">Upload Documents</h3>
        <span class="modern-help-block">
            Upload PDF, JPG, JPEG, or PNG files under <?= $document_size ?> MB.
        </span>
        <hr style="margin: 16px 0 24px 0; border-top: 1px E8E8E8;">
        <div class="col-md-12" id="newDocTabV1">

        </div>

    </div>
</div>

<?php } ?>
</div>


<style type="text/css">
.fileinput-remove-button {
    display: none !important;
}

.fileinput-remove {
    display: none !important;
}
</style>
<style type="text/css">
.bootbox-close-button {
    display: none;
}
</style>

<style>
.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 8px;
    background: none;
}

.modern-table th,
.modern-table td {
    background: none;
    border: none;
    padding: 6px 10px;
    font-size: 16px;
    vertical-align: middle;
}

.modern-table thead th {
    color: #878787;
    font-weight: 500;
    font-size: 14px;
    background: none;
    border-bottom: 1px solid #eee;
}

.modern-table tbody tr {
    background: #fcfcfc;
    border-radius: 8px;
    box-shadow: none;
    transition: background 0.2s;
}

.modern-table td:first-child {
    font-weight: 500;
    color: #131927;
    font-size: 14px;
}

.modern-table tbody tr:hover {
    background-color: #f6f6f8;
    /* or any color you prefer */
}

.modern-table .add-details-btn {
    color: #6c3ef5;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    justify-content: flex-end;
    background: none;
    border: none;
    padding: 0;
    font-size: 15px;
}

.modern-table .add-details-btn .plus-icon {
    font-size: 18px;
    font-weight: bold;
    color: #6c3ef5;
    margin-right: 2px;
}

.modern-table .add-details-btn:disabled {
    color: #999 !important;
    cursor: not-allowed !important;
    opacity: 0.6;
}

.modern-table .add-details-btn:disabled .plus-icon {
    color: #999 !important;
}

.modern-table td:last-child {
    text-align: right;
}

.modern-table .success-message {
    display: block;
    font-size: 13px;
    color: #28a745;
    margin-top: 2px;
}

.modern-help-block {
    color: #A09EAE;
    font-size: 13px;
    margin-bottom: 18px;
    margin-top: 2px;
    display: block;
}

.modern-table-container {
    margin: 0;
    padding: 0;
    background: none;
    box-shadow: none;
    border: none;
}

.swal2-popup .swal2-html-container {
    max-height: 70vh;
    overflow-y: auto;
    margin: 0;
    padding: 0;
}

/* Optional: Make the form itself scrollable if needed */
.swal2-popup #swal-previous-education-form,
.swal2-popup .container-fluid {
    max-height: 65vh;
    /* overflow-y: auto; */
}

/* Responsive width for smaller screens */
@media (max-width: 600px) {
    .swal2-popup {
        width: 98vw !important;
        min-width: 0 !important;
        padding: 0 !important;
    }

    .swal2-html-container {
        padding: 0 !important;
    }
}
</style>


<!-- Modal Pre-School -->
<div id="hidden-previous-education-form" style="display:none;">
    <form id="swal-previous-education-form" enctype="multipart/form-data">
        <input type="hidden" id="schoolYear" name="schooling_year">
        <input type="hidden" id="schoolYearAfId">
        <input type="hidden" id="admission_prev_school_primary_id" name="admission_prev_school_primary_id">
        <input type="hidden" value='<?php echo $au_id ?>' name="au_id">

        <div class="container-fluid">
            <div class="col-md-12" style="margin-top:1rem">
                <?php if(in_array('year',$disabled_fields)) { ?>
                <div class="row mb-5">
                    <label for="prev_school_year" class="form-label">Year of Passing</label>
                    <select name="prev_school_year" id="prev_school_year" class="form-select">
                        <option value="">Select Year</option>
                        <?php foreach($academic_years as $key =>$val) { ?>
                        <option value="<?= $val->acad_year ?>"><?= $val->acad_year ?></option>
                        <?php }?>
                    </select>
                </div>
                <?php }?>

                <div class="row mb-5">
                    <label for="schooling_school_new" class="form-label"><?php echo $institute ?: 'School'; ?> Name
                        <?php if($required_fields['school_name']['required']=='required') echo '<span class="text-danger">*</span>' ?></label>
                    <input type="text" class="form-control" <?= $required_fields['school_name']['required'] ?>
                        name="schooling_school" id="schooling_school_new" placeholder="Enter the School Name">
                </div>

                <?php if(!in_array('school_address',$disabled_fields)) { ?>
                <div class="row mb-5">
                    <label for="school_address_new" class="form-label"><?php echo $institute ?: 'School'; ?> Address
                        <?php if($required_fields['school_address']['required']=='required') echo '<span class="text-danger">*</span>' ?></label>
                    <input type="text" class="form-control" <?= $required_fields['school_address']['required'] ?>
                        name="school_address" id="school_address_new" placeholder="Enter the School Address">
                </div>
                <?php } ?>

                <?php if(!in_array('type_of_school',$disabled_fields)) { ?>
                <div class="row mb-5">
                    <label for="type_of_school" class="form-label">Type of
                        School<?php if($required_fields['type_of_school']['required']=='required') echo '<span class="text-danger">*</span>' ?></label>
                    <select class="form-control" <?= $required_fields['type_of_school']['required'] ?>
                        name="type_of_school" id="type_of_school">
                        <option value="">Select Type of School</option>
                        <option value="Public school">Public school</option>
                        <option value="Private school">Private school</option>
                        <option value="Home School">Home School</option>
                    </select>
                </div>
                <?php } ?>

                <?php if(!in_array('medium_of_instruction', $disabled_fields)) { ?>
                <div class="row mb-5">
                    <label for="medium_of_instruction" class="form-label">Medium of Instruction
                        <?php if($required_fields['medium_of_instruction']['required']=='required') echo '<span class="text-danger">*</span>' ?>
                    </label>
                    <select class="form-select" <?= $required_fields['medium_of_instruction']['required'] ?>
                        name="medium_of_instruction" id="medium_of_instruction">
                        <option value="">Select Medium of Instruction</option>
                        <option value="English">English</option>
                        <option value="Hindi">Hindi</option>
                        <option value="Telugu">Telugu</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <?php } ?>

                <?php $label = $this->settings->getSetting('your_word_for_class') ?>
                <?php if(admission_is_enabled($disabled_fields, 'class')) :  ?>
                <div class="row mb-5">
                    <label for="schooling_class_new" class="form-label"><?php echo $label ?: 'Grade'; ?>
                        <?php if($required_fields['class']['required']=='required') echo '<span class="text-danger">*</span>' ?></label>
                    <input type="text" class="form-control" <?= $required_fields['class']['required'] ?>
                        name="schooling_class" id="schooling_class_new" placeholder="Enter the Grade">
                </div>
                <?php endif ?>

                <?php if(admission_is_enabled($disabled_fields, 'registration_no')) :  ?>
                <div class="row mb-5">
                    <label for="registration_no_new" class="form-label">Register No/Unique No/Roll No
                        <?php if($required_fields['registration_no']['required']=='required') echo '<span class="text-danger">*</span>' ?></label>
                    <input type="text" class="form-control" <?= $required_fields['registration_no']['required'] ?>
                        name="registration_no" id="registration_no_new"
                        placeholder="Enter Register No/Unique No/Roll No">
                </div>
                <?php endif ?>

                <div class="row mb-5">
                    <label for="schooling_board_new" class="form-label">Board
                        <?php if($required_fields['board']['required']=='required') echo '<span class="text-danger">*</span>'; ?>
                    </label>

                    <select class="form-select" <?= $required_fields['board']['required'] ?> name="schooling_board"
                        id="schooling_board_new">
                        <option value="">Select Board</option>
                        <?php 
                        $selected_board = !empty($final_preview->schooling_board) ? $final_preview->schooling_board : '';
                        $board_list = !empty($boards) ? $boards : ['CBSE', 'ICSE', 'STATE', 'Home School', 'IGCSE', 'IB', 'NIOS', 'PU Board', 'ITI Board', 'KSEEB', 'Other'];
                        
                        foreach ($board_list as $board) {
                            $selected = ($board == $selected_board) ? 'selected' : '';
                            echo "<option value=\"$board\" $selected>$board</option>";
                        }
                        ?>
                    </select>
                </div>

                <div class="row mb-5" id="board_other_show" style="display:none;">
                    <label for="b_other" class="form-label">Others</label>
                    <input placeholder="Enter Please Specify" id="b_other" name="board_other" type="text"
                        class="form-control" data-parsley-pattern="^[a-zA-Z. ]+$" data-parsley-maxlength="6">
                </div>

                <?php if(admission_is_enabled($disabled_fields, 'transfer_reason')) :  ?>
                <div class="row mb-5">
                    <label for="transfer_reason_id_new" class="form-label">Reason for transfer/withdrawal from school
                        <?php if($required_fields['transfer_reason']['required']=='required') echo'<span class="text-danger">*</span>' ?></label>
                    <textarea class="form-control" name="transfer_reason_id" id="transfer_reason_id_new"
                        placeholder="Enter the reason for transfer/withdrawal from school"></textarea>
                </div>
                <?php endif ?>

                <?php if(admission_is_enabled($disabled_fields, 'expelled_or_suspended')) :  ?>

                <div class="row mb-5">
                    <label class="form-label" for="expelled_or_suspended_select">Has student ever been expelled or
                        suspended from school?</label>
                    <select class="form-select" name="expelled_or_suspended" id="expelled_or_suspended_select"
                        onchange="show_suspended_reason(this)">
                        <option value="">Select</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
                <div class="row mb-5" id="expelled_or_suspended_id" style="display: none;">
                    <label class="form-label" for="expelled_or_suspended_description">Add Reason</label>
                    <textarea class="form-control" name="expelled_or_suspended_description"
                            id="expelled_or_suspended_description_new"></textarea>
                </div>

                <?php endif ?>

                <?php if(admission_is_enabled($disabled_fields, 'previous_school_ratings')) : ?>
                <div class="row mb-4">
                    <label class="form-label d-block mb-2">Rating of <?php echo $institute ?: 'School'; ?></label>
                    <select name="previous_school_ratings" id="previous_school_ratings" class="form-select">
                        <option value="">Select Ratings</option>
                        <?php for ($i = 1; $i <= 5; $i++) { ?>
                            <option value="<?= $i ?>"><?= $i ?></option>
                        <?php } ?>
                    </select>
                </div>
                <?php endif ?>
                
                <?php if (admission_is_enabled($disabled_fields, 'report_card')) : ?>
                <div class="row mb-5">
                    <div class="upload-card">
                        <label class="upload-label">
                            Report Card
                            <?php if ($required_fields['report_card']['required'] == 'required') echo '<span class="text-danger">*</span>'; ?>
                        </label>

                            <div id="report_card_upload_div"
                                class="border border-dashed rounded p-4 text-center position-relative"
                                style="cursor: pointer; background-color: #fafafa;">

                                <div
                                    style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                                    <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                                </div>

                                <div class="d-flex flex-column align-items-center mt-4">
                                    <i class="bi bi-upload" style="font-size: 24px;"></i>
                                    <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                                    <small class="text-muted">JPG, JPEG, PNG or PDF (max. 2MB)</small>
                                </div>

                                <input type="file" id="report_card_fileupload" name="report_card" style="display:none;"
                                    <?= $required_fields['report_card']['required'] ?>
                                    accept="image/jpeg, application/pdf" onchange="handleReportCardUpload(this)">

                                <!-- Optional success area -->
                                <span style="font-size:12px;color:red" class="text-danger d-block mt-2"
                                    id="report_card_error"></span>
                            </div>
                        <input type="hidden" name="report_card_path" id="report_card_path">
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </form>
</div>


<script>
function clearform_schooldetails_alert() {
    bootbox.confirm({
        message: 'Do you want clear this form. Are you sure ?',
        title: 'Confirm',
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function(result) {
            if (result) {
                $('#previous_school_details_popup form').trigger("reset");
            }
        }
    });
}
</script>

<?php $label = $this->settings->getSetting('your_word_for_class') ?>
<!-- Modal Pre-Subject -->
<div class="modal fade" id="previous_school_subject_popup" tabindex="-1" role="dialog"
    style="width:100%;margin:auto;top:2%;" data-backdrop="static" aria-labelledby="previous_school_details_popup-label"
    aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;width:50%">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <?php if (!$show_previous_schooling_subjects) { ?>
            <h4 class="modal-title inline-block inline_class" id="">
                <?php if($label) { echo $label;}else{ echo 'Grades' ;} ?> and marks obtained in Final Exam</h4>
            <?php } else { ?>
            <h4 class="modal-title inline-block inline_class" id="">Add Previous School Subjects</h4>
            <?php } ?>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1; padding-top: .5rem;"
                type="button" class="close inline_class" data-dismiss="modal">&times;</button>
        </div>
        <form enctype="multipart/form-data" id="school_subjects_form" class="form-horizontal" data-parsley-validate
            method="post">
            <input type="hidden" id="subject_schoolYear" name="schooling_sub_year">
            <input type="hidden" value="<?php echo $insert_id ?>" name="sub_lastId">
            <input type="hidden" value='<?php echo $au_id ?>' name="sub_au_id">
            <div class="modal-body">

                <div>
                    <div class="subject_details">


                    </div>
                </div>

                <div class="modal-footer">
                    <div class="inline_class2"> <input class="btn btn-primary btn_style4" type="reset"
                            value="Clear Form"></div>
                    <button type="button" class="btn btn-primary inline_class2 btn_style3"
                        onclick="submit_previous_school_subjects()" id="save_school_subjects">Add Subjects</button>
                </div>
        </form>
    </div>
</div>






<style>
.btn_style1 {
    border: 1px solid white;
    border-radius: 12px;
    background-color: #6893ca;
    text-align: center;
}

.btn_style2 {
    border-radius: 12px;
    border: 1px solid white;
    background-color: #17a2b8;
    text-align: center;
}

.btn_style3 {
    border-radius: 12px;
    border: 1px solid white;
    background-color: green;
    text-align: center;
}

.btn_style4 {
    border-radius: 12px;
    border: 1px solid white;
    background-color: gray;
    text-align: center;
}

.inline_class {
    display: inline;
}

.inline_class2 {
    display: inline;
}
</style>

<script type="text/javascript">
function download_report_card_attachment() {
    var staffId = $('#workshop_staff_id').val();
    var workshop_staff_certificate_id = $('#workshop_staff_certificate_id').val();
    window.location.href =
        '<?php echo site_url('staff/Staff_controller/stafftraning_workshop_documents_download/'); ?>' + staffId + '/' +
        workshop_staff_certificate_id;
}
</script>
<script type="text/javascript">
var schoolYear = '<?php echo json_encode($previous_year_settings) ?>';
var schoolYears = $.parseJSON(schoolYear);
var afId = '<?php echo $insert_id ?>';
var gYearsSetting = '<?= json_encode($previous_year_settings)?>';
var gYearDispaly = $.parseJSON(gYearsSetting);
// console.log(gYearDispaly);
// var yearLength = gYear.length;
$(document).ready(function() {
    $("#previous_school_details_popup").find("#expelled_or_suspended_id").css({
        "display": "none"
    });
    $("#previous_school_details_popup").find("#board_other_show").css({
        "display": "none"
    });

    for (var k in schoolYears) {
        get_if_exit_school_detailsperyear(afId, schoolYears[k]);
    }
});

if ($('#schooling_board_new').find(":selected").text() == '') {
    $("#board_other_show").show();
}


function remove_but(row) {
    row.closest('tr').remove();
    // Reorder serial numbers
    $('#doc_id tr').each(function(index) {
        $(this).find('td:first').text(index + 1); // Update serial number
    });
}


function deletedocument_row_new(d_id, sl, required = '') {
    var afid = $('#afid').val();
      var max_size_string = '<?php echo $documents_size_in_admissions ;?>' ;
    $.ajax({
        url: '<?php echo site_url('admission_controller/delete_documentbyId'); ?>',
        type: 'post',
        data: {
            'd_id': d_id
        },
        success: function(data) {
            if (data != '') {
                // Get the document label from the upload card
                var uploadCard = $('#document_row_' + sl).closest('.upload-card');
                var docLabel = uploadCard.find('.upload-label').text() || '';
                var docName = docLabel.replace('*', '').trim(); // Remove asterisk and trim

                console.log('Deleting document:', docName, 'at position:', sl);

                // Check if this is an Aadhar or PAN document
                var isAadharOrPan = false;
                var viewType = '';
                var onclickHandler = '';

                if (docName.toLowerCase().includes('aadhar')) {
                    isAadharOrPan = true;
                    viewType = 'aadhar';
                    onclickHandler = `onclick="show_upload_document_modal('aadhar','${docName}','${sl}','${required}')"`;
                } else if (docName.toLowerCase().includes('pan')) {
                    isAadharOrPan = true;
                    viewType = 'pan';
                    onclickHandler = `onclick="show_upload_document_modal('pan','${docName}','${sl}','${required}')"`;
                } else {
                    onclickHandler = `onclick="document.getElementById('doc-upload${sl}').click();"`;
                }

                console.log('Document type detected:', {
                    isAadharOrPan: isAadharOrPan,
                    viewType: viewType,
                    onclickHandler: onclickHandler
                });

                // Create the upload area HTML based on document type
                var uploadAreaHtml = '';
                if (isAadharOrPan) {
                    uploadAreaHtml = `
                        <div class="border border-dashed rounded p-4 text-center position-relative" id="document_row_${sl}"
                            style="cursor: pointer;border: 1px dashed #D9D1FF;height:10rem;" ${onclickHandler}>
                            <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                            </div>
                            <div class="d-flex flex-column align-items-center mt-4">
                                <span><strong style="color: #6c63ff;">Upload Document</strong></span>
                                <small class="text-muted">Upload your document and enter the required details</small>
                            </div>
                            <input type="hidden" name="document_for" id="document_for${sl}" value="${docName}">
                            <span style="color:red;" id="doc_error${sl}"></span>
                            <span id="percentage_doc_completed${sl}" style="font-size: 20px; display: none;"></span>
                        </div>
                    `;
                } else {
                    uploadAreaHtml = `
                        <div class="border border-dashed rounded p-4 text-center position-relative" id="document_row_${sl}"
                            style="cursor: pointer;border: 1px dashed #D9D1FF;height:10rem;" ${onclickHandler}>
                            <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                            </div>
                            <div class="d-flex flex-column align-items-center mt-4">
                                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                                <small class="text-muted">PNG, JPG or PDF (max. ${max_size_string}MB)</small>
                            </div>
                            <input type="file" id="doc-upload${sl}" name="document_file_path" style="display:none;" ${required} accept="image/jpeg, image/png, application/pdf" onchange="upload_document_file_path(${sl}, this, '', '${required}')">
                            <input type="hidden" name="document_for" id="document_for${sl}" value="${docName}">
                            <span style="color:red;" id="doc_error${sl}"></span>
                            <span id="percentage_doc_completed${sl}" style="font-size: 20px; display: none;"></span>
                        </div>
                    `;
                }

                // Replace the document_row content with upload area
                $('#document_row_' + sl).replaceWith(uploadAreaHtml);

                console.log('Document upload area restored for:', docName);

            } else {
                alert("Something went wrong in Student data, try again.");
                return 0;
            }
        }
    });
}


function upload_document_file_path(sl, document,doc_rowId,required) {
    var max_size_string = '<?php echo $documents_size_in_admissions ;?>';
    var file = document.files[0];
    var fileName = file.name;
    var fileSizeMB = (file.size / 1024 / 1024).toFixed(2);

    if (document.value && validate_documents_config_based(file)) {
        $("#doc_error" + sl).html("");
    } else {
        $("#doc_error" + sl).html(
            `Allowed file size exceeded. (Max.  ${max_size_string} Mb) / Allowed file types are jpeg, jpg and pdf`
        );
        $('#doc-upload' + sl).val('');
        return;
    }
    var document_name = $('#document_for' + sl).val();
    var fileExtension = file.name.split('.').pop().toLowerCase();
    var fileIcon = 'PDF';
    var iconColor = '#FF2D2D';

    if (fileExtension === 'jpg' || fileExtension === 'jpeg' || fileExtension === 'png') {
        fileIcon = 'IMG';
        iconColor = '#4CAF50';
    }
    var cardHtml = `
        <div class="uploaded-file-card d-flex align-items-center justify-content-between p-3 rounded" style="background:#fff; border-radius:6px; border: 1px dashed #dcdcdc;">
            <div class="file-icon d-flex align-items-center justify-content-center me-3" style="width:48px; height:48px; background:${iconColor}; border-radius:10px;">
                <span style="color:#fff; font-weight:700; font-size:1.1em;">${fileIcon}</span>
            </div>
            <div class="flex-grow-1" style="min-width:0;">
                <div class="d-flex align-items-center justify-content-between" style="gap: 16px;">
                    <div class="file-name" style="font-weight:600; color:#181028; font-size:1em; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">
                        ${fileName}
                    </div>
                    <div class="file-size text-muted" style="font-size: 1em; min-width:60px; text-align:right;">
                        ${fileSizeMB} mb
                    </div>
                    <div id="delete_btn_doc${sl}">
                        
                    </div>
                </div>
                <div id="progress_area_doc${sl}" class="d-flex align-items-center mt-2" style="gap:8px;">
                    <div style="flex:1;">
                        <div style="background:#edeafd; border-radius:6px; height:8px; width:100%;">
                            <div id="progress_bar_doc${sl}" style="background:#6c63ff; height:8px; border-radius:6px; width:0%"></div>
                        </div>
                    </div>
                    <div id="progress_percent_doc${sl}" style="min-width:32px; text-align:right; color:#181028; font-weight:500;">0%</div>
                </div>
            </div>
        </div>
        `;
    $('#document_row_' + sl).html(cardHtml);
    saveFileToStorage_document(file, document_name, '<?php echo $insert_id ?>', sl,required);
}

function saveFileToStorage_document(file, document_for, af_id, sl,required) {
    $('#percentage_doc_completed' + sl).show();
    $('#doc-upload' + sl).attr('disabled', 'disabled');
    $("#document_submit").prop('disabled', true);
    $.ajax({
        url: '<?php echo site_url("S3_admission_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
        },
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress_doc(0, sl);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress_doc(e.loaded / e.total * 100 |
                                0, sl);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    // return false;
                    $('#percentage_doc_completed' + sl).hide();
                    update_admission_student_documents(path, document_for, af_id,
                        sl,required);
                    $('#doc-upload' + sl).removeAttr('disabled');
                    $('#document_row_' + sl).css('opacity', '1');
                    $('.file-preview').css('opacity', '1');
                    $("#document_submit").prop('disabled', false);
                    // resolve({path:path, name:file.name, type:file.type});
                    // increaseLoading();
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function(err) {
            reject(err);
        }
    });

}

function single_file_progress_doc(percentage, sl) {
    var percent = Math.round(percentage);
    $("#progress_bar_doc" + sl).css('width', percent + '%');
    $("#progress_percent_doc" + sl).text(percent + '%');

    if (percent >= 100) {
        document.getElementById("progress_area_doc" + sl).style.setProperty('display', 'none', 'important');
        $("#file_size_doc" + sl).css('display', 'inline-block');
        $("#delete_btn_doc" + sl).css('display', 'inline-block');
        $("#file_size_doc" + sl).parent().css({'flex-direction': 'row', 'align-items': 'center', 'gap': '8px'});
    }
}

function update_admission_student_documents(path, document_for, af_id, sl,required) {
    // console.log(document_for);
    $.ajax({
        url: '<?php echo site_url('admission_controller/update_documents_new'); ?>',
        type: 'post',
        data: {
            'path': path,
            'document_for': document_for,
            'af_id': af_id
        },
        success: function(data) {
            var docrowId = data.trim();
            if (docrowId != 0) {
                $('#doc-upload' + sl).attr('disabled', 'disabled');
                $("#remove" + sl).hide();
                    $('#delete_btn_doc'+sl).html(` <button type="button" class="btn btn-link text-danger p-0 ms-2 d-flex align-items-center" onclick="deletedocument_row_new(${docrowId},${sl},'${required}')">
                    <span style="display: flex; align-items: center; width: 24px; height: 24px;">
                        <?php $this->load->view('svg_icons/delete_icon.svg') ?>
                    </span>
                </button>`)
            }

        }
    });
}

function show_suspended_reason(selectElem) {
    console.log(selectElem)
    var value = $(selectElem).val();

    // Check if we're in a SweetAlert popup
    var swalPopup = Swal.getPopup();
    var targetContainer;

    if (swalPopup && $(selectElem).closest('.swal2-popup').length > 0) {
        // We're in SweetAlert, scope selectors to the popup
        targetContainer = $(swalPopup);
    } else {
        // We're in regular modal, use document
        targetContainer = $(document);
    }

    if (value === 'Yes') {
        targetContainer.find('#expelled_or_suspended_id').css('display','block');
    } else {
        targetContainer.find('#expelled_or_suspended_id').css('display','none');
        // Optionally clear the textarea if hidden
        targetContainer.find('#expelled_or_suspended_description_new').val('');
    }
}

function showPreviousEducationSwal(year) {
    // Clone the hidden form HTML
    var $form = $('#hidden-previous-education-form form').clone();

    // Set the year in the hidden input
    $form.find('#schoolYear').val(year);

    // Optionally, update IDs/names for file input, error, and path fields to be unique per year
    $form.find('#report_card_fileupload').attr('id', 'report_card_fileupload_' + year);
    $form.find('#report_card_path').attr('id', 'report_card_path_' + year);
    $form.find('#report_card_error').attr('id', 'report_card_error_' + year);
    $form.find('#report_card_upload_div').attr('id', 'report_card_upload_div_' + year);
    $form.find('#report_card_uploading').attr('id', 'report_card_uploading' + year);
    // REMOVE the inline onchange attribute to avoid double firing and undefined year
    $form.find('input[type="file"]').removeAttr('onchange');

    // Update the upload area's onclick to match the new ID
    $form.find('#report_card_upload_div_' + year).attr('onclick', "document.getElementById('report_card_fileupload_" +
        year + "').click();");

    Swal.fire({
        title: `<div class="swal2-header-custom" style="text-align: center;margin-bottom: 10px;">
                    <span>Add Previous Education Details (${year})</span>
                </div>`,
        html: $form.prop('outerHTML'),
        width: 800,
        showCancelButton: true,
        confirmButtonText: 'Submit',
        cancelButtonText: 'Close',
        reverseButtons: true,
        customClass: {
            popup: 'terms-confirm-popup',
            confirmButton: 'swal2-submit-btn',
            cancelButton: 'swal2-cancel-btn'
        },
        buttonsStyling: false,
        didOpen: () => {
            makeSwalDraggable();
            fill_school_details(year);

            const swalPopup = Swal.getPopup();
            const swalContainer = swalPopup.querySelector('.swal2-html-container');

            // Add CSS for validation styling
            const style = document.createElement('style');
            style.textContent = `
                .is-invalid {
                    border-color: #dc3545 !important;
                    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
                }
                .error-message {
                    color: #dc3545 !important;
                    font-size: 12px !important;
                    margin-top: 5px !important;
                    display: block !important;
                }
                .swal2-validation-message {
                    background: #f8d7da !important;
                    color: #721c24 !important;
                    border: 1px solid #f5c6cb !important;
                    border-radius: 4px !important;
                    padding: 8px 12px !important;
                    margin-top: 10px !important;
                }
            `;
            swalPopup.appendChild(style);

            $(swalPopup).find('input[type="file"]').off('change').on('change', function() {
                handleReportCardUpload(this, year);
            });

            // Bind the event handler for expelled/suspended dropdown
            $(swalPopup).find('#expelled_or_suspended_select').off('change').on('change', function() {
                show_suspended_reason(this);
            });

            // Add real-time validation on input change
            $(swalPopup).find('input, select, textarea').on('input change', function() {
                const $this = $(this);
                const value = $this.val();

                // Check if value exists and has content (safely handle different input types)
                if (value && (typeof value !== 'string' || value.trim() !== '')) {
                    $this.removeClass('is-invalid');
                    $this.closest('.form-group, .mb-3, .mb-4, .row').find('.error-message').remove();
                }
            });

            // Add file upload validation clearing
            $(swalPopup).find('input[type="file"]').on('change', function() {
                const $this = $(this);
                if (this.files && this.files.length > 0) {
                    $this.removeClass('is-invalid');
                    $this.closest('.form-group, .mb-3, .mb-4, .row').find('.error-message').remove();

                    // Also clear error for the upload container/area
                    const fileId = $this.attr('id');
                    if (fileId) {
                        const uploadDiv = $(swalPopup).find(`#${fileId.replace('fileupload', 'upload_div')}`);
                        uploadDiv.removeClass('is-invalid');
                        uploadDiv.closest('.form-group, .mb-3, .mb-4, .row').find('.error-message').remove();
                    }
                }
            });

            // Add validation clearing for hidden file path inputs (when file is uploaded successfully)
            $(swalPopup).find('input[type="hidden"]').on('change', function() {
                const $this = $(this);
                const value = $this.val();

                // If hidden input gets a value (file uploaded), clear related errors
                if (value && value.trim() !== '') {
                    const inputId = $this.attr('id');
                    if (inputId && inputId.includes('_path')) {
                        // Find related file input and clear its errors
                        const fileInputId = inputId.replace('_path', '_fileupload');
                        const fileInput = $(swalPopup).find(`#${fileInputId}`);
                        if (fileInput.length > 0) {
                            fileInput.removeClass('is-invalid');
                            fileInput.closest('.form-group, .mb-3, .mb-4, .row').find('.error-message').remove();
                        }

                        // Also clear error for the upload container
                        const uploadDivId = inputId.replace('_path', '_upload_div');
                        const uploadDiv = $(swalPopup).find(`#${uploadDivId}`);
                        if (uploadDiv.length > 0) {
                            uploadDiv.removeClass('is-invalid');
                            uploadDiv.closest('.form-group, .mb-3, .mb-4, .row').find('.error-message').remove();
                        }
                    }
                }
            });
        },
        preConfirm: () => {
            // Get the form from the popup DOM
            const swalPopup = Swal.getPopup();
            const form = swalPopup.querySelector('form');
            let isValid = true;

            // Clear previous error messages
            $(swalPopup).find('.error-message').remove();
            $(swalPopup).find('.is-invalid').removeClass('is-invalid');

            // Dynamically find all required fields in the form based on configuration
            const requiredInputs = $(swalPopup).find('input[required], select[required], textarea[required]');

            // Validate each required field found in the form
            requiredInputs.each(function() {
                const input = $(this);
                const fieldId = input.attr('id');
                const fieldName = input.attr('name');

                // Get field label from the associated label element
                let fieldLabel = 'This field';
                const labelElement = $(swalPopup).find(`label[for="${fieldId}"]`);
                if (labelElement.length > 0) {
                    // Extract text and remove asterisk if present
                    fieldLabel = labelElement.text().replace('*', '').trim();
                } else if (fieldName) {
                    // Fallback to field name if no label found
                    fieldLabel = fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                }

                // Get field value safely
                const value = input.val();

                // Check if value exists and trim it safely
                if (!value || (typeof value === 'string' && value.trim() === '')) {
                    isValid = false;

                    // Add error styling
                    input.addClass('is-invalid');

                    // Add error message
                    const errorMsg = `<div class="error-message" style="color: #dc3545; font-size: 12px; margin-top: 5px;">${fieldLabel} is required</div>`;
                    input.closest('.form-group, .mb-3, .mb-4, .row').append(errorMsg);
                }
            });

            // If validation fails, show error and prevent submission
            if (!isValid) {
                Swal.showValidationMessage('Please fill in all required fields');
                return false;
            }

            return true;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Get the form from the popup DOM
            var form = Swal.getPopup().querySelector('form');
            var formData = new FormData(form);
            submit_previous_school_details(formData,year);
        }
    });
}

function fill_school_details(year) {
    const swalPopup = Swal.getPopup();
    var afId = '<?php echo $insert_id ?>';

    if (swalPopup) {
        // We're in SweetAlert, scope selectors to the popup
        $(swalPopup).find('form').trigger("reset");
        $(swalPopup).find("#medium_of_instruction_new").val('').trigger("change");
        $(swalPopup).find('.medium_of_instruction_new').selectpicker('refresh');
        $(swalPopup).find('#prev_school_year').val('').trigger("change");
        $(swalPopup).find('.prev_school_year').selectpicker('refresh');
        $(swalPopup).find('#type_of_school').val('').trigger("change");
        $(swalPopup).find('.type_of_school').selectpicker('refresh');
        $(swalPopup).find('#yearName').html(year);
        $(swalPopup).find('#schoolYear').val(year);
        $(swalPopup).find('#schoolYearAfId').val(afId);
    } else {
        // We're in regular modal
        $('#previous_school_details_popup form').trigger("reset");
        $("#medium_of_instruction_new").val('').trigger("change");
        $('.medium_of_instruction_new').selectpicker('refresh');
        $('#prev_school_year').val('').trigger("change");
        $('.prev_school_year').selectpicker('refresh');
        $('#type_of_school').val('').trigger("change");
        $('.type_of_school').selectpicker('refresh');
        $('#yearName').html(year);
        $('#schoolYear').val(year);
        $('#schoolYearAfId').val(afId);
    }

    get_if_exit_school_detailsperyear(afId, year);
}

function updateSubjectButtonState(year) {
    var schoolNameElement = $('#displayschoolName' + year);
    var subjectButton = $('#subject_popup_' + year);

    if (schoolNameElement.length && subjectButton.length) {
        var schoolName = schoolNameElement.html().trim();

        if (schoolName && schoolName !== '' && schoolName !== '-') {
            // School details exist, enable the button
            subjectButton.prop('disabled', false);
            subjectButton.attr('title', 'Add subjects for this year');
            subjectButton.removeClass('disabled-btn');
        } else {
            // No school details, disable the button
            subjectButton.prop('disabled', true);
            subjectButton.attr('title', 'Please add school details first');
            subjectButton.addClass('disabled-btn');
        }
    }
}

function updateAllSubjectButtonStates() {
    // Update all subject buttons based on current school details
    <?php foreach ($previous_year_settings as $key => $year) { ?>
        updateSubjectButtonState('<?php echo $year ?>');
    <?php } ?>
}

function showPreviousSubjectsSwal(year) {
    // Check if school details exist before showing the popup
    var schoolNameElement = $('#displayschoolName' + year);
    var schoolName = schoolNameElement.html().trim();

    if (!schoolName || schoolName === '' || schoolName === '-') {
        Swal.fire({
            icon: 'warning',
            title: 'School Details Required',
            text: 'Please add the previous school details first before adding subjects.',
            confirmButtonText: 'OK',
            customClass: {
                confirmButton: 'swal2-submit-btn'
            },
            buttonsStyling: false
        });
        return;
    }
    // Get the subject form content via AJAX first
    var admission_setting_id = '<?php echo $admission_setting_id ?>';
    var afId = '<?php echo $insert_id ?>';
    var au_id = '<?php echo $au_id ?>';

    // Show loading popup first
    Swal.fire({
        title: 'Loading...',
        text: 'Please wait while we load the subject details.',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            makeSwalDraggable();
            Swal.showLoading();
        }
    });

    get_admission_prev_school_id(afId, year).then(apsId => {
        $.ajax({
            url: '<?php echo site_url('admission_controller/get_subject_details_from_admission_settings'); ?>',
            type: 'post',
            data: {
                'admission_setting_id': admission_setting_id,
                'afId': afId,
                'au_id': au_id,
                'pre_school_year': year,
                'apsId': apsId
            },
            success: function(data) {
                // Create the form HTML
                console.log('Raw data from controller:', data);

                // Pre-process the HTML to ensure values are properly set
                let processedData = data;

                // Extract values from the HTML using regex
                const totalMarksMatch = processedData.match(/name="total_marks_scored"[^>]*value="([^"]*)"/) || [];
                const totalMaxMarksMatch = processedData.match(/name="total_max_marks_entry"[^>]*value="([^"]*)"/) || [];
                const totalPercentageMatch = processedData.match(/name="total_percentage"[^>]*value="([^"]*)"/) || [];

                const totalMarksValue = totalMarksMatch[1] || '';
                const totalMaxMarksValue = totalMaxMarksMatch[1] || '';
                const totalPercentageValue = totalPercentageMatch[1] || '';

                console.log('Extracted values:', {
                    totalMarks: totalMarksValue,
                    totalMaxMarks: totalMaxMarksValue,
                    totalPercentage: totalPercentageValue
                });

                // Alert the extracted values for immediate verification
                if (totalMarksValue || totalMaxMarksValue || totalPercentageValue) {
                    console.log('✅ VALUES FOUND:', `Total Marks: ${totalMarksValue}, Max Marks: ${totalMaxMarksValue}, Percentage: ${totalPercentageValue}`);
                } else {
                    console.log('❌ NO VALUES EXTRACTED - Check HTML structure');
                }

                // Force the values by modifying the HTML with inline styles
                processedData = processedData.replace(
                    /(<input[^>]*name="total_marks_scored"[^>]*)/g,
                    '$1 style="color: #000 !important; background-color: #ffff99 !important; font-weight: bold !important; border: 2px solid red !important;"'
                );

                processedData = processedData.replace(
                    /(<input[^>]*name="total_max_marks_entry"[^>]*)/g,
                    '$1 style="color: #000 !important; background-color: #ffff99 !important; font-weight: bold !important; border: 2px solid red !important;"'
                );

                processedData = processedData.replace(
                    /(<input[^>]*name="total_percentage"[^>]*)/g,
                    '$1 style="color: #000 !important; background-color: #ffff99 !important; font-weight: bold !important; border: 2px solid red !important;"'
                );

                console.log('Processed data:', processedData);

                // Add aggressive CSS to force input values to be visible
                var cssStyles = `
                    <style>
                        /* Aggressive CSS to override all possible conflicts */
                        .swal2-popup input,
                        .swal2-popup input[readonly],
                        .swal2-popup .form-control,
                        .swal2-popup .form-control[readonly] {
                            color: #000 !important;
                            background-color: #fff !important;
                            opacity: 1 !important;
                            font-weight: normal !important;
                            border: 1px solid #ced4da !important;
                            font-size: 14px !important;
                            line-height: 1.5 !important;
                            padding: 6px 12px !important;
                            text-indent: 0 !important;
                            text-shadow: none !important;
                            -webkit-text-fill-color: #000 !important;
                            -webkit-opacity: 1 !important;
                        }

                        /* Specific targeting for total fields */
                        .swal2-popup #total_marks_scored,
                        .swal2-popup #total_max_marks_entry,
                        .swal2-popup #total_percentage,
                        .swal2-popup #total_grade {
                            color: #000 !important;
                            background-color: #ffff99 !important;
                            font-weight: bold !important;
                            border: 2px solid #007bff !important;
                            font-size: 16px !important;
                            -webkit-text-fill-color: #000 !important;
                        }

                        /* Override any possible hiding styles */
                        .swal2-popup input:not([type="hidden"]) {
                            display: block !important;
                            visibility: visible !important;
                            opacity: 1 !important;
                            height: auto !important;
                            width: auto !important;
                            position: relative !important;
                            z-index: 1 !important;
                        }

                        /* Force text to be visible */
                        .swal2-popup input::placeholder {
                            color: #6c757d !important;
                            opacity: 0.5 !important;
                        }
                    </style>
                `;

                var formHtml = cssStyles + `
                    <form enctype="multipart/form-data" id="school_subjects_form_swal" class="form-horizontal" data-parsley-validate method="post">
                        <input type="hidden" id="subject_schoolYear_swal" name="schooling_sub_year" value="${year}">
                        <input type="hidden" value="<?php echo $insert_id ?>" name="sub_lastId">
                        <input type="hidden" value="<?php echo $au_id ?>" name="sub_au_id">
                        <div class="subject_details_swal">
                            ${processedData}
                        </div>
                    </form>
                `;

                Swal.fire({
                    title: `<div class="swal2-header-custom" style="text-align: center;margin-bottom: 10px;">
                                <span>Add Previous School Subjects (${year})</span>
                            </div>`,
                    html: formHtml,
                    width: 800,
                    showCancelButton: true,
                    confirmButtonText: 'Add Subjects',
                    cancelButtonText: 'Close',
                    reverseButtons: true,
                    customClass: {
                        popup: 'terms-confirm-popup',
                        confirmButton: 'swal2-submit-btn',
                        cancelButton: 'swal2-cancel-btn'
                    },
                    buttonsStyling: false,
                    didOpen: () => {
                        makeSwalDraggable();

                        // Force input values to be visible with aggressive debugging
                        setTimeout(() => {
                            const popup = Swal.getPopup();
                            console.log('=== DEBUGGING SWAL INPUT VALUES ===');
                            console.log('Popup element:', popup);

                            // Debug: Check all inputs in the popup
                            const allInputs = popup.querySelectorAll('input');
                            console.log('All inputs found:', allInputs.length);

                            allInputs.forEach((input, index) => {
                                console.log(`Input ${index}:`, {
                                    id: input.id,
                                    name: input.name,
                                    value: input.value,
                                    type: input.type,
                                    readonly: input.readOnly,
                                    style: input.style.cssText
                                });
                            });

                            // Force refresh of readonly input values
                            const readonlyInputs = popup.querySelectorAll('input[readonly]');
                            console.log('Readonly inputs found:', readonlyInputs.length);

                            readonlyInputs.forEach((input, index) => {
                                const currentValue = input.value;
                                console.log(`Readonly input ${index} - ID: ${input.id}, Current value: "${currentValue}"`);

                                // Clear and reset value
                                input.value = '';
                                input.value = currentValue;

                                // Remove all existing styles and apply new ones
                                input.removeAttribute('style');
                                input.style.cssText = 'color: #000 !important; background-color: #fff !important; opacity: 1 !important; font-weight: bold !important; border: 2px solid #007bff !important;';

                                // Also try setting as attribute
                                input.setAttribute('value', currentValue);
                            });

                            // Specifically target the total fields with more aggressive approach
                            const totalFields = ['total_marks_scored', 'total_max_marks_entry', 'total_percentage', 'total_grade'];
                            totalFields.forEach(fieldId => {
                                const field = popup.querySelector(`#${fieldId}`);
                                if (field) {
                                    console.log(`=== ${fieldId} DETAILS ===`);
                                    console.log('Element:', field);
                                    console.log('Current value:', field.value);
                                    console.log('Value attribute:', field.getAttribute('value'));
                                    console.log('Computed styles:', window.getComputedStyle(field));

                                    // Try multiple approaches to set the value
                                    const originalValue = field.value || field.getAttribute('value') || '';

                                    // Method 1: Direct value assignment
                                    field.value = originalValue;

                                    // Method 2: Set as attribute
                                    field.setAttribute('value', originalValue);

                                    // Method 3: Force styles
                                    field.style.cssText = 'color: #000 !important; background-color: #ffff99 !important; opacity: 1 !important; font-weight: bold !important; border: 3px solid red !important; font-size: 16px !important;';

                                    // Method 4: Try innerHTML for debugging
                                    if (originalValue) {
                                        console.log(`Setting ${fieldId} to: ${originalValue}`);
                                    }
                                } else {
                                    console.log(`Field ${fieldId} not found!`);
                                }
                            });

                            // Additional debugging: Check the raw HTML
                            console.log('=== RAW HTML IN POPUP ===');
                            console.log(popup.innerHTML);

                        }, 100);

                        // Initialize any select pickers or other components if needed
                    },
                    preConfirm: () => {
                        // You can add validation here if needed
                        return true;
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Get the form from the popup DOM
                        var form = Swal.getPopup().querySelector('#school_subjects_form_swal');
                        var formData = new FormData(form);
                        submit_previous_school_subjects_swal(formData);
                    }
                });
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load subject details. Please try again.',
                    confirmButtonText: 'OK'
                });
            }
        });
    });
}

function fill_subject_details(year) {
    $('#total_marks_scored').val('');
    $('#total_max_marks_entry').val('');
    $('#total_grade').val('');
    $('#total_percentage').val('');

    var admission_setting_id = '<?php echo $admission_setting_id ?>';
    var afId = '<?php echo $insert_id ?>';
    var au_id = '<?php echo $au_id ?>';
    $('#subject_schoolYear').val(year);
    get_admission_prev_school_id(afId, year).then(apsId => {
        $.ajax({
            url: '<?php echo site_url('admission_controller/get_subject_details_from_admission_settings'); ?>',
            type: 'post',
            data: {
                'admission_setting_id': admission_setting_id,
                'afId': afId,
                'au_id': au_id,
                'pre_school_year': year,
                'apsId': apsId
            },
            success: function(data) {
                $('.subject_details').html(data);
            }
        });
    });
}

function get_admission_prev_school_id(afid, year) {
    return new Promise((resolve, reject) => {
        $.ajax({
            type: "POST",
            url: '<?php echo site_url('admission_controller/get_previous_school_detail_for_the_year'); ?>',
            data: {
                'afid': afid,
                'year': year
            },
            success: response => {
                res = JSON.parse(response);
                resolve(res.id)
            },
            error: error => reject(error)
        });
    });
}

function get_if_exit_school_detailsperyear(afid, year) {
    console.log('Year:', year);
    $.ajax({
        url: '<?php echo site_url('admission_controller/get_previous_school_detail_for_the_year');?>',
        type: 'post',
        data: {
            'afid': afid,
            'year': year
        },
        success: function(data) {
            var schoolDetails = $.parseJSON(data);
            console.log('Parsed school details:', schoolDetails);

            const swalPopup = Swal.getPopup();

            // Clear the admission_prev_school_primary_id field (scope to SweetAlert if it exists)
            if (swalPopup) {
                $(swalPopup).find('#admission_prev_school_primary_id').val('');
            } else {
                $('#admission_prev_school_primary_id').val('');
            }

            if (schoolDetails != null) {
                $('#detail_popup_'+year).html('<span class="plus-icon">✓</span> Edit Details')
                $("#span_fill" + year).hide();
                $("#span_edit" + year).show();

                // Set the admission_prev_school_primary_id field (scope to SweetAlert if it exists)
                if (swalPopup) {
                    $(swalPopup).find('#admission_prev_school_primary_id').val(schoolDetails.id);
                } else {
                    $('#admission_prev_school_primary_id').val(schoolDetails.id);
                }
                $('#displayschoolName' + year).html(schoolDetails.school_name);
                $('#displayclassName' + year).html(schoolDetails.class);
                $('#displayYear' + year).html(schoolDetails.year);
                // $('#schooling_school_new').val(schoolDetails.school_name);
                $('#swal-previous-education-form #schooling_school_new').val(schoolDetails.school_name);
                $('#swal-previous-education-form #school_address_new').val(schoolDetails.school_address);
                $('#swal-previous-education-form #schooling_class_new').val(schoolDetails.class);
                $('#swal-previous-education-form #registration_no_new').val(schoolDetails.registration_no);
                $('#swal-previous-education-form #medium_of_instruction_new').val(schoolDetails
                    .medium_of_instruction);
                $('#swal-previous-education-form #transfer_reason_id_new').val(schoolDetails.transfer_reason)
                $(swalPopup).find("#type_of_school").val(schoolDetails.type_of_school);
                $(swalPopup).find("#medium_of_instruction").val(schoolDetails.medium_of_instruction);
                $(swalPopup).find("#schooling_board_new").val(schoolDetails.board);
                $(swalPopup).find('#expelled_or_suspended_description_new').val(schoolDetails
                .expelled_or_suspended_description);
                $(swalPopup).find('#prev_school_year').val(schoolDetails.year)

                if (schoolDetails.report_card != null && schoolDetails.report_card != '') {
                    var cardHtml = `
                        <div class="uploaded-file-card d-flex align-items-center justify-content-between p-3 rounded" style="background:#fff; border-radius:6px;">
                            <div class="d-flex align-items-center flex-grow-1" style="min-width:0;">
                                <div class="file-icon d-flex align-items-center justify-content-center me-3" style="width:48px; height:48px; background:#FF2D2D; border-radius:10px;">
                                    <span style="color:#fff; font-weight:700; font-size:1.1em;">PDF</span>
                                </div>
                                <div class="file-name" style="font-weight:600; color:#181028; font-size:1em; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">
                                    Report Card
                                </div>
                            </div>
                            <div class="d-flex align-items-center ms-3" style="gap:16px;">
                                <button type="button" class="btn btn-link text-danger p-0 ms-2 d-flex align-items-center" onclick="removeUploadedFile('${year}')">
                                    <span style="display: flex; align-items: center; width: 24px; height: 24px;">
                                        <?php $this->load->view('svg_icons/delete_icon.svg') ?>
                                    </span>
                                </button>
                            </div>
                        </div>
                    `;
                    $(swalPopup).find('#report_card_upload_div_' + year).html(cardHtml);
                }

                // Set expelled/suspended dropdown value and trigger change event
                $(swalPopup).find("#expelled_or_suspended_select").val(schoolDetails.expelled_or_suspended).trigger('change');

                // Set school rating dropdown value
                $(swalPopup).find("#previous_school_ratings").val(schoolDetails.previous_school_ratings);

                // Set transfer reason and expelled/suspended description
                $(swalPopup).find('#transfer_reason_id_new').val(schoolDetails.transfer_reason);
                $(swalPopup).find('#expelled_or_suspended_description_new').val(schoolDetails.expelled_or_suspended_description);
            } else {
                $('#attachmentReport').hide();
                $("#span_edit" + year).hide();
                $("#span_fill" + year).show();
                $('#expelled_or_suspended-0').removeAttr('checked');
                $('#expelled_or_suspended-1').attr('checked', 'checked');
                $('#expelled_or_suspended_id').hide();
                $('input[name="previous_school_ratings"]').prop('checked', false);
            }

            // Update the subject button state based on school details
            updateSubjectButtonState(year);
        },
        error: function(xhr, status, error) {
            console.log('=== AJAX ERROR ===');
            console.log('Status:', status);
            console.log('Error:', error);
            console.log('Response Text:', xhr.responseText);
            console.log('Status Code:', xhr.status);
        }
    });

}

function submit_previous_school_details(formData,year) {
    var afId = '<?php echo $insert_id ?>';
    formData.append('lastId', afId);

    // Get the value of schoolYear from the formData
    // let schoolYear = $('#hidden-previous-education-form #schoolYear').val();
    $.ajax({
        url: '<?php echo site_url('admission_controller/update_previous_school_details_year_wise'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        cache: false,
        success: function(data) {
            Swal.fire({
                title: 'Success',
                text: 'Details added successfully!',
                icon: 'success',
                didOpen: () => {
                    document.querySelector('.swal2-title').style.textAlign = 'center';
                    document.querySelector('.swal2-title').style.backgroundColor = 'transparent';
                    document.querySelector('.swal2-html-container').style.textAlign = 'center';
                }
            });

            // Now call with correct year
            get_if_exit_school_detailsperyear(afId, year);
        }
    });
}

function submit_previous_school_subjects() {

    var form = $('#school_subjects_form')[0];
    var formData = new FormData(form);
    $.ajax({
        url: '<?php echo site_url('admission_controller/update_previous_school_subjects_year_wise'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        cache: false,
        success: function(data) {
            // console.log(data);
            $("#previous_school_subject_popup").modal('hide');
        }
    });
}

function submit_previous_school_subjects_swal(formData) {
    // Show loading
    Swal.fire({
        title: 'Saving...',
        text: 'Please wait while we save the subject details.',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: '<?php echo site_url('admission_controller/update_previous_school_subjects_year_wise'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        cache: false,
        success: function(data) {
            // console.log(data);
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Subject details have been saved successfully.',
                confirmButtonText: 'OK',
                customClass: {
                    confirmButton: 'swal2-submit-btn'
                },
                buttonsStyling: false
            });
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to save subject details. Please try again.',
                confirmButtonText: 'OK'
            });
        }
    });
}

function max_marks_total(m) {
    var maxmarks = $('#maxMarks_' + m).val();
    if (maxmarks == '') {
        $('#maxMarks_' + m).val('0');
    }
    var tmaxMarks = 0;
    $('.maxMarks').each(function() {
        tmaxMarks += parseFloat($(this).val());
    });

    $('#total_max_marks_entry').val(tmaxMarks);
    var total_max_marks_entry = $('#total_max_marks_entry').val();
    var total_marks_scored = $('#total_marks_scored').val();
    var percentage = parseFloat(total_marks_scored) / parseFloat(total_max_marks_entry) * 100;
    $('#total_percentage').val(percentage.toFixed(2));
}

function max_marks_scored_total(m) {
    var maxMarkScored = $('#maxMarkScored_' + m).val();
    if (maxMarkScored == '') {
        $('#maxMarkScored_' + m).val('0');
    }
    var tmaxMakrsScored = 0;
    $('.maxMakrsScored').each(function() {
        tmaxMakrsScored += parseFloat($(this).val());
    });
    $('#total_marks_scored').val(tmaxMakrsScored);

    var total_max_marks_entry = $('#total_max_marks_entry').val();
    var total_marks_scored = $('#total_marks_scored').val();
    var percentage = parseFloat(total_marks_scored) / parseFloat(total_max_marks_entry) * 100;
    $('#total_percentage').val(percentage.toFixed(2));
}

function check_is_value_not_empty(e, sl) {
    if (e.value != '') {
        $('#maxMarks_' + sl).attr({
            "max": 125,
            "min": 100
        });

        $('#maxMarkScored_' + sl).attr({
            "max": 125,
            "min": 0
        });
    } else {
        $('#maxMarks_' + sl).attr({
            "max": 0,
            "min": 0
        });

        $('#maxMarkScored_' + sl).attr({
            "max": 0,
            "min": 0
        });
    }
}


$("#btn_go").on('click', function() {
    validateUserDetails().done(function(data) {
        if (data == "someValue")
            return "whatever you want";
    });
});

function documentSubmit_new() {
    var document_input_version = '<?php echo $config_val['document_input_version'] ?>';
    if (document_input_version == 'V2') {
        check_document_uploaded()
    } else if (document_input_version == 'V1') {
        check_previous_school_details()
    }
}

function check_document_uploaded() {
    var af_id = '<?php echo $final_preview->id ?>';
    var adm_setting_id = '<?php echo $final_preview->admission_setting_id ?>';
    $.ajax({
        url: '<?php echo site_url('admission_controller/check_document_uploaded'); ?>',
        type: 'post',
        data: {
            'af_id': af_id,
            'adm_setting_id': adm_setting_id
        },
        success: function(data) {
            console.log(data);
            var resData = data.trim();
            if (resData == 0) {
                alert('Upload the documents');
                return false;
            } else {
                check_previous_school_details()
            }
        }
    });
}

function check_previous_school_details() {
    var required = '<?php echo $required_fields['year']['required'] ?>';
    if (required == 'required' && gYearDispaly != null) {
        var resData = [];
        for (var k in schoolYears) {
            var valueCheck = $('#displayschoolName' + schoolYears[k]).html();
            resData.push(valueCheck);
        }
        var found = 0;
        $.each(resData, function(index, value) {
            if (value == '') {
                found = 0;
                return false;
            } else {
                found = 1;
            }
        });
        if (found) {
            $('.removeAttrFileUpload').removeAttr('required');

            // var url = '<?php // echo site_url('admissions/preview') ?>';
            // $('#document-form').attr('action', url);
            // $('#document-form').submit();
            goto_preview_page('document-form');
            return true;
        } else {
            alert("Enter previous details, then you can 'Save and Preview'.");
        }
    } else {
        $('.removeAttrFileUpload').removeAttr('required');
        $('#report_card_fileupload').removeAttr('required');
        $('#schooling_school_new').removeAttr('required');
        $('#school_address_new').removeAttr('required');
        $('#medium_of_instruction_new').removeAttr('required');
        $('#schooling_class_new').removeAttr('required');
        $('#registration_no').removeAttr('required');
        $('#schooling_board_new').removeAttr('required');
        // var url = '<?php  //echo site_url('admissions/preview') ?>';
        // $('#document-form').attr('action', url);
        // $('#document-form').submit();
        goto_preview_page('document-form');
        return true;
    }
}

function handleReportCardUpload(input, year) {
    const swalPopup = Swal.getPopup();
    const isInPopup = swalPopup !== null;
    const container = isInPopup ? $(swalPopup) : $(document);

    if (input.files && input.files[0]) {
        var file = input.files[0];
        var fileSizeMB = file.size / 1024 / 1024;
        var fileName = file.name;
        var allowedTypes = [
            'application/pdf',
            'image/jpeg',
            'image/jpg',
            'image/png'
        ];

        // Determine element IDs based on context
        var errorElementId = isInPopup ? '#report_card_error_' + year : '#report_card_error';
        var uploadDivId = isInPopup ? '#report_card_upload_div_' + year : '#report_card_upload_div';
        var pathElementId = isInPopup ? '#report_card_path_' + year : '#report_card_path';

        // Validate file type
        if (!allowedTypes.includes(file.type)) {
            container.find(errorElementId).html('Only PDF, JPG, JPEG, and PNG files are allowed.');
            input.value = '';
            return;
        }
        container.find(errorElementId).html('');

        // Validate file size
        if (fileSizeMB > 2) {
            container.find(errorElementId).html('File size must be 2MB or less.');
            input.value = '';
            return;
        }
        container.find(errorElementId).html('');

        // Show loading state
        container.find(uploadDivId).html(`
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="mt-2">Uploading ${fileName}...</div>
            </div>
        `);

        // Upload file and update UI on success
        saveFileToStorage_report_card(file, year, isInPopup).then(() => {
            // File uploaded successfully, show file preview
            var isPDF = file.type === 'application/pdf';
            var iconColor = isPDF ? '#FF2D2D' : '#28a745';
            var iconText = isPDF ? 'PDF' : 'IMG';

            // Different remove function based on context
            var removeFunction = isInPopup ? `removeUploadedFile('${year}')` : `removeUploadedFileMain()`;

            var cardHtml = `
                <div class="uploaded-file-card d-flex align-items-center justify-content-between p-3 rounded" style="background:#fff; border-radius:6px; min-height:70px;">
                    <div class="d-flex align-items-center flex-grow-1" style="min-width:0; flex:1;">
                        <div class="file-icon d-flex align-items-center justify-content-center me-3" style="width:50px; height:50px; min-width:50px; background:${iconColor}; border-radius:10px; flex-shrink:0;">
                            <span style="color:#fff; font-weight:700; font-size:1.1em; line-height:1;">${iconText}</span>
                        </div>
                        <div class="file-info flex-grow-1" style="min-width:0; overflow:hidden;">
                            <div class="file-name" style="font-weight:600; color:#181028; font-size:1em; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; margin-bottom:2px;">
                                ${fileName}
                            </div>
                            <div class="file-size text-muted d-block d-sm-none" style="font-size:0.9em;">${fileSizeMB.toFixed(1)} mb</div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center ms-3" style="gap:12px; flex-shrink:0;">
                        <div class="file-size text-muted d-none d-sm-block" style="font-size: 1em; min-width:60px; text-align:right;">${fileSizeMB.toFixed(1)} mb</div>
                        <button type="button" class="btn btn-link text-danger p-0 d-flex align-items-center" onclick="${removeFunction}" style="min-width:24px; min-height:24px;">
                            <span style="display: flex; align-items: center; width: 24px; height: 24px;">
                                <?php $this->load->view('svg_icons/delete_icon.svg') ?>
                            </span>
                        </button>
                    </div>
                </div>
            `;
            container.find(uploadDivId).html(cardHtml);
            container.find(uploadDivId).removeAttr('onclick');
        }).catch((error) => {
            // File upload failed, show error and restore upload area
            container.find(errorElementId).html('File upload failed. Please try again.');
            if (isInPopup) {
                restoreUploadArea(year);
            } else {
                restoreUploadAreaMain();
            }
        });
    }
}

function removeUploadedFile(year) {
    const swalPopup = Swal.getPopup();
    var html = `
        <div id="report_card_upload_div_${year}" class="border border-dashed rounded p-4 text-center position-relative"
            style="cursor: pointer; background-color: #fafafa;">
            <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
            </div>
            <div class="d-flex flex-column align-items-center mt-4">
                <i class="bi bi-upload" style="font-size: 24px;"></i>
                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                <small class="text-muted">JPG, JPEG, PNG or PDF (max. 2MB)</small>
            </div>
            <input type="file" id="report_card_fileupload_${year}"
                name="report_card"
                style="display:none;"
                accept="image/jpeg, application/pdf">
            <input type="hidden" name="report_card_path" id="report_card_path_${year}">
            <span style="font-size:12px;color:red" class="text-danger d-block mt-2" id="report_card_error_${year}"></span>
        </div>
    `;
    // Replace the upload area in the popup
    $(swalPopup).find(`#report_card_upload_div_${year}`).replaceWith(html);

    // Re-bind click event for the new div
    $(swalPopup).find(`#report_card_upload_div_${year}`).off('click').on('click', function(e) {
        if (e.target.tagName.toLowerCase() !== 'input') {
            $(swalPopup).find(`#report_card_fileupload_${year}`).trigger('click');
        }
    });

    // Re-bind file input change event
    $(swalPopup).find(`#report_card_fileupload_${year}`).off('change').on('change', function() {
        handleReportCardUpload(this, year);
    });
}

function saveFileToStorage_report_card(file, year, isInPopup = true) {

    return new Promise((resolve, reject) => {
        var fileInputId = isInPopup ? '#report_card_fileupload_' + year : '#report_card_fileupload';
        $(fileInputId).attr('disabled', 'disabled');

        $.ajax({
            url: '<?php echo site_url("S3_admission_controller/getSignedUrl"); ?>',
            type: 'post',
            data: {
                'filename': file.name,
                'file_type': file.type,
                'folder': 'profile'
            },
            success: function(response) {
                // console.log('Response: ',response)
                response = JSON.parse(response);
                var path = response.path;
                var signedUrl = response.signedUrl;

                $.ajax({
                    url: signedUrl,
                    type: 'PUT',
                    headers: {
                        "Content-Type": file.type,
                        "x-amz-acl": "public-read"
                    },
                    processData: false,
                    data: file,
                    xhr: function() {
                        var xhr = $.ajaxSettings.xhr();
                        xhr.upload.onprogress = function(e) {
                            // For uploads
                            if (e.lengthComputable) {}
                        };
                        return xhr;
                    },
                    success: function(response) {
                        var pathElementId = isInPopup ? '#report_card_path_' + year : '#report_card_path';
                        var fileInputId = isInPopup ? '#report_card_fileupload_' + year : '#report_card_fileupload';

                        $(pathElementId).val(path);
                        $(fileInputId).removeAttr('disabled');
                        resolve(path); // Resolve the promise on success
                    },
                    error: function(err) {
                        var fileInputId = isInPopup ? '#report_card_fileupload_' + year : '#report_card_fileupload';
                        $(fileInputId).removeAttr('disabled');
                        reject(err); // Reject the promise on error
                    }
                });
            },
            error: function(err) {
                var fileInputId = isInPopup ? '#report_card_fileupload_' + year : '#report_card_fileupload';
                $(fileInputId).removeAttr('disabled');
                reject(err); // Reject the promise on error
            }
        });
    });
}

function restoreUploadArea(year) {
    const swalPopup = Swal.getPopup();
    var html = `
        <div id="report_card_upload_div_${year}" class="border border-dashed rounded p-4 text-center position-relative"
            style="cursor: pointer; background-color: #fafafa;" onclick="document.getElementById('report_card_fileupload_${year}').click();">
            <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
            </div>
            <div class="d-flex flex-column align-items-center mt-4">
                <i class="bi bi-upload" style="font-size: 24px;"></i>
                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                <small class="text-muted">JPG, JPEG, PNG or PDF (max. 2MB)</small>
            </div>
            <input type="file" id="report_card_fileupload_${year}"
                name="report_card"
                style="display:none;"
                accept="image/jpeg, application/pdf">
            <input type="hidden" name="report_card_path" id="report_card_path_${year}">
            <span style="font-size:12px;color:red" class="text-danger d-block mt-2" id="report_card_error_${year}"></span>
        </div>
    `;
    $(swalPopup).find('#report_card_upload_div_' + year).replaceWith(html);

    // Re-bind file input change event
    $(swalPopup).find(`#report_card_fileupload_${year}`).off('change').on('change', function() {
        handleReportCardUpload(this, year);
    });
}

// Function to remove uploaded file from main form (non-popup)
function removeUploadedFileMain() {
    var html = `
        <div id="report_card_upload_div" class="border border-dashed rounded p-4 text-center position-relative"
            style="cursor: pointer; background-color: #fafafa;" onclick="document.getElementById('report_card_fileupload').click();">
            <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
            </div>
            <div class="d-flex flex-column align-items-center mt-4">
                <i class="bi bi-upload" style="font-size: 24px;"></i>
                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                <small class="text-muted">JPG, JPEG, PNG or PDF (max. 2MB)</small>
            </div>
            <input type="file" id="report_card_fileupload" name="report_card" style="display:none;"
                accept="image/jpeg, application/pdf" onchange="handleReportCardUpload(this)">
            <input type="hidden" name="report_card_path" id="report_card_path">
            <span style="font-size:12px;color:red" class="text-danger d-block mt-2" id="report_card_error"></span>
        </div>
    `;
    $('#report_card_upload_div').replaceWith(html);
}

// Function to restore upload area for main form (non-popup)
function restoreUploadAreaMain() {
    var html = `
        <div id="report_card_upload_div" class="border border-dashed rounded p-4 text-center position-relative"
            style="cursor: pointer; background-color: #fafafa;" onclick="document.getElementById('report_card_fileupload').click();">
            <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
            </div>
            <div class="d-flex flex-column align-items-center mt-4">
                <i class="bi bi-upload" style="font-size: 24px;"></i>
                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                <small class="text-muted">JPG, JPEG, PNG or PDF (max. 2MB)</small>
            </div>
            <input type="file" id="report_card_fileupload" name="report_card" style="display:none;"
                accept="image/jpeg, application/pdf" onchange="handleReportCardUpload(this)">
            <input type="hidden" name="report_card_path" id="report_card_path">
            <span style="font-size:12px;color:red" class="text-danger d-block mt-2" id="report_card_error"></span>
        </div>
    `;
    $('#report_card_upload_div').replaceWith(html);
}

</script>

<style>
.uploaded-file-card {
    background: #fff;
    border-radius: 12px;
}

.file-icon.pdf-icon {
    width: 48px;
    height: 48px;
    background: #FF1A1A;
    color: #fff;
    font-weight: bold;
    font-size: 1.1em;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-name {
    font-weight: 600;
    color: #181028;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 250px;
}

.file-size {
    color: #b0b0b0;
    font-size: 1em;
}

.btn-link.text-danger {
    color: #FF1A1A !important;
}
@media (max-width: 576px) {
  .add-details-btn {
    white-space: nowrap;
    font-size: 14px; /* Optional: reduce font size for mobile */
    padding: 4px 8px; /* Optional: adjust padding for mobile */
  }
}
@media (max-width: 576px) {
  .modern-table td, .modern-table th {
    white-space: nowrap;
  }
}
@media (max-width: 576px) {
  .add-details-btn {
    white-space: nowrap;
    font-size: 14px;
    padding: 4px 8px;
  }
}
</style>

<script>
$(document).ready(function() {
    // Initialize all subject button states when page loads
    updateAllSubjectButtonStates();
});
</script>

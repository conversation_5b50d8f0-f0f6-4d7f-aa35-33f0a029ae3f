<input type="hidden" id="adId" name="ad_id">
<?php $father_name_label = "Father's First Name ";
if($config_val['father_first_name_label'])  { $father_name_label = $config_val['father_first_name_label']; } ?>
<!-- <h4 style="margin-bottom: 14px; background-color: #f5f5f5; height: 50px; width: 100%; padding: 15px 5px;" class=""><span style="padding: auto;">Parent Details</span></h4> -->

<!-- Parent Details Row Container -->
<div class="row">
    <!-- Father Details Column -->
    <div class="col-12 col-lg-6" id="father_details_tab">
    <?php $freez_fname = '' ; if(in_array('f_name',$freez_primary_fields) && $freez_parent_details) $freez_fname = 'readonly'; ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_name"><?= $father_name_label; ?> <font color="red">&nbsp;*</font></label>
        <input required="" placeholder="Enter First Name" id="f_name"
            <?php if(!empty($final_preview->f_name)) echo 'value="'.$final_preview->f_name.'"' ?> name="f_name"
            type="text" class="form-control input-md"
            data-parsley-error-message="Cannot be empty; only alphabets and spaces are allowed."
            data-parsley-pattern="^[a-zA-Z. ]+$" data-parsley-minlength="2" <?= $freez_fname ?>>
        <?php if($this->settings->getSetting('enquiry_help_block_parent_name')) { ?>
        <span class="help-block"><?php echo $this->settings->getSetting('enquiry_help_block_parent_name')?></span>
        <?php } else{ ?>
        <span class="help-block">As per official document</span>
        <?php }?>
    </div>
    <?php if(admission_is_enabled($disabled_fields, 'f_last_name')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_last_name">Father's Last Name
            <?php if($required_fields['f_last_name']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Enter Last Name"
            <?php if(!empty($final_preview->f_last_name)) echo 'value="'.$final_preview->f_last_name.'"' ?>
            id="father_lastname" name="f_last_name" type="text"
            <?php echo $required_fields['f_last_name']['required'] ?> class="form-control input-md"
            data-parsley-error-message="Cannot be empty, only alphabets" data-parsley-pattern="^[a-zA-Z. ]+$">
        <?php if($this->settings->getSetting('enquiry_help_block_parent_name')) { ?>
        <span class="help-block"><?php echo $this->settings->getSetting('enquiry_help_block_parent_name')?></span>
        <?php } else{ ?>
        <span class="help-block">As per official document</span>
        <?php }?>
    </div>
    <?php endif ?>

     <?php if(admission_is_enabled($disabled_fields, 'f_dob')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_dob">
            Date of Birth
            <?php if($required_fields['f_dob']['required']=='required') echo'<font color="red">*</font>' ?>
        </label>

        <div class="position-relative p-0">
            <input type="text" class="form-control f_datepick pe-5" autocomplete="off"
                <?php if(!empty($final_preview->f_dob) && $final_preview->f_dob != '00-00-0000') echo 'value="'.date('d-m-Y', strtotime($final_preview->f_dob)) .'"' ?>
                name="f_dob" placeholder="Enter Date Of birth"
                <?php echo $required_fields['f_dob']['required'] ?>
                data-parsley-error-message="Please enter father's date of birth in DD-MM-YYYY format"
                data-parsley-pattern="^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$">

            <!-- Calendar icon -->
            <div style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); z-index: 10;padding-right:20px">
                <?php $this->load->view('svg_icons/calendar_icon.svg'); ?>
            </div>
        </div>

        <label class="control-label" id="age_cal_f"></label>
    </div>
     <?php endif ?>

    <?php $freez_f_mobile_number = '' ; if(in_array('f_mobile_no',$freez_primary_fields) && $freez_parent_details) $freez_f_mobile_number = 'readonly'; ?>
    <?php if(admission_is_enabled($disabled_fields, 'f_mobile_no')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css">Mobile Number
            <?php if($required_fields['f_mobile_no']['required']=='required') echo'<font color="red">*</font>' ?>
        </label>
        <div class="d-flex gap-2 p-0">
            <div class="w-25" style="position: relative;">
                <?php 
                    $array = array();
                    foreach ($this->config->item('country_codes') as $key => $code) {
                        $array[$code] =  $code;
                    }
                    echo form_dropdown("f_country_code", $array, set_value("f_country_code",$final_preview->f_country_code), "id='f_country_code' ".$required_fields['f_country_code']['required']." class='form-control' data-parsley-error-message='Please select country code'");
                ?>
                <div style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                    <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                </div>
            </div>
            <div class="w-75">
                <input type="text" data-parsley-error-message="Please enter a valid mobile number (8-20 digits)"
                    data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]" placeholder="Enter Mobile Number"
                    <?php if(!empty($final_preview->f_mobile_no)) echo 'value="'.$final_preview->f_mobile_no.'"' ?>
                    id="f_mobile_no" data-type="father" name="f_mobile_no" class="form-control input-md"
                    <?= $freez_f_mobile_number ?> <?php echo $required_fields['f_mobile_no']['required'] ?>>
            </div>
        </div>
    </div>
    <?php endif ?>

    <?php $freez_f_email= '' ; if(in_array('f_email_id',$freez_primary_fields) && $freez_parent_details) $freez_f_email = 'readonly'; ?>
    <?php if(admission_is_enabled($disabled_fields, 'f_email_id')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_email">Father's Email Id
            <?php if($required_fields['f_email_id']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="email" <?php echo $required_fields['f_email_id']['required'] ?>
            data-parsley-error-message="Please enter a valid email address for father" placeholder="Enter Valid Email Id" id="f_email"
            <?php if(!empty($final_preview->f_email_id)) echo 'value="'.$final_preview->f_email_id.'"' ?>
            data-type="f_email" name="f_email" class="form-control input-md" <?= $freez_f_email ?>>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'f_nationality')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_nationality">Father's Nationality
            <?php if($required_fields['f_nationality']['required']=='required') echo'<font color="red">*</font>' ?>
        </label>
        <div class="p-0" style="position: relative;">
        <?php if(!empty($final_preview->f_nationality)){
                $f_nationality = $final_preview->f_nationality;
            }else{
                $f_nationality = '';
            } ?>
            <?php 
                $array = array();
                foreach ($this->config->item('nationality') as $key => $nation) {
                    $array[$nation] =  $nation;
                }
                echo form_dropdown("f_nationality", $array, set_value("f_nationality",$f_nationality), "id='f_nationality' ".$required_fields['f_nationality']['required']." class='form-control' onchange='add_required_f_aadhar()' data-parsley-error-message='Please select father\\'s nationality'");
            ?>
            <div style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'father_aadhar')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="father_aadhar">Father's Aadhar Card No
            <?php if($required_fields['father_aadhar']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" <?php echo $required_fields['father_aadhar']['required'] ?>
            data-parsley-error-message="Please enter a valid 12-digit Aadhar number" data-parsley-type="digits"
            data-parsley-length="[12, 12]" placeholder="Enter Aadhar Number"
            <?php if(!empty($final_preview->father_aadhar)) echo 'value="'.$final_preview->father_aadhar.'"' ?>
            id="father_aadhar" name="father_aadhar" class="form-control input-md">
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'father_religion')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="father_religion">Father Religion
            <?php if($required_fields['father_religion']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <?php if(!empty($final_preview->father_religion)){
                $relig = $final_preview->father_religion;
            }else{
                $relig = '';
            } 
            ?>
        <div class="p-0" style="position: relative;">
        <?php 
            $array = array();
            $array['-'] =  'Select Religion';
            foreach ($this->config->item('religions') as $key => $religion) {
                $array[$religion] =  $religion;
            }
            echo form_dropdown("father_religion", $array, set_value("father_religion", $relig), "id='father_religion' ".$required_fields['father_religion']['required']." class='form-control' data-parsley-error-message='Please select father\\'s religion'");
            ?>
            <div style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'father_caste')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="father_caste">Father Caste
            <?php if($required_fields['father_caste']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" <?php echo $required_fields['father_caste']['required'] ?> placeholder="Enter Father Caste"
            <?php if(!empty($final_preview->father_caste)) echo 'value="'.$final_preview->father_caste.'"' ?>
            id="father_caste" name="father_caste" class="form-control input-md"
            data-parsley-error-message="Please enter father's caste (only alphabets and spaces allowed)"
            data-parsley-pattern="^[a-zA-Z ]+$" data-parsley-minlength="2">
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'father_mother_tongue')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="mother_tongue">Father's Mother Tongue
            <?php if($required_fields['father_mother_tongue']['required']=='required') echo'<font color="red">*</font>' ?></label>
                <?php if(!empty($final_preview->father_mother_tongue)){
                        $father_tongue = $final_preview->father_mother_tongue;
                    }else{
                        $father_tongue = '';
                    } ?>
            <div class="p-0" style="position: relative;">
                <?php 
                $lang = $this->config->item('languages');
                $array = array('' => 'Select Mother Tongue');
                foreach ($lang as $key => $languages) {
                    $array[$languages] =  $languages;
                }

                echo form_dropdown("father_mother_tongue", $array, set_value("father_mother_tongue", $father_tongue), "id='father_mother_tongue' ".$required_fields['father_mother_tongue']['required']."  class='form-control' data-parsley-error-message='Please select mother tongue'");
            ?>
            <div style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>
    </div>
    <?php endif ?>
    <div id="f_mother_tongue_name" class="row mb-4" style="display: none;">
        <label class="form-label parent_css" for="f_tongue_others">Others</label>
        <div class="col-md-8">
            <input placeholder="Enter Please Specify"
                <?php if(!empty($final_preview->father_mother_tongue_other)) echo 'value="'.$final_preview->father_mother_tongue_other.'"' ?>
                id="f_mother_tongue_other" name="father_mother_tongue_other" type="text" class="form-control input-md"
                data-parsley-error-message="Cannot be empty" data-parsley-pattern="^[a-zA-Z. ]+$"
                data-parsley-minlength="2">
            <span class="help-block">Please Specify</span>
        </div>
    </div>

    <?php if(admission_is_enabled($disabled_fields, 's_present_addr') && admission_is_enabled($disabled_fields, 'f_addr')) : ?>
    <div class="row mb-4" style="display: block;">
        <div class="col-md-8 p-0">
            <label for="student_copy_address" class="mobile-checkbox-label" style="cursor: pointer; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: flex; align-items: center; padding: 15px 10px; margin: 0; border-radius: 8px; transition: background-color 0.2s ease; -webkit-tap-highlight-color: rgba(0,0,0,0.1); position: relative;">
                <input type="checkbox" id="student_copy_address" value="" style="margin-right: 12px; transform: scale(1.5); cursor: pointer; min-width: 20px; min-height: 20px; z-index: 10; position: relative;">
                <span style="margin-left: 8px; font-size: 16px; line-height: 1.4; pointer-events: none;">Same As Student Present Address</span>
            </label>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'f_addr')) : ?>
    <!-- Home Address -->
    <div class="row mb-3">
        <label class="form-label parent_css" for="f_addr">
            Home Address
            <?php if($required_fields['f_addr']['required']=='required') echo'<font color="red">*</font>' ?>
        </label>
        <textarea class="form-control" id="f_addr" name="f_addr" placeholder="Enter Address"
            <?= $required_fields['f_addr']['required'] ?>><?php if(!empty($final_preview->f_addr)) echo $final_preview->f_addr ?></textarea>
    </div>

    <!-- Area + District -->
    <div class="row">
        <div class="col-12 col-md-6 p-0 p-md-3 ps-md-0 mb-3 mb-md-0">
            <input type="text" id="f_area" name="f_area" class="form-control" placeholder="Enter Area"
                <?= $required_fields['f_area']['required'] ?>
                <?php if(!empty($final_preview->f_area)) echo 'value="'.$final_preview->f_area.'"' ?>
                data-parsley-error-message="Please enter a valid area name (minimum 2 characters)"
                data-parsley-minlength="2">
        </div>

        <div class="col-12 col-md-6 p-0 p-md-3 mb-3 pe-md-0">
            <input type="text" id="f_district" name="f_district" class="form-control" placeholder="Enter District"
                <?= $required_fields['f_district']['required'] ?>
                <?php if(!empty($final_preview->f_district)) echo 'value="'.$final_preview->f_district.'"' ?>
                data-parsley-error-message="Please enter a valid district name (minimum 2 characters)"
                data-parsley-minlength="2">
        </div>
    </div>

    <!-- Country + State + Pincode -->
    <div class="row mb-4">
        <!-- Country -->
        <div class="col-12 col-md-4 p-0 p-md-3 ps-md-0 mb-3 mb-md-0 position-relative">
            <?php 
            $array = array(''=>'Country');
            foreach ($this->config->item('country') as $key => $nation) {
                $array[$nation] =  $nation;
            }
            echo form_dropdown("f_county", $array, set_value("f_county",$final_preview->f_county), "id='f_county' ".$required_fields['f_county']['required']." class='form-control'");
            ?>
            <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>

        <!-- State Text Input -->
        <div class="col-12 col-md-4 p-0 p-md-3 mb-3 mb-md-0" id="f_state_text">
            <input type="text" id="f_state" name="f_state" class="form-control" placeholder="Enter State"
                <?php if(!empty($final_preview->f_state)) echo 'value="'.$final_preview->f_state.'"' ?>
                data-parsley-error-message="Please enter a valid state name (minimum 2 characters)"
                data-parsley-minlength="2">
        </div>

        <!-- State Dropdown -->
        <div class="col-12 col-md-4 p-0 p-md-3 mb-3 mb-md-0 position-relative" id="f_state_dropdown_div" style="display: none;">
            <select class="form-control" name="f_state_dropdown" id="f_state_dropdown"
                data-parsley-error-message="Please select father's state">
                <option value="">Select State</option>
                <?php foreach ($this->config->item('states') as $key => $state) {
                    $selected = ($final_preview->f_state == $state) ? 'selected' : ''; ?>
                    <option value="<?= $state ?>" <?= $selected ?>><?= $state ?></option>
                <?php } ?>
            </select>
            <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>

        <!-- Pincode -->
        <div class="col-12 col-md-4 p-0 p-md-3 pe-md-0">
            <input type="text" id="f_pincode" name="f_pincode" class="form-control" placeholder="Pincode"
                <?= $required_fields['f_pincode']['required'] ?>
                <?php if(!empty($final_preview->f_pincode)) echo 'value="'.$final_preview->f_pincode.'"' ?>
                data-parsley-type="digits"
                data-parsley-length="[3, 10]"
                data-parsley-error-message="Enter a valid pin-code">
        </div>
    </div>
    <?php endif ?>


    <?php if(admission_is_enabled($disabled_fields, 'f_qualification')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_qualification">Qualification
            <?php if($required_fields['f_qualification']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <?php if($need_qualification_dropdown) { ?>
        <div style="position: relative;" class="p-0">
        <select <?php echo $required_fields['f_qualification']['required'] ?> id="f_qualification"
            data-type="f_qualification" name="f_qualification" class="form-control"
            data-parsley-error-message="Please select father's qualification">
            <option value="">Select Qualification</option>
            <option <?php if($final_preview->f_qualification == 'Diploma') echo 'selected' ?> value="Diploma">Diploma
            </option>
            <option <?php if($final_preview->f_qualification == 'Graduate') echo 'selected' ?> value="Graduate">Graduate
            </option>
            <option <?php if($final_preview->f_qualification == 'Post-Graduate') echo 'selected' ?>
                value="Post-Graduate">Post-Graduate</option>
            <option <?php if($final_preview->f_qualification == 'Doctorate') echo 'selected' ?> value="Diploma">
                Doctorate</option>

        </select>
        <div style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
        </div>
        <?php }else { ?>
        <input type="text" placeholder="Enter Qualification"
            <?php echo $required_fields['f_qualification']['required'] ?>
            <?php if(!empty($final_preview->f_qualification)) echo 'value="'.$final_preview->f_qualification.'"' ?>
            id="f_qualification" data-type="f_qualification" name="f_qualification" class="form-control"
            data-parsley-error-message="Please enter father's qualification (minimum 2 characters)"
            data-parsley-minlength="2">
        <span class="help-block">Mention all qualifications.</span>
        <?php }?>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'f_profession')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_profession">Profession
            <?php if($required_fields['f_profession']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" placeholder="Enter Profession" <?php echo $required_fields['f_profession']['required'] ?>
            <?php if(!empty($final_preview->f_profession)) echo 'value="'.$final_preview->f_profession.'"' ?>
            id="f_profession" data-type="f_profession" name="f_profession" class="form-control">
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'f_position')) :  ?>
    <div class="row mb-4" style="display: block;">
        <div class="col-md-8 p-0">
            <label for="check_f_designation" class="mobile-checkbox-label" style="cursor: pointer; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: flex; align-items: center; padding: 15px 10px; margin: 0; border-radius: 8px; transition: background-color 0.2s ease; -webkit-tap-highlight-color: rgba(0,0,0,0.1); position: relative;">
                <input type="checkbox" id="check_f_designation" value="" style="margin-right: 12px; transform: scale(1.5); cursor: pointer; min-width: 20px; min-height: 20px; z-index: 10; position: relative;">
                <span style="margin-left: 8px; font-size: 16px; line-height: 1.4; pointer-events: none;">Not working ?</span>
            </label>
        </div>
    </div>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_position">Designation
            <?php if($required_fields['f_position']['required']=='required') echo'<font class="f_company_color" color="red">*</font>' ?>
        </label>
        <input type="text" placeholder="Enter Designation" <?php echo $required_fields['f_position']['required'] ?>
            <?php if(!empty($final_preview->f_position)) echo 'value="'.$final_preview->f_position.'"' ?>
            id="f_position" data-type="f_position" name="f_position" class="form-control">
        
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'f_type_of_organization')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_type_of_organization">Type of Organization
            <?php if($required_fields['f_type_of_organization']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div style="position: relative;" class="p-0">
        <select name="f_type_of_organization" id="f_type_of_organization" class="form-control"
            <?php echo $required_fields['f_type_of_organization']['required'] ?>
            data-parsley-error-message="Please select father's type of organization">
            <option value="">Select</option>
            <option value="Self Employed"
                <?php if($final_preview->f_type_of_organization == 'Self Employed') { echo 'selected';} ?>>Self Employed
            </option>
            <option value="Private" <?php if($final_preview->f_type_of_organization == 'Private') { echo 'selected';}?>>
                Private</option>
            <option value="Government"
                <?php if($final_preview->f_type_of_organization == 'Government') { echo 'selected';}?>>Government
            </option>
        </select>
        <div style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'f_company_name')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_company">Company Name
            <?php if($required_fields['f_company_name']['required']=='required') echo'<font class="f_company_color" color="red">*</font>' ?></label>
        <input type="text" <?php echo $required_fields['f_company_name']['required'] ?> placeholder="Enter Company Name"
            <?php if(!empty($final_preview->f_company_name)) echo 'value="'.$final_preview->f_company_name.'"' ?>
            id="f_company" data-type="f_company" name="f_company" class="form-control input-md">
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'f_company_addr')) :  ?>
    <div class="row mb-3">
        <label class="form-label parent_css" for="f_company_addr">Company Address &nbsp;
            <?php if($required_fields['f_company_addr']['required']=='required') echo'<font class="f_company_color" color="red">*</font>' ?>
        </label>
        <textarea class="form-control" <?php echo $required_fields['f_company_addr']['required']; ?> id="f_company_addr"
            name="f_company_addr"
            placeholder="Enter Address"><?php if(!empty($final_preview->f_company_addr)) echo $final_preview->f_company_addr ?></textarea>
    </div>

    <div class="row">
        <div class="col-12 col-md-6 p-0 p-md-3 ps-md-0 mb-3 mb-md-0">
            <input type="text" id="f_company_area"
                name="f_company_area"
                placeholder="Enter Area"
                class="form-control"
                <?= $required_fields['f_company_area']['required'] ?>
                <?php if(!empty($final_preview->f_company_area)) echo 'value="'.$final_preview->f_company_area.'"' ?>>
        </div>

        <div class="col-12 col-md-6 p-0 p-md-3 mb-3 pe-md-0">
            <input type="text" id="f_company_district"
                name="f_company_district"
                placeholder="Enter District"
                class="form-control"
                <?= $required_fields['f_company_district']['required'] ?>
                <?php if(!empty($final_preview->f_company_district)) echo 'value="'.$final_preview->f_company_district.'"' ?>>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12 col-md-4 p-0 p-md-3 ps-md-0 mb-3 mb-md-0 position-relative">
            <?php 
            $array = array(''=>'Country');
            foreach ($this->config->item('country') as $key => $nation) {
                $array[$nation] =  $nation;
            }
            echo form_dropdown("f_company_county", $array, set_value("f_company_county",$final_preview->f_company_county), "id='f_company_county' ".$required_fields['f_company_county']['required']." class='form-control'");
            ?>
            <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>

        <div class="col-12 col-md-4 p-0 p-md-3 mb-3 mb-md-0">
            <input type="text" id="f_company_state"
                name="f_company_state"
                placeholder="Enter State"
                class="form-control"
                <?= $required_fields['f_company_state']['required'] ?>
                <?php if(!empty($final_preview->f_company_state)) echo 'value="'.$final_preview->f_company_state.'"' ?>>
        </div>

        <div class="col-12 col-md-4 p-0 p-md-3 pe-md-0">
            <input id="f_company_pincode"
                name="f_company_pincode"
                placeholder="Pincode"
                type="text"
                class="form-control"
                data-parsley-type="digits"
                data-parsley-length="[3, 10]"
                data-parsley-error-message="Enter a valid pin-code"
                <?= $required_fields['f_company_pincode']['required'] ?>
                <?php if(!empty($final_preview->f_company_pincode)) echo 'value="'.$final_preview->f_company_pincode.'"' ?>>
        </div>
    </div>

    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'f_office_ph')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css">Office and Residential Numbers
            <?php if($required_fields['f_office_ph']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="col-md-6 pe-md-2">
            <input type="text" <?php echo $required_fields['f_office_ph']['required'] ?> placeholder="Office Number"
                <?php if(!empty($final_preview->f_office_ph)) echo 'value="'.$final_preview->f_office_ph.'"' ?>
                id="f_office_ph" data-type="father" name="f_office_ph" class="form-control">
        </div>
        <div class="col-md-6 ps-md-2">
            <input type="text" <?php echo $required_fields['f_res_ph']['required'] ?> placeholder="Residential Number"
                <?php if(!empty($final_preview->f_res_ph)) echo 'value="'.$final_preview->f_res_ph.'"' ?> id="f_res_ph"
                data-type="father" name="f_res_ph" class="form-control">
        </div>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'f_annual_gross_income')) :  ?>
    <div class="row mb-4">
    <label class="form-label parent_css" for="f_annual_income">
        Gross Annual Income (INR)
        <?php if($required_fields['f_annual_gross_income']['required']=='required') echo '<font color="red">*</font>'; ?>
    </label>

        <?php if ($config_val['parent_annual_income_type'] == 'whole_number') { ?>
        <input id="f_annual_income" name="f_annual_income" type="text"
            <?php if(!empty($final_preview->f_annual_gross_income)) echo 'value="'.$final_preview->f_annual_gross_income.'"' ?>
            placeholder="Enter Annual Income" class="form-control"
            <?php echo $required_fields['f_annual_gross_income']['required'] ?>
            data-parsley-error-message="Only numbers and commas are allowed" pattern="^[0-9,]*$">
        <?php }else { 
                    
                        $fAnnualIncome = ['0 to 5 Lac','5 Lac to 10 Lac','10 Lac to 20 Lac','20 Lac 50 Lac','Above 50 Lac'];
                        if(!empty($config_val['annual_income_options'])){
                            $fAnnualIncome = json_decode($config_val['annual_income_options']); 
                        }
                        $array = array('' => '- Select -');
                        foreach ($fAnnualIncome as $key => $income) {
                            $array[$income] =  $income;
                        }
                        echo form_dropdown("f_annual_income", $array, set_value("f_annual_gross_income",$final_preview->f_annual_gross_income), "id='f_annual_income' ".$required_fields['f_annual_gross_income']['required']." class='form-control'");
                    ?>
        <?php }
            ?>
        <span class="col-sm-3 control-label">
        </span>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'f_pan_number')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="f_pan_number">PAN Number
            <?php if($required_fields['f_pan_number']['required']=='required') echo'<font color="red">*</font>' ?></label>
            <input id="f_pan_number" name="f_pan_number" type="text"
                <?php if(!empty($final_preview->f_pan_number)) echo 'value="'.$final_preview->f_pan_number.'"' ?>
                placeholder="Enter PAN Number" class="form-control"
                <?php echo $required_fields['f_pan_number']['required'] ?>
                data-parsley-error-message="Enter valid PAN Number" pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}">
        <span class="col-sm-3 control-label">
        </span>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'father_interest')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="father_interest">Any Interest/talent/art which you like to
            share?<?php if($required_fields['father_interest']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input id="father_interest" name="father_interest" type="text"
            <?php if(!empty($final_preview->father_interest)) echo 'value="'.$final_preview->father_interest.'"' ?>
            placeholder="Any Interest/talent/art which you like to share" class="form-control"
            <?php echo $required_fields['father_interest']['required'] ?> data-parsley-error-message="">
        <span class="col-sm-3 control-label">
        </span>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'father_photo')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css fw-semibold mb-2">
            Upload Father
            Photo<?php if($required_fields['father_photo']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>
        <div class="border border-dashed rounded p-4 text-center position-relative"
            style="cursor: pointer; background-color: #fafafa;"
            onclick="document.getElementById('photo_fileupload_f').click();">
            <?php if (!empty($final_preview->father_photo)) { ?>
                <img src="<?= $this->filemanager->getFilePath($final_preview->father_photo) ?>"
                    alt="Preview" id="photo_previewing_f" class="mb-2" style="max-height: 80px; max-width: 150px;margin: 0 auto;" />
            <?php } else { ?>
                <img src=""
                    alt="Preview" id="photo_previewing_f" class="mb-2" style="max-height: 80px; max-width: 150px;display:none;margin:0 auto" />
                <div id="photo_placeholder_f" style="max-height: 100px; max-width: 150px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                    <?php $this->load->view('svg_icons/empty_image.svg'); ?>
                </div>
            <?php } ?>
            <div class="d-flex flex-column align-items-center">
                <i class="bi bi-upload" style="font-size: 24px;"></i>
                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                <small class="text-muted">JPEG, JPG, PNG (max. <?= $image_size_in_admissions ?>MB)</small>
            </div>
            <?php if($final_preview->father_photo){ ?>
            <input type="file" name="father_photo" id="photo_fileupload_f" accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;" />
            <?php }else{ ?>
            <input type="file" name="father_photo" id="photo_fileupload_f" accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;"
                <?= $required_fields['father_photo']['required'] ?? '' ?> />
            <?php } ?>
            <input type="hidden" name="f_photo_path" id="f_photo_path">
            <span id="f_photo_fileuploadError" class="text-danger d-block mt-2"></span>
        </div>
        <div class="mt-2">
            
            <span class="help-block">
                <?= $this->settings->getSetting('student_photo_note_display') ?: 'Upload recent passport size photograph' ?>
            </span>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'f_signature')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css fw-semibold mb-2">
            Upload Father Signature<?php if($required_fields['f_signature']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>
        <div class="border border-dashed rounded p-4 text-center position-relative"
             style="cursor: pointer; background-color: #fafafa;"
             onclick="document.getElementById('signature_upload').click();">
            <?php if (!empty($final_preview->f_signature)) { ?>
                <img src="<?= $this->filemanager->getFilePath($final_preview->f_signature) ?>"
                     alt="Preview" id="f_sign_previewing" class="mb-2" style="max-height: 100px; max-width: 150px;margin: 0 auto;" />
            <?php } else { ?>
                <img src=""
                    alt="Preview" id="f_sign_previewing" class="mb-2" style="max-height: 100px; max-width: 150px;display:none;margin:0 auto" />
                <div id="signature_placeholder_f" style="max-height: 100px; max-width: 150px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                    <?php $this->load->view('svg_icons/empty_image.svg'); ?>
                </div>
            <?php } ?>
            <div class="d-flex flex-column align-items-center">
                <i class="bi bi-upload" style="font-size: 24px;"></i>
                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                <small class="text-muted">JPEG, JPG, PNG (max. <?= $image_size_in_admissions ?>MB)</small>
            </div>
            <?php if($final_preview->f_signature){ ?>
            <input type="file" name="f_signature" id="signature_upload" accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;" />
            <?php }else{ ?>
            <input type="file" name="f_signature" id="signature_upload" accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;"
                <?= $required_fields['f_signature']['required'] ?? '' ?> />
            <?php } ?>
            <input type="hidden" name="f_signature_path" id="f_signature_path">
            <span id="f_fileuploadError" class="text-danger d-block mt-2"></span>
        </div>
        <div class="mt-2">
            
            <span class="help-block">
                <?= $this->settings->getSetting('student_photo_note_display') ?: 'Upload recent passport size photograph' ?>
            </span>
        </div>
    </div>
    <?php endif ?>

</div>

<div class="col-12 col-lg-6" id="mother_details_tab">

<?php $mother_name_label = "Mother's First Name";
if($config_val['mother_first_name_label'])  { $mother_name_label = $config_val['mother_first_name_label']; } ?>
<?php $freez_mname = '' ; if(in_array('m_name',$freez_primary_fields) && $freez_parent_details) $freez_mname = 'readonly'; ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_name"><?= $mother_name_label; ?>
            <?php if($required_fields['m_name']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input <?php echo $required_fields['m_name']['required'] ?> placeholder="Enter First Name" id="m_name"
            <?php if(!empty($final_preview->m_name)) echo 'value="'.$final_preview->m_name.'"' ?> name="m_name"
            type="text" class="form-control input-md" data-parsley-error-message="Cannot be empty, Only alphabets"
            data-parsley-pattern="^[a-zA-Z. ]+$" data-parsley-minlength="2" <?= $freez_mname ?>>
        <?php if($this->settings->getSetting('enquiry_help_block_parent_name')) { ?>
        <span class="help-block"><?php echo $this->settings->getSetting('enquiry_help_block_parent_name')?></span>
        <?php } else{ ?>
        <span class="help-block">As per official document</span>
        <?php }?>
    </div>
    <?php if(admission_is_enabled($disabled_fields, 'm_last_name')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_last_name">Mother's Last Name
            <?php if($required_fields['m_last_name']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Enter Last Name"
            <?php if(!empty($final_preview->m_last_name)) echo 'value="'.$final_preview->m_last_name.'"' ?>
            id="mother_lastname" name="m_last_name" type="text"
            <?php echo $required_fields['m_last_name']['required'] ?> class="form-control input-md"
            data-parsley-error-message="Cannot be empty, only alphabets" data-parsley-pattern="^[a-zA-Z. ]+$">
            <?php if($this->settings->getSetting('enquiry_help_block_parent_name')) { ?>
            <span class="help-block"><?php echo $this->settings->getSetting('enquiry_help_block_parent_name')?></span>
            <?php } else{ ?>
            <span class="help-block">As per official document</span>
            <?php }?>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'm_dob')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_dob">
            Date of Birth 
            <?php if($required_fields['m_dob']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <div class="position-relative p-0">
            <input type="text" class="form-control m_datepick pe-5" autocomplete="off"
                <?php if (!empty($final_preview->m_dob) && $final_preview->m_dob != '00-00-0000') echo 'value="' . date('d-m-Y', strtotime($final_preview->m_dob)) . '"'; ?>
                name="m_dob" placeholder="Enter Date Of Birth"
                <?php echo $required_fields['m_dob']['required'] ?>
                data-parsley-error-message="Please enter mother's date of birth in DD-MM-YYYY format"
                data-parsley-pattern="^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$">

            <!-- Calendar icon -->
            <div style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); z-index: 10;padding-right:20px">
                <?php $this->load->view('svg_icons/calendar_icon.svg'); ?>
            </div>
        </div>

        <label class="control-label" id="age_cal_m"></label>
    </div>
    <?php endif ?>


    <?php $freez_m_mobile_no= '' ; if(in_array('m_mobile_no',$freez_primary_fields) && $freez_parent_details) $freez_m_mobile_no = 'readonly'; ?>
    <?php if(admission_is_enabled($disabled_fields, 'm_mobile_no')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css">Mobile Number
            <?php if($required_fields['m_mobile_no']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>
        <div class="d-flex gap-2 p-0">
            <div class="w-25" style="position: relative;">
                <?php 
                    $array = array();
                    foreach ($this->config->item('country_codes') as $key => $code) {
                        $array[$code] =  $code;
                    }
                    echo form_dropdown("m_country_code", $array, set_value("m_country_code", $final_preview->m_country_code), "id='m_country_code' ".$required_fields['m_country_code']['required']." class='form-control' data-parsley-error-message='Please select country code'");
                ?>
                <div style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                    <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                </div>
            </div>
            <div class="w-75">
                <input type="text"
                    <?= $required_fields['m_mobile_no']['required'] ?>
                    data-parsley-pattern="^[0-9 -()+]+$"
                    data-parsley-length="[8, 20]"
                    data-parsley-error-message="Please enter a valid mobile number for mother (8-20 digits)"
                    placeholder="Enter Mobile Number"
                    <?php if(!empty($final_preview->m_mobile_no)) echo 'value="'.$final_preview->m_mobile_no.'"' ?>
                    id="m_mobile_no" data-type="mother" name="m_mobile_no"
                    class="form-control input-md" <?= $freez_m_mobile_no ?>>
            </div>
        </div>
    </div>
    <?php endif ?>

    <?php $freez_m_email= '' ; if(in_array('m_email_id',$freez_primary_fields) && $freez_parent_details) $freez_m_email = 'readonly'; ?>
    <?php if(admission_is_enabled($disabled_fields, 'm_email_id')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_email">Mother's Email Id
            <?php if($required_fields['m_email_id']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="email" <?php echo $required_fields['m_email_id']['required'] ?> placeholder="Enter Valid Email Id"
            <?php if(!empty($final_preview->m_email_id)) echo 'value="'.$final_preview->m_email_id.'"' ?> id="m_email"
            data-type="m_email" name="m_email" class="form-control input-md" <?= $freez_m_email ?>
            data-parsley-error-message="Please enter a valid email address for mother">
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'm_nationality')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_nationality">Mother's Nationality
            <?php if($required_fields['m_nationality']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <?php if(!empty($final_preview->m_nationality)){
                $m_nationality = $final_preview->m_nationality;
            }else{
                $m_nationality = '';
            } ?>
        <div style="position: relative;" class="p-0">
            <?php 
                $array = array();
                foreach ($this->config->item('nationality') as $key => $nation) {
                    $array[$nation] =  $nation;
                }
                echo form_dropdown("m_nationality", $array, set_value("m_nationality",$m_nationality), "id='m_nationality' ".$required_fields['m_nationality']['required']." class='form-control' onchange='add_required_m_aadhar()' data-parsley-error-message='Please select mother\\'s nationality'");
            ?>
            <div style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'mother_aadhar')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="mother_aadhar">Mother's Aadhar Card No
            <?php if($required_fields['mother_aadhar']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" <?php echo $required_fields['mother_aadhar']['required'] ?>
            data-parsley-error-message="Please enter a valid 12-digit Aadhar number" data-parsley-type="digits"
            data-parsley-length="[12, 12]" placeholder="Enter Aadhar Number"
            <?php if(!empty($final_preview->mother_aadhar)) echo 'value="'.$final_preview->mother_aadhar.'"' ?>
            id="mother_aadhar" name="mother_aadhar" class="form-control input-md">
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'mother_religion')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="father_religion ">Mother Religion
            <?php if($required_fields['mother_religion']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <?php if(!empty($final_preview->mother_religion)){
                $relig = $final_preview->mother_religion;
            }else{
                $relig = '';
            } 
            ?>

        <div style="position: relative;" class="p-0">
            <?php 
                $array = array();
                $array['-'] =  'Select Religion';
                foreach ($this->config->item('religions') as $key => $religion) {
                    $array[$religion] =  $religion;
                }
                echo form_dropdown("mother_religion", $array, set_value("mother_religion", $relig), "id='mother_religion' ".$required_fields['mother_religion']['required']." class='form-control'");
            ?>
            <div style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'mother_caste')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="mother_caste">Mother Caste
            <?php if($required_fields['mother_caste']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" <?php echo $required_fields['mother_caste']['required'] ?> placeholder="Enter Mother Caste"
            <?php if(!empty($final_preview->mother_caste)) echo 'value="'.$final_preview->mother_caste.'"' ?>
            id="mother_caste" name="mother_caste" class="form-control input-md">
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'mother_mother_tongue')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="mother_tongue">Mother's Mother Tongue
            <?php if($required_fields['mother_mother_tongue']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <?php if(!empty($final_preview->mother_mother_tongue)){
                $mother_tongue = $final_preview->mother_mother_tongue;
            }else{
                $mother_tongue = '';
            } ?>
            <div style="position: relative;" class="p-0">
        <?php 
                $lang = $this->config->item('languages');
                $array = array('' => 'Select Mother Tongue');
                foreach ($lang as $key => $languages) {
                    $array[$languages] =  $languages;
                }

                echo form_dropdown("mother_mother_tongue", $array, set_value("mother_mother_tongue", $mother_tongue), "id='mother_mother_tongue' ".$required_fields['mother_mother_tongue']['required']."  class='form-control' data-parsley-error-message='Please select mother tongue'");
            ?>
             <div style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
            </div>
    </div>
    <?php endif ?>
    <div id="m_mother_tongue_name" class="row mb-4" style="display: none;">
        <label class="form-label parent_css" for="f_tongue_others">Others</label>
        <div class="col-md-8">
            <input placeholder="Enter Please Specify"
                <?php if(!empty($final_preview->mother_mother_tongue_other)) echo 'value="'.$final_preview->mother_mother_tongue_other.'"' ?>
                id="m_mother_tongue_other" name="mother_mother_tongue_other" type="text" class="form-control input-md"
                data-parsley-error-message="Cannot be empty" data-parsley-pattern="^[a-zA-Z. ]+$"
                data-parsley-minlength="2">
            <span class="help-block">Please Specify</span>
        </div>
    </div>
    <?php if(admission_is_enabled($disabled_fields, 'm_addr')) : ?>
    <!-- Checkbox -->
    <div class="row mb-4" style="display: block;">
        <div class="col-md-8 p-0">
            <label for="copy_address" class="mobile-checkbox-label" style="cursor: pointer; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: flex; align-items: center; padding: 15px 10px; margin: 0; border-radius: 8px; transition: background-color 0.2s ease; -webkit-tap-highlight-color: rgba(0,0,0,0.1); position: relative;">
                <input type="checkbox" id="copy_address" value="" style="margin-right: 12px; transform: scale(1.5); cursor: pointer; min-width: 20px; min-height: 20px; z-index: 10; position: relative;">
                <span style="margin-left: 8px; font-size: 16px; line-height: 1.4; pointer-events: none;">Same As Father Home Address</span>
            </label>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'm_addr')) : ?>
    <!-- Home Address -->
    <!-- Address -->
    <div class="row mb-3">
        <label class="form-label parent_css" for="m_addr">
            Home Address
            <?php if($required_fields['m_addr']['required']=='required') echo '<font color="red">*</font>' ?>
        </label>
        <textarea class="form-control" id="m_addr" name="m_addr" placeholder="Enter Address"
            <?= $required_fields['m_addr']['required'] ?>><?php if(!empty($final_preview->m_addr)) echo $final_preview->m_addr ?></textarea>
    </div>

    <!-- Area + District -->
    <div class="row">
        <div class="col-12 col-md-6 p-0 p-md-3 ps-md-0 mb-3 mb-md-0">
            <input type="text" id="m_area" name="m_area" class="form-control" placeholder="Enter Area"
                <?= $required_fields['m_area']['required'] ?>
                <?php if(!empty($final_preview->m_area)) echo 'value="'.$final_preview->m_area.'"' ?>
                data-parsley-error-message="Please enter a valid area name (minimum 2 characters)"
                data-parsley-minlength="2">
        </div>
        <div class="col-12 col-md-6 p-0 p-md-3 pe-md-0">
            <input type="text" id="m_district" name="m_district" class="form-control" placeholder="Enter District"
                <?= $required_fields['m_district']['required'] ?>
                <?php if(!empty($final_preview->m_district)) echo 'value="'.$final_preview->m_district.'"' ?>
                data-parsley-error-message="Please enter a valid district name (minimum 2 characters)"
                data-parsley-minlength="2">
        </div>
    </div>

    <!-- Country + State + Pincode -->
    <div class="row mb-4">
        <!-- Country -->
        <div class="col-12 col-md-4 p-0 p-md-3 ps-md-0 mb-3 mb-md-0 position-relative">
            <?php 
            $array = array(''=>'Country');
            foreach ($this->config->item('country') as $key => $nation) {
                $array[$nation] =  $nation;
            }
            echo form_dropdown("m_county", $array, set_value("m_county",$final_preview->m_county), "id='m_county' ".$required_fields['m_county']['required']." class='form-control'");
            ?>
            <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>

        <!-- State Input -->
        <div class="col-12 col-md-4 p-0 p-md-3 mb-3 mb-md-0" id="m_state_text">
            <input type="text" id="m_state" name="m_state" class="form-control" placeholder="Enter State"
                <?php if(!empty($final_preview->m_state)) echo 'value="'.$final_preview->m_state.'"' ?>
                data-parsley-error-message="Please enter a valid state name (minimum 2 characters)"
                data-parsley-minlength="2">
        </div>

        <!-- State Dropdown (Initially Hidden) -->
        <div class="col-12 col-md-4 p-0 p-md-3 mb-3 mb-md-0 position-relative" id="m_state_dropdown_div" style="display: none;">
            <select class="form-control" name="m_state_dropdown" id="m_state_dropdown"
                data-parsley-error-message="Please select mother's state">
                <option value="">Select State</option>
                <?php foreach ($this->config->item('states') as $key => $state) {
                    $selected = ($final_preview->m_state == $state) ? 'selected' : ''; ?>
                <option value="<?= $state ?>" <?= $selected ?>><?= $state ?></option>
                <?php } ?>
            </select>
            <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
        </div>

        <!-- Pincode -->
        <div class="col-12 col-md-4 p-0 p-md-3 pe-md-0">
            <input type="text" id="m_pincode" name="m_pincode" class="form-control" placeholder="Pincode"
                <?= $required_fields['m_pincode']['required'] ?>
                <?php if(!empty($final_preview->m_pincode)) echo 'value="'.$final_preview->m_pincode.'"' ?>
                data-parsley-type="digits" data-parsley-length="[3, 10]"
                data-parsley-error-message="Enter a valid pin-code">
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'm_qualification')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_qualification">Qualification
            <?php if($required_fields['m_qualification']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <?php if($need_qualification_dropdown) { ?>
            <div style="position:relative" class="p-0">
        <select <?php echo $required_fields['m_qualification']['required'] ?> id="m_qualification"
            data-type="m_qualification" name="m_qualification" class="form-control"
            data-parsley-error-message="Please select mother's qualification">
            <option value="">Select Qualification</option>
            <option <?php if($final_preview->m_qualification == 'Diploma') echo 'selected' ?> value="Diploma">Diploma
            </option>
            <option <?php if($final_preview->m_qualification == 'Graduate') echo 'selected' ?> value="Graduate">Graduate
            </option>
            <option <?php if($final_preview->m_qualification == 'Post.Graduate') echo 'selected' ?>
                value="Post.Graduate">Post.Graduate</option>
            <option <?php if($final_preview->m_qualification == 'Doctorate') echo 'selected' ?> value="Diploma">
                Doctorate</option>
        </select>
        <div style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
            </div>
        <?php }else {?>
        <input type="text" <?php echo $required_fields['m_qualification']['required'] ?>
            placeholder="Enter Qualification"
            <?php if(!empty($final_preview->m_qualification)) echo 'value="'.$final_preview->m_qualification.'"' ?>
            id="m_qualification" data-type="m_qualification" name="m_qualification" class="form-control"
            data-parsley-error-message="Please enter mother's qualification (minimum 2 characters)"
            data-parsley-minlength="2">
        <span class="help-block">Mention all qualifications.</span>
        <?php }?>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'm_profession')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_profession">Profession
            <?php if($required_fields['m_profession']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" <?php echo $required_fields['m_profession']['required'] ?> placeholder="Enter Profession"
            <?php if(!empty($final_preview->m_profession)) echo 'value="'.$final_preview->m_profession.'"' ?>
            id="m_profession" data-type="m_position" name="m_profession" class="form-control">
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'm_position')) :  ?>
    <div class="row mb-4" style="display: block;">
        <div class="col-md-8 p-0">
            <label for="check_m_designation" class="mobile-checkbox-label" style="cursor: pointer; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: flex; align-items: center; padding: 15px 10px; margin: 0; border-radius: 8px; transition: background-color 0.2s ease; -webkit-tap-highlight-color: rgba(0,0,0,0.1); position: relative;">
                <input type="checkbox" id="check_m_designation" value="" style="margin-right: 12px; transform: scale(1.5); cursor: pointer; min-width: 20px; min-height: 20px; z-index: 10; position: relative;">
                <span style="margin-left: 8px; font-size: 16px; line-height: 1.4; pointer-events: none;">Not working ?</span>
            </label>
        </div>
    </div>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_position">Designation
            <?php if($required_fields['m_position']['required']=='required') echo'<font class="m_company_color" color="red">*</font>' ?></label>
        <input type="text" <?php echo $required_fields['m_position']['required'] ?> placeholder="Enter Designation"
            <?php if(!empty($final_preview->m_position)) echo 'value="'.$final_preview->m_position.'"' ?>
            id="m_position" data-type="m_position" name="m_position" class="form-control input-md">
        
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'm_type_of_organization')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_type_of_organization">Type of Organization
            <?php if($required_fields['m_type_of_organization']['required']=='required') echo'<font color="red">*</font>' ?></label>
            <div style="position: relative;" class="p-0">
        <select name="m_type_of_organization" id="m_type_of_organization" class="form-control"
            <?php echo $required_fields['m_type_of_organization']['required'] ?>
            data-parsley-error-message="Please select mother's type of organization">
            <option value="">Select</option>
            <option value="Self Employed"
                <?php if($final_preview->m_type_of_organization == 'Self Employed') { echo 'selected';}?>>Self Employed
            </option>
            <option value="Private"
                <?php if($final_preview->m_type_of_organization == 'Private') { echo 'selected';} ?>>Private</option>
            <option value="Government"
                <?php if($final_preview->m_type_of_organization == 'Government') { echo 'selected';} ?>>Government
            </option>
        </select>
        <div style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
            </div>
            </div>
    </div>
    <?php endif ?>


    <?php if(admission_is_enabled($disabled_fields, 'm_company_name')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_company">Company Name
            <?php if($required_fields['m_company_name']['required']=='required') echo'<font class="m_company_color" color="red">*</font>' ?></label>
        <input type="text" <?php echo $required_fields['m_company_name']['required'] ?> placeholder="Enter company name"
            <?php if(!empty($final_preview->m_company_name)) echo 'value="'.$final_preview->m_company_name.'"' ?>
            id="m_company" data-type="m_company" name="m_company" class="form-control input-md">
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'm_company_addr')) :  ?>
        <div class="row mb-3">
    <label class="form-label parent_css" for="m_company_addr">
        Company Address &nbsp;
        <?php if($required_fields['m_company_addr']['required']=='required') echo'<font class="m_company_color" color="red">*</font>' ?>
    </label>
    <textarea class="form-control" <?php echo $required_fields['m_company_addr']['required'] ?> id="m_company_addr"
        name="m_company_addr"
        placeholder="Enter Address"><?php if(!empty($final_preview->m_company_addr)) echo $final_preview->m_company_addr ?></textarea>
</div>

<div class="row">
    <div class="col-12 col-md-6 p-0 p-md-3 ps-md-0 mb-3 mb-md-0">
        <input type="text" id="m_company_area"
            name="m_company_area"
            placeholder="Enter Area"
            class="form-control"
            <?= $required_fields['m_company_area']['required'] ?>
            <?php if(!empty($final_preview->m_company_area)) echo 'value="'.$final_preview->m_company_area.'"' ?>>
    </div>

    <div class="col-12 col-md-6 p-0 p-md-3 mb-3 pe-md-0">
        <input type="text" id="m_company_district"
            name="m_company_district"
            placeholder="Enter District"
            class="form-control"
            <?= $required_fields['m_company_district']['required'] ?>
            <?php if(!empty($final_preview->m_company_district)) echo 'value="'.$final_preview->m_company_district.'"' ?>>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12 col-md-4 p-0 p-md-3 ps-md-0 mb-3 mb-md-0 position-relative">
        <?php 
        $array = array(''=>'Country');
        foreach ($this->config->item('country') as $key => $nation) {
            $array[$nation] =  $nation;
        }
        echo form_dropdown("m_company_county", $array, set_value("m_company_county",$final_preview->m_company_county), "id='m_company_county' ".$required_fields['m_company_county']['required']." class='form-control'");
        ?>
        <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>

    <div class="col-12 col-md-4 p-0 p-md-3 mb-3 mb-md-0">
        <input type="text" id="m_company_state"
            name="m_company_state"
            placeholder="Enter State"
            class="form-control"
            <?= $required_fields['m_company_state']['required'] ?>
            <?php if(!empty($final_preview->m_company_state)) echo 'value="'.$final_preview->m_company_state.'"' ?>>
    </div>

    <div class="col-12 col-md-4 p-0 p-md-3 pe-md-0">
        <input id="m_company_pincode"
            name="m_company_pincode"
            placeholder="Pincode"
            type="text"
            class="form-control"
            data-parsley-type="digits"
            data-parsley-length="[3, 10]"
            data-parsley-error-message="Enter a valid pin-code"
            <?= $required_fields['m_company_pincode']['required'] ?>
            <?php if(!empty($final_preview->m_company_pincode)) echo 'value="'.$final_preview->m_company_pincode.'"' ?>>
    </div>
</div>

    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'm_office_ph')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css">Office and Residential Numbers
            <?php if($required_fields['m_office_ph']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="col-md-6 pe-md-2">
            <input type="text" <?php echo $required_fields['m_office_ph']['required'] ?> placeholder="Office Number"
                <?php if(!empty($final_preview->m_office_ph)) echo 'value="'.$final_preview->m_office_ph.'"' ?>
                id="m_office_ph" data-type="mother" name="m_office_ph" class="form-control input-md">
        </div>
        <div class="col-md-6 ps-md-2">
            <input type="text" <?php echo $required_fields['m_res_ph']['required'] ?> placeholder="Residential Number"
                <?php if(!empty($final_preview->m_res_ph)) echo 'value="'.$final_preview->m_res_ph.'"' ?> id="m_res_ph"
                data-type="mother" name="m_res_ph" class="form-control input-md">
        </div>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'm_annual_gross_income')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_annual_income">Gross Annual Income (INR)
            <?php if($required_fields['m_annual_gross_income']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <?php 
                if ($config_val['parent_annual_income_type'] == 'whole_number') { ?>
        <input id="m_annual_income"
            <?php if(!empty($final_preview->m_annual_gross_income)) echo 'value="'.$final_preview->m_annual_gross_income.'"' ?>
            name="m_annual_income" type="text" placeholder="Enter Annual Income" class="form-control input-md"
            <?php echo $required_fields['m_annual_gross_income']['required'] ?>
            data-parsley-error-message="Only numbers and commas are allowed" pattern="^[0-9,]*$">

        <?php }else{ ?>
        <?php 
                    $mAnnualIncome = ['0 to 5 Lac','5 Lac to 10 Lac','10 Lac to 20 Lac','20 Lac – 50 Lac','Above 50 Lac'];
                    if(!empty($config_val['annual_income_options'])){
                        $mAnnualIncome = json_decode($config_val['annual_income_options']);
                    }
                    $array = array('' => '- Select -');
                    foreach ($mAnnualIncome as $key => $income) {
                        $array[$income] =  $income;
                    }
                    echo form_dropdown("m_annual_income", $array, set_value("m_annual_income",$final_preview->m_annual_gross_income), "id='m_annual_income' ".$required_fields['m_annual_gross_income']['required']." class='form-control'");
                    ?>
        <?php }
            ?>
        <span class="col-sm-3 control-label">
        </span>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'm_pan_number')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="m_pan_number">PAN Number
            <?php if($required_fields['m_pan_number']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input id="m_pan_number" name="m_pan_number" type="text"
            <?php if(!empty($final_preview->m_pan_number)) echo 'value="'.$final_preview->m_pan_number.'"' ?>
            placeholder="Enter PAN Number" class="form-control"
            <?php echo $required_fields['m_pan_number']['required'] ?>
            data-parsley-error-message="Enter valid PAN  Number" pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}">
        <span class="col-sm-3 control-label">
        </span>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'mother_interest')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css" for="mother_interest">Any Interest/talent/art which you like to
            share?<?php if($required_fields['mother_interest']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input id="mother_interest" name="mother_interest" type="text"
            <?php if(!empty($final_preview->mother_interest)) echo 'value="'.$final_preview->mother_interest.'"' ?>
            placeholder="Any Interest/talent/art which you like to share" class="form-control"
            <?php echo $required_fields['mother_interest']['required'] ?> data-parsley-error-message="">
        <span class="col-sm-3 control-label">
        </span>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'mother_photo')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css fw-semibold mb-2">
            Upload Mother Photo<?php if($required_fields['mother_photo']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>
        <div class="border border-dashed rounded p-4 text-center position-relative"
             style="cursor: pointer; background-color: #fafafa;"
             onclick="document.getElementById('photo_fileupload_m').click();">
            <?php if (!empty($final_preview->mother_photo)) { ?>
                <img src="<?= $this->filemanager->getFilePath($final_preview->mother_photo) ?>"
                     alt="Preview" id="photo_previewing_m" class="mb-2" style="max-height: 80px; max-width: 150px;margin: 0 auto;" />
            <?php } else { ?>
                <img src=""
                    alt="Preview" id="photo_previewing_m" class="mb-2" style="max-height: 80px; max-width: 150px;display:none;margin:0 auto" />
                <div id="photo_placeholder_m" style="max-height: 100px; max-width: 150px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                    <?php $this->load->view('svg_icons/empty_image.svg'); ?>
                </div>
            <?php } ?>
            <div class="d-flex flex-column align-items-center">
                <i class="bi bi-upload" style="font-size: 24px;"></i>
                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                <small class="text-muted">JPEG, JPG, PNG (max. <?= $image_size_in_admissions ?>MB)</small>
            </div>
            <?php if($final_preview->mother_photo){ ?>
            <input type="file" name="mother_photo" id="photo_fileupload_m" accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png"style="display: none;" />
            <?php }else{ ?>
            <input type="file" name="mother_photo" id="photo_fileupload_m" accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;"
                <?= $required_fields['mother_photo']['required'] ? 'required' : '' ?> />
            <?php } ?>
            <input type="hidden" name="m_photo_path" id="m_photo_path">
            <span id="m_photo_fileuploadError" class="text-danger d-block mt-2"></span>
        </div>
        <div class="mt-2">
           
            <span class="help-block">
                <?= $this->settings->getSetting('student_photo_note_display') ?: 'Upload recent passport size photograph' ?>
            </span>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'm_signature')) :  ?>
    <div class="row mb-4">
        <label class="form-label parent_css fw-semibold mb-2">
            Upload Mother Signature<?php if($required_fields['m_signature']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>
        <div class="border border-dashed rounded p-4 text-center position-relative"
             style="cursor: pointer; background-color: #fafafa;"
             onclick="document.getElementById('fileupload_m').click();">
            <?php if (!empty($final_preview->m_signature)) { ?>
                <img src="<?= $this->filemanager->getFilePath($final_preview->m_signature) ?>"
                alt="Preview" id="previewing_m" class="mb-2" style="max-height: 100px; max-width: 150px;margin: 0 auto;" />
            <?php } else { ?>
                <img src="" alt="Preview" id="previewing_m" class="mb-2" style="max-height: 100px; max-width: 150px;display:none;margin:0 auto;" />
                <div id="signature_placeholder_m" style="max-height: 100px; max-width: 150px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                    <?php $this->load->view('svg_icons/empty_image.svg'); ?>
                </div>
            <?php } ?>
            <div class="d-flex flex-column align-items-center">
                <i class="bi bi-upload" style="font-size: 24px;"></i>
                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                <small class="text-muted">JPEG, JPG, PNG (max. <?= $image_size_in_admissions ?>MB)</small>
            </div>
            <?php if($final_preview->m_signature){ ?>
            <input type="file" name="m_signature" id="fileupload_m" accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;" />
            <?php }else{ ?>
            <input type="file" name="m_signature" id="fileupload_m" accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;"
                <?= $required_fields['m_signature']['required'] ? 'required' : '' ?> />
            <?php } ?>
            <input type="hidden" name="m_signature_path" id="m_signature_path">
            <span id="m_fileuploadError" class="text-danger d-block mt-2"></span>
        </div>
        <div class="mt-2">
            <span class="help-block">
                <?= $this->settings->getSetting('student_photo_note_display') ?: 'Upload recent passport size photograph' ?>
            </span>
        </div>
    </div>
    <?php endif ?>

    </div>
    <!-- End Mother Details Column -->
</div>
<!-- End Parent Details Row Container -->



<script type="text/javascript">
$(document).ready(function() {
    add_required_m_aadhar();
    add_required_f_aadhar();

    // Initialize comprehensive form validation
    initializeParentFormValidation();
});

function initializeParentFormValidation() {
    // Custom validation for date format
    window.Parsley.addValidator('dateformat', {
        validateString: function(value) {
            if (!value) return true; // Let required handle empty values
            return /^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/.test(value);
        },
        messages: {
            en: 'Please enter date in DD-MM-YYYY format'
        }
    });

    // Custom validation for names (only alphabets, spaces, and dots)
    window.Parsley.addValidator('nameformat', {
        validateString: function(value) {
            if (!value) return true;
            return /^[a-zA-Z. ]+$/.test(value);
        },
        messages: {
            en: 'Only alphabets, spaces and dots are allowed'
        }
    });

    // Custom validation for mobile numbers
    window.Parsley.addValidator('mobilenumber', {
        validateString: function(value) {
            if (!value) return true;
            return /^[0-9 -()+]+$/.test(value) && value.replace(/[^0-9]/g, '').length >= 8;
        },
        messages: {
            en: 'Please enter a valid mobile number'
        }
    });

    // Add validation classes to form elements
    $('#f_name, #m_name, #father_lastname, #mother_lastname, #father_caste').attr('data-parsley-nameformat', '');
    $('#f_dob, #m_dob').attr('data-parsley-dateformat', '');
    $('#f_mobile_no, #m_mobile_no').attr('data-parsley-mobilenumber', '');

    // Validate email uniqueness
    $('#f_email, #m_email').on('blur', function() {
        validateEmailUniqueness($(this));
    });

    // Validate mobile uniqueness
    $('#f_mobile_no, #m_mobile_no').on('blur', function() {
        validateMobileUniqueness($(this));
    });
}

function validateEmailUniqueness(element) {
    var email = element.val();
    var fieldType = element.attr('data-type');

    if (email && email.includes('@')) {
        // Check if father and mother emails are the same
        var fatherEmail = $('#f_email').val();
        var motherEmail = $('#m_email').val();

        if (fatherEmail && motherEmail && fatherEmail === motherEmail) {
            element.attr('data-parsley-error-message', 'Father and mother cannot have the same email address');
            element.parsley().validate();
            return false;
        }
    }
    return true;
}

function validateMobileUniqueness(element) {
    var mobile = element.val();
    var fieldType = element.attr('data-type');

    if (mobile && mobile.length >= 8) {
        // Check if father and mother mobiles are the same
        var fatherMobile = $('#f_mobile_no').val();
        var motherMobile = $('#m_mobile_no').val();

        if (fatherMobile && motherMobile && fatherMobile === motherMobile) {
            element.attr('data-parsley-error-message', 'Father and mother cannot have the same mobile number');
            element.parsley().validate();
            return false;
        }
    }
    return true;
}

function add_required_f_aadhar() {
    var father_aadhar = '<?php echo $required_fields['father_aadhar']['required'] ?>';
    var f_nationality = $('#f_nationality').val();
    if (f_nationality == 'Indian' && father_aadhar) {
        $('#father_aadhar').attr('required', 'required');
    } else {
        $('#father_aadhar').removeAttr('required');
    }
    var document_version = '<?php echo $config_val['document_input_version'] ?>';
    if (document_version == 'V2') {
        check_uploaded_parent_documents('father')
    }
}

function check_uploaded_parent_documents(relation) {
    var p_nationality = '';
    var p_old_nationality = '';
    if (relation == 'father') {
        p_nationality = $('#f_nationality').val();
        p_old_nationality = '<?php echo $final_preview->f_nationality ?>';
    } else {
        p_nationality = $('#m_nationality').val();
        p_old_nationality = '<?php echo $final_preview->m_nationality ?>';
    }
    var af_id = '<?php echo $final_preview->id ?>';
    var adm_setting_id = '<?php echo $final_preview->admission_setting_id ?>';
    if (p_nationality != p_old_nationality) {
        $.ajax({
            url: '<?php echo site_url('admission_controller/get_admission_documents'); ?>',
            type: 'post',
            data: {
                'af_id': af_id,
                'adm_setting_id': adm_setting_id,
                'nationality': p_nationality,
                'relation': relation
            },
            success: function(data) {
                parsed_data = $.parseJSON(data);
                if (parsed_data != '') {
                    delete_uploaded_documents(parsed_data, p_old_nationality, relation);
                }
            },
            error: function(err) {
                console.log(err);
            }

        });
    }
}

function add_required_m_aadhar() {
    var mother_aadhar = '<?php echo $required_fields['mother_aadhar']['required'] ?>';
    var m_nationality = $('#m_nationality').val();
    if (m_nationality == 'Indian' && mother_aadhar) {
        $('#mother_aadhar').attr('required', 'required');
    } else {
        $('#mother_aadhar').removeAttr('required');
    }
    var document_version = '<?php echo $config_val['document_input_version'] ?>';
    if (document_version == 'V2') {
        check_uploaded_parent_documents('mother');
    }
}

$(document).ready(function() {
    father_state_validate_required_field();
    mother_state_validate_required_field();
});

function father_state_validate_required_field() {
    var present_state_required = '<?php echo $required_fields['f_state']['required'] ?>';
    var f_country = $('#f_county').val();
    if (f_country == 'India') {
        $('#f_state_dropdown_div').show();
        $('#f_state_text').hide();
        $('input[name=f_state]').removeAttr('required', 'required');
        if (present_state_required) {
            $('select[name=f_state_dropdown]').attr('required', 'required');
        }
    } else {
        $('#f_state_text').show();
        $('#f_state_dropdown_div').hide();
        $('select[name=f_state_dropdown]').removeAttr('required', 'required');
        if (present_state_required) {
            $('input[name=f_state]').attr('required', 'required');
        }
    }
}

function mother_state_validate_required_field() {
    var present_state_required = '<?php echo $required_fields['m_state']['required'] ?>';
    var m_county = $('#m_county').val();
    if (m_county == 'India') {
        $('#m_state_dropdown_div').show();
        $('#m_state_text').hide();
        $('select[name=m_state]').removeAttr('required', 'required');
        if (present_state_required) {
            $('select[name=m_state_dropdown]').attr('required', 'required');
        }
    } else {
        $('#m_state_text').show();
        $('#m_state_dropdown_div').hide();
        $('select[name=m_state_dropdown]').removeAttr('required', 'required');
        if (present_state_required) {
            $('input[name=m_state]').attr('required', 'required');
        }
    }
}

$('#f_county').change(function() {
    father_state_validate_required_field();
});

$('#m_county').change(function() {
    mother_state_validate_required_field();
});


var mAnual = '<?php echo $required_fields['m_annual_gross_income']['required'] ?>';
var mResph = '<?php echo $required_fields['m_res_ph']['required'] ?>';
var mOfficePh = '<?php echo $required_fields['m_office_ph']['required'] ?>';
var mCompPincode = '<?php echo $required_fields['m_company_pincode']['required'] ?>';
var mCompCountry = '<?php echo $required_fields['m_company_county']['required'] ?>';
var mCompState = '<?php echo $required_fields['m_company_state']['required'] ?>';
var mCompDist = '<?php echo $required_fields['m_company_district']['required'] ?>';
var mCompArea = '<?php echo $required_fields['m_company_area']['required'] ?>';
var mCompAdd = '<?php echo $required_fields['m_company_addr']['required'] ?>';
var mCompName = '<?php echo $required_fields['m_company_name']['required'] ?>';
var m_type_of_organization = '<?php echo $required_fields['m_type_of_organization']['required'] ?>';

$('#check_m_designation').click(function() {
    if ($(this).prop("checked") == true) {
        $('.m_company_color').hide();
        $("#m_annual_income").val('');
        $('#m_company').val('');
        $('#m_company_addr').val('');
        $('#m_company_area').val('');
        $('#m_company_district').val('');
        $('#m_company_state').val('');
        $('#m_company_pincode').val('');
        $('#m_company').removeAttr('required');
        $('#m_company_addr').removeAttr('required');
        $('#m_company_area').removeAttr('required');
        $('#m_company_district').removeAttr('required');
        $('#m_company_state').removeAttr('required');
        $('#m_company_pincode').removeAttr('required');
        $('#m_company_county').removeAttr('required');
        $('#m_office_ph').removeAttr('required');
        $('#m_type_of_organization').removeAttr('required');

        $('#m_annual_income').removeAttr('required');
        $('#m_position').attr('readonly', 'readonly');
        $('#m_annual_income').attr('readonly', 'readonly');
        $('#m_annual_income')
            .attr('readonly', 'readonly')
            .css('pointer-events', 'none');
        $('#m_office_ph').attr('readonly', 'readonly');
        $('#m_company_county').attr('readonly', 'readonly');
        $('#m_position').val('Not working');
        $('#m_company').attr('readonly', 'readonly');
        $('#m_company_addr').attr('readonly', 'readonly');
        $('#m_company_addr').val('');
        $('#m_company_area').attr('readonly', 'readonly');
        $('#m_company_area').val('');
        $('#m_company_district').attr('readonly', 'readonly');
        $('#m_company_district').val('');
        $('#m_company_state').attr('readonly', 'readonly');
        $('#m_company_state').val('');
        $('#m_company_pincode').attr('readonly', 'readonly');
        $('#m_company_pincode').val('');
        $('#m_type_of_organization').attr('readonly', 'readonly');
        $('#m_type_of_organization').val('');
         $('#m_type_of_organization').css('pointer-events', 'none');
        $('#m_company_county').css('pointer-events', 'none');
    } else {
        $('.m_company_color').show();
        $('#m_position').removeAttr('readonly');
        $('#m_position').val('');


        if (mCompName) {
            $('#m_company').attr('required', 'required');
        }

        if (mCompAdd) {
            $('#m_company_addr').attr('required', 'required');
        }

        if (mCompArea) {
            $('#m_company_area').attr('required', 'required');
        }

        if (mCompDist) {
            $('#m_company_district').attr('required', 'required');
        }

        if (mCompDist) {
            $('#m_company_state').attr('required', 'required');
        }

        if (mCompPincode) {
            $('#m_company_pincode').attr('required', 'required');
        }

        if (mAnual) {
            $('#m_annual_income').attr('required', 'required');
        }
        if (mCompCountry) {
            $('#m_company_county').attr('required', 'required');
        }
        if (mOfficePh) {
            $('#m_office_ph').attr('required', 'required');
        }
        if (m_type_of_organization) {
            $('#m_type_of_organization').attr('required', 'required');
        }
        $('#m_company').removeAttr('readonly');
        $('#m_company_addr').removeAttr('readonly');
        $('#m_company_area').removeAttr('readonly');
        $('#m_company_district').removeAttr('readonly');
        $('#m_company_state').removeAttr('readonly');
        $('#m_company_pincode').removeAttr('readonly');
        $('#m_annual_income').removeAttr('readonly');
        $('#m_annual_income').css('pointer-events', '');
        $('#m_office_ph').removeAttr('readonly');
        $('#m_company_county').removeAttr('readonly');
        $('#m_type_of_organization').removeAttr('readonly');
        $('#m_company_county').css('pointer-events', '');
        $('#m_type_of_organization').css('pointer-events', '');
    }
});

var fAnual = '<?php echo $required_fields['f_annual_gross_income']['required'] ?>';
var fResph = '<?php echo $required_fields['f_res_ph']['required'] ?>';
var fOfficePh = '<?php echo $required_fields['f_office_ph']['required'] ?>';
var fCompPincode = '<?php echo $required_fields['f_company_pincode']['required'] ?>';
var fCompCountry = '<?php echo $required_fields['f_company_county']['required'] ?>';
var fCompState = '<?php echo $required_fields['f_company_state']['required'] ?>';
var fCompDist = '<?php echo $required_fields['f_company_district']['required'] ?>';
var fCompArea = '<?php echo $required_fields['f_company_area']['required'] ?>';
var fCompAdd = '<?php echo $required_fields['f_company_addr']['required'] ?>';
var fCompName = '<?php echo $required_fields['f_company_name']['required'] ?>';
var f_type_of_organization = '<?php echo $required_fields['f_type_of_organization']['required'] ?>';

$('#check_f_designation').click(function() {
    if ($(this).prop("checked") == true) {
        $("#f_annual_income").val('');

        $('.f_company_color').hide();
        $('#f_company').val('');
        $('#f_company_addr').val('');
        $('#f_company_area').val('');
        $('#f_company_district').val('');
        $('#f_company_state').val('');
        $('#f_company_pincode').val('');
        $('#f_company').removeAttr('required');
        $('#f_company_addr').removeAttr('required');
        $('#f_company_area').removeAttr('required');
        $('#f_company_district').removeAttr('required');
        $('#f_company_state').removeAttr('required');
        $('#f_company_pincode').removeAttr('required');
        $('#f_company_county').removeAttr('required');
        $('#f_office_ph').removeAttr('required');
        $('#f_annual_income').removeAttr('required');
        $('#f_type_of_organization').removeAttr('required');
        $('#f_type_of_organization').attr('readonly', 'readonly');
        $('#f_type_of_organization').val('');

        $('#f_position').attr('readonly', 'readonly');
        $('#f_position').val('Not working');
        $('#f_company').attr('readonly', 'readonly');
        $('#f_annual_income')
            .attr('readonly', 'readonly')
            .css('pointer-events', 'none');
        $('#f_office_ph').attr('readonly', 'readonly');
        $('#f_company_county').attr('readonly', 'readonly');
        $('#f_company_addr').attr('readonly', 'readonly');
        $('#f_company_addr').val('');
        $('#f_company_area').attr('readonly', 'readonly');
        $('#f_company_area').val('');
        $('#f_company_district').attr('readonly', 'readonly');
        $('#f_company_district').val('');
        $('#f_company_state').attr('readonly', 'readonly');
        $('#f_company_state').val('');
        $('#f_company_pincode').attr('readonly', 'readonly');
        $('#f_company_pincode').val('');
        $('#f_type_of_organization').css('pointer-events', 'none');
        $('#f_company_county').css('pointer-events', 'none');
    } else {
        $('.f_company_color').show();
        $('#f_position').val('');
        $('#f_position').removeAttr('readonly');

        if (fCompName) {
            $('#f_company').attr('required', 'required');
        }

        if (fCompAdd) {
            $('#f_company_addr').attr('required', 'required');
        }

        if (fCompArea) {
            $('#f_company_area').attr('required', 'required');
        }

        if (fCompDist) {
            $('#f_company_district').attr('required', 'required');
        }

        if (fCompDist) {
            $('#f_company_state').attr('required', 'required');
        }

        if (fCompPincode) {
            $('#f_company_pincode').attr('required', 'required');
        }

        if (fAnual) {
            $('#f_annual_income').attr('required', 'required');
        }
        if (fCompCountry) {
            $('#f_company_county').attr('required', 'required');
        }
        if (fOfficePh) {
            $('#f_office_ph').attr('required', 'required');
        }
        if (f_type_of_organization) {
            $('#f_type_of_organization').attr('required', 'required');
        }
        $('#f_company').removeAttr('readonly');
        $('#f_company_addr').removeAttr('readonly');
        $('#f_company_area').removeAttr('readonly');
        $('#f_company_district').removeAttr('readonly');
        $('#f_company_state').removeAttr('readonly');
        $('#f_company_pincode').removeAttr('readonly');
        $('#f_annual_income').removeAttr('readonly');
        $('#f_annual_income').css('pointer-events', '');
        $('#f_office_ph').removeAttr('readonly');
        $('#f_company_county').removeAttr('readonly');
        $('#f_type_of_organization').removeAttr('readonly');
        $('#f_type_of_organization').css('pointer-events', '');
        $('#f_company_county').css('pointer-events', '');
    }
});



$('#copy_address').click(function() {
    if ($(this).prop("checked") == true) {
        var f_addr = $('#f_addr').val();
        var f_area = $('#f_area').val();
        var f_district = $('#f_district').val();

        var f_county = $('#f_county').val();
        var f_pincode = $('#f_pincode').val();
        $('#m_addr').val(f_addr);
        $('#m_area').val(f_area);
        $('#m_district').val(f_district);
        if (f_county == 'India') {
            var f_state = $('#f_state_dropdown').val();
            $('#m_state_dropdown_div').show();
            $('#m_state_text').hide();
            $('#m_state_dropdown').val(f_state);
            $('input[name=m_state]').removeAttr('required', 'required');
        } else {
            var f_state = $('#f_state').val();
            $('#m_state_text').show();
            $('#m_state_dropdown_div').hide();
            $('#m_state').val(f_state);
            $('select[name=m_state_dropdown]').removeAttr('required', 'required');
        }
        $('#m_county').val(f_county);
        $('#m_pincode').val(f_pincode);
    } else {
        $('#m_addr').val('');
        $('#m_area').val('');
        $('#m_district').val('');
        $('#m_state').val('');
        $('#m_county').val('');
        $('#m_pincode').val('');
        $('#m_state_dropdown').val('');
    }
});
$('#student_copy_address').click(function() {
    if ($(this).prop("checked") == true) {
        var s_present_addr = $('#s_present_addr').val();
        var s_present_area = $('#s_present_area').val();
        var s_present_district = $('#s_present_district').val();
        var s_present_state = $('#s_present_state').val();
        var s_present_county = $('#s_present_country').val();
        var s_present_pincode = $('#s_present_pincode').val();

        if (s_present_county == 'India') {
            $('#f_state_dropdown_div').show();
            $('#f_state_text').hide();
            $('input[name=f_state]').removeAttr('required', 'required');
            $('select[name=f_state_dropdown]').val(s_present_state);
            $('#f_state').val('');
        } else {
            $('#f_state_text').show();
            $('#f_state_dropdown_div').hide();
            $('select[name=f_state_dropdown]').removeAttr('required', 'required');
            $('#f_state').val(s_present_state);
            $('select[name=f_state_dropdown]').val('');
        }
        $('#f_addr').val(s_present_addr);
        $('#f_area').val(s_present_area);

        $('#f_district').val(s_present_district);
        // $('#f_state').val(s_present_state);
        $('#f_county').val(s_present_county);
        $('#f_pincode').val(s_present_pincode);
    } else {
        $('#f_addr').val('');
        $('#f_area').val('');
        $('#f_district').val('');
        $('#f_state').val('');
        $('#f_pincode').val('');
        $('#f_county').val('');
        $('select[name=f_state_dropdown]').val('');

    }
});
</script>

<script type="text/javascript">
function readURL_f(input, photo_type) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            if (photo_type == 'f_signature') {
                $('#f_sign_previewing').attr('src', e.target.result);
            } else if (photo_type == 'f_photo') {
                $('#photo_previewing_f').attr('src', e.target.result);
            }
        }

        reader.readAsDataURL(input.files[0]);
    }
}

$('#signature_upload').change(function() {
    var src = $(this).val();
    if (src && validate_photo_size(this.files[0], 'signature_upload', 'f_fileuploadError')) {
        $('#signature_placeholder_f').css('display','none');
        $('#f_sign_previewing').css('display','block');
        $("#f_fileuploadError").html("");
        readURL_f(this, 'f_signature');

        // Start upload
        saveFileToStorage_parent(this.files[0],'signature_upload','f_signature_path');
    } else {
        this.value = null;
    }
});

$('#photo_fileupload_f').change(function() {
    var src = $(this).val();
    if (src && validate_photo_size(this.files[0], 'photo_fileupload_f', 'f_photo_fileuploadError')) {
        $('#photo_placeholder_f').css('display','none');
        $('#photo_previewing_f').css('display','block');
        $("#f_photo_fileuploadError").html("");
        readURL_f(this, 'f_photo');

        // Start upload
        saveFileToStorage_parent(this.files[0],'photo_fileupload_f','f_photo_path');
    } else {
        this.value = null;
    }
});

function validate_photo_size(files, id, error_id) {
    var max_size_string = '<?php echo $image_size_in_admissions ?>';
    var file_size = parseFloat(files.size / 1024 / 1024);
    var max_file_size = parseInt(max_size_string);
    if (file_size > max_file_size) {
        $("#" + error_id).html('File size exceeded.');
        $("#" + id).val('');
        return false;
    } else {
        $("#" + error_id).html('');
        return true;
    }
}

function readURL_m(input, photo_type) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            if (photo_type == 'm_signature') {
                $('#previewing_m').attr('src', e.target.result);
            } else if (photo_type == 'm_photo') {
                $('#photo_previewing_m').attr('src', e.target.result);
            }
        }

        reader.readAsDataURL(input.files[0]);
    }
}

$('#fileupload_m').change(function() {
    var src = $(this).val();
    if (src && validate_photo_size(this.files[0], 'fileupload_m', 'm_fileuploadError')) {
        $('#signature_placeholder_m').css('display','none');
        $('#previewing_m').css('display','block');
        $("#m_fileuploadError").html("");
        readURL_m(this, 'm_signature');

        // Start upload
        saveFileToStorage_parent(this.files[0],'fileupload_m','m_signature_path');
    } else {
        this.value = null;
    }
});

$('#photo_fileupload_m').change(function() {
    var src = $(this).val();
    if (src && validate_photo_size(this.files[0], 'photo_fileupload_m', 'm_photo_fileuploadError')) {
        $('#photo_placeholder_m').css('display','none');
        $('#photo_previewing_m').css('display','block');
        $("#m_photo_fileuploadError").html("");
        readURL_m(this, 'm_photo');

        // Start upload
        saveFileToStorage_parent(this.files[0],'photo_fileupload_m','m_photo_path');
    } else {
        this.value = null;
    }
});

function saveFileToStorage_parent(file, id_name, hight_quality_url_id) {
    console.log('Starting parent upload for:', id_name, 'file:', file.name);

    // Disable form controls during upload
    $('#'+id_name).attr('disabled', 'disabled');
    $(".save-step1").prop('disabled', true);
    $(".save-step2").prop('disabled', true);
    $('.save-step4').prop('disabled', true);
    $('.prev-step').prop('disabled', true);

    // Determine preview selector based on input ID
    var previewSelector = '';
    if (id_name === 'photo_fileupload_f') {
        previewSelector = '#photo_previewing_f';
    } else if (id_name === 'signature_upload') {
        previewSelector = '#f_sign_previewing';
    } else if (id_name === 'photo_fileupload_m') {
        previewSelector = '#photo_previewing_m';
    } else if (id_name === 'fileupload_m') {
        previewSelector = '#previewing_m';
    }

    // Create progress overlay
    if (previewSelector) {
        var $preview = $(previewSelector);
        var $parent = $preview.parent();

        // Remove any existing overlay
        $parent.find('.upload-progress-overlay').remove();

        // Add new progress overlay
        $parent.append(`
            <div class="upload-progress-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 1000; border-radius: 8px;">
                <div style="color: white; font-size: 24px; font-weight: bold; margin-bottom: 10px;">
                    <span id="upload-percentage-${id_name}">0%</span>
                </div>
                <div style="color: white; font-size: 14px;">Uploading...</div>
                <div style="width: 80%; background: rgba(255,255,255,0.3); border-radius: 10px; margin-top: 10px; height: 6px;">
                    <div id="upload-progress-bar-${id_name}" style="width: 0%; background: #6c63ff; height: 100%; border-radius: 10px; transition: width 0.3s ease;"></div>
                </div>
            </div>
        `);
    }

    $.ajax({
        url: '<?php echo site_url("S3_admission_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
        },
        success: function(response) {
            console.log('Got signed URL response for parent:', response);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;

            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        if (e.lengthComputable) {
                            var percentComplete = Math.round((e.loaded / e.total) * 100);
                            console.log('Parent upload progress:', percentComplete + '%');

                            // Update progress display
                            $('#upload-percentage-' + id_name).text(percentComplete + '%');
                            $('#upload-progress-bar-' + id_name).css('width', percentComplete + '%');

                            // Also update the global progress indicator
                            $("#percentage_student_completed").html(`${percentComplete} %`);
                            if (percentComplete < 100) {
                                $('#percentage_student_completed').show();
                            }
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    console.log('Parent upload successful, path:', path);

                    // Store the S3 path in the hidden input
                    $(`#${hight_quality_url_id}`).val(path);

                    // Hide global progress indicator
                    $('#percentage_student_completed').hide();

                    // Re-enable form controls
                    $('#'+id_name).removeAttr('disabled');
                    $(".save-step1").prop('disabled', false);
                    $(".save-step2").prop('disabled', false);
                    $('.save-step4').prop('disabled', false);
                    $('.prev-step').prop('disabled', false);

                    // Remove progress overlay
                    $('.upload-progress-overlay').remove();

                    // Reset opacity for all preview elements
                    $('#previewing').css('opacity', '1');
                    $('#stud_sig_previewing').css('opacity', '1');
                    $('#family_previewing').css('opacity', '1');
                    $('#photo_previewing_f').css('opacity', '1');
                    $('#f_sign_previewing').css('opacity', '1');
                    $('#photo_previewing_m').css('opacity', '1');
                    $('#previewing_m').css('opacity', '1');
                    $('#previewing_g').css('opacity', '1');

                    console.log('Parent file uploaded successfully: ' + path);
                },
                error: function(err) {
                    console.log('Parent upload error:', err);

                    // Re-enable form controls
                    $('#'+id_name).removeAttr('disabled');
                    $(".save-step1").prop('disabled', false);
                    $(".save-step2").prop('disabled', false);
                    $('.save-step4').prop('disabled', false);
                    $('.prev-step').prop('disabled', false);

                    // Hide progress and remove overlay
                    $('#percentage_student_completed').hide();
                    $('.upload-progress-overlay').remove();

                    // Reset opacity
                    if (previewSelector) {
                        $(previewSelector).css('opacity', '1');
                    }

                    alert('Upload failed. Please try again.');
                }
            });
        },
        error: function(err) {
            console.log('Parent signed URL error:', err);

            // Re-enable form controls
            $('#'+id_name).removeAttr('disabled');
            $(".save-step1").prop('disabled', false);
            $(".save-step2").prop('disabled', false);
            $('.save-step4').prop('disabled', false);
            $('.prev-step').prop('disabled', false);

            // Hide progress and remove overlay
            $('#percentage_student_completed').hide();
            $('.upload-progress-overlay').remove();

            // Reset opacity
            if (previewSelector) {
                $(previewSelector).css('opacity', '1');
            }

            alert('Failed to get upload URL. Please try again.');
        }
    });
}

function single_file_progress(percentage) {
    if (percentage == 100) {
        in_progress_promises--;
        if (in_progress_promises == 0) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }
    $("#percentage_student_completed").html(`${current_percentage} %`);
    return false;
}

// Fix for all checkbox clickability issues
$(document).ready(function() {
    // List of all checkboxes in parent form
    var checkboxes = ['#check_f_designation', '#student_copy_address', '#copy_address', '#check_m_designation'];

    // Ensure all checkboxes are clickable
    checkboxes.forEach(function(checkboxId) {
        $(checkboxId).css({
            'z-index': '999',
            'position': 'relative',
            'pointer-events': 'auto',
            'opacity': '1',
            '-webkit-appearance': 'checkbox',
            'appearance': 'checkbox'
        });

        // Add click handler with debugging
        $(checkboxId).off('click').on('click', function(e) {
            console.log(checkboxId + ' checkbox clicked:', $(this).is(':checked'));
            // Let the default behavior happen
        });

        // Label click handler for mobile - ensure it triggers the main functions
        if (checkboxId !== '#student_copy_address') {
            // For other checkboxes, enable label click functionality
            $('label[for="' + checkboxId.substring(1) + '"]').off('click').on('click', function(e) {
                var checkbox = $(checkboxId);
                if (e.target !== checkbox[0]) {
                    e.preventDefault();
                    // Toggle checkbox state
                    checkbox.prop('checked', !checkbox.prop('checked'));
                    // Trigger click event to ensure main functions execute
                    checkbox.trigger('click');
                    console.log('Label clicked for ' + checkboxId + ', checkbox now:', checkbox.is(':checked'));
                }
            });
        } else {
            // For student_copy_address, prevent label clicks but allow checkbox clicks
            $('label[for="student_copy_address"]').off('click').on('click', function(e) {
                // Only prevent if clicking on the label text (span), not the checkbox
                if (e.target.tagName.toLowerCase() === 'span' || e.target === this) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Label click prevented for student_copy_address');
                    return false;
                }
                // If clicking on the checkbox, let it work normally
                console.log('Checkbox click allowed for student_copy_address');
            });
        }
    });

    // Add mobile-specific styles for all checkboxes
    $('<style>').prop('type', 'text/css').html(`
        #check_f_designation, #student_copy_address, #copy_address, #check_m_designation {
            z-index: 999 !important;
            position: relative !important;
            pointer-events: auto !important;
            opacity: 1 !important;
            -webkit-appearance: checkbox !important;
            appearance: checkbox !important;
            transform: scale(1.5) !important;
            margin-right: 12px !important;
        }

        @media (max-width: 768px) {
            .mobile-checkbox-label {
                min-height: 48px !important;
                padding: 15px !important;
                background-color: transparent !important;
                border: none !important;
                border-radius: 8px !important;
                margin-bottom: 10px !important;
                display: flex !important;
                align-items: center !important;
            }
            .mobile-checkbox-label:active {
                background-color: rgba(0,0,0,0.05) !important;
                transform: scale(0.98) !important;
            }

            /* Ensure all checkboxes are properly sized on mobile */
            #check_f_designation, #student_copy_address, #copy_address, #check_m_designation {
                min-width: 20px !important;
                min-height: 20px !important;
                transform: scale(1.6) !important;
            }
        }
    `).appendTo('head');
});
</script>

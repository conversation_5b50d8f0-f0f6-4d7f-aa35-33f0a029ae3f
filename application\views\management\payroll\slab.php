<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('management/payroll/'); ?>">Payroll dashboard</a></li>
    <li>Manage Payroll Slabs</li>
</ul>

<?php
// Count how many dynamic columns are shown
$extraCols = 0;
if ($this->settings->payrollColumn('hra')) $extraCols++;
if ($this->settings->payrollColumn('da')) $extraCols++;
$totalCols = 3 + $extraCols; // 3 = #, Name, Action
?>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-6 pl-0">
                    <h3 class="card-title panel_title_new_style_staff" style="margin-top: 3px;">
                        <a class="back_anchor" href="<?php echo site_url('management/payroll/'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Payroll Slabs
                    </h3>
                </div>
                <div class="col-md-6 pl-0 d-flex justify-content-end">
                    <button class="btn btn-primary mr-2" onclick="window.location.href='<?php echo site_url('management/payroll/settings'); ?>'">
                        Slab Settings
                    </button>
                    <a href="#" class="new_circleShape_res" data-toggle="modal" data-target="#payroll_slab_add" style="background-color: #fe970a;">
                        <span class="fa fa-plus" style="font-size: 19px;"></span>
                    </a>
                </div>
            </div>
        </div>
        <div class="panel-body pt-0">
            <table class='table table-bordered' id='list_tab'>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <?php if ($this->settings->payrollColumn('hra')) : ?>
                            <th>HRA</th>
                        <?php endif ?>
                        <?php if ($this->settings->payrollColumn('da')) : ?>
                            <th>DA</th>
                        <?php endif ?>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($slabs)) {
                        $i=1; foreach ($slabs as $key => $slab) { ?>
                            <tr>
                                <td><?= $i++ ?></td>
                                <td><?= $slab->slab_name ?></td>
                                <?php if ($this->settings->payrollColumn('hra')) : ?>
                                <td><?= $slab->hra ?></td>
                                <?php endif ?>
                                <?php if ($this->settings->payrollColumn('da')) : ?>
                                <td><?= $slab->da ?></td>
                                <?php endif ?>
                                <td><a href="#" onclick="view_slab_byId(<?= $slab->id ?>)" data-target="#view_slab_id"
                                        data-toggle="modal" id=" " class="btn btn-primary">View Details</a>
                                    <a href="#" onclick="edit_slab_byId(<?= $slab->id ?>)" data-target="#edit_slab_byId_model"
                                        data-toggle="modal" id=" " class="btn btn-secondary" style="margin: 2px;">Edit</a>
                                    <a href="#" onclick="delete_slab_byId(<?= $slab->id ?>)" data-target=" " data-toggle="modal"
                                        id=" " class="btn btn-danger">Delete</a>
                                </td>
                            </tr>
                        <?php }
                    } else { ?>
                        <tr>
                            <td colspan="<?= $totalCols ?>" class="text-center">No Slabs Found</td>
                        </tr>
                    <?php }?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style type="text/css">
.new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}
</style>

<div class="modal fade" id="payroll_slab_add" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="width:80%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Add Payroll Slab</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>   
            <div class="modal-body">
                <form enctype="multipart/form-data" id="addPayrollSlab" data-parsley-validate method="post" action="<?php echo site_url('management/payroll/save_settings') ?>">
                <!-- <div class="row"> -->
                    <div class="form-horizontal" style="width: 100%">
                        <div class="form-group">
                            <label class="col-md-2 control-label">Slab Name <font color="red">*</font></label>
                            <div class="col-md-10">
                                <input name="slab_name" type="text" placeholder="Enter Slab Name" class="form-control input-md" data-parsley-error-message="Cannot be empty." required="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">Basic Mode <font color="red">*</font></label>
                            <div class="col-md-10">
                                <select class="form-control" required="" id="basic_mode" name="basic_mode">
                                    <option value="">Select Mode</option>
                                    <option value="1">Basic Salary in Manual</option>
                                    <option value="2">Basic Salary in (%)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group" id="basic_salary_percentage_dispaly" style="display: none;">
                            <label class="col-md-2 control-label">Basic Salary (%) <font color="red">*</font></label>
                            <div class="col-md-10">
                                <input name="basic_salary" id="basic_salary" type="text" class="form-control input-md" placeholder="Enter Basic Percentage" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)">
                            </div>
                        </div>
                        <div class="form-group" id="basic_salary_amount_display" style="display: none;">
                            <label class="col-md-2 control-label">Basic Salary (₹) <font color="red">*</font></label>
                            <div class="col-md-10">
                                <input name="basic_salary_amount" id="basic_salary_amount" type="text" class="form-control input-md" placeholder="Enter Basic Amount" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)">
                            </div>
                        </div>

                        <?php if ($this->settings->payrollColumn('hra')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label" required>HRA <font color="red">*</font></label>
                                <div class="col-md-2">
                                    <select class="form-control" name="hra_algo">
                                        <option value="percentage">(%)</option>
                                        <option value="amount">Amount</option>
                                    </select>
                                </div>
                                <div class="col-md-8">
                                    <input name="hra" type="text" class="form-control input-md" placeholder="Enter HRA" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)">
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('da')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label" required>DA <font color="red">*</font></label>

                                <div class="col-md-2">
                                    <select class="form-control" name="da_algo">
                                        <option value="percentage">(%)</option>
                                        <option value="amount">Amount</option>
                                    </select>
                                </div>

                                <div class="col-md-8">
                                    <input name="da" type="text" class="form-control input-md" placeholder="Enter DA" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)">
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('esi')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label">ESI <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <select name="esi" required id="" class="form-control">
                                        <option value="">--Select ESI Type--</option>
                                        <option value="1">Rule 1</option>
                                        <option value="2">Rule 2</option>
                                        <option value="3">Rule 3</option>
                                        <option value="4">Rule 4</option>
                                    </select>
                                    <span class="help-block">Rule 1 - 0 <br> Rule 2 - 0.75 of gross salary  <br> Rule 3 -  1.75 of gross salary <br> Rule 4 - Above 21000 > 0 and 21000 < 0.75 </span>
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('esi_employee_contribution')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label">ESI Employee Contribution <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <select name="esi_employee_contribution" required id="" class="form-control">
                                        <option value="">--Select ESI Type--</option>
                                        <option value="1">0</option>
                                        <option value="3">3.25%</option>
                                    </select>
                                    <span class="help-block">Above 21000 of basic salary '0', less than 21000 3.25%</span>
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('pf')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label">PF <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <select name="pf" required id="" class="form-control">
                                        <option value="">--Select PF Type--</option>
                                        <option value="1">Rule 1</option>
                                        <option value="2">Rule 2</option>
                                        <option value="3">Rule 3</option>
                                        <option value="4">Rule 4</option>
                                        <option value="5">Rule 5</option>
                                        <option value="6">Rule 6</option>
                                        <option value="7">Rule 7</option>
                                    </select>
                                    <span class="help-block">
                                        Rule 1 - If Basic + DA > ₹15,000: ₹1,800, otherwise 12% of Basic salary. <br>
                                        Rule 2 - If Basic + DA > ₹15,000: 12%, otherwise 12% of Basic salary. <br>
                                        Rule 3 - No contribution (₹0). <br>
                                        Rule 4 - If Basic + DA > ₹25,000: ₹1,800, otherwise 10% of Basic salary. <br>
                                        Rule 5 - Salary PF calculated for present days. (Amount must be added in salary)<br>
                                        Rule 6 - 12% of ₹15,000 if Basic + DA ≥ ₹15,000, otherwise 12% of Basic + DA. <br>
                                        Rule 7 - ₹1,800 if Basic + DA ≥ ₹15,000, otherwise Salary PF for present days. (Amount must be added in salary)
                                    </span>
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('pf_for_employer')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label">EPF <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <select name="pf_for_employer" required id="" class="form-control">
                                        <option value="">--Select EPF Type--</option>
                                        <option value="1">Rule 1</option>
                                        <option value="2">Rule 2</option>
                                        <option value="3">Rule 3</option>
                                        <option value="4">Rule 4</option>
                                        <option value="5">Rule 5</option>
                                        <option value="6">Rule 6</option>
                                        <option value="7">Rule 7</option>
                                    </select>
                                    <span class="help-block">
                                        Rule 1 - If Basic + DA > ₹15,000: ₹1,800, otherwise 12% of Basic salary. <br>
                                        Rule 2 - If Basic + DA > ₹15,000: 12%, otherwise 12% of Basic salary. <br>
                                        Rule 3 - No contribution (₹0). <br>
                                        Rule 4 - If Basic + DA > ₹25,000: ₹1,800, otherwise 10% of Basic salary. <br>
                                        Rule 5 - Salary PF calculated for present days. (Amount must be added in salary)<br>
                                        Rule 6 - 12% of ₹15,000 if Basic + DA ≥ ₹15,000, otherwise 12% of Basic + DA. <br>
                                        Rule 7 - ₹1,800 if Basic + DA ≥ ₹15,000, otherwise Salary PF for present days. (Amount must be added in salary)
                                    </span>
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('gratuity')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label" required>Gratuity <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <select name="gratuity" id="" class="form-control">
                                        <option value="">--Select Gratuity Type--</option>
                                        <option value="1">Monthly</option>
                                        <option value="2">Bulk Amount at Termination</option>
                                    </select>
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('cca')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label" required>CCA <font color="red">*</font></label>
                                <div class="col-md-2">
                                    <select class="form-control" name="cca_algo">
                                        <option value="amount">Amount</option>
                                        <option value="percentage">(%)</option>
                                    </select>
                                </div>
                                <div class="col-md-8">
                                    <input name="cca" type="text" class="form-control input-md" placeholder="Enter CCA" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)">
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('lta')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label">LTA <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <div class="col-md-2 pl-0">
                                    <select class="form-control" name="lta_rules">
                                        <option value="1">Rule 1</option>
                                        <option value="2">Rule 2</option>
                                        <option value="3">Rule 3</option>
                                        <option value="5">Rule 4</option>
                                    </select>
                                    
                                </div>
                                    <div class="col-md-2">
                                    <select class="form-control" name="lta_slab">
                                        <option value="amount">Amount</option>
                                        <option value="percentage">(%)</option>
                                    </select>
                                </div>
                                <div class="col-md-8">
                                    <input type="text" placeholder="Enter Amount" required name="lta" plaecholder="Enter LTA" class="form-control" oninput="validateAlphabets(this)">
                                </div>
                                <span class="help-block"><br> <br> Rule 1 - 0 <br> Rule 2 - > 35000, of basic salary <br> Rule 3 -  >=27500 CTC - 8.33% on basic, 27500 CTC - 0 <br> Rule 4 - basic salary >= 27500 - basic salary * 8.33%</span>
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('medical_allowance')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label">Medical Allowance <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <div class="col-md-2 pl-0">
                                    <select class="form-control" name="medical_allowance_rules">
                                        <option value="1">Rule 1</option>
                                        <option value="2">Rule 2</option>
                                        <option value="3">Rule 3</option>
                                    </select>
                                    
                                </div>
                                <div class="col-md-10">
                                    <input type="text" placeholder="Enter Amount" required name="medical_allowance" placeholder="Enter Medical Allowance" class="form-control" oninput="validateAlphabets(this)">
                                </div>
                                <span class="help-block"><br> <br> Rule 1 - 0 <br> Rule 2 - > 20,000 of basic salary <br> Rule 3 - >=20K CTC - 1250, <20K CTC - 0 </span>
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('conveyance')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label" required>Conveyance <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <input name="conveyance" id="conveyance" type="text" class="form-control input-md" placeholder="Enter Convenyance" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)" placeholder="Enter Conveyance">
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('transport_allowance')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label">Transport Allowance <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <input name="transport_allowance" type="text" class="form-control input-md" placeholder="Enter Transport Allowance" oninput="validateAlphabets(this)" data-parsley-error-message="Cannot be empty." required="">
                                </div>
                            </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('slab_variable_pay')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label" required>Variable Pay <font color="red">*</font></label>
                                <div class="col-md-2">
                                    <select class="form-control" name="hra_algo">
                                        <option value="percentage">(%)</option>
                                        <!-- <option value="amount">Amount</option> -->
                                    </select>
                                </div>
                                <div class="col-md-8">
                                    <input name="slab_variable_pay" id="slab_variable_pay" type="text" class="form-control input-md" placeholder="Enter Variable Pay" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)">
                                </div>
                            </div>
                        <?php endif ?>
                    </div>
                <!-- </div> -->
                </form>
            </div>
            <div class="modal-footer" style="text-align:right;">
                <button id="submitBtn" name="SuBmit" onclick="add_payrool_submit()" type="button" class="btn btn-primary">Add</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="view_slab_id" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
        <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
            <h4 class="modal-title">View Payroll Slab </h4>
        </div>
            <div class="modal-body" style="height:450px; overflow: scroll;">
            <div id="slab_detail_byId">
            </div>
            </div>
            <div class="modal-footer">
            <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="edit_slab_byId_model" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="width:80%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Edit Payroll Slab</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" data-parsley-validate="" enctype="multipart/form-data" id="editPayrollSlab" method="post">
                <!-- <div class="row"> -->
                    <div class="form-horizontal" style="width: 100%">
                        <input type="hidden" name="edit_slab_id" id="edit_slab_id">
                        <div class="form-group">
                            <label class="col-md-2 control-label">Slab Name <font color="red">*</font></label>
                            <div class="col-md-10">
                                <input name="edit_slab_name" id="edit_slab_name" placeholder="Enter Slab Name" type="text" class="form-control input-md" data-parsley-error-message="Cannot be empty." required="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">Basic Mode <font color="red">*</font></label>
                            <div class="col-md-10">
                                <select class="form-control" required="" id="edit_basic_mode" name="edit_basic_mode">
                                    <option value="">Select Mode</option>
                                    <option value="1">Basic Salary in Manual</option>
                                    <option value="2">Basic Salary in (%)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group" id="edit_basic_salary_precentage_dispaly" name="edit_basic_salary_precentage_dispaly" style="display: none;">
                            <label class="col-md-2 control-label">Basic Salary (%) <font color="red">*</font></label>
                            <div class="col-md-10">
                                <input name="edit_basic_salary" id="edit_basic_salary" oninput="validateAlphabets(this)" type="text" placeholder="Enter Basic Percentage" class="form-control input-md" data-parsley-error-message="Cannot be empty." required="">
                            </div>
                        </div>

                        <div class="form-group" id="edit_basic_salary_amount_display" name="edit_basic_salary_amount_display" style="display: none;">
                            <label class="col-md-2 control-label">Basic Salary (₹) <font color="red">*</font></label>
                            <div class="col-md-10">
                                <input name="edit_basic_salary_amount" oninput="validateAlphabets(this)" id="edit_basic_salary_amount" type="text" placeholder="Enter Basic Amount" class="form-control input-md" data-parsley-error-message="Cannot be empty." required="">
                            </div>
                        </div>

                        <?php if ($this->settings->payrollColumn('hra')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">HRA <font color="red">*</font></label>
                            <div class="col-md-2">
                                <select class="form-control" id="edit_hra_algo" name="edit_hra_algo" required>
                                    <option value="percentage">(%)</option>
                                    <option value="amount">Amount</option>
                                </select>
                            </div>
                            <div class="col-md-8">
                                <input name="edit_hra" id="edit_hra" type="text" class="form-control input-md" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)" placeholder="Enter HRA">
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('da')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">DA <font color="red">*</font></label>
                            <div class="col-md-2">
                                <select class="form-control" id="edit_da_algo" name="edit_da_algo" required>
                                    <option value="percentage">(%)</option>
                                    <option value="amount">Amount</option>
                                </select>
                            </div>
                            <div class="col-md-8">
                                <input name="edit_da" type="text" id="edit_da" class="form-control input-md" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)" placeholder="Enter DA">
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('esi')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">ESI <font color="red">*</font></label>
                            <div class="col-md-10">
                                <select name="edit_esi" required id="edit_esi" class="form-control">
                                    <option value="0">--Select ESI Type--</option>
                                    <option value="1">Rule 1</option>
                                    <option value="2">Rule 2</option>
                                    <option value="3">Rule 3</option>
                                    <option value="4">Rule 4</option>
                                </select>
                                <span class="help-block">
                                    Rule 1 - 0 <br> 
                                    Rule 2 - 0.75 of gross salary <br> 
                                    Rule 3 - 1.75 of gross salary <br> 
                                    Rule 4 - Above 21000 > 0 and 21000 < 0.75 
                                </span>
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('esi_employee_contribution')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">ESI Employee Contribution <font color="red">*
                                </font></label>
                            <div class="col-md-10">
                                <select name="edit_esi_employee_contribution" required id="edit_esi_employee_contribution" class="form-control">
                                    <option value="0">--Select ESI Type--</option>
                                    <option value="1">0</option>
                                    <option value="3">3.25%</option>
                                </select>
                                <span class="help-block">Above 21000 of basic salary '0', less than 21000 3.25%</span>
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('pf')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">PF <font color="red">*</font></label>
                            <div class="col-md-10">
                                <select name="edit_pf" required id="edit_pf" class="form-control">
                                    <option value="">--Select PF Type--</option>
                                    <option value="1">Rule 1</option>
                                    <option value="2">Rule 2</option>
                                    <option value="3">Rule 3</option>
                                    <option value="4">Rule 4</option>
                                    <option value="5">Rule 5</option>
                                    <option value="6">Rule 6</option>
                                    <option value="7">Rule 7</option>
                                </select>
                                <span class="help-block">
                                    Rule 1 - If Basic + DA > ₹15,000: ₹1,800, otherwise 12% of Basic salary. <br>
                                    Rule 2 - If Basic + DA > ₹15,000: 12%, otherwise 12% of Basic salary. <br>
                                    Rule 3 - No contribution (₹0). <br>
                                    Rule 4 - If Basic + DA > ₹25,000: ₹1,800, otherwise 10% of Basic salary. <br>
                                    Rule 5 - Salary PF calculated for present days. (Amount must be added in salary)<br>
                                    Rule 6 - 12% of ₹15,000 if Basic + DA ≥ ₹15,000, otherwise 12% of Basic + DA.
                                    <br>
                                    Rule 7 - ₹1,800 if Basic + DA ≥ ₹15,000, otherwise Salary PF for present days. (Amount must be added in salary)
                                </span>
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('pf_for_employer')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">EPF <font color="red">*</font></label>
                            <div class="col-md-10">
                                <select name="edit_pf_for_employer" id="edit_pf_for_employer" required id=""
                                    class="form-control">
                                    <option value="">--Select EPF Type--</option>
                                    <option value="1">Rule 1</option>
                                    <option value="2">Rule 2</option>
                                    <option value="3">Rule 3</option>
                                    <option value="4">Rule 4</option>
                                    <option value="5">Rule 5</option>
                                    <option value="6">Rule 6</option>
                                    <option value="7">Rule 7</option>
                                </select>
                                <span class="help-block">
                                    Rule 1 - If Basic + DA > ₹15,000: ₹1,800, otherwise 12% of Basic salary. <br>
                                    Rule 2 - If Basic + DA > ₹15,000: 12%, otherwise 12% of Basic salary. <br>
                                    Rule 3 - No contribution (₹0). <br>
                                    Rule 4 - If Basic + DA > ₹25,000: ₹1,800, otherwise 10% of Basic salary. <br>
                                    Rule 5 - Salary PF calculated for present days. (Amount must be added in salary)<br>
                                    Rule 6 - 12% of ₹15,000 if Basic + DA ≥ ₹15,000, otherwise 12% of Basic + DA.
                                    <br>
                                    Rule 7 - ₹1,800 if Basic + DA ≥ ₹15,000, otherwise Salary PF for present days. (Amount must be added in salary)
                                </span>
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('gratuity')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">Gratuity <font color="red">*</font></label>
                            <div class="col-md-10">
                                <select name="edit_gratuity" id="edit_gratuity" class="form-control" required>
                                    <option value="">--Select Gratuity Type--</option>
                                    <option value="1">Monthly</option>
                                    <option value="2">Bulk Amount at Termination</option>
                                </select>
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('cca')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">CCA <font color="red">*</font></label>
                            <div class="col-md-2">
                                <select class="form-control" name="edit_cca_algo" id="edit_cca_algo" required>
                                    <option value="amount">Amount</option>
                                    <option value="percentage">(%)</option>
                                </select>
                            </div>
                            <div class="col-md-8">
                                <input name="edit_cca" type="text" id="edit_cca" class="form-control input-md" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)" placeholder="Enter CCA">
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('lta')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">LTA <font color="red">*</font></label>
                            <div class="col-md-10">
                                <div class="col-md-2 pl-0">
                                    <select class="form-control" name="edit_lta_rules" id="edit_lta_rules" required>
                                        <option value="1">Rule 1</option>
                                        <option value="2">Rule 2</option>
                                        <option value="3">Rule 3</option>
                                        <option value="5">Rule 4</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-control" name="edit_lta_slab" id="edit_lta_slab" required>
                                        <option value="amount">Amount</option>
                                        <option value="percentage">(%)</option>
                                    </select>
                                </div>
                                <div class="col-md-8">
                                    <input type="text" placeholder="Enter LTA" required name="edit_lta" id="edit_lta" class="form-control" oninput="validateAlphabets(this)">
                                </div>
                                <span class="help-block"><br> <br> Rule 1 - 0 <br> Rule 2 - > 35000, of basic salary
                                    <br> Rule 3 - >=27500 CTC - 8.33% on basic, 27500 CTC - 0 <br> Rule 4 - basic
                                    salary >= 27500 - basic salary * 8.33%</span>
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('medical_allowance')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">Medical Allowance <font color="red">*</font>
                                </label>
                            <div class="col-md-10">
                                <div class="col-md-2 pl-0">
                                    <select class="form-control" id="edit_medical_allowance_rules" name="edit_medical_allowance_rules" required>
                                        <option value="1">Rule 1</option>
                                        <option value="2">Rule 2</option>
                                        <option value="3">Rule 3</option>
                                    </select>
                                </div>
                                <div class="col-md-10">
                                    <input type="text" placeholder="Enter Medical Amount" required name="edit_medical_allowance" id="edit_medical_allowance" class="form-control" oninput="validateAlphabets(this)">
                                </div>
                                <span class="help-block"><br> <br> Rule 1 - 0 <br> Rule 2 - > 20,000 of basic salary
                                    <br> Rule 3 - >=20K CTC - 1250, <20K CTC - 0 </span>
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('conveyance')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label" required>Conveyance <font color="red">*</font></label>
                            <div class="col-md-10">
                                <input name="edit_conveyance" id="edit_conveyance" type="text" class="form-control input-md" data-parsley-error-message="Cannot be empty." required="" placeholder="Enter Conveyance" oninput="validateAlphabets(this)">
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('transport_allowance')) : ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">Transport Allowance <font color="red">*</font></label>
                            <div class="col-md-10">
                                <input type="text" placeholder="Enter Transpot Allowance" name="edit_transport_allowance" id="edit_transport_allowance" class="form-control" oninput="validateAlphabets(this)">
                            </div>
                        </div>
                        <?php endif ?>

                        <?php if ($this->settings->payrollColumn('slab_variable_pay')) : ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label" required>Variable Pay <font color="red">*</font></label>
                                <div class="col-md-2">
                                    <select class="form-control" name="hra_algo">
                                        <option value="percentage">(%)</option>
                                        <!-- <option value="amount">Amount</option> -->
                                    </select>
                                </div>
                                <div class="col-md-8">
                                    <input name="edit_variable_pay" id="edit_variable_pay" type="text" class="form-control input-md" placeholder="Enter Variable Pay" data-parsley-error-message="Cannot be empty." required="" oninput="validateAlphabets(this)">
                                </div>
                            </div>
                        <?php endif ?>
                    </div>
                    <!-- </div> -->
                </form>
            </div>
            <div class="modal-footer" style="text-align:right;">
                <button class="btn btn-primary" type="button" onclick="update_payroll_submit()">Update</button>
            </div>
        </div>
    </div>
</div>


<script>
    function validateAlphabets(el) {
        el.value = el.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');
    }
    $('#edit_basic_mode').on('change', function() {
        edit_basic_mode = $('#edit_basic_mode').val();
        if (edit_basic_mode == 2) {
            $('#edit_basic_salary_precentage_dispaly').show();
            $('#edit_basic_salary').prop('required', true);
            $('#edit_basic_salary_amount_display').hide();
            $('#edit_basic_salary_amount').prop('required', false);
        } else if(edit_basic_mode == 1) {
            $('#edit_basic_salary_precentage_dispaly').hide();
            $('#edit_basic_salary').prop('required', false);
            $('#edit_basic_salary_amount_display').show();
            $('#edit_basic_salary_amount').prop('required', true);
        } else {
            $('#edit_basic_salary_precentage_dispaly').hide();
            $('#edit_basic_salary').prop('required', false);
            $('#edit_basic_salary_amount_display').hide();
            $('#edit_basic_salary_amount').prop('required', false);
        }
    });
</script>

<script type="text/javascript">
    $('#basic_mode').on('change',function(){
        var basic_mode = $('#basic_mode').val();
        if (basic_mode == 2) {
            $('#basic_salary_percentage_dispaly').show();
            $('#basic_salary').attr('required');
            $('#basic_salary_amount_display').hide();
            $('#basic_salary_amount').removeAttr('required');
        } else if(basic_mode == 1) {
            $('#basic_salary_percentage_dispaly').hide();
            $('#basic_salary').removeAttr('required');
            $('#basic_salary_amount_display').show();
            $('#basic_salary_amount').attr('required');
        } else {
            $('#basic_salary_percentage_dispaly').hide();
            $('#basic_salary').removeAttr('required');
            $('#basic_salary_amount_display').hide();
            $('#basic_salary_amount').removeAttr('required');
        }
    });

    function view_slab_byId(id) {
        // console.log(id);
        $.ajax({
            url: '<?php echo site_url('management/payroll/view_slab_byId'); ?>',
            type: 'post',
            data: {
                'id': id,
            },
            success: function(result) {
                var resData = $.parseJSON(result);
                //   console.log(resData);
                $('#slab_detail_byId').html(construct_view_slab_id(resData));
            }
        });
    }

    function construct_view_slab_id(resData) {
        var basicsalValue = resData.basic_salary;
        var basicsalParts = basicsalValue.split('.');
        var selectedbasicsalValue = basicsalParts[0];

        var hraValue = resData.hra;
        var hraParts = hraValue.split('.');
        var selectedhraValue = hraParts[0];

        var daValue = resData.da;
        var daParts = daValue.split('.');
        var selecteddaValue = daParts[0];

        var basicmode_name = '';
        var esi_name = '';
        var pf_for_employer_name = '';
        var pf_name = '';
        var esi_employee_contribution_name = '';
        var gratuity_name = '';

        if (resData.basic_mode == 1) {
            basicmode_name = 'Basic Salary in Manual';
            basicmode_symbol = '(₹)';
        }else {
            basicmode_name = 'Basic Salary in Percentage';
            basicmode_symbol = '(%)';
        }

        if (resData.esi == 1.00) {
            esi_name = 'Rule 1 - 0';
        }else if (resData.esi == 2.00) {
            esi_name = 'Rule 2 - 0.75 of gross salary';
        }else if (resData.esi == 3.00) {
            esi_name = 'Rule 3 - 1.75 of gross salary';
        }else if (resData.esi == 4.00) {
            esi_name = 'Rule 4 - Above 21000 > 0 and 21000 < 0.75';
        }

        if (resData.pf_for_employer == 1.00) {
            pf_for_employer_name = 'Rule 1 - 15000 > 1800, 15000 < 12% of basic salary';
        }else if (resData.pf_for_employer == 2.00) {
            pf_for_employer_name = 'Rule 2 - 15000 > 12%, 15000 < 12% of basic salary';
        }else if (resData.pf_for_employer == 3.00) {
            pf_for_employer_name = 'Rule 3 - 0';
        }else if (resData.pf_for_employer == 4.00) {
            pf_for_employer_name = 'Rule 4 - 25000 > 1800, 25000 < 10% of basic salary';
        }else if (resData.pf_for_employer == 5.00) {
            pf_for_employer_name = 'Rule 5 - Salary PF calculated for present days. (Amount must be added in salary)';
        }else if (resData.pf_for_employer == 6.00) {
            pf_for_employer_name = 'Rule 6 - 25000 > 1800, 25000 < 10% of basic salary';
        }else if (resData.pf_for_employer == 7.00) {
            pf_for_employer_name = 'Rule 7 - ₹1,800 if Basic + DA ≥ ₹15,000, otherwise Salary PF for present days. (Amount must be added in salary)';
        }else{
            pf_for_employer_name = 'No PF for Employer';
        }

        if (resData.pf == 1.00) {
            pf_name = 'Rule 1 - 15000 > 1800, 15000 < 12% of basic salary';
        }else if (resData.pf == 2.00) {
            pf_name = 'Rule 2 - 15000 > 12%, 15000 < 12% of basic salary';
        }else if (resData.pf == 3.00) {
            pf_name = 'Rule 3 - 0';
        }else if (resData.pf == 4.00) {
            pf_name = 'Rule 4 - 25000 > 1800, 25000 < 10% of basic salary';
        }else if (resData.pf == 5.00) {
            pf_name = 'Rule 5 - Salary PF calculated for present days. (Amount must be added in salary)';
        }else if (resData.pf == 6.00) {
            pf_name = 'Rule 6 - 25000 > 1800, 25000 < 10% of basic salary';
        }else if (resData.pf == 7.00) {
            pf_name = 'Rule 7 - ₹1,800 if Basic + DA ≥ ₹15,000, otherwise Salary PF for present days. (Amount must be added in salary)';
        }else{
            pf_name = 'No PF';
        }

        if (resData.esi_employee_contribution == 1.00) {
            esi_employee_contribution_name = '0';
        }else {
            esi_employee_contribution_name = '3.25%'; 
        }

        if (resData.gratuity == 1.00) {
            gratuity_name = 'Monthly';
        }else {
            gratuity_name = 'Bank Amount at Termination'; 
        }

        var viewhtml = '';
        viewhtml += '<table class="table table-bordered" width="100%">';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  Name  </b> </td>';
        viewhtml += '<td>'+resData.slab_name+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  Basic Mode  </b> </td>';
        viewhtml += '<td>'+basicmode_name+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  Basic Salary ' + basicmode_symbol + '  </b> </td>';
        viewhtml += '<td>'+selectedbasicsalValue+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  HRA  </b> </td>';
        viewhtml += '<td>'+selectedhraValue+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  DA  </b> </td>';
        viewhtml += '<td>'+selecteddaValue+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>ESI </b> </td>';
        viewhtml += '<td>'+esi_name+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>ESI Employee Contribution </b> </td>';
        viewhtml += '<td>'+esi_employee_contribution_name+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  PF  </b> </td>';
        viewhtml += '<td>'+pf_name+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  EPF </b> </td>';
        viewhtml += '<td>'+pf_for_employer_name+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  Gratuity  </b> </td>';
        viewhtml += '<td>'+gratuity_name+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  CCA  </b> </td>';
        viewhtml += '<td>'+resData.cca+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  LTA  </b> </td>';
        viewhtml += '<td>'+resData.lta+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b>  Medical Allowance  </b> </td>';
        viewhtml += '<td>'+resData.medical_allowance+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b> Conveyance</b> </td>';
        viewhtml += '<td>'+resData.conveyance+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '<tr>';
        viewhtml += '<td><b> Transport Allowance </b> </td>';
        viewhtml += '<td>'+resData.transport_allowance+ '</td>';
        viewhtml += '</tr>';

        viewhtml += '</table>';

        return viewhtml;
    }

    function edit_slab_byId(id) {

        $('#edit_slab_id').val('');
        $('#edit_slab_name').val('');
        $('#edit_basic_mode').val('');
        $('#edit_basic_salary').val('');
        $('#edit_hra').val('');
        $('#edit_hra_algo').val('');
        $('#edit_da').val('');
        $('#edit_da_algo').val('');
        $('#edit_esi').val('');
        $('#edit_pf').val('');
        $('#edit_esi_employee_contribution').val('');
        $('#edit_gratuity').val('');
        $('#edit_cca_algo').val('');
        $('#edit_cca').val('');
        $('#edit_lta').val('');
        $('#edit_lta_rules').val('');
        $('#edit_lta_slab').val('');
        $('#edit_transport_allowance').val('');
        $('#edit_medical_allowance').val('');
        $('#edit_conveyance').val('');
        $('#edit_pf_for_employer').val('');
        $('#edit_variable_pay').val('');

        $.ajax({
            url: '<?php echo site_url('management/payroll/edit_slab_byId'); ?>',
            type: 'post',
            data: {'id':id} ,
            success: function(data) {
                parsed_data = JSON.parse(data);
                // console.log(parsed_data);

                $("#edit_slab_id").val(parsed_data.id);
                $("#edit_slab_name").val(parsed_data.slab_name);
                $("#edit_basic_mode").val(parsed_data.basic_mode);

                var basicsalValue = parsed_data.basic_salary;
                var basicsalParts = basicsalValue.split('.');
                var selectedbasicsalValue = basicsalParts[0];

                if (parsed_data.basic_mode == 2) {
                    $('#edit_basic_salary_precentage_dispaly').show();
                    $('#edit_basic_salary').prop('required', true);
                    $("#edit_basic_salary").val(selectedbasicsalValue)
                    $('#edit_basic_salary_amount_display').hide();
                    $('#edit_basic_salary_amount').prop('required', false);
                } else if(parsed_data.basic_mode == 1) {
                    $('#edit_basic_salary_precentage_dispaly').hide();
                    $('#edit_basic_salary').prop('required', false);
                    $('#edit_basic_salary_amount_display').show();
                    $('#edit_basic_salary_amount').prop('required', true);
                    $("#edit_basic_salary_amount").val(selectedbasicsalValue)
                } else {
                    $('#edit_basic_salary_precentage_dispaly').hide();
                    $('#edit_basic_salary').prop('required', false);
                    $('#edit_basic_salary_amount_display').hide();
                    $('#edit_basic_salary_amount').prop('required', false);
                }

                var hraValue = parsed_data.hra;
                var hraParts = hraValue.split('.');
                var selectedhraValue = hraParts[0];
                $("#edit_hra").val(selectedhraValue)

                $("#edit_hra_algo").val(parsed_data.hra_algo)

                var daValue = parsed_data.da;
                var daParts = daValue.split('.');
                var selecteddaValue = daParts[0];
                $("#edit_da").val(selecteddaValue)

                $("#edit_da_algo").val(parsed_data.da_algo)

                var esiValue = parsed_data.esi;
                var esiParts = esiValue.split('.');
                var selectedesiValue = esiParts[0];
                $("#edit_esi").val(selectedesiValue)

                var pfValue = parsed_data.pf;
                var pfParts = pfValue.split('.');
                var selectedpfValue = pfParts[0];
                $("#edit_pf").val(selectedpfValue)

                var esiempValue = parsed_data.esi_employee_contribution;
                var esiempParts = esiempValue.split('.');
                var selectedesiempValue = esiempParts[0];
                $("#edit_esi_employee_contribution").val(selectedesiempValue)

                var gratuityValue = parsed_data.gratuity;
                var gratuityParts = gratuityValue.split('.');
                var selectedgratuityValue = gratuityParts[0];
                $("#edit_gratuity").val(selectedgratuityValue)

                $("#edit_cca_algo").val(parsed_data.cca_algo)
                $("#edit_cca").val(parsed_data.cca)

                
                $("#edit_transport_allowance").val(parsed_data.transport_allowance)

                $("#edit_lta").val(parsed_data.lta)
                $("#edit_lta_rules").val(parsed_data.lta_rules)
                $("#edit_lta_slab").val(parsed_data.lta_slab)

                $("#edit_medical_allowance").val(parsed_data.medical_allowance);
                $("#edit_medical_allowance_rules").val(parsed_data.medical_allowance_rules);
                $('#edit_conveyance').val(parsed_data.conveyance);
                $('#edit_variable_pay').val(parsed_data.slab_variable_pay);

                var pfforemployer = parsed_data.pf_for_employer;
                var pfforemployerParts = pfforemployer.split('.');
                var selectedpfforemployerValue = pfforemployerParts[0];
                $("#edit_pf_for_employer").val(selectedpfforemployerValue)
            },
        });
    }

    function delete_slab_byId(id){
        bootbox.confirm({
            title: "Do you want to Delete the Slab?",
            message: `<h5><center>Are you sure you want to delete?</center></h5>`,
            style: 'width=50%',
            size: 'small',
            centerVertical: true,
            className:'widthadjust',
            width: '50%',
            buttons: {
                confirm: {
                    label: "<i class='fa fa-check'></i> Yes",
                    className: 'btn-success'
                },
                cancel: {
                    label: "<i class='fa fa-times'></i> No",
                    className: 'btn-danger'
                }
            },
            callback: function (result) {
                if (result) {
                    $.ajax({
                        url: '<?php echo site_url('management/payroll/delete_slab_id'); ?>',
                        type: 'post',
                        data: {'id':id} ,
                        success: function(data) {
                            parsed_data = $.parseJSON(data);
                            Swal.fire({
                                // position: "top-end",
                                icon: "success",
                                title: "Slab Deleted Sucessfully",
                                showConfirmButton: false,
                                timer: 1500
                            });
                            setTimeout(()=>{
                                location.reload();
                            },1503);
                        },
                    });
                }
            },
        }).find("div.modal-content").addClass("confirmWidth");
    }

    function update_payroll_submit() {
        var $form = $('#editPayrollSlab');
        if ($form.parsley().validate()) {
            var $form = $('#editPayrollSlab')[0];
            var formData = new FormData($form);
            $.ajax({
                url: '<?php echo site_url('management/payroll/update_slab_byId'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                success: function(data) {
                    // return false;
                    // console.log(data);
                    if (data) {
                        Swal.fire({
                            // position: "top-end",
                            icon: "success",
                            title: "Slab Upadated Sucessfully",
                            showConfirmButton: false,
                            timer: 1500
                        }).then(()=>{
                            location.reload();
                        });
                    }
                    else{
                        
                        Swal.fire({
                            // position: "top-end",
                            icon: "error",
                            title: "Slab Upadation Failed",
                            showConfirmButton: false,
                            timer: 1500
                        });
                    }
                },
                error: function(err) {
                    console.log(err);
                }
            });
        }
    }

    function add_payrool_submit() {
        if($('#addPayrollSlab').parsley().validate()) {
            $("#submitBtn").attr('disabled','disabled').html('Please wait...');
            Swal.fire({
                // position: "top-end",
                icon: "success",
                title: "Slab Added Sucessfully",
                showConfirmButton: false,
                timer: 1500
            });
            $("#addPayrollSlab").submit();
        }
    }

</script>

<style type="text/css">
.widthadjust {
    width: 50% !important;
    margin-left: 25%;
}
</style>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<input type="hidden" id="adId" name="ad_id">
<!-- <h4 style="margin-bottom: 14px; background-color: #f5f5f5; height: 50px; width: 100%; padding: 15px 5px;" class=""><span style="padding: auto;">Guardian Details</span></h4> -->

<div class="row">
    <div class="col-md-6 mb-4">
        <label class="form-label" for="f_name">Name &nbsp;
            <?php if($required_fields['g_name']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Enter Guardian Name" id="g_name"
            <?php if(!empty($final_preview->g_name)) echo 'value="'.$final_preview->g_name.'"' ?> name="g_name"
            type="text" <?php echo $required_fields['g_name']['required'] ?> class="form-control input-md"
            data-parsley-error-message="Cannot be empty, Only alphabets" data-parsley-pattern="^[a-zA-Z. ]+$"
            data-parsley-minlength="2">
        <span class="help-block">As per Aadhar card/Passport</span>
    </div>
    <div class="col-md-6 mb-4">
        <label class="form-label">Mobile Number &nbsp;
            <?php if($required_fields['g_mobile_no']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="d-flex gap-2">
            <div style="flex: 0 0 auto; min-width: 80px; position: relative;">
                <?php 
                $array = array();
                foreach ($this->config->item('country_codes') as $key => $code) {
                    $array[$code] =  $code;
                }
                echo form_dropdown("g_country_code", $array, set_value("g_country_code",$final_preview->g_country_code), "id='g_country_code' ".$required_fields['g_country_code']['required']." class='form-control'");
            ?>
                <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                    <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                </div>
            </div>
            <div style="flex: 1;">
                <input type="text" data-parsley-error-message="Enter valid phone number" data-parsley-type="number"
                    minlength="10" maxlength="10" data-parsley-maxlength="10"
                    <?php echo $required_fields['g_mobile_no']['required'] ?> placeholder="Enter Mobile Number"
                    <?php if(!empty($final_preview->g_mobile_no)) echo 'value="'.$final_preview->g_mobile_no.'"' ?>
                    id="g_mobile_no" data-type="guardian" name="g_mobile_no" class="form-control input-md">
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="g_email">Email Id &nbsp;
            <?php if($required_fields['g_email_id']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="email" <?php echo $required_fields['g_email_id']['required'] ?>
            data-parsley-error-message="Enter valid email id" placeholder="Enter Email" id="g_email"
            <?php if(!empty($final_preview->g_email_id)) echo 'value="'.$final_preview->g_email_id.'"' ?>
            data-type="g_email" name="g_email" class="form-control input-md">
    </div>
    <?php if(admission_is_enabled($disabled_fields, 'guardian_aadhar')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="guardian_aadhar">Aadhar Card No &nbsp;
            <?php if($required_fields['guardian_aadhar']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" <?php echo $required_fields['guardian_aadhar']['required'] ?>
            data-parsley-error-message="Enter valid number" data-parsley-type="number" placeholder="Enter Aadhar Number"
            <?php if(!empty($final_preview->guardian_aadhar)) echo 'value="'.$final_preview->guardian_aadhar.'"' ?>
            id="guardian_aadhar" name="guardian_aadhar" class="form-control input-md">
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'guardian_mother_tongue')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="guardian_tongue">Mother Tongue &nbsp;
            <?php if($required_fields['guardian_mother_tongue']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <?php if(!empty($final_preview->guardian_mother_tongue)){
                $mother_tongue = $final_preview->guardian_mother_tongue;
            }else{
                $mother_tongue = '';
            } ?>
        <?php 
                $lang = $this->config->item('languages');
                $array = array('' => 'Select Mother Tongue');
                foreach ($lang as $key => $languages) {
                    $array[$languages] =  $languages;
                }

                echo form_dropdown("guardian_mother_tongue", $array, set_value("guardian_mother_tongue", $mother_tongue), "id='father_mother_tongue' ".$required_fields['guardian_mother_tongue']['required']." class='form-control'");
            ?>
            <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'guardian_mother_tongue_other')) :  ?>
    <div id="g_mother_tongue_name" class="col-md-6 mb-4" style="display: none;">
        <label class="form-label" for="f_tongue_others">Others &nbsp;</label>
        <input placeholder="Enter Please Specify"
            <?php if(!empty($final_preview->guardian_mother_tongue_other)) echo 'value="'.$final_preview->guardian_mother_tongue_other.'"' ?>
            id="g_mother_tongue_other" name="guardian_mother_tongue_other" type="text" class="form-control input-md"
            data-parsley-error-message="Cannot be empty" data-parsley-pattern="^[a-zA-Z. ]+$"
            data-parsley-minlength="2">
        <span class="help-block">Please Specify</span>
    </div>
    <?php endif ?>
    <br>
    <?php if(admission_is_enabled($disabled_fields, 'g_addr')) : ?>
<div class="row">
    <!-- Label takes full width -->
    <div class="col-12">
        <label class="form-label" for="g_addr">
            Home Address
            <?php if($required_fields['g_addr']['required']=='required') echo '<font color="red">*</font>' ?>
        </label>
    </div>
    <!-- Fields row: textarea left, other fields right -->
    <div class="col-md-6 mb-4 d-flex flex-column">
        <textarea class="form-control mb-2 flex-grow-1" <?php echo $required_fields['g_addr']['required'] ?> id="g_addr" name="g_addr" rows="7" style="min-height: 160px; height:100%;"
            placeholder="Enter Address"><?php if(!empty($final_preview->g_addr)) echo $final_preview->g_addr ?></textarea>
    </div>
    <div class="col-md-6 mb-4 d-flex flex-column" style="padding-right:0px">
        <div class="row flex-grow-1">
            <div class="col-12 mb-3">
                <input type="text" <?php echo $required_fields['g_area']['required'] ?> id="g_area" placeholder="Enter Area"
                    class="form-control"
                    <?php if(!empty($final_preview->g_area)) echo 'value="'.$final_preview->g_area.'"' ?> name="g_area">
            </div>
            <div class="col-12 mb-3">
                <input type="text" <?php echo $required_fields['g_district']['required'] ?> id="g_district"
                    placeholder="Enter District" class="form-control"
                    <?php if(!empty($final_preview->g_district)) echo 'value="'.$final_preview->g_district.'"' ?>
                    name="g_district">
            </div>
            <div class="row  g-0">
                <div class="col-md-4 mb-3 position-relative">
                    <?php 
                    $array = array(''=>'Country');
                    foreach ($this->config->item('country') as $key => $nation) {
                        $array[$nation] =  $nation;
                    }
                    echo form_dropdown("g_county", $array, set_value("g_county",$final_preview->g_county), 
                        "id='g_county' ".$required_fields['g_county']['required']." class='form-control'");
                    ?>
                    <div style="position: absolute; right: 20px; top: 57%; transform: translateY(-50%); pointer-events: none;">
                        <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                    </div>
                </div>
                <div class="col-md-4 mb-3 position-relative">
                    <input type="text" id="g_state" <?php echo $required_fields['g_state']['required'] ?>
                        placeholder="Enter State" class="form-control"
                        <?php if(!empty($final_preview->g_state)) echo 'value="'.$final_preview->g_state.'"' ?> name="g_state">
                </div>
                
                <div class="col-md-4 mb-3">
                    <input id="g_pincode" name="g_pincode" <?php echo $required_fields['g_pincode']['required'] ?>
                        placeholder="Pincode" type="text"
                        <?php if(!empty($final_preview->g_pincode)) echo 'value="'.$final_preview->g_pincode.'"' ?>
                        class="form-control" data-parsley-type="digits" data-parsley-length="[3, 10]"
                        data-parsley-error-message="Enter a valid pin-code, only digits">
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'g_qualification')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="g_qualification">Qualification &nbsp;
            <?php if($required_fields['g_qualification']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" <?php echo $required_fields['g_qualification']['required'] ?>
            placeholder="Enter Qualification"
            <?php if(!empty($final_preview->g_qualification)) echo 'value="'.$final_preview->g_qualification.'"' ?>
            id="g_qualification" data-type="g_qualification" name="g_qualification" class="form-control">
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'g_profession')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="g_profession">Profession &nbsp;
            <?php if($required_fields['g_profession']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input type="text" placeholder="Enter Profession" <?php echo $required_fields['g_profession']['required'] ?>
            <?php if(!empty($final_preview->g_profession)) echo 'value="'.$final_preview->g_profession.'"' ?>
            id="g_profession" data-type="g_profession" name="g_profession" class="form-control">
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'g_position')) :  ?>
    <div class="col-md-12 mb-4" style="display: block;">
        <div class="col-md-8 p-0">
            <label for="check_g_designation" class="mobile-checkbox-label" style="cursor: pointer; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: flex; align-items: center; padding: 15px 10px; margin: 0; border-radius: 8px; transition: background-color 0.2s ease; -webkit-tap-highlight-color: rgba(0,0,0,0.1); position: relative;">
                <input type="checkbox" id="check_g_designation" value="" style="margin-right: 12px; transform: scale(1.5); cursor: pointer; min-width: 20px; min-height: 20px; z-index: 10; position: relative;">
                <span style="margin-left: 8px; font-size: 16px; line-height: 1.4; pointer-events: none;">Not working?</span>
            </label>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <label class="form-label fw-semibold" for="g_position">
            Designation &nbsp;
            <?php if($required_fields['g_position']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>

        <input type="text" class="form-control" id="g_position" name="g_position" data-type="g_position"
            placeholder="Enter Designation" <?= $required_fields['g_position']['required'] ?>
            <?= !empty($final_preview->g_position) ? 'value="'.$final_preview->g_position.'"' : '' ?>>
    </div>
    <?php endif; ?>
    <?php if(admission_is_enabled($disabled_fields, 'g_company_name')) : ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="g_company">Company Name
            <?php if($required_fields['g_company_name']['required']=='required') echo '<font color="red">*</font>' ?>
        </label>
        <input type="text" <?php echo $required_fields['g_company_name']['required'] ?> placeholder="Enter Company Name"
            <?php if(!empty($final_preview->g_company_name)) echo 'value="'.$final_preview->g_company_name.'"' ?>
            id="g_company" data-type="g_company" name="g_company" class="form-control">
    </div>
    <?php endif ?>


    <div class="row">
    <!-- Full-width Label -->
        <div class="col-12">
            <label class="form-label" for="g_company_addr">
                Company Address
                <?php if($required_fields['g_company_addr']['required']=='required') echo '<font color="red">*</font>' ?>
            </label>
        </div>

    <!-- Address Textarea -->
        <div class="col-md-6 mb-4 d-flex flex-column">
            <textarea class="form-control" id="g_company_addr" name="g_company_addr" rows="7"
                placeholder="Enter Address"><?php if(!empty($final_preview->g_company_addr)) echo $final_preview->g_company_addr ?></textarea>
        </div>

    <!-- Area -->
    <div class="col-md-6 mb-4 d-flex flex-column">
    <div class="row flex-grow-1">
        <div class="col-12 mb-3">
            <input type="text" id="g_company_area" <?php echo $required_fields['g_company_area']['required'] ?>
                placeholder="Enter Area" class="form-control"
                <?php if(!empty($final_preview->g_company_area)) echo 'value="'.$final_preview->g_company_area.'"' ?>
                name="g_company_area">
        </div>

    <!-- District -->
        <div class="col-12 mb-3">
            <input type="text" id="g_company_district" <?php echo $required_fields['g_company_district']['required'] ?>
                placeholder="Enter District" class="form-control"
                <?php if(!empty($final_preview->g_company_district)) echo 'value="'.$final_preview->g_company_district.'"' ?>
                name="g_company_district">
        </div>

        <div class="row g-0">
            <div class="col-md-4  mb-3 position-relative">
                <?php 
                $array = array(''=>'Country');
                foreach ($this->config->item('country') as $key => $nation) {
                    $array[$nation] =  $nation;
                }
                echo form_dropdown("g_company_county", $array, set_value("g_company_county",$final_preview->g_company_county),
                    "id='g_company_county' ".$required_fields['g_company_county']['required']." class='form-control'");
                ?>
                <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                    <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                </div>
            </div>

        <!-- State -->
            <div class="col-md-4  mb-3 position-relative">
                <input type="text" id="g_company_state" <?php echo $required_fields['g_company_state']['required'] ?>
                    placeholder="Enter State" class="form-control"
                    <?php if(!empty($final_preview->g_company_state)) echo 'value="'.$final_preview->g_company_state.'"' ?>
                    name="g_company_state">
            </div>

        <!-- Pincode -->
            <div class="col-md-4 mb-3">
                <input id="g_company_pincode" <?php echo $required_fields['g_company_pincode']['required'] ?>
                    name="g_company_pincode" placeholder="Pincode" type="text"
                    <?php if(!empty($final_preview->g_company_pincode)) echo 'value="'.$final_preview->g_company_pincode.'"' ?>
                    class="form-control" data-parsley-type="digits" data-parsley-length="[3, 10]"
                    data-parsley-error-message="Enter a valid pin-code, only digits">
            </div>
        </div>
    </div>
</div>
    </div>

    <?php if(admission_is_enabled($disabled_fields, 'g_office_ph')) :  ?>
        <div class="col-md-6 mb-4">
            <label class="form-label">Office and Residential Numbers</label>
            <div class="d-flex flex-column flex-md-row">
                <div class="flex-fill pe-md-2 mb-2 mb-md-0">
                <input type="text" id="g_office_ph" name="g_office_ph"
                        placeholder="Office Number"
                        class="form-control" value="">
                </div>
                <div class="flex-fill ps-md-2">
                <input type="text" id="g_res_ph" name="g_res_ph"
                        placeholder="Residential Number"
                        class="form-control" value="">
                </div>
            </div>
        </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'g_annual_gross_income')) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="g_annual_income">Gross Annual Income (INR) &nbsp;
            <?php if($required_fields['g_annual_gross_income']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input id="g_annual_income" name="g_annual_income"
            <?php echo $required_fields['g_annual_gross_income']['required'] ?> type="text"
            <?php if(!empty($final_preview->g_annual_gross_income)) echo 'value="'.$final_preview->g_annual_gross_income.'"' ?>
            placeholder="Enter Annual Income" class="form-control"
            data-parsley-error-message="Enter valid currency value" data-parsley-pattern="^[0-9]\d*(\.\d+)?$">
        <span class="col-sm-3 control-label">
        </span>
    </div>
    <?php endif ?>
    <div class="row">
        <?php if(admission_is_enabled($disabled_fields, 'g_photo_uri')) :  ?>
        <div class="col-md-6 mb-4">
            <label class="form-label fw-semibold mb-2">Guardian Photo &nbsp;
                <?php if($required_fields['g_photo_uri']['required']=='required') echo '<font color="red">*</font>'; ?>
            </label>

            <div class="border border-dashed rounded p-4 text-center position-relative"
                style="cursor: pointer; background-color: #fafafa;"
                onclick="document.getElementById('fileupload_g').click();">

                <?php if(!empty($final_preview->g_photo_uri)) { ?>
                <img src="<?= $this->filemanager->getFilePath($final_preview->g_photo_uri) ?>" alt="Preview"
                    id="previewing_g" class="mb-2" style="max-height: 80px; max-width: 150px;margin: 0 auto;" />
                <?php } else { ?>
                <img src="" alt="Preview" id="previewing_g" class="mb-2"
                    style="max-height: 80px; max-width: 150px;margin: 0 auto;display:none;" />
                <div id="photo_placeholder_g"
                    style="max-height: 100px; max-width: 150px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                    <?php $this->load->view('svg_icons/empty_image.svg'); ?>
                </div>

                <?php } ?>

                <div class="d-flex flex-column align-items-center">
                    <i class="bi bi-upload" style="font-size: 24px;"></i>
                    <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                    <small class="text-muted">JPEG, PNG or JPG | Max size: <?= $image_size_in_admissions ?>MB</small>
                </div>

                <input type="file" name="guardian_photo" id="fileupload_g"
                    accept=".jpeg,.jpg,.png,image/jpeg,image/jpg,image/png" style="display: none;"
                    <?= empty($final_preview->g_photo_uri) ? ($required_fields['g_photo_uri']['required'] ?? '') : '' ?> />

                <input type="hidden" name="guardian_high_quality_url" id="guardian_high_quality_url">
                <span id="fileuploadError_g" class="text-danger d-block mt-2"></span>
            </div>

            <div class="mt-2">

                <span><?= $this->settings->getSetting('student_photo_note_display') ?: 'Upload recent passport size photograph' ?></span>
            </div>
        </div>
    </div>
    <script type="text/javascript">
    function readURL_g(input) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#previewing_g').attr('src', e.target.result);
            }

            reader.readAsDataURL(input.files[0]);
        }
    }

    function validate_photo_size(files, id, error_id) {
        var max_size_string = '<?php echo $image_size_in_admissions ?>';
        var file_size = parseFloat(files.size / 1024 / 1024);
        var max_file_size = parseInt(max_size_string);
        if (file_size > max_file_size) {
            $("#" + error_id).html('File size exceeded.');
            $("#" + id).val('');
            return false;
        } else {
            $("#" + error_id).html('');
            return true;
        }
    }

    $('#fileupload_g').change(function() {
        var src = $(this).val();

        if (src && validate_photo_size(this.files[0], 'fileupload_g', 'fileuploadError_g')) {
            $('#photo_placeholder_g').css('display', 'none');
            $('#previewing_g').css('display', 'block');
            $("#fileuploadError_g").html("");
            readURL_g(this);

            // Start upload
            saveFileToStorage_guardian(this.files[0], 'fileupload_g', 'guardian_high_quality_url');
        } else {
            this.value = null;
        }
    });

    function saveFileToStorage_guardian(file, inputId, pathInputId) {
        console.log('Starting guardian upload for:', inputId, 'file:', file.name);

        // Disable form controls during upload
        $('#' + inputId).attr('disabled', 'disabled');
        $(".save-step1").prop('disabled', true);
        $(".save-step2").prop('disabled', true);
        $('.save-step4').prop('disabled', true);
        $('.prev-step').prop('disabled', true);

        // Create progress overlay for guardian photo
        var $preview = $('#previewing_g');
        var $parent = $preview.parent();

        // Remove any existing overlay
        $parent.find('.upload-progress-overlay').remove();

        // Add new progress overlay
        $parent.append(`
            <div class="upload-progress-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 1000; border-radius: 8px;">
                <div style="color: white; font-size: 24px; font-weight: bold; margin-bottom: 10px;">
                    <span id="upload-percentage-${inputId}">0%</span>
                </div>
                <div style="color: white; font-size: 14px;">Uploading...</div>
                <div style="width: 80%; background: rgba(255,255,255,0.3); border-radius: 10px; margin-top: 10px; height: 6px;">
                    <div id="upload-progress-bar-${inputId}" style="width: 0%; background: #6c63ff; height: 100%; border-radius: 10px; transition: width 0.3s ease;"></div>
                </div>
            </div>
        `);

        $.ajax({
            url: '<?php echo site_url("S3_admission_controller/getSignedUrl"); ?>',
            type: 'post',
            data: {
                'filename': file.name,
                'file_type': file.type,
                'folder': 'profile'
            },
            success: function(response) {
                console.log('Got signed URL response for guardian:', response);
                response = JSON.parse(response);
                var path = response.path;
                var signedUrl = response.signedUrl;

                $.ajax({
                    url: signedUrl,
                    type: 'PUT',
                    headers: {
                        "Content-Type": file.type,
                        "x-amz-acl": "public-read"
                    },
                    processData: false,
                    data: file,
                    xhr: function() {
                        var xhr = $.ajaxSettings.xhr();
                        xhr.upload.onprogress = function(e) {
                            if (e.lengthComputable) {
                                var percentComplete = Math.round((e.loaded / e.total) * 100);
                                console.log('Guardian upload progress:', percentComplete + '%');

                                // Update progress display
                                $('#upload-percentage-' + inputId).text(percentComplete + '%');
                                $('#upload-progress-bar-' + inputId).css('width', percentComplete + '%');
                            }
                        };
                        return xhr;
                    },
                    success: function(response) {
                        console.log('Guardian upload successful, path:', path);

                        // Store the S3 path in the hidden input
                        $('#' + pathInputId).val(path);

                        // Re-enable form controls
                        $('#' + inputId).removeAttr('disabled');
                        $(".save-step1").prop('disabled', false);
                        $(".save-step2").prop('disabled', false);
                        $('.save-step4').prop('disabled', false);
                        $('.prev-step').prop('disabled', false);

                        // Remove progress overlay
                        $('.upload-progress-overlay').remove();

                        // Reset opacity
                        $('#previewing_g').css('opacity', '1');

                        console.log('Guardian file uploaded successfully: ' + path);
                    },
                    error: function(err) {
                        console.log('Guardian upload error:', err);

                        // Re-enable form controls
                        $('#' + inputId).removeAttr('disabled');
                        $(".save-step1").prop('disabled', false);
                        $(".save-step2").prop('disabled', false);
                        $('.save-step4').prop('disabled', false);
                        $('.prev-step').prop('disabled', false);

                        // Remove progress overlay
                        $('.upload-progress-overlay').remove();

                        // Reset opacity
                        $('#previewing_g').css('opacity', '1');

                        alert('Upload failed. Please try again.');
                    }
                });
            },
            error: function(err) {
                console.log('Guardian signed URL error:', err);

                // Re-enable form controls
                $('#' + inputId).removeAttr('disabled');
                $(".save-step1").prop('disabled', false);
                $(".save-step2").prop('disabled', false);
                $('.save-step4').prop('disabled', false);
                $('.prev-step').prop('disabled', false);

                // Remove progress overlay
                $('.upload-progress-overlay').remove();

                // Reset opacity
                $('#previewing_g').css('opacity', '1');

                alert('Failed to get upload URL. Please try again.');
            }
        });
    }

    </script>
    <?php endif ?>
</div>


<script type="text/javascript">
// Enhanced mobile-friendly checkbox handler for guardian designation
$('#check_g_designation').on('click change touchend', function(e) {
    e.stopPropagation();
    setTimeout(() => {
    if ($(this).prop("checked") == true) {
        $('.g_company_color').hide();
        $('#g_company').val('');
        $('#g_company_addr').val('');
        $('#g_company_area').val('');
        $('#g_company_district').val('');
        $('#g_company_state').val('');
        $('#g_company_pincode').val('');
        $('#g_company').removeAttr('required');
        $('#g_company_addr').removeAttr('required');
        $('#g_company_area').removeAttr('required');
        $('#g_company_district').removeAttr('required');
        $('#g_company_state').removeAttr('required');
        $('#g_company_pincode').removeAttr('required');
        $('#g_annual_income').removeAttr('required');
        $('#g_position').attr('readonly', 'readonly');
        $('#g_position').val('Not working');
        $('#g_company').attr('readonly', 'readonly');
        $('#g_company_addr').attr('readonly', 'readonly');
        $('#g_company_addr').val('');
        $('#g_company_area').attr('readonly', 'readonly');
        $('#g_company_area').val('');
        $('#g_company_district').attr('readonly', 'readonly');
        $('#g_company_district').val('');
        $('#g_company_state').attr('readonly', 'readonly');
        $('#g_company_state').val('');
        $('#g_company_pincode').attr('readonly', 'readonly');
        $('#g_company_pincode').val('');
        $('#g_annual_income').attr('readonly', 'readonly');
        $('#g_office_ph').attr('readonly', 'readonly');
        $('#g_company_county').attr('readonly', 'readonly');
        $('#g_company_county').removeAttr('required');
         $('#g_company_county').css('pointer-events', 'none');
    } else {
        $('.g_company_color').show();
        $('#g_position').val('');
        $('#g_position').removeAttr('readonly');
        $('#g_company').attr('required', 'required');
        $('#g_company_addr').attr('required', 'required');
        $('#g_company_area').attr('required', 'required');
        $('#g_company_district').attr('required', 'required');
        $('#g_company_state').attr('required', 'required');
        $('#g_company_pincode').attr('required', 'required');
        $('#g_annual_income').attr('required', 'required');
        $('#g_company').removeAttr('readonly');
        $('#g_company_addr').removeAttr('readonly');
        $('#g_company_area').removeAttr('readonly');
        $('#g_company_district').removeAttr('readonly');
        $('#g_company_state').removeAttr('readonly');
        $('#g_company_pincode').removeAttr('readonly');
        $('#g_annual_income').removeAttr('readonly');
        $('#g_office_ph').removeAttr('readonly');
        $('#g_company_county').removeAttr('readonly');
        $('#g_company_county').css('pointer-events', '');
    }
    }, 50); // Small delay to ensure checkbox state is properly updated
});



$('#copy_address').click(function() {
    if ($(this).prop("checked") == true) {
        var f_addr = $('#f_addr').val();
        var f_area = $('#f_area').val();
        var f_district = $('#f_district').val();
        var f_state = $('#f_state').val();
        var f_county = $('#f_county').val();
        var f_pincode = $('#f_pincode').val();
        $('#m_addr').val(f_addr);
        $('#m_area').val(f_area);
        $('#m_district').val(f_district);
        $('#m_state').val(f_state);
        $('#m_county').val(f_county);
        $('#m_pincode').val(f_pincode);
    } else {
        $('#m_addr').val('');
        $('#m_area').val('');
        $('#m_district').val('');
        $('#m_state').val('');
        $('#m_county').val('');
        $('#m_pincode').val('');
    }
});

// Fix for checkbox clickability issues in guardian form
$(document).ready(function() {
    // Ensure guardian designation checkbox is clickable
    $('#check_g_designation').css({
        'z-index': '999',
        'position': 'relative',
        'pointer-events': 'auto',
        'opacity': '1',
        '-webkit-appearance': 'checkbox',
        'appearance': 'checkbox'
    });

    // Add click handler with debugging
    $('#check_g_designation').off('click').on('click', function(e) {
        console.log('Guardian designation checkbox clicked:', $(this).is(':checked'));
        // Let the default behavior happen
    });

    // Label click handler for mobile - prevent label clicks, allow checkbox clicks only
    $('label[for="check_g_designation"]').off('click').on('click', function(e) {
        // Only prevent if clicking on the label text (span) or label background, not the checkbox
        if (e.target.tagName.toLowerCase() === 'span' || e.target === this) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Label click prevented for check_g_designation');
            return false;
        }
        // If clicking on the checkbox, let it work normally
        console.log('Checkbox click allowed for check_g_designation');
    });

    // Add mobile-specific styles for guardian form checkbox
    $('<style>').prop('type', 'text/css').html(`
        #check_g_designation {
            z-index: 999 !important;
            position: relative !important;
            pointer-events: auto !important;
            opacity: 1 !important;
            -webkit-appearance: checkbox !important;
            appearance: checkbox !important;
            transform: scale(1.5) !important;
            margin-right: 12px !important;
        }

        @media (max-width: 768px) {
            .mobile-checkbox-label {
                min-height: 48px !important;
                padding: 15px !important;
                background-color: transparent !important;
                border: none !important;
                border-radius: 8px !important;
                margin-bottom: 10px !important;
                display: flex !important;
                align-items: center !important;
            }
            .mobile-checkbox-label:active {
                background-color: rgba(0,0,0,0.05) !important;
                transform: scale(0.98) !important;
            }

            /* Ensure checkbox is properly sized on mobile */
            #check_g_designation {
                min-width: 20px !important;
                min-height: 20px !important;
                transform: scale(1.6) !important;
            }
        }
    `).appendTo('head');
});
</script>

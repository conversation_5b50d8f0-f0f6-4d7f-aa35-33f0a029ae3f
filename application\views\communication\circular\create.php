<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('communication_dashboard');?>">Communication</a></li>
    <li><a href="<?php echo site_url('communication/circulars/list');?>"><?php echo $tileName; ?></a></li>
    <li>Create <?php echo $tileName; ?></li>
</ul>

<hr>
<form enctype="multipart/form-data" id="demo-form" action="<?php echo site_url('communication/Circulars/send');?>" class="form-horizontal" data-parsley-validate method="post">
  <div class="col-md-12">
    <div class="card cd_border">
      <div class="card-header panel_heading_new_style_staff_border">
        <div class="row" style="margin: 0px;">
          <div class="d-flex justify-content-between" style="width:100%;">
            <h3 class="card-title panel_title_new_style_staff">
              <a class="back_anchor" href="<?php echo site_url('communication/circulars/list'); ?>">
                <span class="fa fa-arrow-left"></span>
              </a> 
              Create <?php echo $tileName; ?>
            </h3>

            <button type="button" id="copy-from" class="btn btn-primary">Clone from</button>
          </div>
        </div>
      </div>
      <div class="card-body pt-1">
        <input type="hidden" name="send_to" id="send_to" value="Both">
        <input type="hidden" name="send_to_class" id="send_to_class" value="Both">
        <input type="hidden" id="selection_type" value="Student">
        <input type="hidden" id="class_batch" name="class_batch" value="all">
        <div class="form-group">
        <div class="col-md-8 p-0">
            <div class="form-group">
                  <lable class="col-md-2  from-control" style="width:250px;margin-top:6px;padding-right:0px" ><strong>Select From Email</strong><font color="red">*</font> </lable>
                  <div class="col-md-4" style="margin-left:0px">
                    <!-- <select  required="required" name="fromemail" id="fromemail" class="form-control select"  > -->
                      <?php 
                      $crFormEmail = $this->settings->getSetting('circularv2_from_email');
                      $emails = json_decode($this->settings->getSetting('registered_emails'));
                      if(!empty($emails)){
                        echo '<select  required="required" name="fromemail" id="fromemail" class="form-control"  >';
                        echo '<option value="" >Select from Email-Id</option>';
                        foreach ($emails as $key => $val){
                          $selected = !empty($crFormEmail) && $crFormEmail != "NULL" && $crFormEmail == $val ? 'selected' : '';?>
                            <option <?= $selected ?> value="<?= $val ?>"><?= $val ?></option>
                        <?php }?>
                        <?php echo "</select>";?>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                      <?php }else{?>
                        <div class="mt-2" style="color: red !important;">Please Check Registered Emails In Config</div>
                      <?php }?>
                    <!-- </select> -->
                </div>
          </div>
          </div>
          <div class="col-md-12">
          
            <div class="d-flex justify-content-between align-items-center" style="width: 100%;">
              <div>
                <span id="counts">
                  <strong>Students:</strong> <span id="stdCount">0</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;
                  <strong>Sections:</strong> <span id="secCount">0</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;
                  <strong>Staff:</strong> <span id="stfCount">0</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;
                </span>
              </div>
              <div>
                <button type="button" onclick="savetoGroup()" class="btn btn-outline-secondary">Add selected contacts to group</button>&nbsp;&nbsp;
                <button type="button" class="btn btn-outline-secondary pull-right" onclick="clearAll()">Clear All</button>
              </div>
                
            
             <!--  <label class="checkbox-inline"><input style="width:20px;height: 20px;" type="checkbox" name="send_to_student" id="send_to_student">
                <span style="font-size:16px; margin-left: 5px;font-family: inherit;">Send to students also.</span> <small>(Send emails to student's email ids also when Circular&Email is selected)</small>
              </label> -->
          </div>
          
          

            <div id="contact-container" class="mt-2">
            
              <button id="add-contact" style="font-weight: bold;" data-toggle="modal" data-target="#contact" type="button" class="btn btn-outline-secondary">Send To &nbsp;&nbsp;<i class="fa fa-group"></i></button>
              <span class="student-box"></span>
              <span class="class-box"></span>
              <span class="staff-box"></span>
            </div>
          </div>
        </div>

        <div class="form-group">
            <div class="col-md-12">
                <input type="text" autocomplete="off" name="title" id="title" class="form-control" placeholder="Title / Subject">
            </div>
        </div>

        <div class="form-group">
          <div class="col-md-3">
              <select required="required" name="category" id="category" class="form-control">
                  <option value="">Select Category</option>
                  <?php foreach ($categories as $key => $category) { ?>
                      <option value="<?php echo addslashes($category->category_name).'_'.$category->require_approval; ?>"><?= $category->category_name ?></option>
                  <?php } ?> 
              </select>
              <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                  <i class="fa fa-caret-down"></i>
              </div>
          </div>
          <br class="visible-xs visible-sm">
          <div class="col-md-3">
              <select class="form-control" name="communication_type" id="communication_type">
                <option value="circular"><?php echo $tileName; ?> Only</option>
                <?php 
                  $mail_id = $this->settings->getSetting('circularv2_from_email');
                  if($mail_id) { ?>
                    <!-- <option value="email">Email Only</option> -->
                    <option value="circular_email" selected><?php echo $tileName; ?> & Email</option>
                <?php } ?>
              </select>
              <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                  <i class="fa fa-caret-down"></i>
              </div>
          </div>
          <br class="visible-xs visible-sm">
          <div class="col-md-3">
              <select class="form-control" id="templateName" onchange="applyTemplate()">
                <?php foreach ($templates as $template) {
                  echo '<option value="' . $template['name'] .'">' . $template['name'] . '</option>';
                } ?>
              </select>
              <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                  <i class="fa fa-caret-down"></i>
              </div>
          </div>
          <br class="visible-xs visible-sm">
          <?php 
              switch ($circularv2_sendto_mode) {
                case 'students-only':
                  $select_parents = '';
                  $select_students = 'selected';
                  $select_both = '';
                  $make_disabled = 'disabled';
                  break;
                case 'parents-only':
                  $select_parents = 'selected';
                  $select_students = '';
                  $select_both = '';
                  $make_disabled = 'disabled';
                  break;
                case 'both':
                  $select_parents = '';
                  $select_students = '';
                  $select_both = 'selected';
                  $make_disabled = 'disabled';
                  break;
                case 'show-selection':
                  $select_parents = '';
                  $select_students = '';
                  $select_both = 'selected';
                  $make_disabled = '';
                  break;
                default:
                  $select_parents = '';
                  $select_students = '';
                  $select_both = 'selected';
                  $make_disabled = 'disabled';
                  break;
            }
          ?>
          <div class="col-md-3">
              <select class="form-control" name="send_to_type" id="send_to_type" <?= $make_disabled ?>>
                <option value="parents_only" <?= $select_parents ?>>Send to parents only</option>
                <option value="parents_and_students" <?= $select_both ?>>Send to parents & students</option>
                <option value="students_only" <?= $select_students ?>>Send to students only</option>
              </select>
              <div style="position: absolute; right: 25px; top: 22%; transform: translateY(-50%);">
                  <i class="fa fa-caret-down"></i>
              </div>
              <span class="help-block">This option is applicable for students/parents only. Ignored for Staff.</span>
          </div>
        </div>
          
        <?php if ($this->authorization->isSuperAdmin()) { ?>
        <div class="form-group">
            <div class="col-md-12 d-flex justify-content-between">
                <input type="text" autocomplete="off" name="basic_prompt" id="basic_prompt" class="form-control" placeholder="Enter your prompt here">
                <button type="button" class="btn btn-primary" id="generate" style="margin-left: 5%">Generate Circular with AI</button>
            </div>
        </div>
        <?php } ?>

        <div class="form-group">
            <div class="col-md-12">
              <span style="color:red;">Please do not copy circular content from 'Word document'.</span><br>
              <span style="color:red;">Don't paste images inside the content if you select Circular & Email.(If needed add attachment)</span>
                <textarea name="body" id="body" placeholder="Body" class="summernote" style="height: 100px;"></textarea>
            </div>
        </div>
        <div class="form-group">
          <div class="col-md-12">
              <input type="file" multiple="" class="form-control" id="fileupload" name="attachment[]" accept="image/jpeg, image/gif, image/png, application/pdf, application/msword, application/vnd.ms-excel, .xlsx,.xls,.doc, .docx" onchange="validateFileInput(this)">
              <div id="filePreview" class="mt-2"></div>
              <span class="help-block">Note: Maximum file size: <?php echo $resource_size ?><br>
                Allowed file types - JPEG, JPG, PNG, PDF, GIF, xls, Word;<br>
                The file preview is available only for JPEG, JPG, PNG, PDF and GIF files.
              </span>
              <span id="fileuploadError" style="color: red;"></span>
          </div>
        </div>
      </div>

      <div class="card-footer panel_footer_new">
        <center>
            <a class="btn btn-warning" href="<?php echo site_url('communication/circulars/list')?>">Cancel</a>
            <button type="button" id="previewBtn" data-toggle="modal" data-target="#summary" class="btn btn-primary">Preview & Send</button>
          </center>
      </div>
    </div>
  </div>

    <div id="summary" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog">

        <div class="modal-dialog" style="width: 60%;margin:2% auto;">

          <!-- Modal content-->
          <div class="modal-content">
            <div class="loader-background">
              <div style="color:white;text-align:center;height: 100%;">
                <h4 style="position: relative;top:45%;width:70%;margin:auto;color:white;">Sending Circulars</h4>
                <div class="progress" style="position: relative;top:50%;width:70%;margin:auto;">
                  <div id="circular_progress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                </div>
                <h4 class="blink">Note: Please do not close the window until the process is fully completed.</h4>
              </div>
            </div>
    	        <div class="modal-header">
      	        <h4 class="modal-title">Preview and Send &nbsp;&nbsp;<br>
                    <span style="float: left; font-size: 14px;"><span class="label label-primary" id="sending"><?php echo $tileName; ?> Only </span>&nbsp;&nbsp;&nbsp;</span>
                </h4>
                <button type="button" class="btn btn-primary pull-right" onclick="previewContent()">Preview Content</button>
    	        </div>
    	        <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;max-height:425px;">
    	            <div id="modal-loader" style="display: none; text-align: center;">
    	                <!-- ajax loader -->
    	                <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>" style="width:400px; height:400px;">
    	           </div>
                 <div id="msgBody">
                  
                  </div>
                  <div id="countBody">
                  
                  </div>
    	            <table class="table" id="dynamic-content" width="100%">
    	                
    	            </table>
    	        </div>
    	        <div class="modal-footer">
    	          <button type="button" id="cancelModal" class="btn btn-danger" data-dismiss="modal">Close</button>
                <input type="button" onclick="save_circular_beta()" id="confirmBtn_beta" class="btn btn-primary" style="margin-top: 0px;" value="Confirm">
    	        </div>
    	    </div>
	    </div>
	</div>

  <!-- group modal -->
    <div id="save-group" class="modal fade" role="dialog">
      <div class="modal-dialog" style="width:50%; margin-top: 2% !important; margin: auto;">
        <div class="modal-content">
            <div class="modal-header">
              <h4 class="modal-title">Save contacts to group</h4>
              <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div id="test-number" class="modal-body">
              <div class="form-group">
                <label class="col-md-3 control-label">Group Name</label>
                <div class="col-md-9">
                  <input type="text" id="group_name" name="group_name" class="form-control" placeholder="Enter a name for the group">
                  <span id="groupNameErrorMessage" style="display: none; color: red;">Group Name Is Required.</span>
                </div>
              </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" style="margin-top: 0px;" onclick="saveGroupName()">Done</button>
            </div>
        </div>
      </div>
    </div>
</form>

<div id="copy-from-modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog">

        <div class="modal-dialog" style="width: 60%;margin:2% auto;">

          <!-- Modal content-->
          <div class="modal-content">
              <div class="modal-header">
                <h4 class="modal-title">Clone circular data from</h4>
              </div>
              <div class="modal-body">
                <label class="control-label">Select circular to be copied</label>
                <select class="form-control" name="selected_circular_id" id="selected_circular_id">
                  <option value="">Select Circular</option>
                </select>
                <div style="position: absolute; right: 25px; top: 65%; transform: translateY(-50%);">
                    <i class="fa fa-caret-down"></i>
                </div>
              </div>
              <div class="modal-footer" style="justify-content: center;">
                <button type="button" id="cancelModal" style="width: 120px;" class="btn btn-danger my-0" data-dismiss="modal">Close</button>
                <button type="button" style="width: 120px;" onclick="copy_circular()" id="confirmBtn" class="btn btn-primary my-0">Confirm</button>
              </div>
          </div>
      </div>
  </div>

<div class="visible-xs visible-sm visible-md">
  <a href="<?php echo site_url('communication/circulars/list');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<div class="col-md-12 form-horizontal">
    <div id="contact" class="modal fade" role="dialog">
        <div class="modal-dialog" style="width: 60%;margin-top: 2% !important;margin:auto;">

            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                  <h4 class="modal-title">Select the Student/Class/Staff</h4>
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;max-height:500px;">
                  <div class="form-group">
                    <!-- <label class="control-label col-md-2">Type</label> -->
                    <div class="col-md-12">
                      <div class="btn-group sType d-flex">
                        <div class="btn-group">
                          <button type="button" data-val="Student" class="btn btn-secondary default-active">Student</button>
                        </div>
                        <div class="btn-group">
                          <button type="button" data-val="Class" class="btn btn-secondary">Class</button>
                        </div>
                        <div class="btn-group">
                          <button type="button" data-val="Staff" class="btn btn-secondary">Staff</button>
                        </div>
                        <div class="btn-group">
                          <button type="button" data-val="Group" class="btn btn-secondary">Group</button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- <div class="form-group">
                    <div class="col-md-12">
                      <label class="checkbox-inline"><input style="width:20px;height: 20px;" type="checkbox" name="send_to_student" id="send_to_student"><span style="font-size:16px; margin-left: 5px;font-family: inherit;">Send to students also.</span><small>(Send emails to student emails also when Circular&Email is selected)</small></label>
                    </div>
                  </div> -->

                  <!-- <div id="parent">
                    <div class="form-group">
                      <div class="col-md-12">
                        <div class="btn-group pType btn-group-justified">
                          <div class="btn-group">
                            <button type="button" data-val="Both" class="btn  btn-secondary default-active">Both</button>
                          </div>
                          <div class="btn-group">
                            <button type="button" data-val="Father" class="btn  btn-secondary">Father</button>
                          </div>
                          <div class="btn-group">
                            <button type="button" data-val="Mother" class="btn  btn-secondary">Mother</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div> -->

                  <div id="individual">
                    <div class="form-group">
                      <div class="col-md-4">
                        <select id="batch" name="batch" class="form-control input-md">
                          <option value="0">All</option>
                          <?php foreach ($batches as $key => $batch) { ?>
                            <option value="<?php echo $batch->id; ?>">Joined in <?php echo $batch->acad_year; ?></option>
                          <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 20%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                        <span class="help-block">Select the academic year to send only to students joined in that academic year.</span>
                      </div>
                      <div class="col-md-4">
                        <!-- <label for="class" class="col-md-2 control-label">Selct Class</label> -->
                        <select id="class_id" class="form-control input-md">
                          <option value="">Select Class</option>
                          <?php foreach ($classes as $key => $cls) { ?>
                            <option value="<?php echo $cls->id; ?>"><?php echo $cls->class_name; ?></option>
                           <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 44%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <select disabled="true" id="section_dtails" class="form-control" onchange="getSectionStudents()" >
                          <option value="">Select Section</option>
                        </select>
                        <div style="position: absolute; right: 25px; top: 44%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                      </div>
                    </div>

                    <div class="form-group">
                      <!-- <label class="control-label col-md-2" for="BookType">Student</label> -->
                      <div class="col-md-12">
                        <button type="button" onclick="selectAll()" class="btn btn-secondary mb-2">Select All</button>
                        <select multiple title="Select Student" disabled="true" size="10" id="students" class="form-control ">
                        </select>
                      </div>
                    </div>
                  </div>

                  <div id="classwise" style="display:none;">
                    <!-- <div class="form-group">
                      <div class="col-md-12">
                        <div class="btn-group cType btn-group-justified">
                          <div class="btn-group">
                            <button type="button" data-val="Both" class="btn  btn-secondary default-active">Both</button>
                          </div>
                          <div class="btn-group">
                            <button type="button" data-val="Father" class="btn  btn-secondary">Father</button>
                          </div>
                          <div class="btn-group">
                            <button type="button" data-val="Mother" class="btn  btn-secondary">Mother</button>
                          </div>
                        </div>
                      </div>
                    </div> -->
                    <div class="form-group">
                      <div class="col-md-12">
                        <select id="batch_class" name="batch_class" class="form-control input-md">
                          <option value="0">All Batches</option>
                          <?php foreach ($batches as $key => $batch) { ?>
                            <option value="<?php echo $batch->id; ?>">Joined in <?php echo $batch->acad_year; ?></option>
                          <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 27%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                        <span class="help-block">Select the academic year to send only to students joined in that academic year.</span>
                      </div>
                    </div>
                    <div class="form-group">
                      <div class="col-md-12">
                        <button type="button" onclick="selectAll()" class="btn btn-secondary mb-2">Select All</button>
                          <select id="multi_class_sections"  class="form-control" size="10" multiple="multiple">
                            <?php foreach ($class_section as $key => $cls_section) { ?>
                             <option value="<?= $cls_section->id;?>"><?= $cls_section->class_name.''.$cls_section->section_name;?></option>
                           <?php } ?>
                          </select>
                      </div>
                    </div>
                  </div>

                  <div id="staff_individual" style="display: none;">
                    <div class="form-group">
                        <div class="col-md-12">
                          <button type="button" onclick="selectAll()" class="btn  btn-secondary mb-2">Select All</button>
                          <div class="col-md-4 pl-0">
                            <select class="form-control input-md mb-2" name="staff_type[]" id="staff_type" onchange="getStaffyByStaffType()">
                              <option value="">All</option>
                              <?php if(!empty($staff_type)){?>
                                <?php foreach ($staff_type as $key => $val) { ?>
                                  <option value="<?php echo $key; ?>">
                                    <?php echo $val ?>
                                  </option>
                                <?php } ?>
                              <?php } else {?>
                                <option value="" disabled>Staff Type Not Enabled In Config</option>
                              <?php }?>
                            </select>
                            <div style="position: absolute; right: 25px; top: 44%; transform: translateY(-50%);">
                                <i class="fa fa-caret-down"></i>
                            </div>
                          </div>
                        </div>
                        
                        <div class="col-md-12">
                          <select id="multi_staff"  class="form-control" size="10" multiple="multiple">
                            <?php foreach ($staff_details as $key => $val) { ?>
                              <option  value="<?php echo $val->id; ?>"><?php echo $val->staff_name?></option> 
                            <?php } ?> 
                          </select>
                        </div>
                        <div id="multi_staff_error" style="color: red;"></div>
                      </div>
                    </div>
                  </div>

                  <div class="form-group" id="groupNumbers" style="display: none;">
                    <div class="col-md-4">
                        <select class="form-control" name="group_id" id="group_id">
                          <option value="0">Select group</option>
                          <?php if(!empty($groups)) { ?>
                            <?php foreach ($groups as $key => $group) {
                              echo '<option value="'.$group->id.'">'.$group->group_name.'</option>';
                            } ?>
                          <?php } else { ?>
                              <option value="" disabled>No Groups Found</option>
                          <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 44%; transform: translateY(-50%);">
                              <i class="fa fa-caret-down"></i>
                          </div>
                    </div>
                  </div>

                  <div class="form-group" id="customNumbers" style="display: none;">
                    <!-- <label for="sms_template" class="col-md-2 control-label">Custom </label> -->
                    <div class="col-md-12">
                        <input type="text" class="form-control" id="custom_numbers" placeholder="Enter Comma Seperated Phone Numbers">
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="col-md-12" style="max-height: 150px;overflow-y: scroll;">
                      <span class="student-box"></span>
                      <span class="class-box"></span>
                      <span class="staff-box"></span>
                    </div>
                  </div>
                </div>
                <div class="modal-footer" style="justify-content: flex-start;">
                  <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-primary" onclick="addMembers()" style="margin-bottom: 3px">Add</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="content-preview" class="modal fade" role="dialog">
        <div class="modal-dialog" style="width: 60%;margin:auto;">

        <div class="modal-content">
            <div class="modal-header">
              <h4 class="modal-title"><?php echo $tileName; ?> Content Preview</h4>
              <strong id="fileCount">No Attachments</strong>
            </div>
            <div id="content-body" class="modal-body" style="overflow-y:auto;max-height:500px;">

            </div>
            <div class="modal-footer" style="justify-content: flex-start;">
              <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
  .btn{
    border: 1px solid #6c757d;
  }
  .loader-background {
    width: 100%;
    height: 100%;            
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #000;
    border-radius: 8px;
  }
  .blink {
      animation: blinker 3s linear infinite;
      width: 85%;
      font-weight: bold;
      font-size: 1.5rem;
      background-color: yellow;
      color: red;
      padding: 10px 20px;
      border: 2px solid red;
      border-radius: 5px;
      text-align: center;
      box-shadow: 0 0 15px red;
      position: relative;
      top: 57%;
      margin: auto;
  }

  @keyframes blinker {
      50% {
          opacity: 0;
      }
  }
</style>


<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>
<?php $this->load->view('communication/circular/__css.php'); ?>
<?php $this->load->view('communication/circular/__script.php'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="importmap">
  {
    "imports": {
      "@google/generative-ai": "https://esm.run/@google/generative-ai"
    }
  }
</script>

<script type="module">
  import { GoogleGenerativeAI } from "@google/generative-ai";
  const API_KEY = "AIzaSyA1DN-G7IP96t3E96YU72rRIWvfgsPwt84";
  const genAI = new GoogleGenerativeAI(API_KEY);
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

  $('#openModal').on('click', function() {
    $('#myModal').modal('show');
  });

  $("#generate").on('click', async function() {
    const basicPrompt = `Your task is to draft a formal letter based strictly on the provided information. 
    The Letter should start from Subject and must not specify From and To details.
    The letter must not be less than 300 words and not more than 500 words under any circumstances. 
    It should be clear, concise, and include only essential details. 
    Adhere to a professional tone throughout, avoiding unnecessary elaboration or filler language. 
    Brevity and precision are critical. 
    The provided information may relate to various topics such as requests, official notifications, or announcements. Please ensure that the letter is appropriate for the provided context.`;

    const userPrompt = $("#basic_prompt").val();
    const finalPrompt = `${basicPrompt}\n\nHere is the information: ${userPrompt}`;
    console.log(finalPrompt);

    try {
        const result = await model.generateContent(finalPrompt);
        let aiText = await result.response.text();

        aiText = aiText.replace(/\*\*(.*?)\*\*/g, "<b>$1</b>");
        aiText = aiText.replace(/\n/g, "<br>");

        // Insert the AI-generated text into the Summernote editor
        $('.summernote').code(aiText);

        // Hide the modal after the AI-generated content is inserted
        $('#myModal').modal('hide');
    } catch (error) {
        console.error('Error:', error);
    }
});
</script>
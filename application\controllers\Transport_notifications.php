<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Transport_notifications extends CI_Controller {

	public function __construct() {
        parent::__construct();
        $this->load->model('transportation_model');
        $this->load->helper('notification_helper');
        $this->acad_year->loadAcadYearDataToSession();
  	}

  	public function dhundhoo_events() {
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
  		$eventType = $data['event_type'];

      //Check the API key
      // $apiKey = $this->settings->getSetting('transport_api_key');
      // if($data['api_key'] != $apiKey) {
      //   return json_encode(['status' => 401, 'message' => 'Api key mismatch.']);
      // }

  		if($eventType === 'eta') {
  			$this->_sendETANotification($data);
  		} else if ($eventType === 'geofence') {
  			$this->_sendGeofenceNotification($data);
  		} else if ($eventType === 'attendance') {
        $this->_sendAttendanceNotification($data);
      }
  		return 200;
  	}

    // Use This Function For Local Testing Of The Notification.
    public function localTransportNotificationTrigger() {
      // Sample Data
      /*
        {
          "event_type": "eta",
          "distance_km": "1.6",
          "updated_at": 1750663361771,
          "thing_id": "KA526086",
          "stop_id": "290",
          "triggered_at": 1750663361771,
          "journey_id": "73",
          "message": "Your commute ROUTE 01 BUS is 1.6 kms away and will be reaching at drop point shortly.",
          "journey_type": "Pick"
        }
      */
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      if (empty($data)) {
        echo json_encode('Invalid or missing JSON data.');
        die();
      }
      $this->_sendETANotification($data);
    }

    //Webhook for recieving geofence notifications
    public function _sendETANotification($data) {
      $journey_type = $data['journey_type'];
      $journey_id = $data['journey_id'];
      $stop_id = $data['stop_id'];
      $message = $data['message'];
      // $thing_id = $this->transportation_model->getThingByJourneyId($journey_id);
      $journey_data = $this->transportation_model->getJourneyData($journey_id);
      $stop_name = $this->transportation_model->getStopName($stop_id);
      $message = str_replace("pick up point", $stop_name, $message);
      $message = str_replace("drop point", $stop_name, $message);
      $students = $this->transportation_model->getStudentsByJourneyStop($stop_id, $journey_id);
      $staff = $this->transportation_model->getStaffByJourneyStop($stop_id, $journey_id);

      $journey = 'ETA<br><b>Journey: </b>'.$journey_data->journey_name.'<br><b>Bus: </b>'.$journey_data->thing_name.'<br><b>Stop: </b>'.$stop_name;
      $log_data = array(
        'journey_data' => $journey,
        'journey_id' => $journey_id,
        'thing_id' => $journey_data->thing_id,
        'stop_id' => $stop_id,
        'event_type' => 'Eta',
        'event' => json_encode($data),
        'message' => $message
      );
      $this->_handleNotification($log_data, $journey, $students, $staff);
    }

    private function _handleNotification($log_data, $journey, $students, $staff) {
      $thing_id = $log_data['thing_id'];
      $response = array();
      $notification_enabled = $this->settings->getSetting('transport_enable_notification');
      if(!empty($students)) {
        $saved = $this->_saveNotifications($students, $log_data['message'], 1);
        $stdData = $this->transportation_model->getStudentData($students);
        $log_data['notifications_sent_to'] = json_encode($stdData);
        $response = array('request' => '', 'response' => '');
        if($notification_enabled) {
          $url = site_url('parent_controller/track_bus_noti/').$thing_id;
          if(isset($log_data['stop_id'])) {
            $url .= '/'.$log_data['stop_id'];
          }
          $date = date('Y-m-d');
          $isHoliday = $this->transportation_model->checkStudentHoliday($date);
          if($isHoliday) {
            $response['request'] = 'Notification not sent.';
            $response['response'] = 'Today is holiday.';
          } else {
            $response = sendTransportNotifications($saved['student_tokens'], 'School Bus', $log_data['message'], $url);
          }
        }
        $log_data['notification_json'] = json_encode($response['request']);
        $log_data['notification_response'] = json_encode($response['response']);
        $this->transportation_model->addLog($log_data);
      }

      if(!empty($staff)) {
        $saved = $this->_saveNotifications($staff, $log_data['message'], 2);
        $staffData = $this->transportation_model->getStaffData($staff);
        $log_data['notifications_sent_to'] = json_encode($staffData);
        $response = array('request' => '', 'response' => '');
        if($notification_enabled) {
          $url = site_url('staff/Staff_view/track_bus_noti/').$thing_id;
          if(isset($log_data['stop_id'])) {
            $url .= '/'.$log_data['stop_id'];
          }
          $date = date('Y-m-d');
          $isHoliday = $this->transportation_model->checkStaffHoliday($date);
          if($isHoliday) {
            $response['request'] = 'Notification not sent.';
            $response['response'] = 'Today is holiday.';
          } else {
            $response = sendTransportNotifications($saved['staff_tokens'], 'School Bus', $log_data['message'], $url);
          }
        }
        $log_data['notification_json'] = json_encode($response['request']);
        $log_data['notification_response'] = json_encode($response['response']);
        $this->transportation_model->addLog($log_data);
      }

      if(empty($students) && empty($staff)) {
        $response['request'] = 'No students/staff';
        $response['response'] = '';
        $this->transportation_model->addLog($log_data);
      }
    }

    public function _sendGeofenceNotification($data) {
      $journey_id = $data['journey_id'];
      $journey_type = $data['journey_type'];
      $message = $data['message'];
      $date = date('Y-m-d');
      $journey_data = $this->transportation_model->getJourneyData($journey_id);
      $students = $this->transportation_model->getStudentsByAttendance($journey_id, $journey_type, $date);
      // $stdData = $this->transportation_model->getStudentData($students);
      $journey = 'Geo-Fence<br><b>Journey: </b>'.$journey_data->journey_name.'<br><b>Bus: </b>'.$journey_data->thing_name;
      $log_data = array(
        'journey_data' => $journey,
        'journey_id' => $journey_id,
        'thing_id' => $journey_data->thing_id,
        'event_type' => 'Geofence',
        'event' => json_encode($data),
        'message' => $message
      );
      $this->_handleNotification($log_data, $journey, $students, []);
    }

    public function _sendAttendanceNotification($data) {
      //We get notification as the following JSON
      //{"event_type":"attendance","updated_at":1694006360715,"thing_id":"KA53B6328","latitude":17.483532,"triggered_at":1694006354000,"rfid":"2526978","longitude":78.422958}

      //Get all the parameters
      $journey_id = (!isset($data['journey_id']) || empty($data['journey_id'])) ? '0' : $data['journey_id'];
      $journey_type = isset($data['journey_type']) ? $data['journey_type'] : 'Unknown';

      //Save the data into tx_attendance
      if ($journey_id == '0') {
        //Journey ID is coming as null which means bus has not started the journey. Thing ID should be used
        //Use reg number instead
        $thing_data = $this->transportation_model->getThingDataUsingRegNumber($data['thing_id']);
        $data['thing_data_id'] = $thing_data->thing_id;
        $data['thing_name'] = $thing_data->thing_name;
        $data['journey_id'] = '0';
        $data['journey_type'] = $thing_data->journey_type;
        $journey_data = $thing_data;
      } else {
        //Journey ID is present which means bus has started.
        $journey_data = $this->transportation_model->getJourneyData($journey_id);
        $data['thing_data_id'] = $journey_data->thing_id;
        $data['journey_id'] = $journey_data->id;
        $data['journey_type'] = $journey_data->journey_type;
      }
      $status = $this->transportation_model->addAttendanceData($data);

      //Send notification to the student
      if($status) {
        $std_data = $this->transportation_model->getStudentByRFID($data['rfid']);
        $students = array();
        $std_name = '';
        if(!empty($std_data)) {
          array_push($students, $std_data->id);
          $std_name = $std_data->std_name;
        }
        $journey = 'Attendance <br>'.$std_name.'<br><b>Journey: </b>'.$journey_data->journey_name.'<br><b>Bus: </b>'.$journey_data->thing_name;
        $message = $std_name.' swiped rfid on the bus '.$journey_data->thing_name;
        $log_data = array(
          'journey_data' => $journey,
          'journey_id' => $journey_id,
          'thing_id' => $journey_data->thing_id,
          'student_id' => isset($std_data->id) ? $std_data->id : '0',
          'event_type' => 'Attendance',
          'event' => json_encode($data),
          'message' => $message
        );
        $this->_handleNotification($log_data, $journey, $students, []);
        if(!empty($std_data)) {
          $this->transportation_model->update_mismatch_if_exist($std_data->id, $journey_id, $journey_type);
        }
      }
    }

    private function _saveNotifications($stakeholder_ids, $message, $type) {
      $this->load->model('communication/texting_model');
      if($type == 1) {
        $reciever_data = $this->texting_model->getStudents($stakeholder_ids, 'Both', 'External');
        $reciever_type = 'Students';
      } else {
        $reciever_data = $this->texting_model->getStaff($stakeholder_ids);
        $reciever_type = 'Staff';
      }
      $text_count = count($reciever_data);
      //texting_master data
      $masterData = array(
        'title' => 'School Bus',
        'message' => $message,
        'sent_by' => 1,//admin
        'reciever' => $reciever_type,
        // 'acad_year_id' => $this->acad_year->getAcadYearId(),
        'acad_year_id' => $this->settings->getSetting('academic_year_id'),
        'source' => 'Transportation',
        'text_count' => $text_count,
        'visible' => 1,
        'mode' => 'notification',
        'sms_credits' => 0,
        'is_unicode' => 0
      );

      $saved = $this->texting_model->saveTexting($masterData, $reciever_data);
      return $saved;
    }

}

?>
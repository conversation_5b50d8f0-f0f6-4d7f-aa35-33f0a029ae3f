<div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">
    <div class="card-header panel_heading_new_style_padding d-flex justify-content-between align-items-center" style="border-radius: 8px;padding: 15px;">
        <h3 class="card-title panel_title_new_style mb-0">
            Previous Schooling information
        </h3>
        <?php if ($this->settings->isProfile_edit_enabled('year_id') ||
                  $this->settings->isProfile_edit_enabled('school_name') ||
                  $this->settings->isProfile_edit_enabled('class') ||
                  $this->settings->isProfile_edit_enabled('board') ||
                  $this->settings->isProfile_edit_enabled('medium_of_instruction') ||
                  $this->settings->isProfile_edit_enabled('school_address') ||
                  $this->settings->isProfile_edit_enabled('university') ||
                  $this->settings->isProfile_edit_enabled('period') ||
                  $this->settings->isProfile_edit_enabled('subjects')) : ?>
        <button type="button" class="btn btn-primary btn-sm" onclick="addPreviousSchoolDetails()">
           <span class="fa fa-plus"></span> Add
        </button>
        <?php endif; ?>
    </div>
    <div class="card-body">
        <!-- Loading state -->
        <div id="prev_school_loading" class="text-center" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="mt-2">Loading previous schooling details...</p>
        </div>

        <!-- Content container for AJAX data -->
        <div id="prev_school_content">
           
        </div>
    </div>
</div>

<!-- Modal for Add/Edit Previous School Details -->
<div class="modal fade" id="previousSchoolModal" tabindex="-1" role="dialog" aria-labelledby="previousSchoolModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document" style="width: 70%;margin: auto;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previousSchoolModalLabel">Add Previous School Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="padding: 30px;">
                <form id="previousSchoolForm">
                    <input type="hidden" id="school_id" name="school_id" value="">

                    <!-- Academic Year Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('year_id')) : ?>
                    <div class="form-group mb-4">
                        <label for="year_id" class="form-label">Academic Year <span class="text-danger">*</span></label>
                        <div class="position-relative">
                            <select name="year_id" id="year_id" class="form-control form-select" required>
                                <option value="">Select Year</option>
                                <?php foreach ($acad_years as $ay) {
                                    echo '<option value="' . $ay->acad_year . '">' . $ay->acad_year . '</option>';
                                }   ?>
                            </select>
                            <div class="select-arrow">
                                <i class="fa fa-chevron-down"></i>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- School Name Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('school_name')) : ?>
                    <div class="form-group mb-4">
                        <label for="school_name" class="form-label">School/College Name<span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="school_name" data-parsley-pattern="^[a-zA-Z. ]+$" name="school_name" placeholder="Enter school name" required>
                    </div>
                    <?php endif; ?>

                    <!-- Class Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('class')) : ?>
                    <div class="form-group mb-4">
                        <label for="class" class="form-label">Class/Degree name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="class" name="class" placeholder="e.g., Grade 10" required>
                    </div>
                    <?php endif; ?>

                    <!-- Board Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('board')) : ?>
                    <div class="form-group mb-4">
                        <label for="board" class="form-label">Board <span class="text-danger">*</span></label>
                        <select name="board" id="board" class="form-control form-select" required>
                            <option value="">Select Board</option>
                            <option value="CBSE">CBSE</option>
                            <option value="ICSE">ICSE</option>
                            <option value="STATE">STATE</option>
                            <option value="Home School">Home School</option>
                            <option value="IGCSE">IGCSE</option>
                            <option value="IBDP">IBDP</option>
                            <option value="NIOS">NIOS</option>
                            <option value="PU Board">PU Board</option>
                            <option value="ITI Board">ITI Board</option>
                            <option value="KSEEB">KSEEB</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <?php endif; ?>

                    <!-- Medium of Instruction Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('medium_of_instruction')) : ?>
                    <div class="form-group mb-4">
                        <label for="medium_of_instruction" class="form-label">Medium of Instruction</label>
                        <select name="medium_of_instruction" id="medium_of_instruction" class="form-control form-select">
                            <option value="">Select Medium of Instruction</option>
                            <option value="English">English</option>
                            <option value="Kannada">Kannada</option>
                        </select>
                    </div>
                    <?php endif; ?>

                    <!-- School Address Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('school_address')) : ?>
                    <div class="form-group mb-4">
                        <label for="school_address" class="form-label">School Address</label>
                        <textarea class="form-control" id="school_address" name="school_address" rows="4" placeholder="Enter complete school address"></textarea>
                    </div>
                    <?php endif; ?>

                    <!-- University Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('university')) : ?>
                    <div class="form-group mb-4">
                        <label for="university" class="form-label">University</label>
                        <input type="text" class="form-control" id="university" name="university" placeholder="Enter university or board affiliation">
                    </div>
                    <?php endif; ?>

                    <!-- Period Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('period')) : ?>
                    <div class="form-group mb-4">
                        <label class="form-label">Period/Duration</label>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="period_from" class="form-label" style="font-size: 0.9rem; margin-bottom: 0.5rem;">From Date</label>
                                <input type="date" class="form-control" id="period_from" name="period_from">
                            </div>
                            <div class="col-md-6">
                                <label for="period_to" class="form-label" style="font-size: 0.9rem; margin-bottom: 0.5rem;">To Date</label>
                                <input type="date" class="form-control" id="period_to" name="period_to">
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Subjects Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('subjects')) : ?>
                    <div class="form-group mb-4">
                        <label for="subjects" class="form-label">Subjects</label>
                        <textarea class="form-control" id="subjects" name="subjects" rows="3" placeholder="Enter subjects studied (comma separated)"></textarea>
                    </div>
                    <?php endif; ?>

                    <!-- Registration Number Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('registration_no')) : ?>
                    <div class="form-group mb-4">
                        <label for="registration_number" class="form-label">Registration Number</label>
                        <input type="text" class="form-control" id="registration_number" name="registration_number" placeholder="Enter registration number">
                    </div>
                    <?php endif; ?>

                    <!-- Marks Fields -->
                    <div class="row">
                        <!-- Total Marks Field -->
                        <?php if ($this->settings->isProfile_edit_enabled('total_marks')) : ?>
                        <div class="col-md-6">
                            <div class="form-group mb-4">
                                <label for="total_marks" class="form-label">Total Marks</label>
                                <input type="number" class="form-control" id="total_marks" name="total_marks" placeholder="Enter total marks" min="0">
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Total Marks Obtained Field -->
                        <?php if ($this->settings->isProfile_edit_enabled('total_marks_scored')) : ?>
                        <div class="col-md-6">
                            <div class="form-group mb-4">
                                <label for="total_marks_obtained" class="form-label">Total Marks Obtained</label>
                                <input type="number" class="form-control" id="total_marks_obtained" name="total_marks_obtained" placeholder="Enter marks obtained" min="0">
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Report Card Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('report_card')) : ?>
                    <div class="form-group mb-4">
                        <label for="report_card" class="form-label">Report Card</label>
                        <input type="hidden" id="report_card_path" name="report_card" value="">
                        <div class="file-upload-container" style="border: 2px dashed #e9ecef; border-radius: 8px; padding: 30px; text-align: center; background: #f8f9fa;">
                            <input type="file" class="form-control-file" id="report_card" accept="image/*,.pdf" style="display: none;">
                            <div class="upload-area" onclick="document.getElementById('report_card').click()" style="cursor: pointer;">
                                <div class="upload-icon mb-3" style="font-size: 3rem; color: #6c757d;">
                                    <i class="fa fa-cloud-upload"></i>
                                </div>
                                <div class="upload-text" style="color: #6c757d;">
                                    <h5>Click to upload report card</h5>
                                    <p class="mb-0">Supports: JPG, PNG, PDF (Max 5MB)</p>
                                </div>
                            </div>
                            <div class="file-preview mt-3" id="report_card_preview" style="display: none;">
                                <div class="file-info" style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; display: flex; align-items: center; justify-content: space-between;">
                                    <span class="file-name" style="font-weight: 500;"></span>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeReportCard('desktop')">
                                        <i class="fa fa-times"></i> Remove
                                    </button>
                                </div>
                            </div>
                            <div class="upload-progress mt-3" id="report_card_progress" style="display: none;">
                                <div class="progress" style="height: 25px;">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <p class="mt-2 mb-0 text-muted">Uploading...</p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
            <div class="modal-footer" style="padding: 1.25rem 1.5rem; gap: 10px;">
                <button type="button" class="btn btn-secondary px-4" data-dismiss="modal">
                   Cancel
                </button>
                <button type="button" class="btn btn-primary px-4" onclick="savePreviousSchoolDetails()">
                    Save
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Function to load previous schooling details via AJAX
function loadPreviousSchoolingDetails() {
    // Show loading state
    $('#prev_school_loading').show();
    $('#prev_school_content').hide();

    $.ajax({
        url: '<?php echo site_url("parent_controller/get_prev_schooling_details"); ?>',
        type: 'POST',
        dataType: 'json',
        success: function(data) {
            // Hide loading state
            $('#prev_school_loading').hide();
            $('#prev_school_content').show();

            // Build the table HTML
            var html = buildPreviousSchoolingTable(data);
            $('#prev_school_content').html(html);
        },
        error: function(xhr, status, error) {
            // Hide loading state
            $('#prev_school_loading').hide();
            $('#prev_school_content').show();

            // Show error message
            $('#prev_school_content').html(
                '<div class="text-center">' +
                    '<div class="alert alert-danger">' +
                        '<i class="fas fa-exclamation-triangle"></i> ' +
                        'Error loading previous schooling details. Please try again.' +
                    '</div>' +
                '</div>'
            );
            console.error('Error loading previous schooling details:', error);
        }
    });
}

// Function to build the previous schooling table HTML
function buildPreviousSchoolingTable(data) {
    if (!data || data.length === 0) {
        return '<div class="text-center"><div class="no-data-display">No Data available</div></div>';
    }

    var html = '<div class="table-responsive">';
    html += '<table class="table table-bordered table-striped" style="width:100%; white-space: nowrap;text-align:left; font-size: 0.9rem;">';
    html += '<thead class="thead-light">';
    html += '<tr>';

    // Add table headers based on profile settings
    <?php if ($this->settings->isProfile_profile_enabled('year_id')) : ?>
    html += '<th>Year</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('school_name')) : ?>
    html += '<th>School/College Name</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('class')) : ?>
    html += '<th>Class/Degree</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('board')) : ?>
    html += '<th>Board</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('school_address')) : ?>
    html += '<th>School/College Address</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('medium_of_instruction')) : ?>
    html += '<th>Medium of Instruction</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('university')) : ?>
    html += '<th>University</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('period')) : ?>
    html += '<th>Period</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('subjects')) : ?>
    html += '<th>Subjects</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('registration_no')) : ?>
    html += '<th>Registration Number</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('total_marks')) : ?>
    html += '<th>Total Marks</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('total_marks_scored')) : ?>
    html += '<th>Marks Obtained</th>';
    <?php endif; ?>

    <?php if ($this->settings->isProfile_profile_enabled('report_card')) : ?>
    html += '<th>Report Card</th>';
    <?php endif; ?>

    // Always show actions if user has edit permissions
    <?php if ($this->settings->isProfile_edit_enabled('year_id') ||
              $this->settings->isProfile_edit_enabled('school_name') ||
              $this->settings->isProfile_edit_enabled('class') ||
              $this->settings->isProfile_edit_enabled('board') ||
              $this->settings->isProfile_edit_enabled('medium_of_instruction') ||
              $this->settings->isProfile_edit_enabled('school_address') ||
              $this->settings->isProfile_edit_enabled('university') ||
              $this->settings->isProfile_edit_enabled('period') ||
              $this->settings->isProfile_edit_enabled('subjects') ||
              $this->settings->isProfile_edit_enabled('registration_no') ||
              $this->settings->isProfile_edit_enabled('total_marks') ||
              $this->settings->isProfile_edit_enabled('total_marks_scored') ||
              $this->settings->isProfile_edit_enabled('report_card')) : ?>
    html += '<th width="100">Actions</th>';
    <?php endif; ?>

    html += '</tr>';
    html += '</thead>';
    html += '<tbody>';

    data.forEach(function(item, index) {
        html += '<tr>';

        // Add table data based on profile settings
        <?php if ($this->settings->isProfile_profile_enabled('year_id')) : ?>
        html += '<td>' + escapeHtml(item.year_id || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('school_name')) : ?>
        html += '<td>' + escapeHtml(item.school_name || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('class')) : ?>
        html += '<td>' + escapeHtml(item.class || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('board')) : ?>
        html += '<td>' + escapeHtml(item.board || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('school_address')) : ?>
        html += '<td>' + escapeHtml(item.school_address || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('medium_of_instruction')) : ?>
        html += '<td>' + escapeHtml(item.medium_of_instruction || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('university')) : ?>
        html += '<td>' + escapeHtml(item.university_name || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('period')) : ?>
        html += '<td>' + escapeHtml(item.period || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('subjects')) : ?>
        html += '<td>' + escapeHtml(item.subjects || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('registration_no')) : ?>
        html += '<td>' + escapeHtml(item.registration_no || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('total_marks')) : ?>
        html += '<td>' + escapeHtml(item.total_marks || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('total_marks_scored')) : ?>
        html += '<td>' + escapeHtml(item.total_marks_scored || '-') + '</td>';
        <?php endif; ?>

        <?php if ($this->settings->isProfile_profile_enabled('report_card')) : ?>
        html += '<td>';
        if (item.report_card) {
            var fileExtension = item.report_card.split('.').pop().toLowerCase();
            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                html += '<a href="' + item.report_card_view_url + '" target="_blank" class="btn btn-sm btn-outline-primary" title="View Image">';
                html += '<i class="fa fa-image"></i>';
                html += '</a>';
            } else if (fileExtension === 'pdf') {
                html += '<a href="' + item.report_card_view_url + '" target="_blank" class="btn btn-sm btn-outline-danger" title="View PDF">';
                html += '<i class="fa fa-file-pdf-o"></i>';
                html += '</a>';
            } else {
                html += '<a href="' + item.report_card_view_url + '" target="_blank" class="btn btn-sm btn-outline-secondary" title="View File">';
                html += '<i class="fa fa-file"></i>';
                html += '</a>';
            }
        } else {
            html += '-';
        }
        html += '</td>';
        <?php endif; ?>

        // Add actions column if user has edit permissions
        <?php if ($this->settings->isProfile_edit_enabled('year_id') ||
                  $this->settings->isProfile_edit_enabled('school_name') ||
                  $this->settings->isProfile_edit_enabled('class') ||
                  $this->settings->isProfile_edit_enabled('board') ||
                  $this->settings->isProfile_edit_enabled('medium_of_instruction') ||
                  $this->settings->isProfile_edit_enabled('school_address') ||
                  $this->settings->isProfile_edit_enabled('university') ||
                  $this->settings->isProfile_edit_enabled('period') ||
                  $this->settings->isProfile_edit_enabled('subjects') ||
                  $this->settings->isProfile_edit_enabled('registration_no') ||
                  $this->settings->isProfile_edit_enabled('total_marks') ||
                  $this->settings->isProfile_edit_enabled('total_marks_scored') ||
                  $this->settings->isProfile_edit_enabled('report_card')) : ?>
        html += '<td>';
        html += '<button type="button" class="btn btn-sm" onclick="editPreviousSchoolDetails(' + (item.id) + ')" title="Edit">';
        html += `<?php $this->load->view('svg_icons/edit_icon.svg'); ?>`;
        html += '</button>';
        html += '<button type="button" class="btn btn-sm" onclick="deletePreviousSchoolDetails(' + (item.id || index) + ')" title="Delete">';
        html += `<?php $this->load->view('svg_icons/delete_icon.svg'); ?>`;
        html += '</button>';
        html += '</td>';
        <?php endif; ?>

        html += '</tr>';
    });

    html += '</tbody>';
    html += '</table>';
    html += '</div>'; // Close table-responsive div

    return html;
}

// Handle report card file selection for desktop
$(document).on('change', '#report_card', function() {
    var file = this.files[0];
    if (file) {
        // Validate file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB');
            this.value = '';
            return;
        }

        // Validate file type
        var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid image (JPG, PNG) or PDF file');
            this.value = '';
            return;
        }

        // Upload file to S3
        uploadReportCardToS3Desktop(file);
    }
});

// Upload report card to S3 for desktop
function uploadReportCardToS3Desktop(file) {
    // Show progress
    $('#report_card_progress').show();
    $('#report_card_preview').hide();

    // Get signed URL from server
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
        },
        success: function(response) {
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;

            // Upload file to S3
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        if (e.lengthComputable) {
                            var percentComplete = (e.loaded / e.total) * 100;
                            $('#report_card_progress .progress-bar').css('width', percentComplete + '%');
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    // Store path in hidden input
                    $('#report_card_path').val(path);

                    // Hide progress and show preview
                    $('#report_card_progress').hide();
                    $('#report_card_preview').show();
                    $('#report_card_preview .file-name').text(file.name);

                    console.log('Report card uploaded successfully to:', path);
                },
                error: function(xhr, status, error) {
                    $('#report_card_progress').hide();
                    alert('Failed to upload report card. Please try again.');
                    console.error('S3 upload error:', error);
                }
            });
        },
        error: function(xhr, status, error) {
            $('#report_card_progress').hide();
            alert('Failed to get upload URL. Please try again.');
            console.error('Signed URL error:', error);
        }
    });
}

// Remove report card file
function removeReportCard(type) {
    if (type === 'mobile') {
        $('#report_card_mobile').val('');
        $('#report_card_path_mobile').val('');
        $('#report_card_preview_mobile').hide();
        $('#report_card_progress_mobile').hide();
    } else {
        $('#report_card').val('');
        $('#report_card_path').val('');
        $('#report_card_preview').hide();
        $('#report_card_progress').hide();
    }
}

// Utility function to escape HTML characters for security
function escapeHtml(text) {
    if (text === null || text === undefined) {
        return '';
    }
    var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.toString().replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Function to add new previous school details
function addPreviousSchoolDetails() {
    // Reset form and modal title
    $('#previousSchoolForm')[0].reset();
    $('#school_id').val('');
    $('#previousSchoolModalLabel').text('Add Previous School Details');

    // Populate academic year dropdown
    populateAcademicYearDropdown();

    // Ensure save button has correct text
    $('.modal-footer .btn-primary').text('Save');

    // Show the modal
    $('#previousSchoolModal').modal('show');
}

// Function to populate academic year dropdown
function populateAcademicYearDropdown() {
    // Generate academic years (current year and previous 15 years)
    var currentYear = new Date().getFullYear();
    var yearOptions = '<option value="">Select Academic Year</option>';

    // Add future year (next academic year)
    var futureStartYear = currentYear;
    var futureEndYear = futureStartYear + 1;
    var futureAcademicYear = futureStartYear + '-' + futureEndYear;
    yearOptions += '<option value="' + futureAcademicYear + '">' + futureAcademicYear + '</option>';

    // Add current and previous years
    for (var i = 0; i <= 15; i++) {
        var startYear = currentYear - 1 - i;
        var endYear = startYear + 1;
        var academicYear = startYear + '-' + endYear;
        yearOptions += '<option value="' + academicYear + '">' + academicYear + '</option>';
    }

    $('#year_id').html(yearOptions);
}

// Function to edit previous school details
function editPreviousSchoolDetails(schoolId) {
    console.log('Edit previous school details clicked for ID:', schoolId);

    // Set modal title for editing
    $('#previousSchoolModalLabel').text('Edit Previous School Details');
    $('#school_id').val(schoolId);

    // Populate academic year dropdown first
    populateAcademicYearDropdown();

    // Load existing data for editing
    loadSchoolDetailsForEdit(schoolId);

    // Ensure save button has correct text
    $('.modal-footer .btn-primary').text('Save');

    // Show the modal
    $('#previousSchoolModal').modal('show');
}

// Function to load school details for editing
function loadSchoolDetailsForEdit(schoolId) {
    $.ajax({
        url: '<?php echo site_url("parent_controller/get_previous_school_details_by_id"); ?>',
        type: 'POST',
        data: { school_id: schoolId },
        dataType: 'json',
        success: function(data) {
            if (data) {
                <?php if ($this->settings->isProfile_edit_enabled('year_id')) : ?>
                $('#year_id').val(data.year_id);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('school_name')) : ?>
                $('#school_name').val(data.school_name);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('class')) : ?>
                $('#class').val(data.class);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('board')) : ?>
                $('#board').val(data.board);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('school_address')) : ?>
                $('#school_address').val(data.school_address);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('medium_of_instruction')) : ?>
                $('#medium_of_instruction').val(data.medium_of_instruction);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('university')) : ?>
                $('#university').val(data.university_name);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('period')) : ?>
                // Parse period data if it exists (format: "YYYY-MM-DD to YYYY-MM-DD")
                if (data.period) {
                    var periodParts = data.period.split(' to ');
                    if (periodParts.length === 2) {
                        $('#period_from').val(periodParts[0]);
                        $('#period_to').val(periodParts[1]);
                    } else {
                        // Handle legacy format or single date
                        $('#period_from').val('');
                        $('#period_to').val('');
                    }
                } else {
                    $('#period_from').val('');
                    $('#period_to').val('');
                }
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('subjects')) : ?>
                $('#subjects').val(data.subjects);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('registration_no')) : ?>
                $('#registration_number').val(data.registration_no);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('total_marks')) : ?>
                $('#total_marks').val(data.total_marks);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('total_marks_scored')) : ?>
                $('#total_marks_obtained').val(data.total_marks_scored);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('report_card')) : ?>
                // Handle report card file display
                if (data.report_card) {
                    $('#report_card_path').val(data.report_card);
                    $('#report_card_preview').show();
                    $('#report_card_preview .file-name').text(data.report_card.split('/').pop());
                } else {
                    $('#report_card_path').val('');
                    $('#report_card_preview').hide();
                }
                <?php endif; ?>
            }
        },
        error: function() {
        }
    });

}

// Function to delete previous school details
function deletePreviousSchoolDetails(schoolId) {
    console.log('Delete previous school details clicked for ID:', schoolId);

    // Use Bootbox for confirmation dialog
    bootbox.confirm({
        title: "Delete Previous School Record",
        message: "Are you sure you want to delete this previous school record? This action cannot be undone.",
        buttons: {
            confirm: {
                label: '<i class="fa fa-trash"></i> Delete',
                className: 'btn-danger'
            },
            cancel: {
                label: '<i class="fa fa-times"></i> Cancel',
                className: 'btn-secondary'
            }
        },
        callback: function (result) {
            if (result) {
                // Show loading dialog
                var loadingDialog = bootbox.dialog({
                    title: "Deleting Record",
                    message: '<div class="text-center"><i class="fa fa-spinner fa-spin fa-2x"></i><br><br>Please wait while we delete the record...</div>',
                    closeButton: false
                });

                $.ajax({
                    url: '<?php echo site_url("parent_controller/delete_previous_school"); ?>',
                    type: 'POST',
                    data: { school_id: schoolId },
                    dataType: 'json',
                    success: function(response) {
                        loadingDialog.modal('hide');

                        if (response) {
                            // Show success message
                            bootbox.alert({
                                title: "Success",
                                message: "Previous school record has been deleted successfully!",
                                className: "bootbox-success",
                                callback: function() {
                                    // Reload the table
                                    loadPreviousSchoolingDetails();
                                }
                            });
                        } else {
                            // Show error message
                            bootbox.alert({
                                title: "Error",
                                message: response.message || "Failed to delete the record. Please try again.",
                                className: "bootbox-error"
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        // Close loading dialog
                        loadingDialog.modal('hide');

                        // Show error message
                        bootbox.alert({
                            title: "Error",
                            message: "An error occurred while deleting the record. Please try again.",
                            className: "bootbox-error"
                        });
                        console.error('Delete error:', error);
                    }
                });
            }
        }
    });
}

// Function to save previous school details (add or edit)
function savePreviousSchoolDetails() {
    // Get form data
    var formData = {
        school_id: $('#school_id').val()
    };

    // Add fields based on edit settings
    <?php if ($this->settings->isProfile_edit_enabled('year_id')) : ?>
    formData.year_id = $('#year_id').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('school_name')) : ?>
    formData.school_name = $('#school_name').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('class')) : ?>
    formData.class = $('#class').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('board')) : ?>
    formData.board = $('#board').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('school_address')) : ?>
    formData.school_address = $('#school_address').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('medium_of_instruction')) : ?>
    formData.medium_of_instruction = $('#medium_of_instruction').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('university')) : ?>
    formData.university = $('#university').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('period')) : ?>
    // Merge period from and to dates
    var periodFrom = $('#period_from').val();
    var periodTo = $('#period_to').val();
    if (periodFrom && periodTo) {
        formData.period = periodFrom + ' to ' + periodTo;
    } else if (periodFrom) {
        formData.period = periodFrom + ' to Present';
    } else if (periodTo) {
        formData.period = 'Unknown to ' + periodTo;
    } else {
        formData.period = '';
    }
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('subjects')) : ?>
    formData.subjects = $('#subjects').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('registration_no')) : ?>
    formData.registration_number = $('#registration_number').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('total_marks')) : ?>
    formData.total_marks = $('#total_marks').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('total_marks_scored')) : ?>
    formData.total_marks_scored = $('#total_marks_obtained').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('report_card')) : ?>
    // Handle report card path from S3 upload
    formData.report_card = $('#report_card_path').val();
    <?php endif; ?>

    // Basic validation for edit-enabled fields
    var validationErrors = [];

    <?php if ($this->settings->isProfile_edit_enabled('year_id')) : ?>
    if (!formData.year_id) {
        validationErrors.push('Academic Year is required');
    }
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('school_name')) : ?>
    if (!formData.school_name) {
        validationErrors.push('School/College Name is required');
    }
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('class')) : ?>
    if (!formData.class) {
        validationErrors.push('Class/Degree is required');
    }
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('board')) : ?>
    if (!formData.board) {
        validationErrors.push('Board is required');
    }
    <?php endif; ?>

    if (validationErrors.length > 0) {
        bootbox.alert({
            title: "Validation Error",
            message: "Please fill in all required fields:<br><ul><li>" + validationErrors.join('</li><li>') + "</li></ul>",
            className: "bootbox-warning"
        });
        return;
    }

    // Determine if this is add or edit
    var isEdit = formData.school_id && formData.school_id !== '';
    var url = isEdit ?
        '<?php echo site_url("parent_controller/update_previous_school"); ?>' :
        '<?php echo site_url("parent_controller/add_previous_school_details"); ?>';

    // Show loading state
    var saveBtn = $('.modal-footer .btn-primary');
    saveBtn.prop('disabled', true).text('Saving...');

    // AJAX call to save data
    $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response) {
                // Close modal
                $('#previousSchoolModal').modal('hide');

                // Show success message
                bootbox.alert({
                    title: "Success",
                    message: isEdit ?
                        "Previous school details updated successfully!" :
                        "Previous school details added successfully!",
                    className: "bootbox-success",
                    callback: function() {
                        // Reload the table
                        loadPreviousSchoolingDetails();
                    }
                });
            } else {
                bootbox.alert({
                    title: "Error",
                    message: (response && response.message) || 'Failed to save previous school details. Please try again.',
                    className: "bootbox-error"
                });
            }
        },
        error: function(xhr, status, error) {
            bootbox.alert({
                title: "Error",
                message: "An error occurred while saving the details. Please check your connection and try again.",
                className: "bootbox-error"
            });
            console.error('Save error:', error);
        },
        complete: function() {
            // Restore button to original state
            saveBtn.prop('disabled', false).text('Save');
        }
    });
}

$(document).ready(function() {
    loadPreviousSchoolingDetails();
});
</script>

<style>
.card-header .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-top: none;
}

.table td {
    vertical-align: middle;
    min-width: 120px;
}

.table th:nth-child(1), .table td:nth-child(1) { min-width: 100px; } /* Year */
.table th:nth-child(2), .table td:nth-child(2) { min-width: 180px; } /* School Name */
.table th:nth-child(3), .table td:nth-child(3) { min-width: 100px; } /* Class */
.table th:nth-child(4), .table td:nth-child(4) { min-width: 120px; } /* Board */
.table th:nth-child(5), .table td:nth-child(5) { min-width: 200px; } /* School Address */
.table th:nth-child(6), .table td:nth-child(6) { min-width: 150px; } /* Medium */
.table th:nth-child(7), .table td:nth-child(7) { min-width: 150px; } /* University */
.table th:nth-child(8), .table td:nth-child(8) { min-width: 120px; } /* Period */
.table th:nth-child(9), .table td:nth-child(9) { min-width: 200px; } /* Subjects */
.table th:nth-child(10), .table td:nth-child(10) { min-width: 120px; } /* Actions */

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.btn-outline-primary:hover {
    color: #fff;
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .card-header .btn {
        margin-top: 10px;
        align-self: flex-end;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
}

/* Loading spinner styling */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* No data display styling */
.no-data-display {
    color: #6c757d;
    padding: 2rem;
}

/* Modal form styling */
.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 1.5rem !important;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    padding: 0.75rem;
    font-size: 0.95rem;
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

.form-control::placeholder {
    color: #999;
}

/* Modal header styling */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1.25rem 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: #333;
}

/* Modal footer styling */
.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

/* Required field indicator */
.text-danger {
    color: #dc3545 !important;
}

/* Textarea specific styling */
textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

/* Select dropdown styling */
.form-select, select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.form-select:focus, select.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

.position-relative .select-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #6c757d;
    font-size: 12px;
}

/* Hide custom arrow when using form-select class */
.form-select + .select-arrow {
    display: none;
}

/* Modal button styling */
.modal-footer .btn {
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
}

.modal-footer .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.modal-footer .btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

.modal-footer .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.form-control{
    height: 3rem;
}

/* Modal responsive adjustments */
@media (max-width: 768px) {
    .modal-dialog {
        width: 95% !important;
        margin: 10px auto !important;
    }

    .modal-body {
        padding: 20px !important;
    }

    .form-group {
        margin-bottom: 1.25rem !important;
    }

    .modal-footer {
        flex-direction: column;
        gap: 10px !important;
    }

    .modal-footer .btn {
        width: 100%;
        margin: 0;
    }
}

/* Bootbox dialog styling */
.bootbox .modal-dialog {
    width: 60% !important;
    max-width: 60% !important;
    margin: 30px auto !important;
}

.bootbox-success .modal-header {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.bootbox-success .modal-title {
    color: #155724;
}

.bootbox-error .modal-header {
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.bootbox-error .modal-title {
    color: #721c24;
}

.bootbox-warning .modal-header {
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.bootbox-warning .modal-title {
    color: #856404;
}

.bootbox .modal-body {
    padding: 1.5rem;
    font-size: 1rem;
}

.bootbox .btn {
    min-width: 80px;
    font-weight: 500;
}

.bootbox .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.bootbox .btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.bootbox .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.bootbox .btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* Responsive adjustments for Bootbox dialogs */
@media (max-width: 768px) {
    .bootbox .modal-dialog {
        width: 90% !important;
        max-width: 90% !important;
        margin: 15px auto !important;
    }
}

@media (max-width: 576px) {
    .bootbox .modal-dialog {
        width: 95% !important;
        max-width: 95% !important;
        margin: 10px auto !important;
    }

    .bootbox .modal-body {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .bootbox .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
}
</style>
<html lang="en">
<head>
    <!-- META SECTION -->
    <title><?php echo $this->settings->getSetting('school_name'); ?></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <link rel="icon" href="<?php echo base_url() . $this->settings->getSetting('favicon'); ?>" type="image/x-icon" />
    <!-- END META SECTION -->

    <!-- CSS INCLUDE -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/theme-default.css" />
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/parsley.css" />
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/bootstrap-datetimepicker.css" />
    <!-- <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/chung-timepicker.css"/> -->
    <!-- Notify -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/pnotify.css" />
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/pnotify.brighttheme.css" />
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/pnotify.buttons.css" />
    <link rel="stylesheet" type="text/css" href="<?php echo site_url('assets/css/monthly.css'); ?>" />
    <link rel="stylesheet" type="text/css" href="<?php echo site_url('assets/css/header_admissions.css'); ?>" />

    <style type="text/css" href="https://cdn.datatables.net/buttons/1.5.2/css/buttons.bootstrap.min.css"></style>


    <!-- EOF CSS INCLUDE -->

    <script type="text/javascript" src="<?php echo base_url();?>assets/js/jquery.min.js"></script>

    <!-- END PLUGINS -->
</head>

<body style="background-color: #F9F7FE !important">
    <div class="panel-group" style="margin: 0;padding:0">
        <div class=""
            style="height: 65px; background-color: #fff; box-shadow: 0 0 4px rgba(0,0,0,0.05);">

            <div class="panel-heading"
                style="display: flex; justify-content: space-between; align-items: center; height: 100%; background-color: transparent; padding: 0 15px;">

                <div style="display: flex; align-items: center; gap: 10px;">
                    <img class="img-rounded img-fluid" 
                        src="<?php echo base_url() . $this->settings->getSetting('school_logo'); ?>"
                        alt="School Logo" 
                        style="border-radius: 6px; width: 36px; height: 36px; object-fit: contain;" />

                    <span class="school_name_in_header"
                        style="font-size: 16px; color: #1a1a1a; font-weight: 700; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 200px; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
                        <?php echo $this->settings->getSetting('school_name'); ?>
                    </span>
                </div>

                <div>
                    <a href="#" id="logoutBtn"
                        style="text-decoration: none; display: inline-flex; align-items: center; gap: 6px; padding: 6px 12px; border: 1.5px solid #E73624; color: #E73624; border-radius: 8px; font-size: 13px; font-weight: 500; transition: all 0.2s ease; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">

                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24"
                            stroke="#dc3545" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a2 2 0 01-2 2H6a2 2 0 01-2-2V7a2 2 0 012-2h5a2 2 0 012 2v1" />
                        </svg>
                        Logout
                    </a>
                </div>
            </div>
        </div>
        <style>
        .logout-btn {
            display: inline-flex;
            align-items: center;
            padding: 10px 20px;
            border: 2px solid #e53935;
            border-radius: 12px;
            background-color: white;
            color: #e53935;
            font-size: 16px;
            font-family: Arial, sans-serif;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .logout-btn:hover {
            background-color: #e53935;
            color: white;
        }

        .logout-btn svg {
            margin-right: 8px;
            stroke: currentColor;
        }

        @media (max-width: 480px) {
            .panel-heading {
                padding: 0 12px !important;
            }
            
            .school_name_in_header {
                font-size: 15px !important;
                font-weight: 700 !important;
                max-width: 180px !important;
            }

            #logoutBtn {
                padding: 5px 10px !important;
                font-size: 12px !important;
                border-radius: 6px !important;
            }

            #logoutBtn svg {
                width: 14px !important;
                height: 14px !important;
            }

            .custom-button-container {
                display: flex;
                justify-content: center;
                gap: 12px;
                margin-top: 10px;
            }
        }

        .swal2-confirm.btn-primary {
            background-color: #6A4CFF;
            color: white;
            border: none;
            padding: 10px 18px;
            font-size: 14px;
            border-radius: 8px;
            font-weight: 600;
            min-width: 120px;
        }

        .swal2-cancel.btn-outline {
            background: transparent;
            color: #6A4CFF;
            border: 2px solid #6A4CFF;
            padding: 10px 18px;
            font-size: 14px;
            border-radius: 8px;
            font-weight: 600;
            min-width: 120px;
        }

        .rounded-xl {
            border-radius: 12px;
            /* You can adjust this value */
        }

        .custom-button-container {
            display: flex;
            justify-content: center;
            /* Center the buttons horizontally */
            gap: 12px;
            /* Minimal horizontal space between buttons */
            margin-top: 20px;
            /* Slight vertical gap from message */
        }
        </style>

        <script>
        document.getElementById("logoutBtn").addEventListener("click", function(e) {
            e.preventDefault(); // Prevent default link action
            // $("body").addClass("modal1");
            // $(".modal1").css("display", 'contents');

            Swal.fire({
                title: `
                    Are you sure you want to logout?
                `,
                html: '<p style="text-align:center; font-size: 16px;"></p>',
                customClass: {
                    popup: 'rounded-xl shadow-lg',
                    title: 'text-lg font-semibold text-gray-800',
                    htmlContainer: 'text-sm text-gray-500'
                },
                showCancelButton: false,
                showConfirmButton: false,
                reverseButtons: true,
                didOpen: () => {
                    const popup = Swal.getPopup();

                    // 🔼 Add space ABOVE the title
                    const topPadding = document.createElement('div');
                    topPadding.style.height = "20px";
                    popup.insertBefore(topPadding, popup.firstChild);

                    // ✅ Style the title if needed
                    const titleEl = document.querySelector(".swal2-title");
                    if (titleEl) {
                        titleEl.style.backgroundColor = "transparent";
                    }

                    // ⬇️ Add custom buttons
                    const container = Swal.getHtmlContainer();
                    const footer = document.createElement('div');
                    footer.className =
                        'custom-button-container mt-3 d-flex justify-content-center gap-3';
                    footer.innerHTML = `
                        <button class="swal2-cancel btn-outline" id="cancelBtn">Cancel</button>
                        <button class="swal2-confirm btn-primary" id="createBtn">Logout</button>
                    `;
                    container.parentNode.appendChild(footer);

                    // 🔽 Add space BELOW the buttons
                    const bottomPadding = document.createElement('div');
                    bottomPadding.style.height = "20px";
                    footer.parentNode.appendChild(bottomPadding);

                    // Event listeners
                    document.getElementById('cancelBtn').addEventListener('click', () =>
                        Swal.close());

                    document.getElementById('createBtn').addEventListener('click', () => {
                        window.location.href =
                            "<?php echo site_url('admission_controller/logout'); ?>";
                    });
                }
            });
        });
        </script>

        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

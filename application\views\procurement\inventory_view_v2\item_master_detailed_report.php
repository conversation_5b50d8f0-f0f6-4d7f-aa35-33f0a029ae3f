<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/inventory_controller_v2/item_master_widgets'); ?>">Inventory
            Management</a>
    </li>
    <li class="active">Item Master Report</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="panel-header"
                style="margin: 0px; background: none; border-bottom: 1px solid lightgray; height: 3.7rem;">
                <h3>
                    <a style="" class="back_anchor"
                        href="<?php echo site_url('procurement/inventory_controller_v2/item_master_widgets') ?>"
                        class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    Item Master Report
                </h3>
            </div>
        </div>
        <div class="col-md-12">
            <div class="purchase-container">
                <div class="row mb-5">
                    <!-- <div class="col-md-2 form-group">
                        <label class="control-label">Date Range</label>
                        <div id="reportrange" class="dtrange" style="width: 100%">
                            <span></span>
                            <input type="hidden" id="from_date">
                            <input type="hidden" id="to_date">
                        </div>
                    </div> -->

                    <div class="col-md-2 form-group">
                        <label class="control-label">Category</label>
                        <select title="All" class="form-control selectpicker" name="item_master_category"
                            id="item_master_category">
                            <?php
                            if (empty($item_master_categories)) {
                                echo '<option value="-1">No category found</option>';
                            } else {
                                echo '<option value="All" selected>All</option>';
                                foreach ($item_master_categories as $key => $itemMaster) {
                                    echo '<option value="' . $itemMaster->id . '">' . $itemMaster->category_name . '</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Sub Category</label>
                        <select title="All" class="form-control selectpicker" name="item_master_sub_category"
                            id="item_master_sub_category">
                            <?php
                            if (empty($getItemMasterSubCategory)) {
                                echo '<option value="-1">No category found</option>';
                            } else {
                                echo '<option value="All" selected>All</option>';
                                foreach ($getItemMasterSubCategory as $key => $itemMaster) {
                                    echo '<option value="' . $itemMaster->id . '">' . $itemMaster->subcategory_name . '</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Budget Category</label>
                        <select title="All" class="form-control selectpicker" name="expense_category"
                            id="expense_category">
                            <?php
                            if (empty($getExpenseCategory)) {
                                echo '<option value="-1">No category found</option>';
                            } else {
                                echo '<option value="All" selected>All</option>';
                                foreach ($getExpenseCategory as $key => $itemMaster) {
                                    echo '<option value="' . $itemMaster->id . '">' . $itemMaster->category_name . '</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Budget Sub Category</label>
                        <select title="All" class="form-control selectpicker" name="expense_sub_category"
                            id="expense_sub_category">
                            <?php
                            if (empty($getExpenseSubCategory)) {
                                echo '<option value="-1">No category found</option>';
                            } else {
                                echo '<option value="All" selected>All</option>';
                                foreach ($getExpenseSubCategory as $key => $itemMaster) {
                                    echo '<option value="' . $itemMaster->id . '">' . $itemMaster->sub_category . '</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="col-md-2 d-flex align-items-end" style="height: 4.4rem;">
                        <button onclick="getItemMasterSummary()" class="btn btn-dark" style="width: 120px;">
                            <i class="fa fa-file-alt"></i> Get Summary
                        </button>
                    </div>
                </div>
                <div class="item-master-summary-table">
                    <div style="color: #333; text-align: center; padding: 2rem;">
                        <i class="fa fa-spinner fa-spin" style="font-size:2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .daterangepicker .applyBtn {
        background-color: #343a40 !important;
        border-color: #343a40 !important;
        color: #fff !important;
    }

    .custom-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
</style>

<!-- datatable styles -->
<style>
    .breadcrumb {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
    }

    /* .breadcrumb li a {
        color: #007bff;
        text-decoration: none;
    } */

    /* .breadcrumb li a:hover {
        text-decoration: underline;
    } */

    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
    }

    .btn-primary:hover {
        background-color: #0056b3;
        border-color: #004085;
    }

    .purchase-container {
        background-color: #ffffff;
        /* border: 1px solid #dee2e6; */
        border-radius: 5px;
        padding: 1rem;
    }

    .purchase-details-table {
        margin-top: 1rem;
    }

    .purchase-details-table table {
        border-collapse: collapse;
        width: 100%;
    }

    .purchase-details-table th,
    .purchase-details-table td {
        border: 1px solid #dee2e6;
        padding: 0.75rem;
        text-align: left;
    }

    .purchase-details-table th {
        background-color: #f8f9fa;
    }

    .dataTables_wrapper .dt-buttons {
        float: right;
    }

    .dataTables_filter input {
        background-color: #f2f2f2;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-right: 5px;
    }

    .dataTables_wrapper .dataTables_filter {
        float: right;
        text-align: left;
        width: unset;
    }

    .dt-buttons {
        margin-bottom: 5px;
    }

    /* scrollbar area */
    /* Hide the default scrollbar */
    ::-webkit-scrollbar {
        width: 5px;
        height: 9px;
    }

    /* Create a custom scrollbar */
    ::-webkit-scrollbar-track {
        background-color: #f2f2f2;
        border-radius: 10px;
    }

    /* Create a thumb for the scrollbar */
    ::-webkit-scrollbar-thumb {
        /* background-color: #007bff;  */
        border-radius: 100px;
    }

    /* Make the scrollbar visible when hovering over the track */
    ::-webkit-scrollbar-track-piece-over:hover {
        background-color: #ddd;
    }

    /* Make the scrollbar thumb visible when hovering over it */
    ::-webkit-scrollbar-thumb:hover {
        background-color: #C7C8CC;
    }

    .swal-wide {
        max-width: 600px !important;
        /* Set a proper max width for the modal */
        width: 90% !important;
        /* Ensure responsiveness */
        padding: 20px !important;
        /* Add padding for better spacing */
    }

    .daterangepicker .applyBtn {
        background-color: #343a40 !important;
        border-color: #343a40 !important;
        color: #fff !important;
    }
</style>

<link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/css/bootstrap-select.min.css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript"
    src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">
    $(document).ready(function () {
        getItemMasterSummary();

        // Initialize dynamic filtering
        $('#item_master_category').on('change', function () {
            loadItemMasterSubCategories();
        });

        $('#expense_category').on('change', function () {
            loadExpenseSubCategories();
        });
    });

    // utils start
    // Generate alert-style message box
    function generateMessageHelper(msg = "Loading...") {
        return `
        <div style="
            color: black;
            background: #ebf3ff;
            border: 2px solid #fffafa;
            border-radius: 6px;
            padding: 10px;
            font-size: 14px;
            margin: 14px 0;
            text-align: center;">
            ${msg}
        </div>`;
    }

    // function initializeDateRangePicker() {
    //     $('#reportrange').on('show.daterangepicker', function (ev, picker) {
    //         picker.container.find('.applyBtn').removeClass('btn-success').addClass('btn-dark');
    //     });

    //     const end = moment(); // Today
    //     const start = moment().subtract(6, 'months'); // 6 months ago

    //     $("#reportrange").daterangepicker({
    //         maxDate: end,
    //         ranges: {
    //             'Today': [end.clone(), end.clone()],
    //             'Yesterday': [end.clone().subtract(1, 'days'), end.clone().subtract(1, 'days')],
    //             'Last 7 Days': [end.clone().subtract(6, 'days'), end.clone()],
    //             'Last 30 Days': [end.clone().subtract(29, 'days'), end.clone()],
    //             'Last 6 Months': [start.clone(), end.clone()],
    //             // 'All Time': [moment("2025-01-01"), end.clone()]
    //         },
    //         opens: 'right',
    //         format: 'DD-MM-YYYY',
    //         startDate: start,
    //         endDate: end
    //     }, function (start, end) {
    //         $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    //         $('#from_date').val(start.format('DD-MM-YYYY'));
    //         $('#to_date').val(end.format('DD-MM-YYYY'));
    //     });

    //     // Set initial visible values
    //     $("#reportrange span").html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    //     $('#from_date').val(start.format('DD-MM-YYYY'));
    //     $('#to_date').val(end.format('DD-MM-YYYY'));
    // }

    // initializeDateRangePicker();

    // Dynamic filtering functions
    function loadItemMasterSubCategories() {
        const categoryId = $("#item_master_category").val();
        const $subCategorySelect = $("#item_master_sub_category");

        if (categoryId === 'All' || !categoryId) {
            // Reset to show all sub-categories
            $subCategorySelect.html('<option value="All" selected>All</option>');
            <?php
            if (!empty($getItemMasterSubCategory)) {
                foreach ($getItemMasterSubCategory as $itemMaster) {
                    echo '$subCategorySelect.append(\'<option value="' . $itemMaster->id . '">' . addslashes($itemMaster->subcategory_name) . '</option>\');';
                }
            }
            ?>
            $subCategorySelect.selectpicker('refresh');
            return;
        }

        // Show loading
        $subCategorySelect.html('<option value="">Loading...</option>').selectpicker('refresh');

        $.ajax({
            url: "<?php echo site_url('procurement/inventory_controller_v2/getItemMasterSubCategoriesByCategory') ?>",
            type: "POST",
            data: { category_id: categoryId },
            success: function (response) {
                try {
                    const data = JSON.parse(response);
                    let options = '<option value="All" selected>All</option>';

                    if (data && data.length > 0) {
                        data.forEach(function (item) {
                            options += `<option value="${item.id}">${item.subcategory_name}</option>`;
                        });
                    }

                    $subCategorySelect.html(options).selectpicker('refresh');
                } catch (e) {
                    console.error('Error parsing sub-categories:', e);
                    $subCategorySelect.html('<option value="All">All</option>').selectpicker('refresh');
                }
            },
            error: function () {
                console.error('Error loading sub-categories');
                $subCategorySelect.html('<option value="All">All</option>').selectpicker('refresh');
            }
        });
    }

    function loadExpenseSubCategories() {
        const categoryId = $("#expense_category").val();
        const $subCategorySelect = $("#expense_sub_category");

        if (categoryId === 'All' || !categoryId) {
            // Reset to show all expense sub-categories
            $subCategorySelect.html('<option value="All" selected>All</option>');
            <?php
            if (!empty($getExpenseSubCategory)) {
                foreach ($getExpenseSubCategory as $itemMaster) {
                    echo '$subCategorySelect.append(\'<option value="' . $itemMaster->id . '">' . addslashes($itemMaster->sub_category) . '</option>\');';
                }
            }
            ?>
            $subCategorySelect.selectpicker('refresh');
            return;
        }

        // Show loading
        $subCategorySelect.html('<option value="">Loading...</option>').selectpicker('refresh');

        $.ajax({
            url: "<?php echo site_url('procurement/inventory_controller_v2/getExpenseSubCategoriesByCategory') ?>",
            type: "POST",
            data: { category_id: categoryId },
            success: function (response) {
                try {
                    const data = JSON.parse(response);
                    let options = '<option value="All" selected>All</option>';

                    if (data && data.length > 0) {
                        data.forEach(function (item) {
                            options += `<option value="${item.id}">${item.sub_category}</option>`;
                        });
                    }

                    $subCategorySelect.html(options).selectpicker('refresh');
                } catch (e) {
                    console.error('Error parsing expense sub-categories:', e);
                    $subCategorySelect.html('<option value="All">All</option>').selectpicker('refresh');
                }
            },
            error: function () {
                console.error('Error loading expense sub-categories');
                $subCategorySelect.html('<option value="All">All</option>').selectpicker('refresh');
            }
        });
    }

    function initDataTable(tableId) {
        $(`#${tableId}`).DataTable({
            "language": {
                "search": "",
                "searchPlaceholder": "Enter Search..."
            },
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pageLength": 10,
            "order": false,
            dom: 'lBfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    filename: "Item master Report",
                    className: 'btn btn-dark'
                },
                {
                    extend: 'print',
                    text: 'Print',
                    autoPrint: true,
                    filename: "Item master Report",
                    className: 'btn btn-dark'
                },
                {
                    extend: 'pdfHtml5',
                    text: 'PDF',
                    filename: "Item master Report",
                    className: 'btn btn-dark'
                }
            ]
        });
    }
    // utils end

    // Global variables for chunked loading
    let allData = [];
    let currentOffset = 0;
    let totalCount = 0;
    let chunkSize = 500;
    let isLoading = false;

    function getItemMasterSummary() {
        // Reset global variables
        allData = [];
        currentOffset = 0;
        totalCount = 0;
        isLoading = false;

        // const fromDate = $("#from_date").val();
        // const toDate = $("#to_date").val();
        const itemMasterCategory = $("#item_master_category").val();
        const itemMasterSubCategory = $("#item_master_sub_category").val();
        const expenseCategory = $("#expense_category").val();
        const expenseSubCategory = $("#expense_sub_category").val();

        // Disable the button and show loading
        const $btn = $("button[onclick='getItemMasterSummary()']");
        $btn.prop('disabled', true).html('Please wait...');
        $(".item-master-summary-table").html(
            `<div style="color: #333; text-align: center; padding: 2rem;">
                <i class="fa fa-spinner fa-spin" style="font-size:2rem;"></i>
                <div id="loading-progress" style="margin-top: 10px;">Initializing...</div>
            </div>`
        );

        // Start loading first chunk
        loadDataChunk({
            itemMasterCategory: itemMasterCategory,
            itemMasterSubCategory: itemMasterSubCategory,
            expenseCategory: expenseCategory,
            expenseSubCategory: expenseSubCategory,
        }, $btn);
    }

    function loadDataChunk(filters, $btn) {
        if (isLoading) return;
        isLoading = true;

        const requestData = {
            ...filters,
            chunk_size: chunkSize,
            offset: currentOffset
        };

        $.ajax({
            url: "<?php echo site_url('procurement/inventory_controller_v2/getItemMasterSummary') ?>",
            type: "POST",
            data: requestData,
            success: function (res) {
                try {
                    res = JSON.parse(res);

                    if (currentOffset === 0) {
                        // First chunk - initialize
                        totalCount = res.total_count;
                        allData = [];
                    }

                    // Add new data to our collection
                    allData = allData.concat(res.data);
                    currentOffset += chunkSize;

                    // Update progress
                    const loadedCount = allData.length;
                    const progressPercent = Math.round((loadedCount / totalCount) * 100);
                    $("#loading-progress").html(`Loaded ${loadedCount} of ${totalCount} records (${progressPercent}%)`);

                    if (res.has_more) {
                        // Load next chunk
                        isLoading = false;
                        setTimeout(() => loadDataChunk(filters, $btn), 100); // Small delay to prevent overwhelming
                    } else {
                        // All data loaded, render table
                        renderCompleteTable($btn);
                    }
                } catch (e) {
                    console.error("Parsing error: ", e);
                    $(".item-master-summary-table").html(
                        `<div style="color: red; text-align: center;">Error loading summary report</div>`
                    );
                    $btn.prop('disabled', false).html('<i class="fa fa-file-alt"></i> Get Summary');
                }
            },
            error: function () {
                $(".item-master-summary-table").html(
                    `<div style="color: red; text-align: center;">Server error occurred</div>`
                );
                $btn.prop('disabled', false).html('<i class="fa fa-file-alt"></i> Get Summary');
            }
        });
    }

    function renderCompleteTable($btn) {
        let html = '';

        if (allData.length > 0) {
            // <div style="margin-bottom: 10px; color: #666;">
            //     <strong>Total Records: ${allData.length}</strong>
            // </div>
            html = `
                <div style="overflow-x:auto;">
                <table class="table table-bordered" id="indentSummaryReportDT" style="width: 100%;white-space: nowrap;">
                    <thead style="background: #f2f2f2;">
                        <tr>
                            <th>#</th>
                            <th>Category</th>
                            <th>Sub Category</th>
                            <th>Name</th>
                            <th>Is Active</th>
                            <th>SKU Code</th>
                            <th>Unit Type</th>
                            <th>Budget Category</th>
                            <th>Budget Sub Category</th>
                            <th>Department</th>
                            <th>Approver 1</th>
                            <th>Approver 2</th>
                            <th>Approver 3</th>
                            <th>Approval Strategy</th>
                            </tr>
                            </thead>
                            <tbody>
                            `;

            allData.forEach((row, index) => {
                html += `
                <tr>
                    <td>${++index}</td>
                    <td>${row['item_category'] ?? 'NA'}</td>
                    <td>${row['item_sub_category'] ?? 'NA'}</td>
                    <td>${row['item_name'] ?? 'NA'}</td>
                    <td>${row['is_active'] ?? 'NA'}</td>
                    <td>${row['sku_code'] ?? 'NA'}</td>
                    <td>${row['unit_type'] ?? 'NA'}</td>
                    <td>${row['expense_category'] ?? 'NA'}</td>
                    <td>${row['expense_sub_category'] ?? 'NA'}</td>
                    <td>${row['department_name'] ?? 'NA'}</td>
                    <td>${row['approver_1_name'] ?? 'NA'}</td>
                    <td>${row['approver_2_name'] ?? 'NA'}</td>
                    <td>${row['approver_3_name'] ?? 'NA'}</td>
                    <td>${row['approval_algorithm_name'] ?? 'NA'}</td>
                </tr>
            `;
            });

            html += `</tbody></table></div>`;
        } else {
            html += `${generateMessageHelper("No data found")}`;
        }

        $(".item-master-summary-table").html(html);

        if (allData.length > 0) {
            initDataTable("indentSummaryReportDT");
        }

        // Re-enable the button
        $btn.prop('disabled', false).html('<i class="fa fa-file-alt"></i> Get Summary');
        isLoading = false;
    }
</script>
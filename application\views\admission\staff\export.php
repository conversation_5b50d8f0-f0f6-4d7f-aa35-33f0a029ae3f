
<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('admission_process');?>">Applications</a></li>
  <li class="active">Application Reports</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('admission_process'); ?>">
                          <span class="fa fa-arrow-left"></span>
                        </a> 
                        Application Reports
                    </h3>   
                    <div class="col-md-8 d-flex align-items-center justify-content-end">
                    <div class="col-md-3">
                            <select  class="form-control custom-select"  id="predefined_data" name="predefined_data" onchange="select_feild_names()">
                              <option value="">Select Predefined data</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
        <div class="card-body">
         
          <form enctype="multipart/form-data" id="admission_report" autocomplete="off" id="export-form"  class="form-horizontal" data-parsley-validate method="post">
             
              <div class="col-md-4">
                <div class="form-group">
                  <label class="col-md-4 control-label" for="classsection">Select Grade</label>  
                    <div class="col-md-8">
                        <select class="form-control" name="class_list" id="class_list">
                          <option value="">All</option>
                          <?php foreach ($grades as $key => $val) { ?>
                            <option value="<?php echo $val->class_name ?>"><?php echo $val->class_name ?></option>
                          <?php } ?>
                        </select>
                    </div>
                </div>

                <?php if(!empty($combination)) { ?>
                <div class="form-group" id="combinations_dispaly">
                  <label class="col-md-4 control-label" for="Combinations">Combinations<font color="red">*</font></label>  
                    <div class="col-md-8">
                      <select class="form-control" name="combination" id="combination">
                        <option value="">All</option>
                        <?php  foreach($combination as $key => $val) { ?>
                            <option value="<?= $val->id  ?>"><?= $val->name ?></option>
                        <?php } ?>
                      </select>
                    </div>
                </div>
                <?php } ?>
              </div>

                <div class="col-md-4">
                  <div class="form-group">
                    <label class="col-md-4 control-label" for="classsection"> Select Field(s)</label>  
                      <div class="col-md-8">
                        <select id="fields" name="fields[]" required="" class="form-control input-md selectpicker" multiple="" title="Select Fields">
                        <?php foreach ($columnList as $column) { ?>
                          <option value="<?php echo $column['index']; ?>"><?php echo $column['displayName']; ?></option>
                        <?php } ?>
                          <option value="fee_status"><?php echo 'Fee status'; ?></option>
                        </select>
                      </div>
                  </div>
                </div>

                <div class="col-md-4">
                  <div class="form-group">
                    <label class="col-md-4 control-label" for="classsection"> Select Status</label>  
                      <div class="col-md-8">
                        <select id="ad_status" name="ad_status[]" class="form-control selectpicker" multiple title="All">
                          <option value="">All</option>
                          <?php if(!$this->settings->getSetting('admission_pick_status_from_table')){

                           foreach ($follow_up_status as $key => $value) {
                              echo ' <option value="'.$value.'">'.$value.'</option>';
                            }
                            echo '<option value="Submitted">Submitted</option>';
                            echo ' <option value="Application Amount Paid">Application Amount Paid</option>';
                            echo '  <option value="Admit">Admit</option>';
                            echo ' <option value="Student added to ERP">Student added to ERP</option>';
                            echo ' <option value="Rejected">Rejected</option>';
                            echo ' <option value="Draft">Draft</option>';
                            echo ' <option value="Duplicate">Duplicate</option>';
                          }else{
                            foreach ($follow_up_status as $key => $value) { ?>
                              <option value="<?php echo $value->user_status ?>" style="color:black;"><?php echo $value->user_status ?></option>
                           <?php } ?>
                           <?php } ?>
                        </select>
                      </div>
                  </div>
                </div>

                <div class="col-md-12" style="margin: 20px 0 20px 0;">
                <center>
                  <button type="button" onclick="generate_report()" class="btn btn-primary" >Generate</button>
                  <button type="button" id="save_btn" data-toggle="modal" data-target="#save_field_name" class="btn btn-success" >Save as Report Template</button>
                  <button type="button" id="update_btn" data-toggle="modal" data-target="#update_field_name" style="display: none;" class="btn btn-warning" >Update Report Template</button>
                </center>
              </div>  

              </form>
          </div>
        </div>
    </div>
</div>
<div class="col-md-12">
    <div class="card cd_border" style="display: none;" id="admission_report_data">
            <div class="d-flex justify-content-between" style="width:100%;">
              <h4 style="margin:20px;padding-left:30px"><strong>Admission report</strong></h4>
                <!-- <div class="col-md-6 d-flex align-items-center justify-content-end"> -->
                  <!-- <div class="col-md-4" style="margin-right: 10px;">
                    <input type="text" id="dynamic_search" class="form-control" placeholder="Search in results..." style="display: none;">
                  </div> -->
                  <!-- <ul class="panel-controls" id="excel">
                      <a style="margin-right:3px;" onclick="exportToExcel()" class="btn btn-primary pull-right">Export to excel</a>
                  </ul>
                </div> -->
            </div>
            <div class="card-body">
              <ul class="panel-controls" style="margin-right:10px;width: 300px;" id="range-input"></ul>
            <div class="table-responsive" id="export_admissions">

            </div>
    </div>
</div>


<div class="modal fade" id="save_field_name" tabindex="-1" role="dialog" style="width:35%;margin:auto;top:25%" data-backdrop="static" aria-labelledby="save_field_name-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader">Add Title</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body">
        <form id="titleModalform">
          <div class="col-md-12">
            <div class="form-group">
              <label  class="col-md-4">Title Name<font color="red">*</font></label>
              <div class="col-md-6">
                  <input id="title" class="form-control" placeholder="Enter title" name="title" class="form-control" type="text" maxlength="100" required/>
              </div>
            </div>
          </div>
        </form> 
      </div> 
      <div class="modal-footer">
            <button class="btn btn-primary" id="submit" type="button" onclick="save_admission_fields()">Submit </button>
      </div>
    </div>
  </div>

  <div class="modal fade" id="update_field_name" tabindex="-1" role="dialog" style="width:35%;margin:auto;top:25%" data-backdrop="static" aria-labelledby="update_field_name-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader">Update Title</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body">
      <form id="updateModalform">
        <input type="hidden" id="update_id" name="update_id">
        <div class="col-md-12">
          <div class="form-group">
            <label  class="col-md-4">Title Name<font color="red">*</font></label>
            <div class="col-md-6">
                <input id="update_title" class="form-control" placeholder="Enter title" name="title" class="form-control" type="text" maxlength="100" />
            </div>
          </div>
        </div>
      </form> 
      </div> 
      <div class="modal-footer">
            <button class="btn btn-primary" id="submit" type="button" onclick="update_admission_fields()">Update</button>
      </div>
    </div>
  </div>



<?php 

  $dataTableArr['datatablescript'] = [
    'reference_id'  => 'details_schooling',
    'ordering' => 'false',
    'searching' => 'false',
    'paging' => 'false',
    'buttons' => json_encode(['excel'])
  ];


  $this->session->set_userdata('datatablescript', $dataTableArr);
?>
<script type="text/javascript">
  
  function exportToExcel() {
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
      return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
      return s.replace(/{(\w+)}/g, function(m, p) {
        return c[p];
      })
    };

    var mainTable = $("#export_admissions").html();


    htmls = mainTable;

    var ctx = {
      worksheet: 'Spreadsheet',
      table: htmls
    }


    var link = document.createElement("a");
    link.download = "export.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();

  }

  $(document).ready(function() {
    $('#ad_status').selectpicker({
        liveSearch: true, // Enables search functionality
        liveSearchPlaceholder: 'Search fields...', // Set placeholder text
    });
    $('#fields').selectpicker({
        liveSearch: true, // Enables search functionality
        liveSearchPlaceholder: 'Search fields...', // Set placeholder text
    });

    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
            $('.bootstrap-select').removeClass('open show'); 
            $('.dropdown-menu').removeClass('show'); 
        }
    });
  });

  $('#class_list').on('change',function(){
    var selectedClass = $('#class_list').val();
    $.ajax({
      url:'<?php echo site_url('admission_process/combinations/'); ?>',
      type:'post',
      data: {'selectedClass':selectedClass},
      success: function(data) {
        console.log(data);
        if (data == '') {
          $('#combinations_dispaly').hide();
        }else{
          // $('#combination').html(data);
          $('#combinations_dispaly').show();
        }
      }
    });
  });

  $(document).ready(function() {
    get_predefined_names();
    });

  function get_predefined_names(){
    $.ajax({
      url:'<?php echo site_url('admission_process/get_predefined_names'); ?>',
      type:'post',
      success: function(data) {
        var field_data = JSON.parse(data);
        if(field_data){
       
          html = '';
          html +='<option>Select Predefined data</option>';
          var len = field_data.predefined_data.length;
          // console.log(field_data.predefined_data);
          for(i=0;i<len;i++){
            var data = field_data.predefined_data;
            html +='<option value="'+data[i].id+'">'+data[i].title+'</option>';
          }
          $('#predefined_data').html(html);
        }
      }
    });
  }

  function select_feild_names(){
    var columnList = <?php echo json_encode($columnList) ?>;
    var grades = '<?php echo json_encode($grades) ?>';
    var gradesarr = $.parseJSON(grades);
    var follow_up_status = '<?php echo json_encode($follow_up_status) ?>';
    var follow_up_status_Arr = $.parseJSON(follow_up_status);
    var pickfromtableAdmStatus = '<?php echo $this->settings->getSetting('admission_pick_status_from_table') ?>';
    // console.log(follow_up_status_Arr);
   
    var id = $('#predefined_data').val();

    $.ajax({
      url:'<?php echo site_url('admission_process/get_predefined_data'); ?>',
      type:'post',
      data:{'id':id},
      success: function(data) {
        var field_data = JSON.parse(data);
        
        if(field_data){
          // $('#fields').html(_construct_fields(field_data.field_id,columnList));
          var field_Id_arr = field_data.field_id.split(',');
          console.log(field_data);
          $("#fields option:selected").prop("selected", false);
          field_Id_arr.forEach(field => {
              $("#fields").find(`option[value="${field}"]`).prop("selected", true);
          });
          $('#fields').selectpicker('refresh');

          $('#class_list').html(_construct_grades(field_data.grade,gradesarr));

          var status_arr = field_data.status.split(',');
          $("#ad_status option:selected").prop("selected", false);
          status_arr.forEach(status => {
              $("#ad_status").find(`option[value="${status}"]`).prop("selected", true);
          });
          $('#ad_status').selectpicker('refresh');

          // if(!pickfromtableAdmStatus){
          //   $('#ad_status').html(_construct_status_config(field_data.status,follow_up_status_Arr));
          // }else{
          //   $('#ad_status').html(_construct_status(field_data.status,follow_up_status_Arr));
          // }
          
          $('#update_id').val(field_data.id);
          $('#update_title').val(field_data.title);
          $('#save_btn').hide();
          $('#update_btn').show();
        }
      }
    });
    
    
  }

  function _construct_fields(field_Id,columnList){
    var field_Id_arr = field_Id.split(',');
    console.log(field_Id_arr);
    var len = columnList.length;
    
    var html = '';
    var selectedFee = '';
    for(var i in columnList){
      var selected = '';
      selectedFee = '';
      for(j=0;j<field_Id_arr.length;j++){
        if(columnList[i].index == field_Id_arr[j]) {
          selected = 'selected';
        }
        if(field_Id_arr[j] == 'fee_status') {
          selectedFee = 'selected';
        }
      }
      
      html +='<option '+selected+' value="'+columnList[i].index+'">'+columnList[i].displayName+'</option>';
    }
    html +='<option '+selectedFee+' value="fee_status">Fee status</option>';
    return html;
  }

  function _construct_grades(grade,gradesarr){
     var grade_output = '';
     grade_output +='<option value="">All</option>';
     for(i=0;i<gradesarr.length;i++){
      var selected = '';
      if(gradesarr[i].class_name == grade){
        selected = 'selected';
      }
      grade_output +='<option '+selected+' value="'+gradesarr[i].class_name +'">'+gradesarr[i].class_name+'</option>';
     }
     return grade_output;
  }

  function _construct_status(status,follow_up_status_Arr){
    // console.log(follow_up_status_Arr);
    var status_output = '';
     status_output +='<option value="">All</option>';
     for(i=0;i<follow_up_status_Arr.length;i++){
      var selected = '';
      if(follow_up_status_Arr[i].user_status == status){
        selected = 'selected';
      }
      status_output +='<option '+selected+' value="'+follow_up_status_Arr[i].user_status +'">'+follow_up_status_Arr[i].user_status+'</option>';
     }
     return status_output;
    }

   
  function _construct_status_config(status,follow_up_status_Arr){
    var defaultStatus = ['Submitted','Application Amount Paid','Admit','Student added to ERP','Rejected','Draft','Duplicate'];
    var mergeData = $.merge( follow_up_status_Arr, defaultStatus )
    var status_output = '';
     status_output +='<option value="">All</option>';
     for(i=0;i<mergeData.length;i++){
      var selected = '';
      if(mergeData[i] == status){
        selected = 'selected';
      }
      status_output +='<option '+selected+' value="'+mergeData[i] +'">'+mergeData[i]+'</option>';
     }
     return status_output;
  }

  function save_admission_fields(){
    var form = $('#titleModalform');
        if (!form.parsley().validate()) {
            return 0;
        }
    var fied_names = $('#fields').val();
    var class_list = $('#class_list').val();
    var ad_status = $('#ad_status').val();
    var title = $('#title').val();
    $.ajax({
      url:'<?php echo site_url('admission_process/save_admission_fields'); ?>',
      type:'post',
      data: {'fied_names':fied_names,'class_list':class_list,'ad_status':ad_status,'title':title},
      success: function(data) {
        var field_data = JSON.parse(data);
        if(field_data == 1){
          $("#save_field_name").modal('hide');
        }
      },
      complete:function(){
        get_predefined_names();
      }
    });
  }

  function update_admission_fields(){
    var form = $('#updateModalform');
    if (!form.parsley().validate()) {
        return 0;
    }
    var id = $('#update_id').val();
    var fied_names = $('#fields').val();
    var class_list = $('#class_list').val();
    var ad_status = $('#ad_status').val();
    var title = $('#update_title').val();
    $.ajax({
      url:'<?php echo site_url('admission_process/update_admission_fields'); ?>',
      type:'post',
      data: {'id':id,'fied_names':fied_names,'class_list':class_list,'ad_status':ad_status,'title':title},
      success: function(data) {
        var field_data = JSON.parse(data);
        if(field_data == 1){
          $("#update_field_name").modal('hide');
        }
      },
      complete: function () {
        get_predefined_names();
      }
    });
  }

  function generate_report(){
    var fields = $('#fields').val();
    if(fields == null){
      return false;
    }
    $("#export_admissions").html('<div class="text-center"><i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i></div>');
    var form = $('#admission_report')[0];
    var formData = new FormData(form);
    $.ajax({
      url:'<?php echo site_url('admission_process/generateReport'); ?>',
      type:'post',
      data: formData,
      processData: false,
      contentType: false,
      success: function(data) {
        console.log(data);
        $('#admission_report_data').show();
        if(data != 0){
          var admission_data = JSON.parse(data)
          // console.log(admission_data.header);
          if(admission_data){
          $('#export_admissions').html(construct_admission_report(admission_data));
          // add_scroller('report-container');
          var table = $('#data_table').DataTable( {
                  dom: 'lBfrtip',
                      "language": {
                      "search": "",
                      "searchPlaceholder": "Enter Search..."
                  },
                  "pageLength": 10,
                  ordering:false,
                  "bPaginate":true,
                  "scrollX": true,
                  "scrollY": 300,
                  scrollCollapse: true,
                  buttons: [
                      {
                          extend: 'excelHtml5',
                          text: 'Excel',
                          filename: 'Admission Report',
                          className: 'btn btn-info'
                      },
                      {
                          extend: 'print',
                          text: 'Print',
                          filename: 'Admission Report',
                          className: 'btn btn-info'
                      }
                  ]
              } ); 


          }
        }else{
          // $('#excel').hide();
          $('#export_admissions').html('<h3 class="no-data-display">Result not found</h3>')
        }
       
      }
    });
  }

  function construct_admission_report(admission_data){
    html ='';
    if(admission_data == ''){
        html += '<h3 class="no-data-display">Please select the required fields and click generate button then click on export to excel</h3>';
         return html;
    }else{
        html += '<div class="table-responsive" id="report-container">';
        html += '<table id="data_table" class="table table-bordered" style="width:100%; white-space: nowrap;">';
        html += '<thead>';
        html +='<tr>';
        html += '<th>#</th>';
        $.each(admission_data['header'], function (key, head) {
          html += '<th>'+head.toUpperCase().replaceAll("_", " ")+'</th>';
        });
        html +='</tr>';
        html +='</thead>';
        html +='<tbody>';
      
        var i=1;
        var statusColor = {
              'Not Paid' : '<span class="text-danger">Not Paid</span>',
              'Full Paid' : '<span class="text-success">Paid</span>',
              'Partial Paid' : '<span class="text-warning">Partial</span>'
        };
        $.each(admission_data['result'], function (key, val) {
          html +='<tr>';
          html +='<td>'+(i++)+'</td>';

          $.each(admission_data['header'], function (key, head) {
            if (head == 'fee_status') { 
              if (val['fee_status']) {
              html += '<td>';
                $.each(val['fee_status'], function (key, fees) {
                  html += fees['blueprint_name']+': '+statusColor[fees['fee_status']]+'<br>';
                });
              html += '</td>';
              }else{
                    html += '<td></td>';
              }}else{
                if (val[head]) {

                switch (head) {
                    case 'boarding':
                      if(val[head] == '1'){ val[head] ='Day Scholar';}else if(val[head] == '2'){ val[head] ='Regular Boadrder';}else{ val[head] ='Weekly Boadrder'; }
                        break;
                    case 'gender':
                      if(val[head] == 'M'){ val[head] ='Male';}else{ val[head] ='Female';}
                        break;
                    case 'physical_disability':
                      if(val[head] == 'N'){ val[head] ='No';}else{ val[head] ='Yes'; }
                        break;
                    case 'enquiry_id':
                      if(val[head] == 0){ val[head] ='No';}else{ val[head] ='Yes'; }
                        break;
                    case 'learning_disability':
                      if(val[head] == 'N'){ val[head] ='No';}else{ val[head] ='Yes'; }
                        break;
                    default:
                        break;
                }

                  html += '<td>'+val[head]+'</td>';
                }else{
                  html += '<td></td>';
                }
              }
            });
          html +='</tr>';
        });
       
        html +='</tbody>';
        html +='</table>';
        html +='</div>';
    }
    return html;
  }
</script>

<style type="text/css">

  /* .buttons-excel{
    display: none;
  } */
  #export_admissions {
   height: 350px;
   overflow-y: scroll;
  }
  .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
    #DataTables_Table_0_length{
        text-align: left;
    }

    .dataTables_scrollBody::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    background-color: #F5F5F5;
    border-radius: 0px;
}

.dataTables_scrollBody::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb {
    background-color: #777;
    border-radius: 0px;
}

.dataTables_scrollBody{
    margin-top: -13px;
  }

  .sticky-column {
    position: sticky;
    left: 0;
    z-index: 1;
    background-color: #ffffff;
  }

  .sticky-last-column{
    position: sticky;
    right: 0;
    z-index: 1;
    background-color: #ffffff;
  }
</style>
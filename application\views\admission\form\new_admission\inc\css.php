


<style>
.parent_css {
    padding-left: 0;
}

/* Mobile Stepper Styles */
@media (max-width: 768px) {
    .mobile-stepper-container {
        background: white !important;
        border-radius: 12px !important;
    }

    .step-icon-circle {
        width: 48px !important;
        height: 48px !important;
        border-radius: 50% !important;
        flex-shrink: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .step-icon {
        color: white !important;
        font-size: 16px !important;
        width: 24px !important;
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .step-icon svg {
        width: 20px !important;
        height: 20px !important;
        fill: currentColor !important;
    }

    .step-counter {
        font-size: 14px !important;
        color: #6B7280 !important;
        font-weight: 500 !important;
    }

    .step-title {
        font-size: 18px !important;
        color: #111 !important;
        font-weight: 600 !important;
        margin-top: 2px !important;
    }

    .progress {
        height: 6px !important;
        background: #E5E7EB !important;
        border-radius: 3px !important;
        overflow: hidden !important;
    }

    .progress-bar {
        height: 100% !important;
        transition: width 0.3s ease !important;
        border-radius: 3px !important;
    }

    /* Hide desktop stepper on mobile */
    .stepper {
        display: none !important;
    }
}

/* Desktop - Hide mobile stepper */
@media (min-width: 769px) {
    .mobile-stepper-container {
        display: none !important;
    }
}

/* Mobile Button Improvements */
@media (max-width: 768px) {
    .save-step1, .save-step2, .save-step4, .save-step5 {
        min-height: 44px !important;
        touch-action: manipulation !important;
        font-size: 16px !important;
        padding: 13px 16px !important;
        border-radius: 8px !important;
        /* width: 100% !important; */
        cursor: pointer !important;
        -webkit-appearance: none !important;
        appearance: none !important;
        -webkit-tap-highlight-color: transparent !important;
    }

    .save-step1:active, .save-step2:active, .save-step4:active, .save-step5:active {
        transform: scale(0.98) !important;
        transition: transform 0.1s ease !important;
    }

    /* Ensure buttons are not disabled by default on mobile */
    .save-step1:not([disabled]), .save-step2:not([disabled]),
    .save-step4:not([disabled]), .save-step5:not([disabled]) {
        pointer-events: auto !important;
        opacity: 1 !important;
    }

    /* Form validation improvements for mobile */
    .parsley-errors-list {
        font-size: 14px !important;
        margin-top: 5px !important;
        color: #DC2626 !important;
    }

    .parsley-error {
        border-color: #DC2626 !important;
        box-shadow: 0 0 0 1px #DC2626 !important;
    }
}

/* Parent Form Layout Improvements */
@media (min-width: 768px) {
    /* Desktop: Ensure proper two-column layout */
    #father_details_tab, #mother_details_tab {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

}

@media (max-width: 767px) {
    /* Mobile: Stack columns vertically */
    #father_details_tab, #mother_details_tab {
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin-bottom: 2rem;
    }

    #father_details_tab {
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 2rem;
    }
}

/* Ensure proper spacing for parent form sections */
.parent-details-section {
    margin-bottom: 2rem;
}

/* Fix any table layout issues */
#father_details_tab .row,
#mother_details_tab .row {
    margin-left: 0;
    margin-right: 0;
}

#father_details_tab .row > [class*="col-"],
#mother_details_tab .row > [class*="col-"] {
    padding-left: 0;
    padding-right: 0;
}

/* Ensure document step is visible on mobile */
@media (max-width: 768px) {
    #step-form-5 {
        display: block !important;
    }

    #step-form-5.d-none {
        display: none !important;
    }

    /* Ensure document form content is visible */
    #step-form-5 .step-form-content,
    #step-form-5 .preview-container,
    #step-form-5 #document-form {
        display: block !important;
        width: 100% !important;
    }

    /* Fix document form layout on mobile */
    #step-form-5 .panel-body {
        padding: 15px !important;
    }

    #step-form-5 .col-md-12 {
        width: 100% !important;
        padding: 0 !important;
    }

    /* Fix document upload section layout on mobile */
    #step-form-5 .panel {
        margin: 0 !important;
        border: 1px solid #e9ecef !important;
        border-radius: 8px !important;
    }

    #step-form-5 .panel-heading {
        background: #f8f9fa !important;
        border-bottom: 1px solid #e9ecef !important;
        padding: 15px !important;
        border-radius: 8px 8px 0 0 !important;
    }

    #step-form-5 .panel-title {
        font-size: 18px !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }

    #step-form-5 .panel-body {
        padding: 20px 15px !important;
    }

    /* Mobile-responsive document upload layout */
    #step-form-5 #documentpanel {
        width: 100% !important;
        float: none !important;
        margin-bottom: 20px !important;
        padding: 0 !important;
    }

    #step-form-5 .col-md-1 {
        width: 100% !important;
        float: none !important;
        margin: 0 0 20px 0 !important;
        text-align: center !important;
    }

    #step-form-5 #display_document {
        width: 100% !important;
        float: none !important;
        padding: 0 !important;
    }

    /* Form groups in document section */
    #step-form-5 .form-group {
        margin-bottom: 20px !important;
    }

    #step-form-5 .control-label {
        width: 100% !important;
        float: none !important;
        text-align: left !important;
        margin-bottom: 8px !important;
        font-weight: 500 !important;
    }

    #step-form-5 .col-md-4,
    #step-form-5 .col-md-8 {
        width: 100% !important;
        float: none !important;
        padding: 0 !important;
    }

    /* Document upload button */
    #step-form-5 #document_add {
        width: 100% !important;
        padding: 12px 20px !important;
        font-size: 16px !important;
        border-radius: 8px !important;
    }

    /* File input styling */
    #step-form-5 input[type="file"] {
        width: 100% !important;
        padding: 10px !important;
        border: 2px dashed #e9ecef !important;
        border-radius: 8px !important;
        background: #f8f9fa !important;
    }

    /* Help text */
    #step-form-5 .help-block {
        font-size: 14px !important;
        color: #6c757d !important;
        margin-top: 5px !important;
    }
}

.terms-confirm-popup .swal2-html-container {
    text-align: left !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

.terms-confirm-popup .swal2-actions {
    justify-content: flex-end !important;
    border-top: 1px solid #e9ecef !important;
}

.swal2-submit-btn.align-right-btn {
    margin: 0 !important;
    transition: all 0.2s ease !important;
}

.swal2-submit-btn.align-right-btn:hover {
    opacity: 0.9 !important;
    transform: translateY(-1px) !important;
}

/* Mobile Submit Button Improvements */
@media (max-width: 768px) {
    #final_submit_button {
        min-height: 44px !important;
        touch-action: manipulation !important;
        font-size: 16px !important;
        padding: 13px 16px !important;
        border-radius: 8px !important;
        width: 100% !important;
        margin-top: 8px !important;
    }

    #final_submit_button:disabled {
        opacity: 0.6 !important;
        cursor: not-allowed !important;
        background-color: #9CA3AF !important;
        border-color: #9CA3AF !important;
    }

    #final_submit_button:not(:disabled) {
        cursor: pointer !important;
    }

    /* Ensure error message is visible on mobile */
    #Error {
        font-size: 14px !important;
        line-height: 1.4 !important;
        margin: 12px 0 !important;
    }
}
.form-control[readonly]{
    background-color: #f0f0f0 !important;
    cursor: not-allowed;
}
input,select,textarea {
    font-size: 16px !important;
    background-color: #fff !important;
    color: #000 !important;
    height: auto !important;
    line-height: normal !important;
    padding: 14px 20px !important;
    gap: 10px;
    border-radius: 8px !important;
    border: 1.4px solid var(--input-field, #EAEAEA);
}

label {
    color:  #212121;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 120%;
    margin-bottom: 9px !important;
}

.form-label {
    text-align: left !important;
    display: flex;
    align-items: center;
    text-transform: capitalize;
}
.custom-button-container {
    display: flex;
    justify-content: center;
    /* Center the buttons horizontally */
    gap: 12px;
    /* Minimal horizontal space between buttons */
    margin-top: 10px;
    /* Slight vertical gap from message */
}

.swal2-confirm.btn-primary {
    background-color: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
    color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>;
    border: none;
    padding: 10px 18px;
    font-size: 14px;
    border-radius: 8px;
    font-weight: 400;
    min-width: 120px;
}

.swal2-cancel.btn-outline {
    background: transparent;
    color:<?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
    border: 2px solid <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
    padding: 10px 18px;
    font-size: 14px;
    border-radius: 8px;
    font-weight: 400;
    min-width: 120px;
}

.rounded-xl {
    border-radius: 12px;
}

.swal2-header-custom,
.swal2-html-container {
    text-align: left;
    /* padding: 10px 20px !important; */
}
.swal2-title {
    background-color: #EDE9FE;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    height: 5rem;
    text-align: left;
    padding-top:0px !important;
}

.align-right-btn {
    display: flex !important;
    justify-content: flex-end !important;
    width: 100%;
    margin-left: auto;
}
.swal2-popup{
    border-radius: 24px !important;
}
.swal2-popup .swal2-html-container {
    padding: 10px 20px !important;
}
    .container {
    max-width: 95%;
    width: 100%;
    margin-right: 0 2rem;
    margin-left: 0 2rem;
    background-color: #fff;
    border-radius: 24px;
    padding-top: 20px;
}
.stepper {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 30px;
  gap: 0;
}

.step {
  text-align: center;
  position: relative;
  flex: 1;
  z-index: 1;
}

/* Circle Styling */
.step .circle {
  margin: auto;
  background-color: #eee;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #999;
  width: 55px;
  height: 55px;
  flex-shrink: 0;
  padding: 12px;
  position: relative;
  z-index: 2;
  cursor: pointer;
}

.step.previous .circle,
.step.completed .circle {
    background-color: <?php echo $light_primary_color; ?>;
    color: <?php echo !empty($primary_fontColor) ? $primary_fontColor : 'white' ?>;
}

/* Active Circle */
.step.active .circle {
  background-color: <?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] : '#623CE7' ?>;
  color: <?php echo !empty($primary_fontColor) ? $primary_fontColor : 'white' ?>;
  border-color: <?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] : '#623CE7' ?>;
}

/* Inactive Circle Hover */
.step:not(.active):not(.completed) .circle:hover {
  background-color: <?php echo !empty($admission_ui_colors['secondary_background_color']) ? $admission_ui_colors['secondary_background_color'] : '#CBBCFF' ?>;
  color: <?php echo !empty($secondary_fontColor) ? $secondary_fontColor : 'black' ?>;
}

/* Label Styling */
.step .label {
  margin-top: 8px;
  font-size: 14px;
  color: #9EA2AE;
  font-weight: 500;
}

.step.active .label {
  color: <?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] : '#623CE7' ?>;
  font-weight: 600;
}

.step.completed .label,
.step.previous .label {
  color: <?php echo $light_primary_color; ?>;
  font-weight: 500;
}

/* Connecting Lines */
.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 28px; /* center line vertically */
  left: 50%;
  width: 100%;
  height: 2px;
  background-color: #E5E7EB;
  z-index: 0;
  transition: background-color 0.2s ease;
}

.step.previous:not(:last-child)::after,
.step.completed:not(:last-child)::after {
    background-color: <?php echo $light_primary_color; ?>;
}

/* Color for Active Line */
.step.active:not(:last-child)::after {
  background-color: <?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] : '#623CE7' ?>;
}
.circle i {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.gender-toggle-group .btn {
    flex: 1;
    border: 1px solid #ccc;
    padding: 15.5px 24px;
    font-size: 14px;
    font-weight: 400;
    color: #333;
    border-radius: 8px !important;
    background-color: #fff;
    transition: all 0.2s ease-in-out;
    text-align: center;
}

.gender-toggle-group .btn-check:checked + .btn {
    background-color: #6c3eea; /* Primary Color */
    color: #fff;
    border-color: #6c3eea;
}

.gender-toggle-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-toggle-group {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn-toggle-group input[type="radio"] {
        display: none;
    }

    .btn-toggle-group label {
        flex: 1;
        text-align: center;
        padding: 18.5px 8px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: white;
        color: #333;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .btn-toggle-group input[type="radio"]:checked + label {
        background-color: #5e3be1;
        color: white;
        border-color: #5e3be1;
    }

    .border-dashed {
        border-style: dashed !important;
    }
@media (max-width: 576px) {
    .gender-toggle-group {
        flex-direction: column;
    }

    .btn-toggle-group label {
            font-size: 14px;
            padding: 8px 0;
        }
}

.parsley-errors-list {
    color: red;
    list-style: none;
    padding-left: 0;
    margin-top: 5px;
    font-size: 13px;
}

input.parsley-error,
select.parsley-error,
textarea.parsley-error {
    border: 1.5px solid #EE443F;
    background-color: #FDECEC !important;
}
input::placeholder,
textarea::placeholder {
    color: #818181 !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 120%; /* 19.2px */
    color: #818181 !important;
}
.border-dashed:hover {
    background-color: #f0f0ff;
    border-color: #6c63ff;
}
.head_label {
    font-size: 14px;
    font-weight: 500;
    color: #A3ABC0;
}
.help-block{
    font-size: 13px;
    color: #9EA2AE;
}
.head_value {
    font-size: 14px;
    font-weight: 600;
    color: #101010;
}
.d-flex {
    display: flex !important;
}
.me-3 {
    margin-right: 1rem !important;
}
.mb-3 {
    margin-bottom: 1rem !important;
}
.swal2-actions {
    justify-content: flex-end !important;
    margin-right: 24px; /* optional */
}
.col-md-6.mb-4 {
       padding-right: 25px !important;
   }
#father_details_tab,#mother_details_tab{
    padding-right: 40px !important;
}

.custom-select-arrow {
    position: relative;
  }
  .custom-select-arrow select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 36px;
    background: none;
  }
  .custom-select-arrow .dropdown-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    height: 18px;
    width: 18px;
    display: flex;
    align-items: center;
  }

  .custom-checkbox-label {
    display: flex;
    align-items: center;
    font-size: 1.5em;
    color: #623CE7;
    font-weight: 400;
    gap: 16px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    color: black;
  }

  .custom-checkbox-label input[type="checkbox"] {
    display: none;
  }

  .custom-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid #623CE7;
    border-radius: 3px;
    display: inline-block;
    position: relative;
    background: #fff;
    transition: border-color 0.2s;
  }

  .custom-checkbox-label input[type="checkbox"]:checked + .custom-checkbox {
    background:#623CE7;
    border-color:#623CE7;
  }

  .custom-checkbox:after {
    content: '';
    position: absolute;
    display: none;
  }

  .custom-checkbox-label input[type="checkbox"]:checked + .custom-checkbox:after {
    display: block;
  }

  .custom-checkbox-label .custom-checkbox:after {
    left: 5px;
    top: 0px;
    width: 7px;
    height: 14px;
    border: solid #fff;
    border-width: 0 2px 2px 0;  /* Thinner tick */
    transform: rotate(45deg);
    content: '';
    position: absolute;
    display: none;
  }

  .custom-checkbox-label input[type="checkbox"]:checked + .custom-checkbox:after {
    display: block;
  }


  .file-icon.pdf-icon {
    width: 48px;
    height: 48px;
    background: #FF1A1A;
    color: #fff;
    font-weight: bold;
    font-size: 1.1em;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}
.file-name {
    font-weight: 600;
    color: #181028;
    font-size: 1.1em;
}
.file-size {
    color: #a3a0a3;
    font-size: 1em;
}
.view-link {
    color: #623CE7;
    font-weight: 500;
    font-size: 1.1em;
    text-decoration: none;
}
.view-link:hover {
    text-decoration: underline;
}
.doc-label {
    color: #a3a0a3;
    font-size: 1.1em;
    margin-bottom: 0.2em;
}
.doc-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
.upload-card {
    padding-right: 24px;
}
.upload-card:last-child {
    padding-right: 0;
}

/* Desktop Stepper Theme Color Overrides - Highest Priority */
@media (min-width: 769px) {
    /* Force active circle colors */
    .stepper .step.active .circle,
    .stepper .step .circle.active-circle,
    div.stepper div.step.active div.circle {
        background-color: <?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] : '#623CE7' ?> !important;
        color: <?php echo !empty($primary_fontColor) ? $primary_fontColor : 'white' ?> !important;
        border-color: <?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] : '#623CE7' ?> !important;
    }

    /* Force completed circle colors */
    .stepper .step.completed .circle,
    .stepper .step.previous .circle,
    div.stepper div.step.completed div.circle,
    div.stepper div.step.previous div.circle {
        background-color: <?php echo $light_primary_color; ?> !important;
        color: <?php echo !empty($primary_fontColor) ? $primary_fontColor : 'white' ?> !important;
        border-color: <?php echo $light_primary_color; ?> !important;
    }

    /* Force inactive circle colors */
    .stepper .step:not(.active):not(.completed):not(.previous) .circle,
    .stepper .step .circle.inactive-circle,
    div.stepper div.step:not(.active):not(.completed):not(.previous) div.circle {
        background-color: #E5E7EB !important;
        color: #9CA3AF !important;
        border-color: #E5E7EB !important;
    }

    /* Force active label colors */
    .stepper .step.active .label,
    div.stepper div.step.active div.label {
        color: <?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] : '#623CE7' ?> !important;
        font-weight: 600 !important;
    }

    /* Force completed label colors */
    .stepper .step.completed .label,
    .stepper .step.previous .label,
    div.stepper div.step.completed div.label,
    div.stepper div.step.previous div.label {
        color: <?php echo $light_primary_color; ?> !important;
        font-weight: 500 !important;
    }

    /* Force connecting line colors */
    .stepper .step.active:not(:last-child)::after,
    div.stepper div.step.active:not(:last-child)::after {
        background-color: <?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] : '#623CE7' ?> !important;
    }

    .stepper .step.completed:not(:last-child)::after,
    .stepper .step.previous:not(:last-child)::after,
    div.stepper div.step.completed:not(:last-child)::after,
    div.stepper div.step.previous:not(:last-child)::after {
        background-color: <?php echo $light_primary_color; ?> !important;
    }

    /* Force inactive connecting line colors */
    .stepper .step:not(.active):not(.completed):not(.previous):not(:last-child)::after,
    div.stepper div.step:not(.active):not(.completed):not(.previous):not(:last-child)::after {
        background-color: #E5E7EB !important;
    }

    /* Hover effects for inactive circles */
    .stepper .step:not(.active):not(.completed):not(.previous) .circle:hover {
        background-color: <?php echo !empty($admission_ui_colors['secondary_background_color']) ? $admission_ui_colors['secondary_background_color'] : '#CBBCFF' ?> !important;
        color: <?php echo !empty($secondary_fontColor) ? $secondary_fontColor : 'black' ?> !important;
        transform: scale(1.05);
    }
}

/* Previous Education Popup Styles */
.previous-education-popup {
    max-height: 90vh !important;
    overflow: hidden !important;
}

.previous-education-popup .swal2-html-container {
    max-height: 60vh !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    padding: 0 20px !important;
    scrollbar-width: thin;
    scrollbar-color: #CBD5E0 #F7FAFC;
}

.previous-education-popup .swal2-html-container::-webkit-scrollbar {
    width: 6px;
}

.previous-education-popup .swal2-html-container::-webkit-scrollbar-track {
    background: #F7FAFC;
    border-radius: 3px;
}

.previous-education-popup .swal2-html-container::-webkit-scrollbar-thumb {
    background: #CBD5E0;
    border-radius: 3px;
}

.previous-education-popup .swal2-html-container::-webkit-scrollbar-thumb:hover {
    background: #A0AEC0;
}

/* Ensure form elements are properly spaced in popup */
.previous-education-popup form {
    padding: 10px 0;
}

.previous-education-popup .form-group {
    margin-bottom: 20px;
}

.previous-education-popup .form-control {
    margin-bottom: 10px;
}

/* Fix button positioning */
.previous-education-popup .swal2-actions {
    margin-top: 20px !important;
    padding-top: 15px !important;
    border-top: 1px solid #E2E8F0 !important;
}

/* Prevent body scroll when popup is open */
body.swal2-shown {
    overflow: hidden !important;
}

</style>
<?php if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) { ?>
    <style>
    .preview-container {
        margin: 0 auto;
        background-color: #fff;
        border-radius: 12px;
    }
    .step .circle {
    width: 40px !important;
    height: 40px !important;
    }
    .step:not(:last-child)::after {
        top: 20px !important; /* center line vertically */
    }
    .stepper{
        margin: 20px 0 !important;
    }
    .step .label {
        font-size: 12px;
    }
    #last_container{
        max-width: 100% !important;
    }

    /* Mobile stepper circle colors */
    .step.active .circle {
        background-color: <?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] : '#623CE7' ?> !important;
        color: <?php echo !empty($primary_fontColor) ? $primary_fontColor : 'white' ?> !important;
    }

    .step.completed .circle,
    .step.previous .circle {
        background-color: <?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] : '#623CE7' ?> !important;
        color: <?php echo !empty($primary_fontColor) ? $primary_fontColor : 'white' ?> !important;
    }
    </style>
<?php } else{ ?>
    <style>
    .preview-container {
        max-width: 85%;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 12px;
        padding: 30px 60px;
    }
    </style>
<?php } ?>
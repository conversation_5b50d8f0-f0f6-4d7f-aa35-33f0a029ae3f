<!-- <div style="width: 124px;height: 156px; border-radius: 12px; object-fit: cover; background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
            <?php //$this->load->view('svg_icons/empty_image.svg'); ?>
        </div> -->
<div class="profile-section d-flex align-items-center">
    <img class="profile-photo"
        src="https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Admission process/student_image.jpg"
        alt="Profile Picture">

    <div class="profile-info ms-3">
        <div class="profile-name fw-bold fs-5"></div>
        <div class="profile-details text-muted">

        </div>
    </div>
</div>

<div class="info-block">
    <h3>Personal Information</h3>
    <div class="row" id="personal-info-grid" style="row-gap: 1.5rem;"></div>
</div>

<div class="info-block">
    <h3>Parent Information</h3>
    <div class="row">
        <div class="col-md-6 p-0" id="father-info-grid" style="row-gap: 1.5rem;"></div>
        <div class="col-md-6" id="mother-info-grid" style="row-gap: 1.5rem;"></div>
    </div>
</div>

<?php if($config_val['show_guardian_details'] !=0) :  ?>
<div class="info-block">
    <h3>Guardian Information</h3>
    <div class="row" id="guardian-info-grid" style="row-gap: 1.5rem;"></div>
</div>
<?php endif; ?>

<?php if($this->settings->getSetting('enabled_medical_form_tab_in_admissions') == 1) {?>
<div class="info-block">
    <h3>Medical Information</h3>
    <div class="row" id="medical-info-grid" style="row-gap: 1.5rem;"></div>
</div>
<?php if($this->settings->getSetting('enabled_hospitalization_details_in_admissions') == 1) {  ?>
<div class="info-block">
    <h3>Hospitalization Details</h3>
    <div class="row" id="hospitalization-info-grid" style="row-gap: 1.5rem;"></div>
</div>
<?php } ?>
<?php if($this->settings->getSetting('enabled_vaccination_details_in_admissions') == 1) {  ?>
<div class="info-block">
    <h3>Vaccination Details</h3>
    <div class="row" id="vaccination-info-grid" style="row-gap: 1.5rem;"></div>
</div>
<?php } ?>
<?php } ?>

<?php if($this->settings->getSetting('disable_document_tab_in_admissions') == 0) {?>
<div class="info-block">
    <h3>Previous Education Details</h3>
    <div class="row" id="previous-education-grid" style="row-gap: 1.5rem;"></div>
    <div class="row" id="subject-details-grid" style="row-gap: 1.5rem;"></div>
</div>
<div class="info-block">
    <h3>Documents Submitted</h3>
    <div class="row" id="document-grid" style="row-gap: 1.5rem;"></div>
</div>
<?php } ?>
<input type="hidden" id="school_short_name" value="<?= $school_short ?>">

<?php if($config_val['is_ready_to_take_proficiency_test']) { ?>
<div class="info-block">
    <h3>Ready To Take Proficiency Test</h3>
    <div class="row mb-4">
        <label class="form-label d-block">Are you ready to take a proficiency test?</label>
        <div class="col-md-6 btn-toggle-group" role="group" aria-label="Proficiency Test Readiness">
            <input type="radio" class="btn-check" name="ready_to_take_test" id="yes_ready_to_test" value="Yes"
                <?= (!empty($final_preview->ready_to_take_test) && $final_preview->ready_to_take_test == 'Yes') ? 'checked' : '' ?>>
            <label class="btn btn-outline-primary d-flex justify-content-center align-items-center"
                style="max-width: 85px; height: 40px;" for="yes_ready_to_test">Yes</label>

            <input type="radio" class="btn-check" name="ready_to_take_test" id="no_ready_to_test" value="No"
                <?= (empty($final_preview->ready_to_take_test) || $final_preview->ready_to_take_test == 'No') ? 'checked' : '' ?>>
            <label class="btn btn-outline-primary d-flex justify-content-center align-items-center"
                style="max-width: 85px; height: 40px;" for="no_ready_to_test">No</label>
        </div>
    </div>
    <?php } ?>
    <?php if ($school_short != 'bwpshyd') { ?>
    <div class="info-block">
        <h3>Declaration By Parent/Guardian</h3>
        <div class="row" id="declaration-grid" style="row-gap: 1.5rem;">
            <span style="font-size: small; line-height: 1.5;">
                <label class="custom-checkbox-label" id="terms_label"
                    style="font-weight:600; color:#181028; font-size:1.1em; display:flex; align-items:center; gap:12px; cursor:pointer;">
                    <input type="checkbox" name="agree" id="terms" style="display:none;">
                    <span class="custom-checkbox"></span>
                    <span>
                        I have <a href="javascript:void(0);" onclick="showRulesModal()" style="color:#623CE7; text-decoration:underline;">
                            read the rules and regulations</a> of <?= $school_name ?> and I fully agree to abide by them.
                    </span>
                </label>
            </span>
        </div>
        <?php if ($this->settings->getSetting('enable_terms_and_condition_strict_mode') == 1) { ?>
        <p>(Read the complete terms and condition, scroll to the end and click on 'Close' to mark the checkbox)</p>
        <?php } ?>
        <div style="display: none; color: #c20000; font-weight: 600; border-radius: 4px;" id="Error">
            You need to agree to the rules and regulations of the school before submitting.
        </div>
    </div>
    <?php } ?>
</div>

<script>
function showRulesModal() {
    Swal.fire({
        title: `<div class="swal2-header-custom" style="text-align: center;margin-bottom: 10px;padding:15px">
                    <span><?= $school_name ?></span>
                </div>`,
        html: `<?php ob_start(); $this->load->view('admission/form/instructions'); $modalContent = ob_get_clean(); echo addslashes($modalContent); ?>`,
        width: '70%',
        heightAuto: true,
        customClass: {
            popup: 'swal-wide'
        },
        showCloseButton: true,
        showCancelButton: false,
        confirmButtonText: 'Close',
        focusConfirm: false,
    });
}

// Improved checkbox handling for mobile
function updateTermsCheckbox() {
    if ($('#terms').prop("checked") == true) {
        $('#Error').hide();
        // Enable all final submit buttons (mobile and desktop)
        $('[id="final_submit_button"]').removeAttr('disabled');
    } else {
        $('#Error').show();
        // Disable all final submit buttons (mobile and desktop)
        $('[id="final_submit_button"]').attr('disabled', true);
    }
}

// Handle both click and touch events
$('#terms').on('change', updateTermsCheckbox);

// Also handle label click for better mobile experience
$('.custom-checkbox-label').on('click', function(e) {
    // Don't trigger if clicking on the link inside the label
    if ($(e.target).is('a')) return;

    // Toggle checkbox state
    $('#terms').prop('checked', !$('#terms').prop('checked'));
    updateTermsCheckbox();
});

// Initialize on page load
$(document).ready(function() {
    // updateTermsCheckbox();

    // Add mobile-specific event handlers
    if (window.matchMedia('(max-width: 768px)').matches) {
        // Add touch event for better mobile responsiveness
        $('.custom-checkbox-label').on('touchend', function(e) {
            e.preventDefault();
            // Don't trigger if clicking on the link inside the label
            if ($(e.target).is('a')) return;

            // Toggle checkbox state
            $('#terms').prop('checked', !$('#terms').prop('checked'));
            updateTermsCheckbox();
        });

        // Ensure submit buttons are properly styled for mobile
        $('[id="final_submit_button"]').css({
            'min-height': '44px',
            'touch-action': 'manipulation'
        });
    }

    // Add debugging for submit button clicks
    $('[id="final_submit_button"]').on('click', function(e) {
        console.log('Submit button clicked');
        console.log('Button disabled:', $(this).prop('disabled'));
        console.log('Terms checked:', $('#terms').prop('checked'));

        if ($(this).prop('disabled')) {
            e.preventDefault();
            console.log('Submit prevented - button is disabled');
            if (!$('#terms').prop('checked')) {
                $('#Error').show();
                alert('Please agree to the terms and conditions before submitting.');
            }
            return false;
        }
    });
});

function terms_check(e) {
    let checkbox = document.getElementById("terms");
    let label = document.getElementById("terms_label");

    if (checkbox.checked) {
        $('#Error').hide();
        // $('.custom-checkbox-label').removeClass('checkbox-error');

    } else {
        $('#Error').show();
        // $('.custom-checkbox-label').addClass('checkbox-error');
        return false;
    }
    var school_short_name = $('#school_short_name').val();
    var confirmations = "";

    if (e == 1) {
        confirmations =
            "Submit and pay' will lead to a payment page. You will not be able to edit the application any more. Are you sure you want to continue?";
    } else {
        confirmations =
        "Are you sure you want to submit the application? Once submitted, application cannot be edited.";
    }

    // Debug logging for mobile
    if ($('#terms').prop('checked') == true || school_short_name == 'bwpshyd') {
        $('#Error').hide();
        $('#terms').removeAttr('required');

        Swal.fire({
            title: 'Confirm',
            text: confirmations,
            showCancelButton: true,
            confirmButtonText: 'Submit',
            cancelButtonText: 'Cancel',
            reverseButtons: true,
            customClass: {
                popup: 'terms-confirm-popup',
                confirmButton: 'swal2-submit-btn',
                cancelButton: 'swal2-cancel-btn'
            },
            buttonsStyling: false,
            didOpen: () => {
                const title = Swal.getTitle();
                if (title) {
                    title.style.paddingTop = '20px';
                    title.style.paddingBottom = '10px';
                    title.style.backgroundColor = 'transparent';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                var url = '<?php echo site_url('admission_controller/submit_final_form') ?>';
                $('#final-form').attr('action', url);
                $('#final-form').submit();
            }
        });

    } else {
        $('#Error').show();
        $('#terms').attr('required', 'required');
    }
}
</script>
<style>
/* Custom Checkbox Styles */
.custom-checkbox-label {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    cursor: pointer !important;
    font-weight: 600 !important;
    color: #181028 !important;
    font-size: 1.1em !important;
    line-height: 1.5 !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

.custom-checkbox {
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #D1D5DB !important;
    border-radius: 4px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
    transition: all 0.2s ease !important;
    background: white !important;
    position: relative !important;
}

.custom-checkbox::after {
    content: '' !important;
    width: 6px !important;
    height: 10px !important;
    border: solid white !important;
    border-width: 0 2px 2px 0 !important;
    transform: rotate(45deg) !important;
    opacity: 0 !important;
    transition: opacity 0.2s ease !important;
}

input[type="checkbox"]:checked+.custom-checkbox {
    background: #623CE7 !important;
    border-color: #623CE7 !important;
}

input[type="checkbox"]:checked+.custom-checkbox::after {
    opacity: 1 !important;
}

/* Mobile specific improvements */
@media (max-width: 768px) {
    .custom-checkbox-label {
        font-size: 16px !important;
        gap: 16px !important;
        padding: 8px 0 !important;
        min-height: 44px !important;
        /* Minimum touch target size */
    }

    .custom-checkbox {
        width: 24px !important;
        height: 24px !important;
        min-width: 24px !important;
        min-height: 24px !important;
    }

    .custom-checkbox::after {
        width: 7px !important;
        height: 12px !important;
    }
}

.swal2-submit-btn {
    background-color: #6C3EEB;
    color: white;
    border: none;
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    height: 42px;
    line-height: 1;
}

/* Cancel (Outlined) */
.swal2-cancel-btn {
    background-color: white;
    color: #6C3EEB;
    border: 2px solid #6C3EEB;
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    height: 42px;
    line-height: 1;
    margin-right: 12px;
    /* space between buttons */
}

.profile-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 10px 0;
}

.swal2-close {
    display: none !important;
}

.profile-photo {
    width: 96px;
    height: 96px;
    border-radius: 12px;
    object-fit: cover;
    border-radius: 8px;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px 60px;
    margin-top: 20px;
}

.info-grid .mb-3 {
    margin-bottom: 0;
    /* already using grid spacing */
}

.label {
    color: #6B7280;
    /* subtle label color */
    font-weight: 500;
    font-size: 14px;
}

.value {
    color: #0A0D14;
    font-weight: 600;
    font-size: 15px;
}

.profile-info h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.profile-info p {
    margin: 4px 0;
    color: #6b7280;
}

.profile-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.info-block {
    margin-top: 30px;
    border-top: 1px solid #e5e7eb;

}

.info-block h3 {
    font-size: 16px;
    font-weight: 700;
    margin: 20px 0;
    padding-bottom: 10px;
    font-style: normal;
    color: #101010;
}

.info-grid div {
    margin-bottom: 10px;
}

.label {
    font-size: 14px;
    color: hsl(218, 10.60%, 64.90%);
    padding: 0;
    font-style: normal;
    font-weight: 400;
    color: #A3ABC0;
    align-self: stretch;
    margin-bottom: 5px;
}

.value {
    color: #101010;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 120%;

}

.info-grid>div {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    /* space between items */
}

.profile-name {
    font-size: 20px;
    font-weight: 700;
    color: #131927;
    margin-bottom: 8px;
}

.profile-details {
    color: #222;
    font-size: 15px;
    font-weight: 400;
    line-height: 1.5;
}

.checkbox-error {
    border: 2px solid #c20000;
    padding: 10px;
    border-radius: 8px;
}
</style>
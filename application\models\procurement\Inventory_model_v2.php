<?php
/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  26 april 2023
 *
 * Description: Controller for Inentory Module. Entry point for Inentory Module
 *
 * Requirements: PHP5 or above
 *
 */

class Inventory_model_v2 extends CI_Model {

  function __construct() {
      parent::__construct();
      $this->yearId = $this->acad_year->getAcadYearID();
  }

  public function newProductVarient(){
    $input = $this->input->post();
    $inputAttr = $input['attributes'];
    $data['prodcutAttr'] =array();

    foreach($inputAttr as $attr_name => $value){
      $data['prodcutAttr'][$attr_name] = $value;
    }
    $attr = json_encode($data['prodcutAttr']);
    $product_id = $input['product_id'];
    $name = $input['variant_name'];
    $data = array(
      'item_name' => $name,
      'sku_code' => isset($input['sku_code']) ? $input['sku_code'] : '',
      'unit_type' => $input['unit_type'],
      'proc_im_subcategory_id' => $product_id,
      'attributes' => $attr,
      // 'initial_quantity' => $input['initial_quantity'],
      'threshold_quantity' => $input['threshold_quantity'],
      // 'current_quantity' => $input['initial_quantity'],
      // 'total_quantity' => $input['initial_quantity'],
      // 'cost_prodcut' => $input['cost_prodcut'],
      // 'selling_price' => isset($input['selling_price']) ? $input['selling_price'] : '',
      'hsn_sac' => $input['hsn_sac'],
      'is_perishable' => $input['is_perishable'],
      'classification' => 'GENERAL',
      // 'item_description' => $input['item_description'],
    );
    $this->db->trans_start();
    $this->db->insert('procurement_itemmaster_items', $data);
    $item_id= $this->db->insert_id();

    // $vendor_id= $this->db->select('id')->where('vendor_name', 'Initial Quantity')->get('procurement_vendor_master')->row()->id;
    // $inv_master_id= $this->db->select('id')->where('vendor_id', $vendor_id)->get('procurement_delivery_challan_master')->row()->id;
    
    // $inv_item_insert= array(
    //   'invoice_master_id' => $inv_master_id,
    //   'proc_im_items_id' => $item_id,
    //   'proc_im_subcategory_id' => $product_id,
    //   'proc_im_category_id' => $input['category_id'],
    //   'price' => $input['cost_prodcut'],
    //   'selling_price' => isset($input['selling_price']) ? $input['selling_price'] : '',
    //   'initial_quantity' => $input['initial_quantity'],
    //   'current_quantity' => $input['initial_quantity'],
    //   'total_quantity' => $input['initial_quantity'],
    //   'sku_code' => isset($input['sku_code']) ? $input['sku_code'] : ''
    // );
    // $this->db->insert('procurement_delivery_challan_items', $inv_item_insert);



    // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Item',
        'source_id' => $item_id,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Item Created',
        'action_description' => "A new item with name - $name is created under sub-category-id - $product_id. Status is set to Active.",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

    $this->db->trans_complete();
    return $this->db->trans_status();
  }
  
  public function get_all_categories($category_admin_only= 'any_one ') {
    $this->db->select("pic.*, ifnull(concat(sm.first_name, ' ', ifnull(sm.last_name, '')), '') as admin")
      ->join('staff_master sm', 'sm.id= pic.category_administrator', 'left')
      ->order_by("pic.category_name","ASC");
    $login= $this->authorization->getAvatarStakeHolderId();
    if($login != 0 && $login != '0' && $category_admin_only == 'category_admin_only') {
      $this->db->where("(pic.category_administrator IS NULL OR pic.category_administrator = $login)");
    }
    // $this->db->where('pic.status', 1); // Only active categories in allocate to staff 
    return $this->db->get('procurement_itemmaster_category pic')->result();
  }
  
  public function submitProductCategory($category_name, $is_sellable, $category_description, $category_type, $category_administrator) {
    $data= array(
      'category_name' => $category_name, 
      'is_sellable' => $is_sellable, 
      'category_description' => $category_description,
      'created_by_id' => $this->authorization->getAvatarStakeHolderId(),
      'category_type' => $category_type,
      // 'approval_algorithm' => $approval_algorithm,
      // 'approvar_1' => isset($approvar_1) ? $approvar_1 : NULL,
      // 'approvar_2' => isset($approvar_2) ? $approvar_2 : NULL,
      'category_administrator' => isset($category_administrator) && $category_administrator != 0 ? $category_administrator : NULL,
    );
    $this->db->trans_start();
    $this->db->insert('procurement_itemmaster_category', $data);
    $status= $this->db->insert_id();
$is_sellable= ($is_sellable == '1') ? 'sellable' : 'non sellable';
    // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Category',
        'source_id' => $status,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Category Created',
        'action_description' => "A new $is_sellable category with name - $category_name is created. Status is set to Active and Category type is set to $category_type",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $x=$this->db->insert('procurement_itemmaster_history', $history_insert);
      $this->db->trans_complete();

      return $status;

  }
  
  //generate product code
  public function get_product_code(){
    $product = $this->db->select('id')->from('procurement_vendor_master')->order_by('id', 'DESC')->limit(1)->get()->row();
    if(empty($product)) {
      return 1;
    } else {
      $id = $product->id + 1;
      return $id;
    }
  }
  
  public function add_sub_category() {
    $number = $this->get_product_code();
    $product_code = 'P'.sprintf("%'.05d",$number);
    
    $input = $this->input->post();
    $created_by = $this->authorization->getAvatarStakeHolderId();
    $attributes = json_encode($input['attributes']);
    $is_stockable = $input['is_stockable'];

    $is_exist= $this->db->where('subcategory_name', $input['product_name'])->get('procurement_itemmaster_subcategory')->result();
    
    $data = array(
      'product_code' => $product_code,
      'subcategory_name' => $input['product_name'],
      'picture_url' => NULL,
      // 'unit_type' => $input['unit_type'],
      'is_stockable' => $is_stockable,
      'subcategory_description' => $input['description'],
      'attributes' => $attributes,
      'proc_im_category_id' => $input['category_id'],
      'created_by' => $created_by,
      'expense_sub_category_id'=>$input["expenseSubCatId"]
      // 'last_modified_by' => $created_by,
      // 'subcategory_item_type' => $input['sub_category_item_type']
    );

    if( $input['call_for_edit'] == 'no') {
      if(!empty($is_exist)) {
        return -1;
      }
      $this->db->insert('procurement_itemmaster_subcategory', $data);
      $return= $this->db->insert_id();

      // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Subcategory',
        'source_id' => $return,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Subcategory Created',
        'action_description' => "A new sub-category with name - ".$input['product_name']." is created under category-id - ".$input['category_id']. ". Status is set to Active.",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

    } else {
      $a= $this->db->where('id', $input['sub_category_id_for_update'])->update('procurement_itemmaster_subcategory', $data);
      // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Subcategory',
        'source_id' => $input['sub_category_id_for_update'],
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Subcategory Updated',
        'action_description' => "A sub-category with name - ".$input['product_name']." is updated under category-id - ".$input['category_id']. ". Status is set to Active.",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

      if($a)
        $return= 1;
    }
    return $return;
  }
  
  public function get_categoryWise_subCategories($category_id) {
    $this->db_readonly->select("pis.*, ifnull(esc.sub_category, '-') as expense_sub_category_name, ifnull(esc.id, '') as expense_sub_category_id, pic.category_name, pic.is_sellable, pii.item_name, pii.id as item_id, concat( ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '') ) as c_by, date_format(pis.created_on, '%d-%m-%Y %h:%i %p') as c_on, date_format(pis.modified_on, '%d-%m-%Y %h:%i %p') as l_m_on,pis.is_item_visible_for_parent,pis.custom_name_flag")
    ->from('procurement_itemmaster_subcategory pis')
    ->join('expense_sub_category esc', 'esc.id=pis.expense_sub_category_id', 'left')
    ->join('procurement_itemmaster_category pic', 'pis.proc_im_category_id=pic.id')
    ->join('procurement_itemmaster_items pii', 'pis.id=pii.proc_im_subcategory_id', 'left')
    ->join('staff_master sm', "sm.id= pis.created_by", 'left');
    if($category_id) {
      $this->db_readonly->where('pis.proc_im_category_id', $category_id);
    }
    $this->db_readonly->order_by("pis.subcategory_name","ASC");
    $res= $this->db_readonly->get()->result();

   

    if(!empty($res)) {
      $this->load->library('filemanager');
      foreach($res as $key => $value) {
        if($value->picture_url && $value->picture_url != '' && $value->picture_url != null && $value->picture_url != '-') {
          $pic= $this->filemanager->getFilePath($value->picture_url);
          //  echo '<pre>';print_r($res);die();
          $value->picture_url= $pic;
        } else {
          $value->picture_url= '-';
        }
      }
    }

    $this->db_readonly->select("concat( ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '') ) as l_m_by, pis.id as sub_cat_id")
    ->from('procurement_itemmaster_subcategory pis')
    ->join('staff_master sm', "sm.id= pis.last_modified_by", 'left');
    if($category_id) {
      $this->db_readonly->where('pis.proc_im_category_id', $category_id);
    }
    $this->db_readonly->order_by("pis.subcategory_name", "ASC");
    $res2= $this->db_readonly->get()->result();

    return [$res, $res2];

  }
  
  public function delete_sub_category($id){
    // deleting all items exist in this sub-category from item table
    $this->db->where('proc_im_subcategory_id',$id);
    $this->db->delete('procurement_itemmaster_items');
    // deleting sub-category from sub-category table
    $this->db->where('id',$id);
    $status= $this->db->delete('procurement_itemmaster_subcategory');

    // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Subcategory',
        'source_id' => $id,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Subcategory Deleted',
        'action_description' => "A sub-category with id - $id is deleted. All items under this sub-category are also deleted.",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

      return $status;
  }
  
  public function delete_items($item_ids_arr){
    $return= 0;
    $details_history='';
    foreach($item_ids_arr as $a => $b) {
      $item= $this->db->select('item_name, proc_im_subcategory_id')->where('id', $b)->get('procurement_itemmaster_items')->row();
      if(!empty($item)) {
        $details_history .= $item->item_name . ' (ID was: ' . $b . ') from Sub-Category ID: ' . $item->proc_im_subcategory_id . ' | ';
      }
      $this->db->where('id', $b);
      $return= $this->db->delete('procurement_itemmaster_items');
    }

    // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Item',
        'source_id' => NULL,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Items Deleted',
        'action_description' => "The following items have beed deleted - $details_history",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);


    return $return;
  }
  
  public function get_items_from_sub_category($sub_category_id) {
    $result= $this->db_readonly->select("pii.id, pii.item_name, ifnull(pst.id, '-1') as is_deletable, pii.current_quantity")
    ->from('procurement_itemmaster_items pii')
    ->join('procurement_sales_transactions pst', "pst.proc_im_items_id= pii.id", 'left')
    ->where('pii.proc_im_subcategory_id', $sub_category_id)
    ->get();
    if($result->num_rows() > 0) {
      return $result->result();
    }
    return array();
  }

  public function get_information_about_item($item_id) {
    $result= $this->db_readonly->where('id', $item_id)->get('procurement_itemmaster_items')->result();
    if(!empty($result)) {
      $result[0]->is_used= '-1';

      $is_sailed= $this->db_readonly->where('proc_im_items_id', $item_id)->get('procurement_sales_transactions')->result();
      if(!empty($is_sailed)) {
        $result[0]->is_used= '1';
      }
      $is_allocated= $this->db_readonly->where('proc_im_items_id', $item_id)->get('procurement_item_allocations_staff')->result();
      if(!empty($is_allocated)) {
        $result[0]->is_used= '1';
      }

      // echo '<pre>'; print_r($result); die();
      return $result;
    }
    return array();
  }

  public function get_category_all_details($category_id) {
    return $this->db_readonly->select("pic.*, ifnull(pic.receipt_template, 0) as rec_temp, ifnull( concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')), 0 ) as creater, if(pic.category_administrator is not null AND pic.category_administrator > 0, concat(sm2.first_name, ' ', ifnull(sm2.last_name, '')), '-') as admin, ifnull(pic.category_administrator, 0) as category_administrator_id")
    ->from('procurement_itemmaster_category pic')
    ->join('staff_master sm', 'sm.id= pic.created_by_id', 'left')
    ->join('staff_master sm2', 'sm2.id= pic.category_administrator', 'left')
    ->where('pic.id', $category_id)
    ->get()->result();
  }

  public function saveReceiptFormat($id, $cat_id) {
    $this->db->where('id', $cat_id)->update('procurement_itemmaster_category', array('receipt_book_id' => $id));
    $status= $this->db->affected_rows();

    // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Category',
        'source_id' => $cat_id,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Receipt Number Format Updated',
        'action_description' =>"Receipt number format is updated for a category with id - ".$_POST['item_category_id'],
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);
    return $status;
  }

  public function submit_receipt_template() {
    $this->db->where('id', $_POST['item_category_id'])->update('procurement_itemmaster_category', array('receipt_template' => $_POST['html_template_id']));
    $status= $this->db->affected_rows();

    // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Category',
        'source_id' => $_POST['item_category_id'],
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Receipt Template Updated',
        'action_description' =>"A receipt template is updated for a category with id - ".$_POST['item_category_id'],
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

      return $status;
  }

  public function editCategory() {
    $this->db->where('id', $_POST['catId'])->update('procurement_itemmaster_category', array('category_name' => $_POST['catName'], 'status' => $_POST['category_status_edit'], 'category_administrator' => $_POST['category_administrator_edit'], 'category_type' => $_POST['category_type_edit'], 'category_description' => $_POST['catDesc'], 'is_sellable' => $_POST['isSellable']));
    $status= $this->db->affected_rows();

    // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Category',
        'source_id' => $_POST['catId'],
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Category Updated',
        'action_description' => "A category with name - ".$_POST['catName']." is updated. Status is set to ".$_POST['category_status_edit']." and Category type is set to ".$_POST['category_type_edit'],
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

      return $status;
  }

  public function deleteCategory() {
    $this->db->where('id', $_POST['id'])->delete('procurement_itemmaster_category');
    $status= $this->db->affected_rows();

    // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Category',
        'source_id' => NULL,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Category Deleted',
        'action_description' => "A category with id - ".$_POST['id']." is deleted.",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

    return $status;
  }

  public function saveItemInforation($path) {
    $input= $this->input->post();
    $attrs= $this->db->select('pii.attributes')
      ->where('pii.id', $input['itemId'])
      ->get('procurement_itemmaster_items pii')->result();
    $atrs= json_decode($attrs[0]->attributes);
    // echo '<pre>rt'; print_r(($input)); die();
    foreach($atrs as $key => $val) {
      $atrs->$key= $input[ucfirst($key)];
    }
    if($input['i_item_name'] == '') {
      return false;
    }

    $data= array(
      'item_name' => $input['i_item_name'],
      'sku_code' => $input['i_sku_code'],
      'unit_type' => $input['i_unit_type'],
      'hsn_sac' => $input['i_hsn'],
      'image' => (!isset($path['file_name']) || $path['file_name'] == '') ? null : $path['file_name'],
      // 'cost_prodcut' => $input['i_cost_product'],
      // 'total_quantity' => $input['i_total_quantity'],
      // 'current_quantity' => $input['i_current_quantity'],
      // 'initial_quantity' => $input['i_initial_quantity'],
      // 'item_description' => $input['i_desc'],
      'threshold_quantity' => $input['i_threashold_quantity'],
      'is_perishable' => $input['i_perishable'],
      'classification' => $input['i_classification'],
      'attributes' => json_encode($atrs)
    );
    if(! $this->settings->getSetting("inventory_shoppingcart_donot_show_price")){ 
      $data['selling_price'] = $input['i_selling_price'];
    }
    $this->db->where('id', $input['itemId'])->update('procurement_itemmaster_items', $data);
    $x= $this->db->affected_rows();
    if($x) {
      // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Item',
        'source_id' => $input['itemId'],
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Item Updated',
        'action_description' => 'Item updated with name - '. $input['i_item_name'],
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);
    }
    return $x;
  }

  public function getProductNames($category_id) {
    return $this->db->select('id, subcategory_name as product_name')->where('proc_im_category_id', $category_id)->get('procurement_itemmaster_subcategory')->result();
  }

  public function getVariantNames($product_id) {
    return $this->db->select('id, item_name as name, current_quantity as current_quantity')->where('proc_im_subcategory_id', $product_id)->get('procurement_itemmaster_items')->result();

  }

  public function getVariantNames2($product_id, $sales_year_id) {
    $all_items= $this->db_readonly->select('id, item_name as name')->where('proc_im_subcategory_id', $product_id)->get('procurement_itemmaster_items')->result();
    foreach($all_items as $key => $item) {
      $x= $this->db_readonly->select('pii.id, pii.current_quantity as current_quantity')
      ->from('procurement_delivery_challan_items pii')
      ->join('procurement_delivery_challan_master pim', 'pim.id= pii.invoice_master_id')
      ->where('pii.current_quantity >', 0)
      ->where('pii.proc_im_items_id', $item->id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->get()->row();

      if(!empty($x)) {
        $item->invoice_items_id= $x->id;
        $item->current_quantity= $x->current_quantity;
      } else {
        unset($all_items[$key]);
      }
    }
    return $all_items;
  }

  public function getTransactionReport($category_id, $product_id, $variant_id, $sales_year_id) {
    // $vendor_ids= $this->db_readonly->select('id')->where('vendor_name', 'Initial Quantity')->get('procurement_vendor_master')->result();
    // $vendor_id= [];
    // if(!empty($vendor_ids)) {
    //   foreach($vendor_ids as $keys => $vals) {
    //     $vendor_id[]= $vals->id;
    //   }
    // } else {
    //   $vendor_id= [0];
    // }
    // if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
    //   $initial_invoice_master_ids= $this->db_readonly->select('id')->where('sales_year_id', $sales_year_id)->where_in('vendor_id', $vendor_id)->get('procurement_delivery_challan_master')->result();
    // } else {
    //   $initial_invoice_master_ids= $this->db_readonly->select('id')->where_in('vendor_id', $vendor_id)->get('procurement_delivery_challan_master')->result();
    // }
    // $initial_invoice_master_id= [];
    // if(!empty($initial_invoice_master_ids)) {
    //   foreach($initial_invoice_master_ids as $kes => $ves) {
    //     $initial_invoice_master_id[]= $ves->id;
    //   }
    // } else {
    //   $initial_invoice_master_id= [];
    // }

    // echo '<pre>'; print_r($initial_invoice_master_id); die();

// Initial transaction status
    $added = $this->db_readonly->select("'Initial' as tx_type, pv.id as variant_id, pv.item_name as variant_name, 'Added Initially' as status, item.initial_quantity as quantity, DATE_FORMAT(pm.created_on, '%d-%m-%Y') as date, DATE_FORMAT(pm.created_on, '%h:%i %p') as dateTime, 'Inventory' as takenBy, '1' as is_stockable, pm.created_on as date_sorting, item.current_quantity, item.price, item.selling_price")
    ->from('procurement_itemmaster_items pv')
    ->join('procurement_delivery_challan_items item',"item.proc_im_items_id=pv.id")
    ->join('procurement_delivery_challan_master pInvMaster',"item.invoice_master_id=pInvMaster.id")
    ->join('procurement_itemmaster_subcategory pm', 'pm.id=pv.proc_im_subcategory_id')
    ->where('pv.id', $variant_id)
    ->where('pInvMaster.dc_type', 'Opening Balance');
  //  if(!empty($initial_invoice_master_id)) {
  //   $this->db_readonly->where_in('item.invoice_master_id', $initial_invoice_master_id);
  //  }
   if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
    $this->db_readonly->where('pInvMaster.sales_year_id', $sales_year_id);
  }
    $added = $this->db_readonly->get()->result();

    $data['Initial'] = 0;
    if(!empty($added)) {
      foreach ($added as $key => $val) {
        $data['Initial'] += $val->quantity;
      }
    }

// Purchased transaction status
    $this->db_readonly->select("'Initial' as tx_type, pv.id as variant_id, pv.item_name as variant_name, CONCAT('Brought through Invoice (', ifnull(im.invoice_no, ''),')') as status, item.initial_quantity as quantity, DATE_FORMAT(im.created_on, '%d-%m-%Y') as date, DATE_FORMAT(im.created_on, '%h:%i %p') as dateTime, 'Inventory' as takenBy, '1' as is_stockable, im.created_on as date_sorting, item.current_quantity, item.price, item.selling_price")
              ->from('procurement_itemmaster_items pv')
              ->join('procurement_delivery_challan_items item','item.proc_im_items_id=pv.id')
              ->join('procurement_delivery_challan_master im', 'item.invoice_master_id=im.id')
              ->where('pv.id', $variant_id)
              ->where('im.dc_type =', 'Normal Purchase');
    // if(!empty($initial_invoice_master_id)) {
    //   $this->db_readonly->where_not_in('im.id', $initial_invoice_master_id);
    // }
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('im.sales_year_id', $sales_year_id);
    }
    $invoices = $this->db_readonly->get()->result();

    $data['Purchased'] = 0;
    if(!empty($invoices)) {
      foreach ($invoices as $key => $val) {
        $data['Purchased'] += $val->quantity;
      }
    }

// Allocated to staff transaction status
    $this->db_readonly->select("'Allocated' as tx_type, pv.id as variant_id, pv.item_name as variant_name, 'Allocated' as status, vs.allocated_quantity as quantity, DATE_FORMAT(vs.given_date, '%d-%m-%Y') as date, CONCAT(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as takenBy, DATE_FORMAT(vs.given_date, '%h:%i %p') as dateTime, '1' as is_stockable, vs.given_date as date_sorting")
              ->from('procurement_itemmaster_items pv')
              ->join('procurement_item_allocations_staff vs','vs.proc_im_items_id=pv.id')
              ->join('staff_master sm', 'sm.id=vs.collected_by')
              ->where('pv.id', $variant_id);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('vs.sales_year_id', $sales_year_id);
    }
    $allocated = $this->db_readonly->get()->result();

    $data['allocatedTotal'] = 0;
    if(!empty($allocated)) {
      foreach ($allocated as $key => $val) {
        $data['allocatedTotal'] += $val->quantity;
      }
    }

    // Collected from staff transaction status
    $this->db_readonly->select("'Allocated' as tx_type, pias.proc_im_items_id as variant_id, pii.item_name as variant_name, 'Collected' as status, pics.returned_quantity as quantity, DATE_FORMAT(pics.return_on, '%d-%m-%Y') as date, TRIM(CONCAT(sm.first_name, ' ', ifnull(sm.last_name, ''))) as takenBy, DATE_FORMAT(pics.return_on, '%h:%i %p') as dateTime, pics.return_type as is_stockable, pics.return_on as date_sorting")
              ->from('procurement_item_collections_staff pics')
              ->join('procurement_item_allocations_staff pias','pics.item_allocations_staff_id=pias.id')
              ->join('staff_master sm', 'sm.id=pias.collected_by')
              ->join('procurement_itemmaster_items pii', 'pii.id= pias.proc_im_items_id')
              ->where('pias.proc_im_items_id', $variant_id);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('pias.sales_year_id', $sales_year_id);
    }
    $collected = $this->db_readonly->get()->result();

    $data['collectedTotal'] = 0;
    $data['collectedDamagedTotal'] = 0;
    $data['collectedNonStockableTotal'] = 0;
    if(!empty($collected)) {
      foreach ($collected as $key => $val) {
        if($val->is_stockable == '1') {
          $data['collectedTotal'] += $val->quantity;
        } else if($val->is_stockable == '2') {
          $data['collectedDamagedTotal'] += $val->quantity;
        }
         else {
          $data['collectedNonStockableTotal'] += $val->quantity;
        }
      }
    }

// Sold to student transaction status
    // getting item name
    $item= $this->db_readonly->select("item_name")->where('id', $variant_id)->get('procurement_itemmaster_items')->row()->item_name;
    $this->db_readonly->select("'Sold' as tx_type, pst.proc_im_items_id as variant_id, '$item' as variant_name, pst.quantity, DATE_FORMAT(psm.created_on, '%d-%m-%Y') as date, DATE_FORMAT(psm.created_on, '%h:%i %p') as dateTime, pst.sales_master_id, psm.student_id, psm.sales_type, psm.student_name, psm.parent_name, '1' as is_stockable, psm.created_on as date_sorting")
              ->from('procurement_sales_transactions pst')
              ->join('procurement_sales_master psm','psm.id= pst.sales_master_id')
              ->where('pst.proc_im_items_id', $variant_id);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('pst.sales_year_id', $sales_year_id);
    }
    $sold = $this->db_readonly->get()->result();

    // echo '<pre>'; print_r( $sold); die();


    $data['soldTotal'] = 0;
    if(!empty($sold)) {
      foreach ($sold as $key => $val) {
        $data['soldTotal'] += $val->quantity;
        if($val->sales_type == 'existing') {
          $val->status= "Sold to an existing student";
          $nm_cl_sn= $this->db_readonly->select("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as name, cs.class_name, cs.section_name")
              ->from('student_admission sa')
              ->join("student_year sy","sy.student_admission_id=sa.id")
              ->join('class_section cs','cs.id= sy.class_section_id')
              ->where("sy.acad_year_id",$this->yearId)
              ->where('sa.admission_status', 2)
              ->where('sy.promotion_status not in ("JOINED", "4", "5")')
              ->where('sa.id', $val->student_id)->get('student_admission');
              if($nm_cl_sn->num_rows() > 0) {
                $nm_cl_sn= $nm_cl_sn->row();
                $val->takenBy= $nm_cl_sn->name. ' (' .$nm_cl_sn->class_name. '-' .$nm_cl_sn->section_name. ')';
                
              } else { // for Alumni student
                $nm_cl_sn= $this->db_readonly->select("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as name")->where('sa.id', $val->student_id)->get('student_admission sa')->row();

                if(!empty($nm_cl_sn)) {
                  $nm_cl_sn= $nm_cl_sn->name;
                } else {
                  $nm_cl_sn= '';
                }
                $val->takenBy= $nm_cl_sn. ' (Alumni)';
                
              }
        } else {
          $val->takenBy= $val->student_name. ' son of '. $val->parent_name; 
          $val->status= "Sold to a new student";
        }
      }
    }

    // echo '<pre>'; print_r( $data['soldTotal']); die();

    
// Item returned transaction status
    $return= $this->db_readonly->select("'Sold' as tx_type, '$variant_id' as variant_id, '$item' as variant_name, return_quantity as quantity, DATE_FORMAT(return_on, '%d-%m-%Y') as date, DATE_FORMAT(return_on, '%h:%i %p') as dateTime, student_id, student_type, student_name, parent_name, '1' as is_stockable, return_on as date_sorting")
            ->where('proc_im_items_id', $variant_id);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('sales_year_id', $sales_year_id);
    }
    $return= $this->db_readonly->get('procurement_sales_return')->result();

    $data['returnedTotal'] = 0;
    if(!empty($return)) {
      foreach ($return as $key => $val) {
        $data['returnedTotal'] += $val->quantity;
        if($val->student_type == 'existing') {
          $val->status= "Returned by an existing student";
          $nm_cl_sn= $this->db_readonly->select("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as name, cs.class_name, cs.section_name")
              ->from('student_admission sa')
              ->join("student_year sy","sy.student_admission_id=sa.id")
              ->join('class_section cs','cs.id= sy.class_section_id')
              ->where("sy.acad_year_id",$this->yearId)
              ->where('sa.admission_status', 2)
              ->where('sy.promotion_status not in ("JOINED", "4", "5")')
              ->where('sa.id', $val->student_id)->get('student_admission');
              if($nm_cl_sn->num_rows() > 0) {
                $nm_cl_sn= $nm_cl_sn->row();
                $val->takenBy= $nm_cl_sn->name. ' (' .$nm_cl_sn->class_name. '-' .$nm_cl_sn->section_name. ')';
                
              } else { // for Alumni student
                // echo '<pre>'; print_r($val); die();
                if($val->student_id > 0) {
                  $nm_cl_sn= $this->db_readonly->select("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as name")->where('sa.id', "$val->student_id")->get('student_admission sa')->row();
                  $val->takenBy= $nm_cl_sn. ' (Alumni)';
                } else {
                  $val->takenBy= 'Admin';
                }
                
              }
        } else {
          $val->takenBy= $val->student_name. ' son of '. $val->parent_name; 
          $val->status= "Returned by a new student";
        }
      }
    }

// Soft deleted transaction status
    $softDeleted = $this->db_readonly->select("'Sold' as tx_type, $variant_id as variant_id, '$item' as variant_name, CONCAT('Receipt - ', psm.receipt_no, ' cancelled') as status, pst.quantity as quantity, DATE_FORMAT(psm.soft_delete_on, '%d-%m-%Y') as date, TRIM(a.friendly_name) as takenBy1, trim(concat(sa.first_name, ' ', ifnull(sa.last_name, ''))) as takenBy, DATE_FORMAT(psm.soft_delete_on, '%h:%i %p') as dateTime, '1' as is_stockable, psm.soft_delete_on as date_sorting")
      ->from('procurement_sales_transactions pst')
      ->join('procurement_sales_master psm', 'psm.id= pst.sales_master_id')
      ->join('avatar a', 'a.id= psm.soft_delete_by')
      ->join('student_admission sa', 'sa.id= psm.student_id')
      ->where('pst.proc_im_items_id', $variant_id)
      ->where('psm.soft_delete', 1);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('pst.sales_year_id', $sales_year_id);
    }
    $softDeleted = $this->db_readonly->get()->result();

    $data['softDeletedItems'] = 0;
    if(!empty($softDeleted)) {
      foreach ($softDeleted as $key => $val) {
        $data['softDeletedItems'] += $val->quantity;
      }
    }

    // Vendor return transaction status
    $vendorReturned = $this->db_readonly->select("'Initial' as tx_type, $variant_id as variant_id, '$item' as variant_name, CONCAT('<b>Vendor Return :</b> Item - <b>$item</b> returned from invoice - <b>', pim.invoice_no, '</b> to the vendor') as status, pvri.return_quantity as quantity, DATE_FORMAT(pvrm.created_on, '%d-%m-%Y') as date, 'Staff' as takenBy1, ifnull(concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as takenBy, DATE_FORMAT(pvrm.created_on, '%h:%i %p') as dateTime, '1' as is_stockable, pvrm.created_on as date_sorting, pInvI.current_quantity, pInvI.price, pInvI.selling_price")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pInvI', 'pim.id= pInvI.invoice_master_id')
      ->join('procurement_delivery_challan_return_items pvri', 'pvri.proc_invoice_items_id= pInvI.id')
      ->join('procurement_delivery_challan_return_master pvrm', 'pvrm.id= pvri.vendor_return_master_id')
      ->join('staff_master sm', 'sm.id= pvrm.created_by', 'left')
      ->where('pInvI.proc_im_items_id', $variant_id);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('pim.sales_year_id', $sales_year_id);
    }
    $vendorReturned = $this->db_readonly->get()->result();

    $data['vendorReturnedItems'] = 0;
    if(!empty($vendorReturned)) {
      foreach ($vendorReturned as $key => $val) {
        $data['vendorReturnedItems'] += $val->quantity;
      }
    }


    
    $data['data'] = array_merge($added, $invoices, $allocated, $collected, $sold, $softDeleted, $return, $vendorReturned);
      usort($data['data'], function($a, $b){ // date wise descending order
        $d1 = strtotime($a->date_sorting);
        $d2 = strtotime($b->date_sorting);
        return ($d2 <=> $d1);
    });

    // echo '<pre>'; print_r($data); die();

    return $data;
  }

  public function getStaffList() {
    return $this->db_readonly->select("id, TRIM(CONCAT(ifnull(first_name, ''), ' ', ifnull(last_name, ''))) as staffName")->where('status', 2)->get('staff_master')->result();
  }

//   public function allocate_products() {
//     $input = $this->input->post();
//     $status= true;
//     $this->db->trans_start();
//     foreach($input['selected_staffs'] as $staff_key => $staff_id_value) {
//       $variants = array();
//       $invoice_iteems_iids= array();
//       $x_key= 0;
//       foreach ($input['quantity'] as $variant_id => $quantity) {
//         $itemInvoiceItemIds= explode('___', $input['items_id'][$x_key]);
//         $variants[] = $itemInvoiceItemIds[0];
//         $invoice_iteems_iids[] = $itemInvoiceItemIds[1];
//         $x_key++;
//         $data = array(
//           'collected_by' => $staff_id_value,
//           'proc_im_items_id' => $itemInvoiceItemIds[0],
//           'proc_invoice_items_id' => $itemInvoiceItemIds[1],
//           'allocated_quantity' => $quantity,
//           'purpose' => $input['purpose'],
//           'sales_year_id' => $input['sales_year_id'],
//           'allocate_date' => date('Y-m-d',strtotime($input['allocate_date'])),
//           'given_by' => $this->authorization->getAvatarStakeHolderId()
//         );

//         $exist_qty_in_invoices= $this->db->select("current_quantity")->where('id', $itemInvoiceItemIds[1])->get('procurement_delivery_challan_items')->row()->current_quantity;
//         // $qty= $this->db->select("current_quantity")->where('id', $itemInvoiceItemIds[0])->get('procurement_itemmaster_items')->row()->current_quantity;
//         if($exist_qty_in_invoices >= $quantity) {
//           $this->db->insert('procurement_item_allocations_staff', $data);
//         } else {
//           $status= false;
//         }

//       }

//       if($staff_key == 0) {
//         $variantData = $this->db->select('id, current_quantity')->where_in('id', $variants)->get('procurement_itemmaster_items')->result();
//         $variantUpdate = array();
//         foreach ($variantData as $k => $value) {
//           $currentQuantity = $value->current_quantity - ($input['quantity'][$k] * count($input['selected_staffs']));
//           $variantUpdate[] = array(
//             'id' => $value->id,
//             'current_quantity' => $currentQuantity
//           );
//         }
//         $this->db->update_batch('procurement_itemmaster_items', $variantUpdate, 'id');

// // Inv item qty update
//         $invItemData = $this->db->select('id, current_quantity')->where_in('id', $invoice_iteems_iids)->get('procurement_delivery_challan_items')->result();
//         $invItemUpdate = array();
//         foreach ($invItemData as $k => $value) {
//           $currentQuantity = $value->current_quantity - ($input['quantity'][$k] * count($input['selected_staffs']));
//           $invItemUpdate[] = array(
//             'id' => $value->id,
//             'current_quantity' => $currentQuantity
//           );
//         }
//         $this->db->update_batch('procurement_delivery_challan_items', $invItemUpdate, 'id');

//       }

//       // $this->db->insert_batch('procurement_item_allocations_staff', $data);
//       $data= [];
      
//     }
//     $this->db->trans_complete();
//     if ($this->db->trans_status() === FALSE || $status === FALSE) {
//         $this->db->trans_rollback();
//         return 0;
//     }
//     else {
//         $this->db->trans_commit();
//         return 1;
//     }
//   }

  public function get_inventory_allocations_staff_report($from_date, $to_date, $selected_staffs, $report_type) {
    $from_date= date('Y-m-d', strtotime($from_date));
    $to_date= date('Y-m-d', strtotime($to_date));
    $res= [];
    $res2= [];
    if($report_type == '3' || $report_type == '1') {
      $this->db_readonly->select("pias.id as allocation_id, pias.allocated_quantity, date_format(pias.given_date, '%d-%m-%Y %h:%i %p') as given_date,  pias.given_date as given_date_time, date_format(pias.allocate_date, '%d-%m-%Y') as allocate_date, pias.purpose, TRIM(CONCAT(sm_cby.first_name, ' ', ifnull(sm_cby.last_name, ''))) as sm_cby, IF(pias.given_by != '0' ,TRIM(CONCAT(sm_gby.first_name, ' ', ifnull(sm_gby.last_name, ''))), 'Super Admin') as sm_gby, pii.item_name")
        ->from('procurement_item_allocations_staff pias')
        ->join('procurement_itemmaster_items pii', 'pii.id= pias.proc_im_items_id')
        ->join('staff_master sm_cby', 'sm_cby.id= pias.collected_by')
        ->join('staff_master sm_gby', 'sm_gby.id= pias.given_by', 'left')
        ->where("pias.allocate_date between '$from_date' and '$to_date'");

      if(!empty($selected_staffs)) {
        $this->db_readonly->where_in('pias.collected_by', $selected_staffs);
      }
      $res= $this->db_readonly->get()->result();
    }

    if($report_type == '3' || $report_type == '2') {
      $this->db_readonly->select("pias.id as allocation_id, pics.returned_quantity as allocated_quantity, pics.return_on as given_date,  pias.given_date as taken_date,  pics.return_reason as purpose, TRIM(CONCAT(sm_cby.first_name, ' ', ifnull(sm_cby.last_name, ''))) as sm_cby, IF(pias.given_by != '0' ,TRIM(CONCAT(sm_gby.first_name, ' ', ifnull(sm_gby.last_name, ''))), 'Super Admin') as sm_gby, pii.item_name")
        ->from('procurement_item_collections_staff pics')
        ->join('procurement_item_allocations_staff pias', 'pias.id= pics.item_allocations_staff_id')
        ->join('procurement_itemmaster_items pii', 'pii.id= pias.proc_im_items_id')
        ->join('staff_master sm_cby', 'sm_cby.id= pias.collected_by')
        ->join('staff_master sm_gby', 'sm_gby.id= pics.collected_by_id', 'left')
        ->where("date_format(pias.allocate_date, '%Y-%m-%d') between '$from_date' and '$to_date'");

      if(!empty($selected_staffs)) {
        $this->db_readonly->where_in('pias.collected_by', $selected_staffs);
      }
      $res2= $this->db_readonly->get()->result();
    }

    if(!empty($res)) {
      foreach($res as $key => $val) {
        $val->given_date= local_time($val->given_date_time, 'd-m-Y h:i A');
      }
    }
    if(!empty($res2)) {
      foreach($res2 as $key => $val) {
        $val->given_date= local_time($val->given_date, 'd-m-Y h:i A');
        $val->taken_date= local_time($val->taken_date, 'd-m-Y h:i A');
      }
    }

    if(!empty($res) || !empty($res2)) {
      
      // echo '<pre>A'; print_r($res);
      // echo '<pre>C'; print_r($res2);
      //  die();
      return ['status' => '1', 'reports' => $res, 'collects' => $res2];
    }
    return ['status' => '-1', 'reports' => array(), 'collects' => array()];

  }

  public function get_allocated_items_of_a_staff($selected_staff_id) {
    $login= $this->authorization->getAvatarStakeHolderId();
    $this->db_readonly->select("pias.proc_invoice_items_id, pias.id as pias_id, pias.collected_by as staff_id, pias.proc_im_items_id, pias.allocated_quantity, pias.allocate_date, pii.item_name, date_format(pias.allocate_date, '%d-%m-%Y') as allocate_date, collected_quantity, collected_quantity as already_returned")
        ->from('procurement_item_allocations_staff pias')
        ->join('procurement_itemmaster_items pii', 'pii.id= pias.proc_im_items_id');
        if($login !== 0 && $login !== '0') {
          $this->db_readonly->join('procurement_itemmaster_subcategory pis', 'pis.id= pii.proc_im_subcategory_id')
            ->join('procurement_itemmaster_category pic', 'pic.id= pis.proc_im_category_id')
            ->where("(pic.category_administrator = '' OR pic.category_administrator IS NULL OR pic.category_administrator = $login)");
        }
    $res= $this->db_readonly->where('pias.collected_by', $selected_staff_id)->get()->result();

    if(!empty($res)) {
      return ['status' => '1', 'reports' => $res];
    }
    return ['status' => '-1', 'reports' => array()];
  }

  public function submit_staff_item_collection($input) {
    $sales_year_current= $this->db->where('is_active', 1)->get('procurement_sales_year')->row();

    $pias_id= $input['pias_id'];
    $proc_invoice_items_id= $input['proc_invoice_items_id'];
    $proc_im_items_id= $input['proc_im_items_id'];
    $already_returned_qty= $input['already_returned_qty'];
    $return_quantity= $input['return_quantity'];
    $return_reason= $input['return_reason'];

    $this->db->trans_start();
    $staff_details_history= '';
    $status= false;
    foreach($return_quantity as $key => $val) {

      
      
      $already_returned= 0;
      if($val != 0) {
        // Get the highest quantity which can be collected
        $collectable_quantity= $this->db->select('allocated_quantity, ifnull(collected_quantity, 0) as collected_quantity')->where('id', $pias_id[$key])->get('procurement_item_allocations_staff')->row();
        if(!empty($collectable_quantity)) {
          $already_returned= 1*$collectable_quantity->collected_quantity;
          $max_collectable= 1*$collectable_quantity->allocated_quantity - 1*$collectable_quantity->collected_quantity;
          if($val > $max_collectable) { // Need to restrct to collect again, because already collected all quantity
            continue;
          }
        }
        $status= true;

      // $prev_curr= $this->db->select('current_quantity')->where('id', $proc_im_items_id[$key])->get('procurement_itemmaster_items')->row()->current_quantity;
      // $item_curr_qty_update= array(
      //   'current_quantity' => $val + $prev_curr
      // );
      // $this->db->where('id', $proc_im_items_id[$key])->update('procurement_itemmaster_items', $item_curr_qty_update);
      
      // $inv_curr_qty= $this->db->select('current_quantity')->where('id', $proc_invoice_items_id[$key])->get('procurement_delivery_challan_items')->row()->current_quantity;
      // $inv_item_qty_update= array(
      //   'id' => $proc_invoice_items_id[$key],
      //   'current_quantity' => $val + $inv_curr_qty
      // );
      // $this->db->where('id', $proc_invoice_items_id[$key])->update('procurement_delivery_challan_items', $inv_item_qty_update);

      if($input["is_stockable_$key"] != '3') {
        $this_inv= $this->db->where('id', $proc_invoice_items_id[$key])->get('procurement_delivery_challan_items')->row();
        $this_invMaster= $this->db->select('pim.*')->where('pii.id', $proc_invoice_items_id[$key])->from('procurement_delivery_challan_items pii')->join('procurement_delivery_challan_master pim', 'pim.id = pii.invoice_master_id')->get()->row();
      
        $this_invMaster->invoice_no= "NormalCollect$this_invMaster->invoice_no";
        $this_invMaster->bill_no= "NormalCollect$this_invMaster->bill_no";
        $this_invMaster->dc_type= "Normal Collect";
        $this_invMaster->total_amount= $this_inv->price * $val;
        if(!empty($sales_year_current)) {
          $this_invMaster->sales_year_id= $sales_year_current->id;
        }
        unset($this_invMaster->id);
        $this->db->insert('procurement_delivery_challan_master', $this_invMaster);
        $master_id= $this->db->insert_id();
        unset($this_inv->id);
        $this_inv->invoice_master_id= $master_id;
        $this_inv->total_amount= $this_inv->price * $val;
        $this_inv->initial_quantity= $val;
        $this_inv->current_quantity= $val;
        $this_inv->is_closed= 1;
        $this_inv->previous_sales_year_closed_invoice_id= NULL;
        $this->db->insert('procurement_delivery_challan_items', $this_inv);
      }


      
        $insert_data[]= array(
          'item_allocations_staff_id' => $pias_id[$key],
          'returned_quantity' => $val,
          'collected_by_id' => $this->authorization->getAvatarStakeHolderId(),
          'return_reason' => $return_reason[$key],
          'return_type' => $input["is_stockable_$key"],
          'sales_year_id' => !empty($sales_year_current) ? $sales_year_current->id : 1
        );

    $staff= $this->db->select("sm.id as staff_id, TRIM(CONCAT(sm.first_name, ' ', ifnull(sm.last_name, ''))) as staff_name")
        ->from('procurement_item_allocations_staff pias')
        ->join('staff_master sm', 'sm.id= pias.collected_by')
        ->where('pias.id', $pias_id[$key])
        ->get()->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }

    $item= $this->db->select("item_name")->where('id', $proc_im_items_id[$key])->get('procurement_itemmaster_items')->row();
        $staff_details_history .= "
         Staff: $staff_name | Item: " . $item->item_name . " | Collected Quantity: " . $val;


        $update_data= array(
          'id' => $pias_id[$key],
          'collected_quantity' => $already_returned + $return_quantity[$key]
        );
        $this->db->where('id', $pias_id[$key])->update('procurement_item_allocations_staff', $update_data);
      }
     
    }
   
    if(isset($insert_data) && !empty($insert_data) && $status) {
      $this->db->insert_batch('procurement_item_collections_staff', $insert_data);
      // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Collection',
        'source_id' => NULL,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Staff Collection',
        'action_description' => "Some items are collected from staff: $staff_name in sales year ". (!empty($sales_year_current) ? $sales_year_current->year_name : '2023-24'). "Following are the details:
         $staff_details_history",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);
    }


    $this->db->trans_complete();
    return $this->db->trans_status() && $status;
  }

  public function get_template_format_by_category_id($category_id) {
    return $this->db_readonly->select('receipt_template')
    ->where('id', $category_id)
    ->get('procurement_itemmaster_category')->row();
  }

  public function requested_items_status() {
    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $this->db_readonly->select("prm.id as request_master_id, prm.request_by, prm.reporting_manager_id as requester_head_of_department, prm.request_type, TRIM( CONCAT(sm_req.first_name, ' ', ifnull(sm_req.last_name, '')) ) as requester_staff, ifnull( TRIM( CONCAT(sm_hod.first_name, ' ', ifnull(sm_hod.last_name, '')) ), 'No Head of Department') as hod_staff, DATE_FORMAT(prm.request_on, '%d-%m-%Y %h:%i %p') as request_on, DATE_FORMAT(prm.last_modified_on, '%d-%m-%Y %h:%i %p') as last_modified_on, prm.document_url, prm.remarks, ifnull(prm.hod_approval_status, '-1') as hod_approval_status, ifnull(prm.hod_approval_remarks, '-1') as hod_approval_remarks, ifnull(DATE_FORMAT(prm.hod_approval_on, '%d-%m-%Y %h:%i %p'), '-1') as hod_approval_on, prm.is_modified, prm.reporting_manager_id as hod_id, prm.request_title")
        ->from('procurement_request_master prm')
        ->join('staff_master sm_req', 'sm_req.id= prm.request_by')
        ->join('staff_master sm_hod', 'sm_hod.id= prm.reporting_manager_id', 'left');
    if($logged_in_staff_id != 0) {
      $this->db_readonly->where('prm.request_by', $logged_in_staff_id);
    }
    $this->db_readonly->order_by('prm.request_on', 'desc');
    $requests= $this->db_readonly->get()->result();

    if(!empty($requests)){
      foreach($requests as $key => $val) {
        $val->items= $this->db_readonly->select("pri.id as proc_request_items_id, pri.quantity, pii.item_name, ifnull(pii.unit_type, '') as unit_type, pii.current_quantity")
                ->from('procurement_request_items pri')
                ->join('procurement_itemmaster_items pii', 'pii.id= pri.proc_im_items_id')
                ->where('pri.proc_request_master_id', $val->request_master_id)
                ->get()->result();
      }
    }

    if(!empty($requests)) {
      return $requests;
    }
    return array();
  }

  public function get_requester_department() {
    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $dept= $this->db_readonly->select('department')->where('id', $logged_in_staff_id)->get('staff_master')->result();
    if(!empty($dept)) {
      return $dept[0]->department;
    }
    return '';
  }

  public function submit_item_request_form($data, $path) {
    // echo '<pre>'; print_r($data); die();
    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
                                $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
                                    ->where('id', $logged_in_staff_id)
                                    ->get('staff_master')->row();
                                $staff_name= "Admin";
                                if(!empty($staff)) {
                                  $staff_name= $staff->staff_name;
                                }
    $apr_algo= $this->db->select("approval_algorithm, approvar_1, approvar_2, approvar_3, category_administrator")->where('id', $data['categories_id'])->get('procurement_itemmaster_category')->row();
    $request_data= array(
      'request_type' => $data['request_type'],
      'request_by' => $logged_in_staff_id,
      'last_modified_on' => date('Y-m-d H:i:s'),
      'document_url' => (!isset($path['file_name']) || $path['file_name'] == '') ? null : $path['file_name'],
      'remarks' => $data['remarks'],
      'request_title' => isset($data['request_title']) ? $data['request_title'] : '',
      'proc_im_category_id' => $data['categories_id'],
      'reporting_manager_id' => $data['hod'],
      'facility_department_staff_id' => isset($apr_algo->category_administrator) ? $apr_algo->category_administrator : 0,
      'approval_algorithm' => isset($apr_algo->approval_algorithm) ? $apr_algo->approval_algorithm : 0,
      'facility_department_staff_id' => isset($apr_algo->category_administrator) ? $apr_algo->category_administrator : 0
    );

    if($apr_algo->approval_algorithm == '1') {
      $request_data['approvar_1_id']= $apr_algo->approvar_1;
      // array_push($request_data, ['approvar_1_id' => $apr_algo->approvar_1]);
    } else if($apr_algo->approval_algorithm == '2') {
      $request_data['approvar_1_id']= $apr_algo->approvar_1;
      $request_data['approvar_2_id']= $apr_algo->approvar_2;
      // array_push($request_data, ['approvar_1_id' => $apr_algo->approvar_1, 'approvar_2_id' => $apr_algo->approvar_2]);
    } else if($apr_algo->approval_algorithm == '3') {
      $request_data['approvar_1_id']= $apr_algo->approvar_1;
      $request_data['approvar_2_id']= $apr_algo->approvar_2;
      $request_data['approvar_3_id']= $apr_algo->approvar_3;
      // array_push($request_data, ['approvar_1_id' => $apr_algo->approvar_1, 'approvar_2_id' => $apr_algo->approvar_2, 'approvar_3_id' => $apr_algo->approvar_3]);
    }

    $this->db->trans_start();
    // echo '<pre>'; print_r($request_data); die();
    $this->db->insert('procurement_request_master', $request_data);
    $request_id= $this->db->insert_id();

    
    $has_items= false;
                        $history_details= '';
    foreach($data['items_id'] as $key => $val) {
      if($data['quantity'][$key] > 0) {
        $has_items= true;
        $item_data[]= array(
          'proc_request_master_id' => $request_id,
          'proc_im_items_id' => $data['items_id'][$key],
          'quantity' => $data['quantity'][$key]
        );
                          $item= $this->db->select("item_name")->where('id', $data['items_id'][$key])->get('procurement_itemmaster_items')->row();
                          $history_details .= "Item: " . $item->item_name . " | Quantity: " . $data['quantity'][$key] . "\n";
      }
    }

    if(!$has_items) {
      return false;
    }

    $this->db->insert_batch('procurement_request_items', $item_data);

    // History insert
      $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Request',
'source_id' => $request_id,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Request Item Allocation',

        'action_description' => "A staff, $staff_name, from your firm has requested for items from Allocation module. with request title: ". $data['request_type']."-". $data['request_title'] .". Following are the details: $history_details",

        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

    $this->db->trans_complete();
    return $this->db->trans_status();
    
  }

  public function all_requested_form_for_hod($filter) {
    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $this->db_readonly->select("prm.facility_department_staff_id, prm.id as request_master_id, prm.request_by, prm.reporting_manager_id as requester_head_of_department, prm.request_type, TRIM( CONCAT(sm_req.first_name, ' ', ifnull(sm_req.last_name, '')) ) as requester_staff, TRIM( CONCAT(sm_hod.first_name, ' ', ifnull(sm_hod.last_name, '')) ) as hod_staff, DATE_FORMAT(prm.request_on, '%d-%m-%Y %h:%i %p') as request_on, DATE_FORMAT(prm.last_modified_on, '%d-%m-%Y %h:%i %p') as last_modified_on, prm.document_url, prm.remarks, prm.is_modified, prm.reporting_manager_id as hod_id, prm.approval_algorithm, prm.allocation_status, prm.hod_approval_status, prm.facility_department_approval_status, prm.approvar_1_status, prm.approvar_2_status, prm.approvar_3_status, ifnull(concat(sm_admin.first_name, ' ', ifnull(sm_admin.last_name, '')), '') as admin")
        ->from('procurement_request_master prm')
        ->join('staff_master sm_req', 'sm_req.id= prm.request_by')
        ->join('staff_master sm_hod', 'sm_hod.id= prm.reporting_manager_id', 'left')
        ->join('staff_master sm_admin', 'sm_admin.id= prm.facility_department_staff_id', 'left')
        ->where("(prm.reporting_manager_id = $logged_in_staff_id OR prm.facility_department_staff_id = $logged_in_staff_id OR prm.approvar_1_id = $logged_in_staff_id OR prm.approvar_2_id = $logged_in_staff_id OR prm.approvar_3_id = $logged_in_staff_id)");
        
    if($filter == 'approvals_pending') {
      $this->db_readonly->where("(prm.hod_approval_status is NULL OR prm.hod_approval_status != 3 OR (prm.approval_algorithm = 1 AND (prm.approvar_1_status is NUll OR prm.approvar_1_status != '2')) OR (prm.approval_algorithm = 2 AND (prm.approvar_2_status is NULL OR prm.approvar_2_status != '2')) OR (prm.approval_algorithm = 3 AND (prm.approvar_3_status is NULL OR prm.approvar_3_status != '2')))");
    }
    if($filter == 'allocation_pending') {
      $this->db_readonly->where("(prm.hod_approval_status = '3' AND (prm.approval_algorithm = '' OR prm.approval_algorithm is NULL OR (prm.approval_algorithm = 1 AND prm.approvar_1_status = '2') AND (prm.approval_algorithm = 2 AND prm.approvar_2_status = '2') OR (prm.approval_algorithm = 3 AND prm.approvar_3_status != '2')) AND prm.allocation_status != 2)");
    }
    if($filter == 'allocated') {
      $this->db_readonly->where("prm.allocation_status", 2);
    }
    if($filter == 'collection_pending') {
      $this->db_readonly->where("prm.allocation_status = 2 AND prm.collection_status != 2");
    }
    if($filter == 'collected') {
      $this->db_readonly->where("prm.collection_status", 2);
    }
    $requests= $this->db_readonly->order_by('prm.id', 'desc')->get()->result();

    if(!empty($requests)){
      foreach($requests as $key => $val) {
        $val->items= $this->db_readonly->select("pri.quantity, pii.item_name, ifnull(pii.unit_type, '') as unit_type, pii.current_quantity")
                ->from('procurement_request_items pri')
                ->join('procurement_itemmaster_items pii', 'pii.id= pri.proc_im_items_id')
                ->where('pri.proc_request_master_id', $val->request_master_id)
                ->get()->result();
      }
    }

    // echo '<pre>'; print_r($requests); die();

    if(!empty($requests)) {
      return $requests;
    }
    return array();
  }

  public function change_status_hod_side($data) {
    if($data['approval_value'] == 1 || $data['approval_value'] == 2) {
      $hod_approval_insert= array(
        'proc_request_master_id' => $data['request_master_id'],
        'hod_approval_status' => $data['approval_value'],
        'hod_approval_remarks' => $data['hod_remarks'],
        'hod_approval_on' => date("Y-m-d H:i:s"),

        'hod_approval_last_modify_on' => date("Y-m-d H:i:s"),
        // 'staff_received_acknowledgement' => 1,
        'facility_department_respond_status' => 1
      );

    } else if($data['approval_value'] == 3) {
      $hod_approval_insert= array(
        'proc_request_master_id' => $data['request_master_id'],
        'hod_approval_status' => $data['approval_value'],
        'hod_approval_remarks' => $data['hod_remarks'],
        'hod_approval_on' => date("Y-m-d H:i:s"),
        'facility_department_staff_id' => $data['facility_department_staff_id'],
        'hod_approval_last_modify_on' => date("Y-m-d H:i:s"),
        // 'staff_received_acknowledgement' => 1,
        'facility_department_respond_status' => 1
      );
    }

    // $hod_respond_update= array(
    //   'hod_respond_status' => 2
    // );

    if($data['approval_value'] == 2) {
      $hod_respond_update['is_modified']= 2;
    }

    // Check for insert or update
    $check_transaction= $this->db->where('id', $data['request_master_id'])->where("hod_approval_status in ('1', '2', '3')")->get('procurement_request_master')->result();


    $this->db->trans_start();
      if(empty($check_transaction)) {
        $this->db->insert('procurement_request_master', $hod_approval_insert);
      } else {
        $this->db->where('proc_request_master_id', $data['request_master_id'])->update('procurement_request_master', $hod_approval_insert);
      }
      // $this->db->where('id', $data['request_master_id'])->update('procurement_request_master', $hod_respond_update);

      $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $logged_in_staff_id)
        ->get('staff_master')->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }
      // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Request',
        'source_id' => $data['request_master_id'],
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Status Change by HOD',
        'action_description' => "A HOD, $staff_name, from your firm has changed the status of a item allocation request to: " . ($data['approval_value'] == 1 ? 'Approved' : ($data['approval_value'] == 2 ? 'Rejected' : 'Pending')) . ". Remarks: " . $data['hod_remarks'],
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

    $this->db->trans_complete();
    return $this->db->trans_status();

  }

  public function get_item_list_from_request($request_master_id) {
    $res= $this->db_readonly->select("pri.id as pri_id, pri.proc_im_items_id, pri.quantity, pii.item_name, pii.unit_type, pii.current_quantity, pii.blocked_quantity")
        ->from('procurement_request_items pri')
        ->join('procurement_itemmaster_items pii', 'pii.id= pri.proc_im_items_id')
        ->where('pri.proc_request_master_id', $request_master_id)
        ->get()->result();

    if(!empty($res)) {
      return $res;
    }
    return array();
  }

  public function save_item_from_request($pri_ids, $item_qtys, $proc_request_master_id) {
    // echo '<pre>'; print_r($pri_ids); die();

    foreach($pri_ids as $key => $val) {
      $update_arr[]= array(
        'id' => $val,
        'quantity' => $item_qtys[$key]
      );
    }

    $this->db->trans_start();
      $this->db->update_batch('procurement_request_items', $update_arr, 'id');
      $this->db->where('id', $proc_request_master_id)->update('procurement_request_master', array('is_modified' => '3', 'last_modified_on' => date('Y-m-d H:i:s')));

      $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $logged_in_staff_id)
        ->get('staff_master')->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }

      // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Request',
        'source_id' => $proc_request_master_id,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Request Item Updated',
        'action_description' => 'A staff, ' . $staff_name . ', from your firm has updated the requested items which was associated to: ' . $staff_name,
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function delete_item_from_request($pri_id) {
    $this->db->trans_start();
    $status= $this->db->where('id', $pri_id)->delete('procurement_request_items');

    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $logged_in_staff_id)
        ->get('staff_master')->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }

    $item_id= $this->db->select('proc_im_items_id')->where('id', $pri_id)->get('procurement_request_items')->row();
    $item= $this->db->select("item_name")->where('id', $item_id->proc_im_items_id)->get('procurement_itemmaster_items')->row();

    // History insert
      $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Request-Item',
        'source_id' => $pri_id,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Item Removed from Request',
        'action_description' => "A staff, $staff_name, from your firm has removed an item: $item->item_name from a request. The item was associated to: $staff_name",
        'action_by' => $logged_in_staff_id
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);
      $this->db->trans_complete();
      return $this->db->trans_status();
  }

  public function all_requests_for_faciliy_department() {
    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $requests= $this->db_readonly->select("prm.id as request_master_id, prm.request_by, prm.request_type, TRIM( CONCAT(sm_req.first_name, ' ', ifnull(sm_req.last_name, '')) ) as requester_staff, ifnull( TRIM( CONCAT(sm_hod.first_name, ' ', ifnull(sm_hod.last_name, '')) ), 'No Head of Department') as hod_staff, DATE_FORMAT(prm.request_on, '%d-%m-%Y %h:%i %p') as request_on, prm.document_url, prm.reporting_manager_id as hod_id, DATE_FORMAT(prm.facility_department_approval_on, '%d-%m-%Y %h:%i %p') as facility_department_approval_on, prm.facility_department_approval_remarks, ifnull(prm.facility_department_approval_status, '-1') as facility_department_approval_status, ifnull(date_format(prm.facility_department_approval_on, '%d-%m-%Y %h:%i %p'), '') as facility_department_approval_on")
        ->from('procurement_request_master prm')
        // ->join('procurement_request_master prm', 'pra.proc_request_master_id= prm.id')
        ->join('staff_master sm_req', 'sm_req.id= prm.request_by')
        ->join('staff_master sm_hod', 'sm_hod.id= prm.reporting_manager_id', 'left')
        ->where('prm.facility_department_staff_id', $logged_in_staff_id)
        ->order_by('prm.request_on', 'desc')
        ->get()->result();

    if(!empty($requests)){
      foreach($requests as $key => $val) {
        $val->items= $this->db_readonly->select("pri.quantity, pii.item_name, ifnull(pii.unit_type, '') as unit_type, pii.current_quantity")
                ->from('procurement_request_items pri')
                ->join('procurement_itemmaster_items pii', 'pii.id= pri.proc_im_items_id')
                ->where('pri.proc_request_master_id', $val->request_master_id)
                ->get()->result();
      }
    }

    // echo '<pre>'; print_r($requests); die();

    if(!empty($requests)) {
      return $requests;
    }
    return array();
  }

  public function change_status_by_facility($request_master_id, $status_value, $facility_remarks) {
    $status= $this->db->where('proc_request_master_id', $request_master_id)->update('procurement_request_approval', ['facility_department_approval_status' => $status_value, 'facility_department_approval_remarks' => $facility_remarks, 'facility_department_approval_on' => date('Y-m-d H:i:s')]);

    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $logged_in_staff_id)
        ->get('staff_master')->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }

    // History insert
      $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Request',
        'source_id' => $request_master_id,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Status Change of a Request',
        'action_description' => "A staff, $staff_name from facility department, from your firm has changed the status of a request to: " . ($status_value == 1 ? 'Approved' : ($status_value == 2 ? 'Rejected' : 'Pending')) . ". Remarks: " . $facility_remarks,
        'action_by' => $logged_in_staff_id
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);
      $this->db->trans_complete();
      return $this->db->trans_status();

  }

  public function allocate_to_staff_by_facility($input, $upload_item_photo, $upload_signed_photo) {
   $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $logged_in_staff_id)
        ->get('staff_master')->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }

    $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $input['allocate_to_staff_request_by_id'])
        ->get('staff_master')->row();
    $allocate_to= "Admin";
    if(!empty($staff)) {
      $allocate_to= $staff->staff_name;
    }
    
    $current_sales_year= $this->db->where('is_active', 1)->get('procurement_sales_year')->row();
    $current_sales_year_id= NULL;
    if(!empty($current_sales_year)) {
      $current_sales_year_id= $current_sales_year->id;
    }
      $inv_items_ids = array();
      $status= true;
      $history_details= '';
      $this->db->trans_start();
      foreach ($input['item_quantities'] as $variant_id => $quantity) {
        $invItemsIdObj= $this->db->select('pii.id')->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')->where('pii.proc_im_items_id', $input['item_ids'][$variant_id])->where('pii.current_quantity >=', $quantity)->where('pim.sales_year_id', $current_sales_year_id)->get('procurement_delivery_challan_master pim')->row();
        // echo '<pre>'; print_r($this->db->last_query($invItemsIdObj)); die();
        if(!empty($invItemsIdObj)) {
          $invItemsId= $invItemsIdObj->id;
        } else {
          $invItemsId= 0;
          $status= false;
          break;
        }
        $inv_items_ids[] = $invItemsId;
        $data = array(
          'collected_by' => $input['allocate_to_staff_request_by_id'],
          'proc_im_items_id' => $input['item_ids'][$variant_id],
          'allocated_quantity' => $quantity,
          'purpose' => $input['allocatio_remarks'],
          'allocate_date' => date('Y-m-d'),
          'given_by' => $this->authorization->getAvatarStakeHolderId(),
          'allocate_picture_url' => (!isset($upload_item_photo['file_name']) || $upload_item_photo['file_name'] == '') ? null : $upload_item_photo['file_name'],
          'allocate_signed_url' => (!isset($upload_signed_photo['file_name']) || $upload_signed_photo['file_name'] == '') ? null : $upload_signed_photo['file_name'],
          'proc_request_master_id' => $input['allocate_to_staff_request_master_id'],
          'proc_invoice_items_id' => $invItemsId,
          'sales_year_id' => $current_sales_year_id
        );

         $item= $this->db->select("item_name")->where('id', $input['item_ids'][$variant_id])->get('procurement_itemmaster_items')->row();
        $history_details .= "Item: " . $item->item_name . " | Quantity: " . $quantity . "\n";

        $qty= $this->db->select("current_quantity")->where('id', $invItemsId)->get('procurement_delivery_challan_items')->row();
        
        $allocation_id_history= 0;
        if($qty >= $quantity) {
          $this->db->insert('procurement_item_allocations_staff', $data);
          $allocation_id_history= $this->db->insert_id();
        } else {
          $status= false;
        }
      }

      $update_request_master= array(
        'allocation_status' => 2
      );
      // Update item stock
        if($status !== false && !empty($inv_items_ids)) {
          $inv_data = $this->db->select('id, current_quantity, blocked_quantity')->where_in('id', $inv_items_ids)->get('procurement_delivery_challan_items')->result();
          $inv_data_update = array();
          foreach ($inv_data as $k => $value) {
            $currentQuantity = $value->current_quantity - $input['item_quantities'][$k];
            $current_blocked= $value->blocked_quantity - $input['item_quantities'][$k];
            $inv_data_update[] = array(
              'id' => $value->id,
              'current_quantity' => $currentQuantity,
              'blocked_quantity' => $current_blocked
            );
          }
          // $this->db->trans_start();
            $this->db->update_batch('procurement_delivery_challan_items', $inv_data_update, 'id');
            // $this->db->insert_batch('procurement_item_allocations_staff', $data);
            $this->db->where('id', $input['allocate_to_staff_request_master_id'])->update('procurement_request_master', $update_request_master);
        }

        // History insert
          $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Allocation',
        'source_id' => $allocation_id_history,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Item Allocated to Staff',
        'action_description' => "A staff, $staff_name from facility department, from your firm has allocated requested items to a staff: $allocate_to in sales year ". (!empty($current_sales_year) ? $current_sales_year->year_name : '2023-24'). ". Following are the details:
         $history_details",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

        $this->db->trans_complete();

    if ($this->db->trans_status() === FALSE || !$status) {
        $this->db->trans_rollback();
        return 0;
    }
    else {
        $this->db->trans_commit();
        return 1;
    }
  }

  public function get_reporting_manager() {
    $log_in_id= $this->authorization->getAvatarStakeHolderId();
    $rm= $this->db_readonly->select('reporting_manager_id')->where('id', $log_in_id)->get('staff_master')->result();
    if(!empty($rm)) {
      return $rm[0]->reporting_manager_id;
    }
    return '-1';
  }

  public function get_category_category_type_wise($input) {
    $cats= $this->db_readonly->where('category_type', $input)->where('status', 1)->get('procurement_itemmaster_category')->result(); // In request for items
    if(!empty($cats)) {
      return $cats;
    }
    return array();
  }

  public function get_sub_categories_and_items($inputs) {
    $cats= $this->db_readonly->select("pis.id as pis_id, pis.subcategory_name, pii.id as pii_id, pii.item_name, pii.unit_type")
      ->from('procurement_itemmaster_subcategory pis')
      ->join('procurement_itemmaster_items pii', 'pii.proc_im_subcategory_id= pis.id')
      ->where('pis.proc_im_category_id', $inputs['cat_id'])
      ->get()->result();
    if(!empty($cats)) {
      return $cats;
    }
    return array();
  }

  public function get_request_full_details($request_master_id) {
    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $requests= $this->db_readonly->select("prm.id as request_master_id, prm.request_by, prm.reporting_manager_id as requester_head_of_department, prm.request_type, TRIM( CONCAT(sm_req.first_name, ' ', ifnull(sm_req.last_name, '')) ) as requester_staff, ifnull( TRIM( CONCAT(sm_hod.first_name, ' ', ifnull(sm_hod.last_name, '')) ), 'No Head of Department') as hod_staff, DATE_FORMAT(prm.request_on, '%d-%m-%Y %h:%i %p') as request_on, DATE_FORMAT(prm.last_modified_on, '%d-%m-%Y %h:%i %p') as last_modified_on, prm.document_url, prm.remarks, prm.is_modified, prm.reporting_manager_id as hod_id, prm.*")
        ->from('procurement_request_master prm')
        ->join('staff_master sm_req', 'sm_req.id= prm.request_by')
        // ->join('staff_master sm_lm', 'prm.last_modified_by= sm_lm.id')
        ->join('staff_master sm_hod', 'sm_hod.id= prm.reporting_manager_id', 'left')
        // ->join('procurement_request_approval pra', 'pra.proc_request_master_id= prm.id', 'left')
        ->where('prm.id', $request_master_id)
        ->order_by('prm.request_on', 'desc')
        ->get()->row();

    if(!empty($requests)){
        $requests->items= $this->db_readonly->select("pri.quantity, pii.item_name, ifnull(pii.unit_type, '') as unit_type, pii.current_quantity, pii.blocked_quantity")
                ->from('procurement_request_items pri')
                ->join('procurement_itemmaster_items pii', 'pii.id= pri.proc_im_items_id')
                ->where('pri.proc_request_master_id', $requests->request_master_id)
                ->get()->result();
    }

    // echo '<pre>'; print_r($requests); die();

    if(!empty($requests)) {
      return $requests;
    }
    return array();
  }

  public function change_status_function($inputs) { // approver_algorithm
    // echo '<pre>'; print_r($inputs); die();
    $req_master_id= $inputs['req_master_id'];
    $approver_id= $inputs['approver_id']; // For checking if approvar is valid (Front end validation already there)
    $function_type= $inputs['function_type'];
    $approvar_type= $inputs['approvar_type'];
    $remarks= $inputs['remarks'];

    $blocked_items= $this->db->select("pii.id as pii_id, pii.blocked_quantity, pri.quantity")
      ->from('procurement_request_items pri')
      ->join('procurement_itemmaster_items pii', 'pii.id= pri.proc_im_items_id')
      ->where('pri.proc_request_master_id', $inputs['req_master_id'])
      ->get()->result();

    $hod_status= $this->db->where("hod_approval_status is not null")->where('id', $req_master_id)->get('procurement_request_master')->result();

    $update_array= array();
    $is_blocked_quantity_updatable= false;
    if($approvar_type == 'rm') { // Reporting Manager update data
        if($function_type == 'rej') {
          if(empty($hod_status)) {
            $update_array= array(
              'hod_approval_status' => 1,
              'hod_approval_remarks' => $remarks,
              'hod_approval_on' => date('Y-m-d H:i:s'),
              'hod_approval_last_modify_on' => date('Y-m-d H:i:s')
            );
          } else {
            $update_array= array(
              'hod_approval_status' => 1,
              'hod_approval_remarks' => $remarks,
              'hod_approval_last_modify_on' => date('Y-m-d H:i:s')
            );
          }
        } else if($function_type == 'apr') {
          if(empty($hod_status)) {
            $update_array= array(
              'hod_approval_status' => 3,
              'hod_approval_remarks' => $remarks,
              'hod_approval_on' => date('Y-m-d H:i:s'),
              'hod_approval_last_modify_on' => date('Y-m-d H:i:s')
            );
          } else {
            $update_array= array(
              'hod_approval_status' => 3,
              'hod_approval_remarks' => $remarks,
              'hod_approval_last_modify_on' => date('Y-m-d H:i:s')
            );
          }
          if(intval($inputs['approver_algorithm']) < 1) {
            $is_blocked_quantity_updatable= true;
            foreach($blocked_items as $bkey => $bval) {
              $updated_qty= (int)$bval->blocked_quantity + (int)$bval->quantity;
              $update_blocked_quantities[]= array(
                'id' => $bval->pii_id,
                'blocked_quantity' => $updated_qty
              );
            }

          }
        } else {
          if(empty($hod_status)) {
            $update_array= array(
              'hod_approval_status' => 2,
              'hod_approval_remarks' => $remarks,
              'hod_approval_on' => date('Y-m-d H:i:s'),
              'hod_approval_last_modify_on' => date('Y-m-d H:i:s')
            );
          } else {
            $update_array= array(
              'hod_approval_status' => 2,
              'hod_approval_remarks' => $remarks,
              'hod_approval_last_modify_on' => date('Y-m-d H:i:s')
            );
          }
        }
    } else if($approvar_type == 'apr_1') { // Approvar level 1 update data
        if($function_type == 'rej') {
          $update_array= array(
            'approvar_1_status' => 1,
            'approvar_1_remarks' => $remarks,
            'approvar_1_datetime' => date('Y-m-d H:i:s')
          );
        } else if($function_type == 'apr') {
          $update_array= array(
            'approvar_1_status' => 2,
            'approvar_1_remarks' => $remarks,
            'approvar_1_datetime' => date('Y-m-d H:i:s')
          );

          if(intval($inputs['approver_algorithm']) == 1) {
            $is_blocked_quantity_updatable= true;
            foreach($blocked_items as $bkey => $bval) {
              $updated_qty= (int)$bval->blocked_quantity + (int)$bval->quantity;
              $update_blocked_quantities[]= array(
                'id' => $bval->pii_id,
                'blocked_quantity' => $updated_qty
              );
            }

          }

        }
    } else if($approvar_type == 'apr_2') { // Approvar level 2 update data
        if($function_type == 'rej') {
          $update_array= array(
            'approvar_2_status' => 1,
            'approvar_2_remarks' => $remarks,
            'approvar_2_datetime' => date('Y-m-d H:i:s')
          );
        } else if($function_type == 'apr') {
          $update_array= array(
            'approvar_2_status' => 2,
            'approvar_2_remarks' => $remarks,
            'approvar_2_datetime' => date('Y-m-d H:i:s')
          );

          if(intval($inputs['approver_algorithm']) == 2) {
            $is_blocked_quantity_updatable= true;
            foreach($blocked_items as $bkey => $bval) {
              $updated_qty= (int)$bval->blocked_quantity + (int)$bval->quantity;
              $update_blocked_quantities[]= array(
                'id' => $bval->pii_id,
                'blocked_quantity' => $updated_qty
              );
            }

          }

        }
    } else if($approvar_type == 'apr_3') { // Approvar level 3 update data
        if($function_type == 'rej') {
          $update_array= array(
            'approvar_3_status' => 1,
            'approvar_3_remarks' => $remarks,
            'approvar_3_datetime' => date('Y-m-d H:i:s')
          );
        } else if($function_type == 'apr') {
          $update_array= array(
            'approvar_3_status' => 2,
            'approvar_3_remarks' => $remarks,
            'approvar_3_datetime' => date('Y-m-d H:i:s')
          );

          if(intval($inputs['approver_algorithm']) == 3) {
            $is_blocked_quantity_updatable= true;
            foreach($blocked_items as $bkey => $bval) {
              $updated_qty= (int)$bval->blocked_quantity + (int)$bval->quantity;
              $update_blocked_quantities[]= array(
                'id' => $bval->pii_id,
                'blocked_quantity' => $updated_qty
              );
            }

          }

        }
    } 
    // else if($approvar_type == 'fd') { // Facility Department Update data
    //     if($function_type == 'rej') {
    //       $update_array= array(
    //         'facility_department_approval_status' => 1,
    //         'facility_department_approval_remarks' => $remarks,
    //         'facility_department_approval_on' => date('Y-m-d H:i:s')
    //       );
    //     } else if($function_type == 'apr') {
    //       $update_array= array(
    //         'facility_department_approval_status' => 2,
    //         'facility_department_approval_remarks' => $remarks,
    //         'facility_department_approval_on' => date('Y-m-d H:i:s')
    //       );
    //     }
    // }

    $this->db->trans_start();
    if($is_blocked_quantity_updatable) {
      $this->db->update_batch('procurement_itemmaster_items', $update_blocked_quantities, 'id');
    }

    if(!empty($update_array)) {
      $this->db->where('id', $req_master_id)->update('procurement_request_master', $update_array);
    }

    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $logged_in_staff_id)
        ->get('staff_master')->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }

    // History insert
      $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Request',
        'source_id' => $req_master_id,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Status Changed',
        'action_description' => "A staff, $staff_name, from your firm has changed the status of a request to: " . ($function_type == 'apr' ? 'Approved' : ($function_type == 'rej' ? 'Rejected' : 'Pending')) . ". Remarks: " . $remarks,
        'action_by' => $logged_in_staff_id
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

    $this->db->trans_complete();
    return $this->db->trans_status();

  }

  private function _get_proc_staff_name($staffId){
    $result = $this->db_readonly->select("concat(ifnull(first_name,''), ' ',ifnull(last_name,'')) as name")
      ->from('staff_master')
      ->where('id',$staffId)
      ->get()->row();

      $staff_name = '';
      if(!empty($result)){
        $staff_name = $result->name;
      }
      return $staff_name;
  }
  
  public function get_history_of_request($req_master_id) { 
    $master  = $this->db_readonly->select("prm.*")
    ->from('procurement_request_master prm')
    ->where('prm.id',$req_master_id)
    ->get()->row();
    $mainrow = new stdClass();
    $mainrow_modified = new stdClass();
    $hodRow = new stdClass();
    $hodRow_modified = new stdClass();
    $facilityRow = new stdClass();
    $facilityRow_collect = new stdClass();
    $approve1 = new stdClass();
    $approve2 = new stdClass();
    $approve3 = new stdClass();
    // $facilityRow_collect = new stdClass();
    if(!empty($master)){ // created status
      $mainrow->name = $this->_get_proc_staff_name($master->request_by);
      $mainrow->remarks = $master->remarks;
      $mainrow->date = date('d-m-Y h:i A',strtotime($master->request_on)); 
      $mainrow->action = 'Request created by staff.';

      // if($master->hod_approval_status == '1' || $master->hod_approval_status == '2' || $master->hod_approval_status == '3') { 
        if(strtotime($master->hod_approval_on) != strtotime($master->hod_approval_last_modify_on) || $master->hod_approval_status == '2') { //hod modify status
          $hodRow->name = $this->_get_proc_staff_name($master->reporting_manager_id);
          $hodRow->remarks = $master->hod_approval_remarks;
          $hodRow->date = date('d-m-Y h:i A',strtotime($master->hod_approval_on));
          $hodRow->action = 'Reporting manager send for modify again to staff.';
        } 
        // else if(strtotime($master->hod_approval_on) == strtotime($master->hod_approval_last_modify_on) || $master->hod_approval_status == '1') {
        //   $hodRow->action = 'Rejected by reporting manager.';
        // } else if(strtotime($master->hod_approval_on) == strtotime($master->hod_approval_last_modify_on) || $master->hod_approval_status == '3') {
        //   $hodRow->action = 'Approved by reporting manager.';
        // }
      // }

      if(strtotime($master->request_on) != strtotime($master->last_modified_on) || $master->is_modified != '1') { // if hod send for modify, requester status
        if($master->is_modified == '3') {
          $mainrow_modified->name = $this->_get_proc_staff_name($master->request_by);
          $mainrow_modified->remarks = $master->remarks;
          $mainrow_modified->date = date('d-m-Y h:i A',strtotime($master->last_modified_on));
          $mainrow_modified->action = 'Request modified by staff.';
          
        }
      } 

      // if(strtotime($master->hod_approval_on) != strtotime($master->hod_approval_last_modify_on)) { // hod modified status
        if($master->hod_approval_status == '1' || $master->hod_approval_status == '3') {
          $hodRow_modified->name = $this->_get_proc_staff_name($master->reporting_manager_id);
          $hodRow_modified->remarks = $master->hod_modify_remarks;
          $hodRow_modified->date = date('d-m-Y h:i A',strtotime($master->hod_approval_last_modify_on));
          if($master->hod_approval_status == '1') {
            $hodRow_modified->action = 'Rejected by reporting manager';
          } else if($master->hod_approval_status == '3'){
            $hodRow_modified->action = 'Approved by reporting manager';
          }

        }
      // }

      if($master->allocation_status != '1') { // facility allocatiion status
        $facilityRow->name = $this->_get_proc_staff_name($master->facility_department_staff_id);
        $allocation_details= $this->db_readonly->select("purpose, date_format(given_date, '%d-%m-%Y %h:%i %p') as given_date, date_format(allocate_date, '%d-%m-%Y') as allocate_date")->where('proc_request_master_id', $req_master_id)->get('procurement_item_allocations_staff')->result();
        $facilityRow->date = '';
        $facilityRow->remarks = '';
        $facilityRow->action= '';
        if($master->allocation_status == '2') {
          if(!empty($allocation_details)) {
            $facilityRow->remarks = $allocation_details[0]->purpose;
            $facilityRow->date = $allocation_details[0]->given_date;
          }
          $facilityRow->action = 'Product allocated by facility department';
        }
      }


// echo '<pre>'; print_r($res2); die();

      $collection_array_detail= [];
      // if($master->collection_status != '1') { // facility collection status
        $collection_arr= $this->db_readonly->select("date_format(pics.return_on, '%d-%m-%Y %h:%i %p') as return_on, pics.return_reason as remarks, pics.item_allocations_staff_id, pics.returned_quantity, pii.item_name")
        ->from('procurement_item_collections_staff pics')
        ->join('procurement_item_allocations_staff pias', 'pias.id= pics.item_allocations_staff_id')
        ->join('procurement_itemmaster_items pii', 'pii.id= pias.proc_im_items_id')
        ->where('pics.proc_request_master_id', $req_master_id)
        ->order_by('pics.return_on')
        ->get()->result();
        $collector_name = $this->_get_proc_staff_name($master->facility_department_staff_id);


      if($master->approvar_1_status == '1' || $master->approvar_1_status == '2'){ // apr 1 status
        $approve1->name = $this->_get_proc_staff_name($master->approvar_1_id);
        $approve1->remarks = $master->approvar_1_remarks;
        $approve1->date = date('d-m-Y h:i A',strtotime($master->approvar_1_datetime));
        if($master->approvar_1_status == '1') {
          $approve1->action = 'Rejected by approver level 1';
        } else if($master->approvar_1_status == '2'){
          $approve1->action = 'Approved by approver level 1';
        }
      }

      if($master->approvar_2_status == '1' || $master->approvar_2_status == '2') { // apr 2 status
        $approve2->name = $this->_get_proc_staff_name($master->approvar_2_id);
        $approve2->remarks = $master->approvar_2_remarks;
        $approve2->date = date('d-m-Y h:i A',strtotime($master->approvar_2_datetime));
        if($master->approvar_2_status == '1') {
          $approve2->action = 'Rejected by approver level 2';
        } else if($master->approvar_2_status == '2'){
          $approve2->action = 'Approved by approver level 2';
        }
      }

      if($master->approvar_3_status == '1' || $master->approvar_3_status == '2') { // apr 3 status
        $approve3->name = $this->_get_proc_staff_name($master->approvar_3_id);
        $approve3->remarks = $master->approvar_3_remarks;
        $approve3->date = date('d-m-Y h:i A',strtotime($master->approvar_3_datetime));
        if($master->approvar_3_status == '1') {
          $approve3->action = 'Rejected by approver level 3';
        } else if($master->approvar_3_status == '2'){
          $approve3->action = 'Approved by approver level 3';
        }
      }

      $history_details = [];
      switch ($master->approval_algorithm) {
        case '1':
          array_push($history_details, $mainrow, $hodRow, $mainrow_modified, $hodRow_modified, $approve1, $facilityRow);
          break;
        case '2':
          array_push($history_details, $mainrow, $hodRow, $mainrow_modified, $hodRow_modified, $approve1, $approve2, $facilityRow);
          break;
        case '3':
          array_push($history_details, $mainrow, $hodRow, $mainrow_modified, $hodRow_modified, $approve1, $approve2, $approve3, $facilityRow);
          break;
        default:
          array_push($history_details, $mainrow, $hodRow, $mainrow_modified, $hodRow_modified, $facilityRow);
          break;
      }

      if(!empty($collection_arr)) {
        foreach($collection_arr as $keyyy => $valll) {
          $collect_detail= array(
            'name' => $collector_name,
            'remarks' => $valll->remarks,
            'date' => $valll->return_on,
            'action' => "$valll->returned_quantity quantity in item - $valll->item_name is collected",
          );
          array_push($history_details, $collect_detail);
        }
      }

      $master->history = $history_details;
      // echo "<pre>"; print_r($master); die();
      return $master;
    }

    // $res= $this->db_readonly->select("date_format(prm.request_on, '%d-%m-%Y %h:%i %p') as request_on1, date_format(prm.last_modified_on, '%d-%m-%Y %h:%i %p') as last_modified_on1, date_format(prm.hod_approval_on, '%d-%m-%Y %h:%i %p') as hod_approval_on1, date_format(prm.hod_approval_last_modify_on, '%d-%m-%Y %h:%i %p') as hod_approval_last_modify_on1, date_format(prm.approvar_1_datetime, '%d-%m-%Y %h:%i %p') as approvar_1_datetime1, date_format(prm.approvar_2_datetime, '%d-%m-%Y %h:%i %p') as approvar_2_datetime1, date_format(prm.approvar_3_datetime, '%d-%m-%Y %h:%i %p') as approvar_3_datetime1, date_format(prm.facility_department_approval_on, '%d-%m-%Y %h:%i %p') as facility_department_approval_on1, if(prm.reporting_manager_id != '', concat(sm1.first_name, ' ', sm1.last_name), '') as rm_name, if(prm.approvar_1_id != '', concat(sm2.first_name, ' ', sm2.last_name), '') as apr1_name, if(prm.approvar_2_id != '', concat(sm3.first_name, ' ', sm3.last_name), '') as apr2_name, if(prm.approvar_3_id != '', concat(sm4.first_name, ' ', sm4.last_name), '') as apr3_name, if(prm.facility_department_staff_id != '', concat(sm5.first_name, ' ', sm5.last_name), '') as facility_name, prm.hod_approval_status, prm.facility_department_approval_status, prm.approvar_1_status, prm.approvar_2_status, prm.approvar_3_status")
    //   ->from('procurement_request_master prm')
    //   ->join('staff_master sm1', 'sm1.id= prm.reporting_manager_id', 'left')
    //   ->join('staff_master sm2', 'sm2.id= prm.approvar_1_id', 'left')
    //   ->join('staff_master sm3', 'sm3.id= prm.approvar_2_id', 'left')
    //   ->join('staff_master sm4', 'sm4.id= prm.approvar_3_id', 'left')
    //   ->join('staff_master sm5', 'sm5.id= prm.facility_department_staff_id', 'left')
    //   ->where('prm.id', $req_master_id)
    //   ->get()->row();

      // echo '<pre>'; print_r($this->db_readonly->last_query($res)); die();
      // return $res;
  }

  function get_current_sales_year_data() { // we should not use DB_READONLY here, as we need to update the history table
    $current_sales_year= $this->db->select('id, year_name, start_date, end_date')->where('is_active', 1)->get('procurement_sales_year')->row();
    if(!empty($current_sales_year)) {
      return $current_sales_year;
    }
    $obj= new stdClass();
    $obj->id= 0;
    $obj->year_name= 'Year not active';
    $obj->start_date= '-';
    $obj->end_date= '-';
    return $obj;
  }

  public function create_approvals_type_wise($input) { 
    $this->db->trans_start();
    if($input['approval_type'] == 'Allocation') {
        $this->db->where('id', $input['item_category_id'])->update('procurement_itemmaster_category', array('approval_algorithm' => $input['approval_algorithm'], 'approvar_1' => $input['approvar_1'], 'approvar_2' => isset($input['approvar_2']) ? $input['approvar_2'] : NULL, 'approvar_3' => isset($input['approvar_3']) ? $input['approvar_3'] : NULL));
// Start: Check if request created from this category and final approvals are not started
        // $is_array_empty= true;
        // $status= $this->db->select("id, hod_approval_status, facility_department_approval_status, allocation_status, collection_status")->where('proc_im_category_id', $input['item_category_id'])->get('procurement_request_master')->result();
        // foreach($status as $key => $val) {
        //   if($val->facility_department_approval_status != '1' && $val->facility_department_approval_status != '2' && $val->allocation_status == '1' && $val->collection_status == '1') {
        //     $is_array_empty= false;
        //     $update_approvers_array[]= array(
        //       'id' => $val->id,
        //       'approval_algorithm' => $input['approval_algorithm'],
        //       'approvar_1_id' => isset($input['approvar_1']) ? $input['approvar_1'] : 0,
        //       'approvar_2_id' => isset($input['approvar_2']) ? $input['approvar_2'] : 0,
        //       'approvar_3_id' => isset($input['approvar_3']) ? $input['approvar_3'] : 0
        //     );
        //   }
        // }
        // if(!$is_array_empty) {
        //   $this->db->update_batch('procurement_request_master', $update_approvers_array, 'id');
        // }
// End: Check if request created from this category and final approvals are not started
    }else if($input['approval_type'] == 'Indent'){
      $this->db->where('id', $input['item_category_id'])->update('procurement_itemmaster_category', array('approval_algorithm_for_indent' => $input['approval_algorithm'], 'bom_approver_1' => $input['approvar_1'], 'bom_approver_2' => isset($input['approvar_2']) ? $input['approvar_2'] : NULL, 'bom_approver_3' => isset($input['approvar_3']) ? $input['approvar_3'] : NULL,
        'min_approver_1_amount' => isset($input['minApproverOneAmout']) ? $input['minApproverOneAmout'] : 0,
        'min_approver_2_amount' => isset($input['minApproverTwoAmout']) ? $input['minApproverTwoAmout'] : 0,
        'min_approver_3_amount' => isset($input['minApproverThreeAmout']) ? $input['minApproverThreeAmout'] : 0,
    ));
    }else if($input['approval_type'] == 'Financial'){
      $this->db->where('id', $input['item_category_id'])->update('procurement_itemmaster_category', array('financial_approver' => $input['approvar_1']));
    }

    // History insert
      $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Category',
        'source_id' => $input['item_category_id'],
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Approvers Updated',
        'action_description' => "A category's approvers has been updated.",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

    $this->db->trans_complete();
    if($this->db->trans_status()==false){
      $this->db->trans_rollback();
      return 0;
    }

    $this->db->trans_commit();
    return 1;
  }

  public function get_category_approvals_details($input) {
    $approvers=$this->db->select("
    IFNULL(CONCAT(IFNULL(sm1.first_name, ''), ' ', IFNULL(sm1.last_name, '')), '') AS approvar_1,
    IFNULL(CONCAT(IFNULL(sm2.first_name, ''), ' ', IFNULL(sm2.last_name, '')), '') AS approvar_2,
    IFNULL(CONCAT(IFNULL(sm3.first_name, ''), ' ', IFNULL(sm3.last_name, '')), '') AS approvar_3,
    pic.approvar_1 AS approvar_1_id,
    pic.approvar_2 AS approvar_2_id,
    pic.approvar_3 AS approvar_3_id,
    pic.approval_algorithm,
    pic.approval_algorithm_for_indent,
    IFNULL(CONCAT(IFNULL(sm4.first_name, ''), ' ', IFNULL(sm4.last_name, '')), '') AS bom_approver_1,
    IFNULL(CONCAT(IFNULL(sm5.first_name, ''), ' ', IFNULL(sm5.last_name, '')), '') AS bom_approver_2,
    IFNULL(CONCAT(IFNULL(sm6.first_name, ''), ' ', IFNULL(sm6.last_name, '')), '') AS bom_approver_3,
    IFNULL(CONCAT(IFNULL(sm7.first_name, ''), ' ', IFNULL(sm7.last_name, '')), '') AS financial_approver,
    pic.min_approver_1_amount,
    pic.min_approver_2_amount,
    pic.min_approver_3_amount
    ")
      ->from('procurement_itemmaster_category pic')
      ->join('staff_master sm1', 'sm1.id = pic.approvar_1', 'left')
      ->join('staff_master sm2', 'sm2.id = pic.approvar_2', 'left')
      ->join('staff_master sm3', 'sm3.id = pic.approvar_3', 'left')
      ->join('staff_master sm4', 'sm4.id = pic.bom_approver_1', 'left')
      ->join('staff_master sm5', 'sm5.id = pic.bom_approver_2', 'left')
      ->join('staff_master sm6', 'sm6.id = pic.bom_approver_3', 'left')
      ->join('staff_master sm7', 'sm7.id = pic.financial_approver', 'left')
      ->where('pic.id', $input['item_category_id'])
      ->get()
      ->row();

      if(!empty($approvers)){
        return $approvers;
      }else{
        return new stdClass();
      }
  }

  public function get_approver_name_time_and_remarks($input) {
    $res= $this->db_readonly->select("ifnull(concat(sm1.first_name, ' ', ifnull(sm1.last_name, '')), '') as approvar_1, ifnull(concat(sm2.first_name, ' ', ifnull(sm2.last_name, '')), '') as approvar_2, ifnull(concat(sm3.first_name, ' ', ifnull(sm3.last_name, '')), '') as approvar_3, ifnull(concat(sm4.first_name, ' ', ifnull(sm4.last_name, '')), '') as facility, ifnull(concat(sm5.first_name, ' ', ifnull(sm5.last_name, '')), '') as reporting_manager, prm.approvar_1_status, prm.approvar_2_status, prm.approvar_3_status, hod_approval_remarks, hod_modify_remarks, date_format(prm.approvar_1_datetime, '%d-%m-%Y %h:%i %p') as approvar_1_datetime1, date_format(prm.approvar_2_datetime, '%d-%m-%Y %h:%i %p') as approvar_2_datetime1, date_format(prm.approvar_3_datetime, '%d-%m-%Y %h:%i %p') as approvar_3_datetime1, date_format(prm.hod_approval_on, '%d-%m-%Y %h:%i %p') as hod_approval_on1, date_format(prm.hod_approval_last_modify_on, '%d-%m-%Y %h:%i %p') as hod_approval_last_modify_on1, prm.*")
      ->from('procurement_request_master prm')
      ->join('staff_master sm1', 'sm1.id= prm.approvar_1_id', 'left')
      ->join('staff_master sm2', 'sm2.id= prm.approvar_2_id', 'left')
      ->join('staff_master sm3', 'sm3.id= prm.approvar_3_id', 'left')
      ->join('staff_master sm4', 'sm4.id= prm.facility_department_staff_id', 'left')
      ->join('staff_master sm5', 'sm5.id= prm.reporting_manager_id', 'left')
      ->where('prm.id', $input['req_master_id'])
      ->get()->row();
      return $res;
  }

  public function get_allocation_status($input) {
    $res= $this->db_readonly->select("ifnull(allocate_picture_url, '') as allocate_picture_url, ifnull(allocate_signed_url, '') as allocate_signed_url, date_format(given_date, '%d-%m-%Y %h:%i %p') as given_date1, purpose as remarks")
        ->from('procurement_item_allocations_staff')
        ->where('proc_request_master_id', $input['req_master_id'])
        ->get()->result();

        // echo '<pre>'; print_r($this->db_readonly->last_query($res)); die();

    $res2= $this->db_readonly->select("date_format(pics.return_on, '%d-%m-%Y %h:%i %p') as return_on, pics.return_reason as remarks, pics.item_allocations_staff_id, pics.returned_quantity, pii.item_name")
        ->from('procurement_item_collections_staff pics')
        ->join('procurement_item_allocations_staff pias', 'pias.id= pics.item_allocations_staff_id')
        ->join('procurement_itemmaster_items pii', 'pii.id= pias.proc_im_items_id')
        ->where('pics.proc_request_master_id', $input['req_master_id'])
        ->order_by('pics.item_allocations_staff_id')
        ->get()->result();

    $res3= $this->db_readonly->select("pias.collected_quantity, pii.item_name, pri.quantity as allocated_qty")
      ->from('procurement_item_allocations_staff pias')
      ->join('procurement_itemmaster_items pii', 'pii.id= pias.proc_im_items_id')
      ->join('procurement_request_items pri', 'pri.proc_request_master_id= pias.proc_request_master_id and pri.proc_im_items_id=pias.proc_im_items_id')
      ->where('pias.proc_request_master_id', $input['req_master_id'])
      ->get()->result();

    

    if(!empty($res)) {
      return ['allo_status' => $res, 'coll_status' => $res2, 'total_coll_status' => $res3];
    }
    return ['allo_status' => [], 'coll_status' => [], 'total_coll_status' => []];
  }

  public function get_allocated_items_of_a_request($selected_staff_id, $req_master_id) {
    $res= $this->db_readonly->select("pias.proc_invoice_items_id, pias.id as pias_id, pias.collected_by as staff_id, pias.proc_im_items_id, pias.allocated_quantity, pias.allocate_date, pii.item_name, date_format(pias.allocate_date, '%d-%m-%Y') as allocate_date, collected_quantity, collected_quantity as already_returned")
        ->from('procurement_item_allocations_staff pias')
        ->join('procurement_itemmaster_items pii', 'pii.id= pias.proc_im_items_id')
        ->where('pias.collected_by', $selected_staff_id)
        ->where('pias.proc_request_master_id', $req_master_id)
        ->get()->result();

    if(!empty($res)) {
      return ['status' => '1', 'reports' => $res];
    }
    return ['status' => '-1', 'reports' => array()];
  }

  public function submit_staff_item_collection_by_facility($input) {
    $sales_year_current= $this->db->where('is_active', 1)->get('procurement_sales_year')->row();
    // echo '<pre>'; print_r($input); die(); 

    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $logged_in_staff_id)
        ->get('staff_master')->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }


    $pias_id= $input['pias_id'];
    $already_returned_qty= $input['already_returned_qty'];
    $return_quantity= $input['return_quantity'];
    $proc_im_items_id= $input['proc_im_items_id'];
    $proc_invoice_items_id= $input['proc_invoice_items_id'];
    $return_reason= $input['return_reason'];

    // $select_items= $this->db->select("pii.id, pii.current_quantity")
    //     ->from('procurement_itemmaster_items pii')
    //     ->join('procurement_request_items pri', 'pri.proc_im_items_id= pii.id')
    //     ->where('pri.proc_request_master_id', $input["req_master_id_for_return"])
    //     ->order_by('pri.id', 'asc')
    //     ->get()->result();
$this->db->trans_start();
$status= false;
    foreach($return_quantity as $key => $val) {
      

      $inv_curr_quat= $this->db->select('current_quantity')->where('id', $proc_invoice_items_id[$key])->get('procurement_delivery_challan_items')->row()->current_quantity;
      $already_returned= 0;
      if($val != 0) {
        // Get the highest quantity which can be collected
        $collectable_quantity= $this->db->select('allocated_quantity, ifnull(collected_quantity, 0) as collected_quantity')->where('id', $pias_id[$key])->get('procurement_item_allocations_staff')->row();
        if(!empty($collectable_quantity)) {
          $already_returned= 1*$collectable_quantity->collected_quantity;
          $max_collectable= 1*$collectable_quantity->allocated_quantity - 1*$collectable_quantity->collected_quantity;
          if($val > $max_collectable) { // Need to restrct to collect again, because already collected all quantity
            continue;
          }
        }
        $status= true;

        if($input["is_stockable_$key"] != '3') {
// To avoid confuson about, if it is going to collect from the last sales year's allocated and closed also
          $this_inv= $this->db->where('id', $proc_invoice_items_id[$key])->get('procurement_delivery_challan_items')->row();
          $this_invMaster= $this->db->select('pim.*')->where('pii.id', $proc_invoice_items_id[$key])->from('procurement_delivery_challan_items pii')->join('procurement_delivery_challan_master pim', 'pim.id = pii.invoice_master_id')->get()->row();
        
          $this_invMaster->invoice_no= "NormalCollect$this_invMaster->invoice_no";
          $this_invMaster->bill_no= "NormalCollect$this_invMaster->bill_no";
          $this_invMaster->dc_type= "Normal Collect";
          $this_invMaster->total_amount= 0;
          if(!empty($sales_year_current)) {
            $this_invMaster->sales_year_id= $sales_year_current->id;
          }
          unset($this_invMaster->id);
          $this->db->insert('procurement_delivery_challan_master', $this_invMaster);
          $master_id= $this->db->insert_id();
          unset($this_inv->id);
          $this_inv->invoice_master_id= $master_id;
          $this_inv->total_amount= 0;
          $this_inv->initial_quantity= 0;
          $this_inv->current_quantity= $val;
          $this_inv->is_closed= 1;
          $this_inv->previous_sales_year_closed_invoice_id= NULL;
          $this->db->insert('procurement_delivery_challan_items', $this_inv);
        }
        $insert_data[]= array(
          'item_allocations_staff_id' => $pias_id[$key],
          'returned_quantity' => $val,
          'collected_by_id' => $this->authorization->getAvatarStakeHolderId(),
          'return_reason' => $return_reason[$key],
          'return_type' => $input["is_stockable_$key"],
          'proc_request_master_id' => $input["req_master_id_for_return"],
          'sales_year_id' => !empty($sales_year_current) ? $sales_year_current->id : 1
        );

        $update_data= array(
          'collected_quantity' => $already_returned + $return_quantity[$key]
        );
        $this->db->where('id', $pias_id[$key])->update('procurement_item_allocations_staff', $update_data);
      }
      
    }


    // echo '<pre>'; print_r($insert_data); die();

      if(isset($insert_data) && !empty($insert_data) && $status) {
        $this->db->insert_batch('procurement_item_collections_staff', $insert_data);
        // History insert
        $currentSalesYear= $this->get_current_sales_year_data();
        $sales_year_id= $currentSalesYear->id;
        $history_insert= array(
          'source_type' => 'Collection',
          'source_id' => NULL,
          'sales_year_id' => $sales_year_id,
          'action_type' => 'Item Collected',
          'action_description' => "A staff, $staff_name from facility department, has collected some items which iwas associated to another staff.",
          'action_by' => $this->authorization->getAvatarStakeHolderId()
        );
        $this->db->insert('procurement_itemmaster_history', $history_insert);
      }




    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function activate_deactivate_item($inputs) {
    $status= $this->db->where('id', $inputs['item_id'])->update('procurement_itemmaster_items', ['status' => $inputs['status']]);
    $item= $this->db->select("item_name")->where('id', $inputs['item_id'])->get('procurement_itemmaster_items')->row();
    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $logged_in_staff_id)
        ->get('staff_master')->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }
    // History insert
      $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Item',
        'source_id' => $inputs['item_id'],
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Item Status Updated',
        'action_description' => "An item, $item->item_name, has been ".($inputs['status'] == '1' ? 'activated' : 'deactivated')." by $staff_name",
        'action_by' => $logged_in_staff_id
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);
      return $status;
  }

  public function open_all_item_in_popup($sub_category_id) {
    $result= $this->db_readonly->where('proc_im_subcategory_id', $sub_category_id)->get('procurement_itemmaster_items')->result();
    if(!empty($result)) {
      foreach($result as $key => $val) {
        $val->is_used= '-1';
        $is_sailed= $this->db_readonly->where('proc_im_items_id', $val->id)->get('procurement_sales_transactions')->result();
        if(!empty($is_sailed)) {
        $val->is_used= '1';
        }
        $is_allocated= $this->db_readonly->where('proc_im_items_id', $val->id)->get('procurement_item_allocations_staff')->result();
        if(!empty($is_allocated)) {
        $val->is_used= '1';
        }
      }
    }

    return $result;
  }

  public function change_initial_quantity_of_item($inputs) {
    $column= $inputs['column'];
    return $this->db->where('id', $inputs['item_id'])->update('procurement_itemmaster_items', ["$column" => $inputs['changed_initial_quantity']]);
  }

  public function update_price_and_sales_transaction($input) {
    
    $this->db->trans_start();
      $this->db->where('id', $input['item_id'])->update('procurement_itemmaster_items', ['selling_price' => $input['price'], 'cost_prodcut' => $input['price']]);
      if($input['is_used'] == 1) {
        $sales_master_ids= $this->db->select("distinct(sales_master_id) as sales_master_id")->where('proc_im_items_id', $input['item_id'])->get('procurement_sales_transactions')->result();
        if(!empty($sales_master_ids)) {
          foreach($sales_master_ids as $key => $sales_master_id) {
            $transactions= $this->db->select("id as tx_id, proc_im_items_id, quantity, amount")->where('sales_master_id', $sales_master_id->sales_master_id)->get('procurement_sales_transactions')->result();
            if(!empty($transactions)) {
              $total_amt= 0;
              foreach($transactions as $k => $tx_details) {
                if($tx_details->proc_im_items_id != $input['item_id']) {
                  $total_amt += $tx_details->amount;
                } else {
                  $total_amt += $tx_details->quantity * $input['price'];
                  $update_tx_arr[]= array(
                    'id'=> $tx_details->tx_id,
                    'amount' => $tx_details->quantity * $input['price']
                  );
                  // $this->db->where('id', $tx_details->tx_id)->update('procurement_sales_transactions', ['amount' => $tx_details->quantity * $input['price']]);
                }
              }
              $update_sales_master_arr[]= array(
                'id'=> $sales_master_id->sales_master_id,
                'total_amount' => $total_amt
              );
              // $this->db->where('id', $sales_master_id->sales_master_id)->update('procurement_sales_master', ['total_amount' => $total_amt]);
            }
          }
// Transactions for both the table
          $this->db->update_batch('procurement_sales_master', $update_sales_master_arr, 'id');
          $this->db->update_batch('procurement_sales_transactions', $update_tx_arr, 'id');

        }
      }
    $this->db->trans_complete();

    if(!$this->db->trans_status()) {
      $this->db->trans_rollback();
      return $this->db->trans_status();
    }
    return $this->db->trans_status();

    
  }

  public function calculater_current_quantity_for_each_item() {
    $all_items= $this->db->select('id')->get('procurement_itemmaster_items')->result();
    if(!empty($all_items)) {
      foreach($all_items as $ikey => $item_id) {

// Initial transaction status
        $added = $this->db->select("pv.initial_quantity as quantity")
        ->from('procurement_itemmaster_items pv')
        ->where('pv.id', $item_id->id)
        ->get()->result();

        $Initial = 0;
        if(!empty($added)) {
          foreach ($added as $key => $val) {
            $Initial += $val->quantity;
          }
        }

// Purchased transaction status
          $invoices = $this->db->select("item.initial_quantity")
          ->where('item.proc_im_items_id', $item_id->id)
          ->get('procurement_delivery_challan_items item')->result();

          $Purchased = 0;
          if(!empty($invoices)) {
          foreach ($invoices as $key => $val) {
          $Purchased += $val->quantity;
          }
          }

// Allocated to staff transaction status
            $allocated = $this->db->select("vs.allocated_quantity as quantity")
            ->where('vs.proc_im_items_id', $item_id->id)
            ->get('procurement_item_allocations_staff vs')->result();

            $allocatedTotal = 0;
            if(!empty($allocated)) {
            foreach ($allocated as $key => $val) {
            $allocatedTotal += $val->quantity;
            }
            }

// Collected from staff transaction status
              $collected = $this->db->select("pics.returned_quantity as quantity, pics.return_type as is_stockable")
              ->from('procurement_item_collections_staff pics')
              ->join('procurement_item_allocations_staff pias','pics.item_allocations_staff_id=pias.id')
              ->where('pias.proc_im_items_id', $item_id->id)
              ->get()->result();

              $collectedTotal = 0;
              if(!empty($collected)) {
              foreach ($collected as $key => $val) {
              if($val->is_stockable == '1') {
              $collectedTotal += $val->quantity;
              } else if($val->is_stockable == '2') {
              $collectedTotal += $val->quantity;
              }
              }
              }

// Sold to student transaction status
              $sold = $this->db->select("pst.quantity")
                        ->where('pst.proc_im_items_id', $item_id->id)
                        ->get('procurement_sales_transactions pst')->result();

              $soldTotal = 0;
              if(!empty($sold)) {
                foreach ($sold as $key => $val) {
                  $soldTotal += $val->quantity;
                }
              }

// Item returned transaction status
              $return= $this->db->select("return_quantity as quantity")
              ->where('proc_im_items_id', $item_id->id)
              ->get('procurement_sales_return')->result();

              $returnedTotal = 0;
              if(!empty($return)) {
                foreach ($return as $key => $val) {
                  $returnedTotal += $val->quantity;
                }
              }

// Soft deleted receipt's item status
              $soft_deletes= $this->db->select("pst.quantity as quantity")
              ->from('procurement_sales_transactions pst')
              ->join('procurement_sales_master psm', 'psm.id= pst.sales_master_id')
              ->where('pst.proc_im_items_id', $item_id->id)
              ->where('psm.soft_delete', 1)
              ->get()->result();

              $softDeletedItems = 0;
              if(!empty($soft_deletes)) {
                foreach ($soft_deletes as $key => $val) {
                  $softDeletedItems += $val->quantity;
                }
              }
// End of getting statuses

              $calculated_cur_qty= intval($Initial) + intval($Purchased) - intval($allocatedTotal) + intval($collectedTotal) - intval($soldTotal) +intval($returnedTotal) + intval($softDeletedItems);

              $update_current_quantity[]= array(
                'id' => $item_id->id,
                'current_quantity' => $calculated_cur_qty
              );

      }

      // echo '<pre>'; print_r($update_current_quantity); die(); 
      $this->db->trans_start();
      $this->db->update_batch('procurement_itemmaster_items', $update_current_quantity, 'id');
      $this->db->trans_complete();
      if(!$this->db->trans_status()) {
        $this->db->trans_rollback();
      }
      return $this->db->trans_status();
    }
    return false;
  }

//   public function get_stock_report() {
//     $input= $this->input->post();
//     $category_id= isset($input['category_id']) ? $input['category_id'] : 0;
//     $sub_category_id= isset($input['sub_category_id']) ? $input['sub_category_id'] : 0;
//     // echo '<pre>'; print_r($category_id); die(); 

//     $this->db_readonly->select("pii.id as id, pii.item_name, pis.subcategory_name, pic.category_name")
//       ->from('procurement_itemmaster_category pic')
//       ->join('procurement_itemmaster_subcategory pis', 'pis.proc_im_category_id = pic.id')
//       ->join('procurement_itemmaster_items pii', 'pii.proc_im_subcategory_id = pis.id');
//     if($category_id != 0) {
//       $this->db_readonly->where('pic.id', $category_id);
//     }
//     if($sub_category_id != 0) {
//       $this->db_readonly->where('pis.id', $sub_category_id);
//     }
//     $this->db_readonly->order_by('pic.id, pis.id, pii.id');
//     $all_items= $this->db_readonly->get()->result();

//     // echo '<pre>'; print_r($this->db_readonly->last_query($all_items)); die();


//     if(!empty($all_items)) {
//       foreach($all_items as $ikey => $item_id) {

// // Initial transaction status
//         // $added = $this->db_readonly->select("pv.initial_quantity as quantity")
//         // ->from('procurement_itemmaster_items pv')
//         // ->where('pv.id', $item_id->id)
//         // ->get()->result();

//         // $Initial = 0;
//         // if(!empty($added)) {
//         //   foreach ($added as $key => $val) {
//         //     $Initial += $val->quantity;
//         //   }
//         // }

// // Purchased transaction status
//           $invoices = $this->db_readonly->select("item.initial_quantity quantity, item.price, item.cgst, item.sgst")
//           ->join('procurement_delivery_challan_master pim', 'pim.id= item.invoice_master_id')
//           ->where('item.proc_im_items_id', $item_id->id)
//           ->where('pim.sales_year_id', $input['sales_year_id'])
//           ->where_in('pim.dc_type', ['Opening Balance', 'Normal Purchase'])
//           ->get('procurement_delivery_challan_items item')->result();

//           $Purchased = 0;
//           $totalCost = 0;
//           $isInvoicesAdded= false;
//           if(!empty($invoices)) {
//             $isInvoicesAdded= true;
//             foreach ($invoices as $key => $val) {
//               $Purchased += $val->quantity;
//               $totalCost += $val->quantity * ($val->price + $val->cgst + $val->sgst);
//             }
//           }

//           $ob_stock = $this->db_readonly->select("item.current_quantity as ob_stock, item.selling_price")
//           ->join('procurement_delivery_challan_master pim', 'pim.id= item.invoice_master_id')
//           ->where('item.proc_im_items_id', $item_id->id)
//           ->where('pim.sales_year_id', $input['sales_year_id'])
//           ->where_in('pim.dc_type', ['Opening Balance'])
//           ->group_by('item.proc_im_items_id')
//           ->get('procurement_delivery_challan_items item')->row();

//           $obStock = 0;
//           $obSelling_price = 0;
//           if(!empty($ob_stock)) {
//             $obStock = $ob_stock->ob_stock;
//             $obSelling_price = $ob_stock->selling_price;
//           }

//           $new_stock = $this->db_readonly->select("sum(item.current_quantity) as new_stock, item.selling_price")
//           ->join('procurement_delivery_challan_master pim', 'pim.id= item.invoice_master_id')
//           ->where('item.proc_im_items_id', $item_id->id)
//           ->where('pim.sales_year_id', $input['sales_year_id'])
//           ->where_in('pim.dc_type', ['Normal Purchase'])
//           ->group_by('item.proc_im_items_id')
//           ->get('procurement_delivery_challan_items item')->row();

//           $newStock = 0;
//           $new_selling_price = 0;
//           if(!empty($new_stock)) {
//             $newStock = $new_stock->new_stock;
//             $new_selling_price = $new_stock->selling_price;
//           }

         

// // Allocated to staff transaction status
//             $allocated = $this->db_readonly->select("vs.allocated_quantity as quantity")
//             ->where('vs.proc_im_items_id', $item_id->id)
//             ->where('vs.sales_year_id', $input['sales_year_id'])
//             ->get('procurement_item_allocations_staff vs')->result();

//             $allocatedTotal = 0;
//               if(!empty($allocated)) {
//               foreach ($allocated as $key => $val) {
//               $allocatedTotal += $val->quantity;
//               }
//             }

// // Collected from staff transaction status
//               $collected = $this->db_readonly->select("pics.returned_quantity as quantity, pics.return_type as is_stockable")
//               ->from('procurement_item_collections_staff pics')
//               ->join('procurement_item_allocations_staff pias','pics.item_allocations_staff_id=pias.id')
//               ->where('pics.sales_year_id', $input['sales_year_id'])
//               ->where('pias.proc_im_items_id', $item_id->id)
//               ->get()->result();

//               $collectedTotal = 0;
//               $damaged= 0;
//               $not_stockaable= 0;
//               if(!empty($collected)) {
//               foreach ($collected as $key => $val) {
//               if($val->is_stockable == '1') {
//               $collectedTotal += $val->quantity;
//               } else if($val->is_stockable == '2') {
//               $collectedTotal += $val->quantity;
//               $damaged ++;
//               } else {
//                 $not_stockaable ++;
//               }
//               }
//               }

// // Sold to student transaction status
//               $sold = $this->db_readonly->select("pst.quantity")
//                         ->where('pst.proc_im_items_id', $item_id->id)
//                         ->where('pst.sales_year_id', $input['sales_year_id'])
//                         ->get('procurement_sales_transactions pst')->result();

//               $soldTotal = 0;
//               if(!empty($sold)) {
//                 foreach ($sold as $key => $val) {
//                   $soldTotal += $val->quantity;
//                 }
//               }

// // Sold to student transaction which is stoked by returned item status (If returned items also get sold)
//               $returnedStokeSold = $this->db_readonly->select("pst.quantity")
//                         ->join('procurement_delivery_challan_items pii', 'pii.id = pst.proc_invoice_items_id')
//                         ->join('procurement_delivery_challan_master pim', 'pim.id = pii.invoice_master_id')
//                         ->where('pst.proc_im_items_id', $item_id->id)
//                         ->where('pst.sales_year_id', $input['sales_year_id'])
//                         ->where_in('pim.dc_type', ['Normal Delete', 'Normal Return'])
//                         ->get('procurement_sales_transactions pst')->result();

//               $returnedStokeSoldQty = 0;
//               if(!empty($returnedStokeSold)) {
//                 foreach ($returnedStokeSold as $key => $val) {
//                   $returnedStokeSoldQty += $val->quantity;
//                 }
//               }
//       // $soldTotal = $soldTotal - $returnedStokeSoldQty;

// // Item returned transaction status
//               $return= $this->db_readonly->select("psr.return_quantity as quantity")
//               // ->join('procurement_sales_master psm', 'psm.id = psr.proc_sales_master_id')
//               // ->join('procurement_sales_transactions pst', 'pst.sales_master_id = psm.id')
//               ->where('psr.proc_im_items_id', $item_id->id)
//               ->where('psr.sales_year_id', $input['sales_year_id'])
//               // ->where('pst.sales_year_id', $input['sales_year_id']) // returned item should be purhased this sales year
//               ->get('procurement_sales_return psr')->result();

//               $returnedTotal = 0;
//               if(!empty($return)) {
//                 foreach ($return as $key => $val) {
//                   $returnedTotal += $val->quantity;
//                 }
//               }

//               // echo '<pre>'; print_r($returnedTotal);

// // Soft deleted / cancelled receipt's item status
//               $soft_deletes= $this->db_readonly->select("pst.quantity as quantity")
//               ->from('procurement_sales_transactions pst')
//               ->join('procurement_sales_master psm', 'psm.id= pst.sales_master_id')
//               ->where('pst.sales_year_id', $input['sales_year_id'])
//               ->where('pst.proc_im_items_id', $item_id->id)
//               ->where('psm.soft_delete', 1)
//               ->get()->result();

//               $softDeletedItems = 0;
//               if(!empty($soft_deletes)) {
//                 foreach ($soft_deletes as $key => $val) {
//                   $softDeletedItems += $val->quantity;
//                 }
//               }


// // End of getting statuses
//                                   // intval($Initial) +
//               $calculated_cur_qty= intval($Purchased) - intval($allocatedTotal) + intval($collectedTotal) - intval($soldTotal) +intval($returnedTotal) + intval($softDeletedItems) + intval($returnedStokeSoldQty);

//               $item_id->total_quantity= intval($Purchased);
//               $item_id->allocatedTotal= intval($allocatedTotal);
//               $item_id->collectedTotal= intval($collectedTotal);
//               $item_id->soldTotal= intval($soldTotal);
//               $item_id->returnedTotal= intval($returnedTotal);
//               $item_id->softDeletedItems= intval($softDeletedItems);
//               $item_id->damaged= intval($damaged);
//               $item_id->not_stockaable= intval($not_stockaable);
//               // $item_id->pending_allocation= intval($pendingAllocation);
//               $item_id->stock= intval($calculated_cur_qty);
//               $item_id->newstock= intval($newStock);
//               $item_id->obstock= intval($obStock);
//               $item_id->newstock_price = $new_selling_price;
//               $item_id->obstock_price = $obSelling_price;
//               $item_id->returnedStokeSoldQty = $returnedStokeSoldQty;
//               $item_id->totalCost = number_format($totalCost, 0);

//               if(!$isInvoicesAdded) {
//                 unset($all_items[$ikey]);
//               }
//       }

//       // die();

//       return $all_items;
//     }
//     return [];

//   }

  public function get_subcategories() {
    return $this->db_readonly->select("id, subcategory_name")->where('proc_im_category_id', $_POST['category_id'])->get('procurement_itemmaster_subcategory')->result();
  }

  public function get_sales_year() {
    return $this->db_readonly->get('procurement_sales_year')->result();
  }

  public function update_price_qty_in_invoices() { // We have disable this button permanently, no need to story history
    $qtyPrev= $this->db->select("id, initial_quantity, current_quantity")->where('proc_im_items_id', $_POST['item_id'])->get('procurement_delivery_challan_items')->row();
    if(!empty($qtyPrev)) {
      $upd_curr_qty= intval($_POST['invQuantity']) + intval($qtyPrev->current_quantity);
      $upd_ini_qty= intval($_POST['invQuantity']) + intval($qtyPrev->initial_quantity);
      return $this->db->where('id', $qtyPrev->id)->update('procurement_delivery_challan_items', ['initial_quantity' => $upd_ini_qty, 'current_quantity' => $upd_curr_qty, 'selling_price' => $_POST['invItemPrice']]);
    }
    return false;
  }

  public function validate_sku_asUnique() {
    $sk= $this->input->post('sku');
    $sku= strtolower($sk);
    $existence= $this->db->where('lower(sku_code)', $sku)->get('procurement_itemmaster_items')->row();
    if(!empty($existence)) {
      return '1'; // if exist
    }
    return '-1';
  }

  // Not using the fn for reconciliate
//   public function easy_update_quantity_super_admin() {
//     $allInvoices= $this->db->select('pii.id, pii.proc_im_items_id, pii.initial_quantity, pii.current_quantity, ifnull(sum(pst.quantity), 0) as sold, pii.proc_im_subcategory_id, pii.proc_im_category_id, pii.price, pii.cgst, pii.sgst, pii.selling_price')
//         ->from('procurement_delivery_challan_master pim')
//         ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
//         ->join('procurement_sales_transactions pst', 'pst.proc_invoice_items_id = pii.id')
//         ->where('pim.sales_year_id', 2)
//         ->order_by('pii.proc_im_items_id', 'asc')
//         ->group_by('pii.id')
//         ->get()->result();

//     if(!empty($allInvoices)) {
//       $invoiceItems_need_to_change_invItemsId_and_addDelivery= [];
//       // Step 1
//       foreach($allInvoices as $key =>$val) {



// // 1 scenario: sold is more than initial but current quantity is >= (sold - initial)
//         $diff= $val->sold - $val->initial_quantity;
//         if($val->initial_quantity != 0 && $val->sold > 0 && $diff > 0 && $val->current_quantity >= $val->sold) {
//           $update_query_invoiceItems[]= array(
//             'id' => $val->id,
//             'current_quantity' => intval($val->current_quantity) - intval($diff)
//           );
//         }
// // 2 scenario: sold is more than initial but current quantity is greather than zero and less than (sold - initial)
//         if($val->initial_quantity != 0 && $val->sold > 0 && $diff > 0 && $val->current_quantity > 0 && $val->current_quantity < $val->sold) {
//           $diff= $diff - $val->current_quantity;
//           $update_query_invoiceItems[]= array(
//             'id' => $val->id,
//             'current_quantity' => 0
//           );
//           $invoiceItems_need_to_change_invItemsId_and_addDelivery[$val->id] = array( // Getting items_id for step 2 processing
//             'proc_im_items_id' => $val->proc_im_items_id,
//             'diff' => $diff,
//             'proc_im_subcategory_id' => $val->proc_im_subcategory_id,
//             'proc_im_category_id' => $val->proc_im_category_id,
//             'selling_price' => $val->selling_price,
//             'price' => $val->price,
//             'cgst' => $val->cgst,
//             'sgst' => $val->sgst
//           );
//         }
// // 3 scenario: sold is more than initial but current quantity is zero
//         if($val->initial_quantity != 0 && $val->sold > 0 && $diff > 0 && $val->current_quantity == 0) {
//             $invoiceItems_need_to_change_invItemsId_and_addDelivery[$val->id] = array(
//               'proc_im_items_id' => $val->proc_im_items_id,
//               'diff' => $diff
//             );
//         } 
// // 4 scenario: initial quantity is zero (Need to think about this)
//         else if($val->initial_quantity == 0) {

//         }



//       } // foreach closed
//       $this->db->trans_start();
//       $this->db->update_batch('procurement_delivery_challan_items', $update_query_invoiceItems, 'id');
//       $this->db->trans_complete();
//       // Step 2
//       if($this->db->trans_status() && !empty($invoiceItems_need_to_change_invItemsId_and_addDelivery)) {
//         foreach($invoiceItems_need_to_change_invItemsId_and_addDelivery as $key => $val) {
//           $oldInvItemsId= $key;
//           $proc_im_items_id= $val['proc_im_items_id'];
//           $diff= $val['diff'];

//           $execute_untill_get_false= true;
//           while($execute_untill_get_false) {
//             $checkDeliveryAvailable= $this->db->select("pii.id, pii.current_quantity")
//               ->from('procurement_delivery_challan_master pim')
//               ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
//               ->where('pim.sales_year_id', 2)
//               ->where('pii.proc_im_items_id', $proc_im_items_id)
//               ->where('pii.current_quantity >', 0)
//               ->get()->row();
// // Start: update quantity and update invItemsId from transaction
//             if(!empty($checkDeliveryAvailable)) {
//               $newInvItemsId= $checkDeliveryAvailable->id;
//               if($checkDeliveryAvailable->current_quantity >= $diff) {
//                 $update_query_invoiceItems[]= array(
//                   'id' => $newInvItemsId,
//                   'current_quantity' => intval($checkDeliveryAvailable->current_quantity) - intval($diff)
//                 );
//                 // Query to update invItemsId from transaction table (Need to do this), use $newInvItemsId to update in tx table

//                 // $diff = 0;
//                 // $execute_untill_get_false = false;
//               } else {

//               }
//             } else { // if diff not zero, then also it should get false so that a new delivery should be added
//               $execute_untill_get_false = false;
//             }
//           }
//           if($diff > 0) {
// // Create new delivery with initial= $diff and current= 0, and update this id somewhere in transaction (Need to do)

//             $invMaster= array(
//               'vendor_id' => 1,
//               'sales_year_id' => 2,
//               'dc_type' => 'Normal Purchase'
//             );
//             $this->db->insert('procurement_delivery_challan_master', $invMaster);
//             $master_id= $this->db->insert_id();
//             $invItems= array(
//               'invoice_master_id' => $master_id,
//               'proc_im_items_id' => $val['proc_im_items_id'],
//               'proc_im_subcategory_id' => $val['proc_im_subcategory_id'],
//               'proc_im_category_id' => $val['proc_im_category_id'],
//               'price' => $val['price'],
//               'cgst' => $val['cgst'],
//               'sgst' => $val['sgst'],
//               'selling_price' => $val['selling_price'],
//               'initial_quantity' => $diff,
//               'current_quantity' => 0, // zero means is all are sold
//               'is_closed' => 1,
//             );
//             $this->db->insert('procurement_delivery_challan_items', $invItems);
//             $invItemsId_need_to_update_in_transaction_table= $this->db->insert_id();
//           // Query to update invItemsId from transaction table (Need to do this)

//           }
          
//         }
//       }
//     } // if empty closed


//     echo '<pre>'; print_r($allInvoices); die();
//   }

  function salesYear_wise_items() {
    $sales_year_id = $_POST['sales_year_id'];

        $uniqueItem= $this->db_readonly->query("select distinct(pii.proc_im_items_id) as proc_im_items_id from procurement_delivery_challan_items pii
            join procurement_delivery_challan_master pim on pim.id = pii.invoice_master_id
            where pim.sales_year_id = '$sales_year_id'")->result();
        if(!empty($uniqueItem)) {
          foreach($uniqueItem as $key =>$val) {
            $proc_im_items_id_arr[]= $val->proc_im_items_id;
          }
        }
        $allInvoices= [];
        if(!empty($proc_im_items_id_arr)) {
          $allInvoices= $this->db_readonly->select("item_name, id as proc_im_items_id, '$sales_year_id' as sales_year_id, status_sales_year_1, status_sales_year_2")
            ->where_in('id', $proc_im_items_id_arr)
            ->order_by('item_name')
            ->get('procurement_itemmaster_items')->result();
        }

        // echo '<pre>'; print_r($uniqueItem); die();

    return $allInvoices;
  }

  public function get_tx_details_of_item() {
    $input= $this->input->post();
    $invDetails= $this->db_readonly->select("pii.id, pii.selling_price, pii.proc_im_items_id, pim.sales_year_id, pii.initial_quantity, pii.current_quantity, pii.is_closed, pim.dc_type")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pim.sales_year_id', $input['sales_year_id'])
      ->where('pii.proc_im_items_id', $input['item_id'])
      ->order_by('pii.id', 'asc')
      ->get()->result();

    $txDetails= $this->db_readonly->select('id, sales_master_id, proc_im_items_id, proc_invoice_items_id, quantity, sales_year_id')
      ->where('sales_year_id', $input['sales_year_id'])
      ->where('proc_im_items_id', $input['item_id'])
      ->order_by('proc_invoice_items_id', 'asc')
      ->get('procurement_sales_transactions')->result();
    if(!empty($txDetails)) {
      // foreach($txDetails as $key => $val) {
      //   $isTxAddedInNewRow= $this->db_readonly->select('id')->where('sales_year_id', $val->sales_year_id)->where('proc_im_items_id', $val->proc_im_items_id)->where('sales_master_id', $val->sales_master_id)->where('id !=', $val->id)->get('procurement_sales_transactions')->result();
      //   if(!empty($isTxAddedInNewRow)) {
      //     $tx_ids_str= $val->id;
      //     foreach($isTxAddedInNewRow as $k11 => $v11) {
      //       $tx_ids_str .= ', '. $v11->id;
      //     }
      //     $val->id= $tx_ids_str;
      //   }
      // }
      $isTxMade= [];
    } else {
      $isTxMade= $this->db_readonly->select("status_sales_year_1, status_sales_year_2")->where('id', $input['item_id'])->get('procurement_itemmaster_items')->row();
    }

    $sold_invItemsWise= $this->db_readonly->select("sum(quantity) as sold, proc_invoice_items_id")->where('sales_year_id', $input['sales_year_id'])->where('proc_im_items_id', $input['item_id'])->group_by('proc_invoice_items_id')->get('procurement_sales_transactions')->result();

    $initial= $this->db->select("sum(pii.initial_quantity) as initial")
      ->from('procurement_delivery_challan_master psm')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = psm.id')
      ->where('psm.sales_year_id', $input['sales_year_id'])
      ->where('pii.proc_im_items_id', $input['item_id'])
      ->group_by('pii.proc_im_items_id')
      ->get()->row();

      $sold= $this->db->select("sum(quantity) as sold")
      ->where('sales_year_id', $input['sales_year_id'])
      ->where('proc_im_items_id', $input['item_id'])
      ->group_by('proc_im_items_id')
      ->get('procurement_sales_transactions')->row();
      
      $sold_and_initial_validating= [];
      if(!empty($initial)) {
        $sold_and_initial_validating['initial']= $initial->initial;
      } else {
        $sold_and_initial_validating['initial']= 0;
      }
      if(!empty($sold)) {
        $sold_and_initial_validating['sold']= $sold->sold;
      } else {
        $sold_and_initial_validating['sold']= 0;
      }




    return ['invDetails' => $invDetails, 'txDetails' => $txDetails, 'sold_invItemsWise' => $sold_invItemsWise, 'sold_and_initial_validating' => $sold_and_initial_validating, 'isTxMade' => $isTxMade];
  }

  function finish_reconciling() {
    $input= $this->input->post();
    $item_id= $input['item_id'];
    $sales_year_id= $input['sales_year_id'];
    if($sales_year_id == 1) {
      $TotalInf= $this->db->select("sum(pst.quantity), sum(pii.initial_quantity), sum(pii.current_quantity)")
        ->from('procurement_delivery_challan_master pim')
        ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
        ->join('procurement_sales_transactions pst', 'pst.proc_invoice_items_id = pii.id')
        ->where('pim.sales_year_id', $sales_year_id)
        ->where('pii.proc_im_items_id', $item_id)
        ->group_by('pii.id')
        ->get()->row();

      if(!empty($TotalInf)) {
        $sold= $TotalInf->quantity;
        $initial_quantity= $TotalInf->initial_quantity;
        $current_quantity= $TotalInf->current_quantity;
        if((intval($sold) + intval($current_quantity)) != intval($initial_quantity)) {
          $getInvDetail= $this->db->select('pii.id, pii.is_closed')
            ->from('procurement_delivery_challan_master pim')
            ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
            ->where('pim.sales_year_id', $sales_year_id)
            ->where('pii.proc_im_items_id', $item_id)
            ->get()->row();
          if(!empty($getInvDetail) && $getInvDetail->is_closed == 2) {
            // $this->db->where()->update();
          }
        }
      }
    } else {

    }
  }

  function finish_allocation_first() {
    $input= $this->input->post();
    $item_id= $input['item_id'];
    $sales_year_id= $input['sales_year_id'];

    $invoices= $this->db->select("pii.id, pii.proc_im_subcategory_id, pii.proc_im_category_id, pii.price, pii.cgst, pii.sgst, pii.selling_price, pii.initial_quantity, pii.current_quantity, pii.is_closed, pim.dc_type")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('sales_year_id', $sales_year_id)
      ->order_by('pii.id', 'asc')
      ->get()->result();

      // echo '<pre>'; print_r($invoices); die();

    $update_invoices= [];

    $invoices_arr= [0];
    if(!empty($invoices)) {
      foreach($invoices as $key => $val) {
        $val->temp_sold= 0;
        array_push($invoices_arr, $val->id);
        
        if($val->dc_type == 'Opening Balance' || $val->dc_type == 'Normal Purchase') { // Collect return items from this invoice can not change the current quantity because it is already sold OR no source update
          $val->current_quantity= $val->initial_quantity;
          $update_invoices[]= array(
            'id' => $val->id,
            'current_quantity' => $val->initial_quantity
          );
        } 
        // else {
        //   $update_invoices[]= array(
        //     'id' => $val->id,
        //     'initial_quantity' => $val->current_quantity
        //   );
        // }
      }
    }

    if(!empty($update_invoices)) {
      $this->db->update_batch('procurement_delivery_challan_items', $update_invoices, 'id');
    }

    // echo '<pre>'; print_r($update_invoices); die();
    // return 1;

    $transactions= $this->db->select("id, proc_invoice_items_id, quantity, amount, sales_master_id, proc_im_subcategory_id")
      ->where_in('proc_invoice_items_id', $invoices_arr)
      ->where_in('proc_im_items_id', $item_id)
      ->order_by('proc_invoice_items_id', 'asc')
      ->get('procurement_sales_transactions')->result();
      
    if(!empty($transactions)) {
      
      $update_tx_invoice_itemsId= [];
      $insert_tx_invoice_itemsId= [];
      foreach($transactions as $key => $val) {
        $update_or_insert= 'update';
        $insert_preset_variable= [];
        for($i = 0; $i< count($invoices); $i ++) {

          if($update_or_insert == 'update') {
            if($invoices[$i]->current_quantity >= $val->quantity) {
              $update_tx_invoice_itemsId[]= array(
                'id' => $val->id,
                'proc_invoice_items_id' => $invoices[$i]->id,
                'quantity' => $val->quantity,
                'amount' => $val->amount
              );
              $invoices[$i]->current_quantity = intval($invoices[$i]->current_quantity) - intval($val->quantity); // updating curr from invoices
              $invoices[$i]->temp_sold = intval($invoices[$i]->temp_sold) + intval($val->quantity);
              $update_or_insert= 'update';
              break;
            } else if($invoices[$i]->current_quantity > 0 && $invoices[$i]->current_quantity < $val->quantity) {
              $amtPerItem= abs($val->amount) / intval($val->quantity);
              $update_tx_invoice_itemsId[]= array(
                'id' => $val->id,
                'proc_invoice_items_id' => $invoices[$i]->id,
                'quantity' => $invoices[$i]->current_quantity,
                'amount' => $amtPerItem * intval($invoices[$i]->current_quantity)
              );
              
              $insert_preset_variable= array(
                'sales_master_id' => $val->sales_master_id,
                'proc_im_items_id' => $item_id,
                'proc_im_subcategory_id' => $val->proc_im_subcategory_id,
                'proc_invoice_items_id' => 0,
                'sales_year_id' => $sales_year_id,
                'quantity' => intval($val->quantity) - intval($invoices[$i]->current_quantity),
                'amount' => $amtPerItem * (intval($val->quantity) - intval($invoices[$i]->current_quantity))
              );
              $invoices[$i]->temp_sold = intval($invoices[$i]->temp_sold) + intval($invoices[$i]->current_quantity);
              $invoices[$i]->current_quantity = 0;
              $update_or_insert= 'insert';
            }
          } else {
            // Insert things
            if($invoices[$i]->current_quantity >= $insert_preset_variable['quantity']) {
              $insert_preset_variable['proc_invoice_items_id']= $invoices[$i]->id;

              $insert_tx_invoice_itemsId[]= $insert_preset_variable;

              $invoices[$i]->current_quantity = intval($invoices[$i]->current_quantity) - intval($insert_preset_variable['quantity']); // updating curr from invoices
              $invoices[$i]->temp_sold = intval($invoices[$i]->temp_sold) + intval($insert_preset_variable['quantity']);
              $update_or_insert= 'update';
              break;
            } else if($invoices[$i]->current_quantity > 0 && $invoices[$i]->current_quantity < $insert_preset_variable['quantity']) {
              $amtPerItem= abs($insert_preset_variable['quantity']) / intval($val->quantity);
              $insert_preset_variable['proc_invoice_items_id']= $invoices[$i]->id;
              $insert_preset_variable['quantity']= $invoices[$i]->current_quantity;
              $insert_preset_variable['amount']= $amtPerItem * intval($invoices[$i]->current_quantity);

              $insert_tx_invoice_itemsId[]= $insert_preset_variable;
              
              $insert_preset_variable= array(
                'sales_master_id' => $val->sales_master_id,
                'proc_im_items_id' => $item_id,
                'proc_im_subcategory_id' => $val->proc_im_subcategory_id,
                'proc_invoice_items_id' => 0,
                'sales_year_id' => $sales_year_id,
                'quantity' => intval($insert_preset_variable['quantity']) - intval($invoices[$i]->current_quantity),
                'amount' => $amtPerItem * (intval($insert_preset_variable['quantity']) - intval($invoices[$i]->current_quantity))
              );
              $invoices[$i]->temp_sold = intval($invoices[$i]->temp_sold) + ( intval($insert_preset_variable['quantity']) - intval($invoices[$i]->current_quantity) );
              $invoices[$i]->current_quantity = 0;
              $update_or_insert= 'insert';
            }
          }

        }
        
      }

      $update_invoices= [];
      $update_closed_sales_year_items= [];

      if(!empty($invoices)) {
        foreach($invoices as $key => $val) {
          $update_invoices[]= array(
            'id' => $val->id,
            'current_quantity' => $val->is_closed == '1' ? $val->current_quantity : 0
          );
        }
      }

      // echo '<pre>'; print_r($update_invoices); die();

      // Update and insert
      if(!empty($update_tx_invoice_itemsId)) {
        $this->db->update_batch('procurement_sales_transactions', $update_tx_invoice_itemsId, 'id');
      } 
      if(!empty($insert_tx_invoice_itemsId)) {
        $this->db->insert_batch('procurement_sales_transactions', $insert_tx_invoice_itemsId);
      }
      
      if(!empty($update_invoices)) {
        $this->db->update_batch('procurement_delivery_challan_items', $update_invoices, 'id');
      }

      if(!empty($update_closed_sales_year_items)) {
        $this->db->update_batch('procurement_delivery_challan_items', $update_closed_sales_year_items, 'id');
      }

    }

    $ststus_manage_allocation_and_vendor_return= $this->__manage_allocation_and_vendor_return($item_id, $sales_year_id, $invoices_arr);

    if($sales_year_id == '1') {
      $this->db->where('id', $item_id)->update('procurement_itemmaster_items', ['status_sales_year_1' => 'Complete']);
    } if($sales_year_id == '2') {
      $this->db->where('id', $item_id)->update('procurement_itemmaster_items', ['status_sales_year_2' => 'Complete']);
    }

    return true;

  }



  
  
  
  
  
  
  
  // /////////////////////////////////////////////////////////////////////////////
  // /////////////////////////////////////////////////////////////////////////////
  // /////////////////////////////////////////////////////////////////////////////

  function __manage_allocation_and_vendor_return($item_id, $sales_year_id, $all_invoices_ids_arr) {
    array_shift($all_invoices_ids_arr);
    $invoices= $this->db->select("pii.id, pii.proc_im_subcategory_id, pii.proc_im_category_id, pii.price, pii.cgst, pii.sgst, pii.selling_price, pii.initial_quantity, pii.current_quantity, pii.is_closed")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where('pii.current_quantity > 0')
      ->order_by('pii.id', 'asc')
      ->get()->result();

    $invoices_obj= new stdClass();
    if(!empty($invoices)) {
      foreach($invoices as $key => $val) {
        $invoices_obj->{$val->id}= $val->current_quantity;
      }
    }

      // echo '<pre>'; print_r($invoices); die();

    // allocation
    $allocation= $this->db->select("id, proc_invoice_items_id, allocated_quantity")->where('proc_im_items_id', $item_id)->where_in('proc_invoice_items_id', $all_invoices_ids_arr)->get('procurement_item_allocations_staff')->result();
    // if(!empty($allocation)) {
    //   foreach($allocation as $key => $val) {

    //     $isSettled= false;
    //     $extraUsedQtyInPrevInvItem= 0;
    //     foreach($invoices_obj as $invItemId => $invQty) {
    //       if($invoices_obj->$invItemId >= $val->allocated_quantity) {
    //         $isSettled= true;
    //         $update_allocation[]= array(
    //           'id' => $val->id,
    //           'proc_invoice_items_id' => $invItemId
    //         );
    //         $invoices_obj->$invItemId = $invoices_obj->$invItemId - $val->allocated_quantity;
    //         break; // Here we are breaking the loop because we searching for the full allocated amount in the invoices
    //       }
    //     }
    //     if(!$isSettled) {
    //       foreach($invoices_obj as $invItemId => $invQty) {
    //         if($invoices_obj->$invItemId > 0) {
    //           $isSettled= true;
    //           $update_allocation[]= array(
    //             'id' => $val->id,
    //             'proc_invoice_items_id' => $invItemId,
    //             // 'allocated_quantity' => $invoices_obj->$invItemId
    //           );
    //           $invoices_obj->$invItemId = 0;
    //           $extraUsedQtyInPrevInvItem= $val->allocated_quantity - $invoices_obj->$invItemId;
    //           break; // Here we are breaking the loop because we searching for the full allocated amount in the invoices
    //         }
    //       }
    //     }

    //   }
    // }







    if(!empty($allocation)) {
      $notSubtractedQtyFromInvoices= 0;
      foreach($allocation as $key => $val) {
          $remainingAllocation = $val->allocated_quantity; // total need to allocated
          
          foreach($invoices_obj as $invItemId => $invQty) {
              if($remainingAllocation <= 0) {
                  break; // We've fully allocated this item
              }
              
              if($invoices_obj->$invItemId > 0) {
                  $allocatedQty = min($invoices_obj->$invItemId, $remainingAllocation);
                  
                  $update_allocation[] = array(
                      'id' => $val->id,
                      'proc_invoice_items_id' => $invItemId,
                      // 'allocated_quantity' => $allocatedQty // No need to update the lessor quantity in the allocation table instead we can subtract it from the invoice table later
                  );
                  
                  $invoices_obj->$invItemId -= $allocatedQty;
                  $remainingAllocation -= $allocatedQty;

                  $notSubtractedQtyFromInvoices += $remainingAllocation;
                  break;
              }
              // if($remainingAllocation <= 0) {
              //     break; // We've fully allocated this item
              // }
          }
    }
    if($notSubtractedQtyFromInvoices > 0) { // If there are any remaining allocations that couldn't be satisfied by the invoices then we have to settle that by following way
      foreach($invoices_obj as $invItemId => $invQty) {
        if($invoices_obj->$invItemId > 0) {
          if($invoices_obj->$invItemId >= $notSubtractedQtyFromInvoices) {
            $invoices_obj->$invItemId= $invoices_obj->$invItemId - $notSubtractedQtyFromInvoices;
          } else if($invoices_obj->$invItemId < $notSubtractedQtyFromInvoices) {
            $notSubtractedQtyFromInvoices= $notSubtractedQtyFromInvoices - $invoices_obj->$invItemId;
            $invoices_obj->$invItemId= 0;
          }
        }

        if($notSubtractedQtyFromInvoices == 0) {
          break; // We've fully allocated this item
        }
      }
    }
  }





    if(!empty($update_allocation)) {
      $this->db->update_batch('procurement_item_allocations_staff', $update_allocation, 'id');
    }

    // vendor return
    $vendor_return= $this->db->select("id, proc_invoice_items_id, return_quantity")->where_in('proc_invoice_items_id', $all_invoices_ids_arr)->get('procurement_delivery_challan_return_items')->result();
    // echo '<pre>'; print_r($vendor_return); die();
    // $allocation= $this->db->select("id, proc_invoice_items_id, allocated_quantity")->where('proc_im_items_id', $item_id)->where_in('proc_invoice_items_id', $all_invoices_ids_arr)->get('procurement_item_allocations_staff')->result();
    if(!empty($vendor_return)) {
      foreach($vendor_return as $key => $val) {

        $isSettled= false;
        foreach($invoices_obj as $invItemId => $invQty) {
          if($invoices_obj->$invItemId >= $val->return_quantity) {
            $isSettled= true;
            $update_vendor_return[]= array(
              'id' => $val->id,
              'proc_invoice_items_id' => $invItemId
            );
            $invoices_obj->$invItemId = $invoices_obj->$invItemId - $val->return_quantity;
            break;
          }
        }
        if(!$isSettled) {
          foreach($invoices_obj as $invItemId => $invQty) {
            if($invoices_obj->$invItemId > 0) {
              $isSettled= true;
              $update_vendor_return[]= array(
                'id' => $val->id,
                'proc_invoice_items_id' => $invItemId,
                'return_quantity' => $invoices_obj->$invItemId
              );
              $invoices_obj->$invItemId = 0;
              break;
            }
          }
        }

      }
    }
    if(!empty($update_vendor_return)) {
      $this->db->update_batch('procurement_delivery_challan_return_items', $update_vendor_return, 'id');
    }

    if(!empty($invoices_obj)) {
      foreach($invoices_obj as $key => $val) {
        $invoices_update[]= array(
          'id' => $key,
          'current_quantity' => $invoices_obj->$key
        );
      }
      if(!empty($invoices_update)) {
        $this->db->update_batch('procurement_delivery_challan_items', $invoices_update, 'id');
      }
    }
    
    return true;
  }

  // /////////////////////////////////////////////////////////////////////////////
  // /////////////////////////////////////////////////////////////////////////////
  // /////////////////////////////////////////////////////////////////////////////












  function close_reconcillation_item() {
    if($_POST['sales_year_id'] == '1') {
      return $this->db->where('id', $_POST['item_id'])->update('procurement_itemmaster_items', ['status_sales_year_1' => 'Complete']);
    } else if($_POST['sales_year_id'] == '2') {
      return $this->db->where('id', $_POST['item_id'])->update('procurement_itemmaster_items', ['status_sales_year_2' => 'Complete']);
    }
  }

  public function get_missmatch_data_entry_invoices() {
    $sales_year_id= 2;
    $uniqueItem= $this->db_readonly->query("select pii.proc_im_items_id, sum(pii.initial_quantity) as initial from procurement_delivery_challan_items pii
            join procurement_delivery_challan_master pim on pim.id = pii.invoice_master_id
            where pim.sales_year_id = '$sales_year_id' group by pii.proc_im_items_id")->result();

        $result= [];
        if(!empty($uniqueItem)) {
          foreach($uniqueItem as $key =>$val) {
            $sold= $this->db_readonly->select("sum(pst.quantity) as sold, '$sales_year_id' as sales_year_id, pii.item_name, pst.proc_invoice_items_id")
              ->from('procurement_sales_transactions pst')
              ->join('procurement_itemmaster_items pii', 'pii.id = pst.proc_im_items_id')
              ->where('pst.sales_year_id', $sales_year_id)
              ->where('pst.proc_im_items_id', $val->proc_im_items_id)
              ->get()->row();

            $returned= $this->db_readonly->select("sum(pst.quantity) as returned")
              ->from('procurement_delivery_challan_master pim')
              ->join('procurement_delivery_challan_items pii', 'pim.id = pii.invoice_master_id')
              ->join('procurement_sales_transactions pst', 'pii.id = pst.proc_invoice_items_id')
              ->where('pst.sales_year_id', $sales_year_id) // should be sold in this sales year
              ->where('pim.sales_year_id', $sales_year_id) // Can be purchased from any sales year but should be returned in this sales year
              ->where('pim.dc_type', 'Normal Return')
              ->where('pst.proc_im_items_id', $val->proc_im_items_id)
              ->get()->row();

            if(!empty($returned)) {
              $sold->sold= $sold->sold - $returned->returned;
            }

            if(!empty($sold)) {
              if($sold->sold > $val->initial) {
                $obj= new stdClass();
                $obj->item_name = $sold->item_name;
                $obj->sales_year_id = $sold->sales_year_id;
                $obj->sold = $sold->sold;
                $obj->proc_invoice_items_id = $sold->proc_invoice_items_id;
                $obj->initial = $val->initial;

                $result[]= $obj;
              }
            }
          }
        }

        echo '<pre>'; print_r($result); die();
  }

  public function get_threshold_report() {
    $input= $this->input->post();
    $category_id= isset($input['category_id']) ? $input['category_id'] : 0;
    $sub_category_id= isset($input['sub_category_id']) ? $input['sub_category_id'] : 0;
    $sales_year_id= $input['sales_year_id'];
    $below_threshold_only= $input['below_threshold_only'];

    $this->db_readonly->select("pii.id")
        ->from('procurement_delivery_challan_master pim')
        ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id');
    if($sales_year_id) {
      $this->db_readonly->where('pim.sales_year_id', $sales_year_id);
    }
    if($category_id && $category_id != 0) {
      $this->db_readonly->where('pii.proc_im_category_id', $category_id);
    }
    if($sub_category_id && $sub_category_id != 0) {
      $this->db_readonly->where('pii.proc_im_subcategory_id', $sub_category_id);
    }
    $items= $this->db_readonly->get()->result();
    $invoices_arr= [];
    $threshold= [];
    if(!empty($items)) {
      foreach($items as $key => $val) {
        $invoices_arr[]= $val->id;
      }

      $threshold= $this->db_readonly->select("pii.item_name, pis.subcategory_name, pic.category_name, pii.threshold_quantity, sum(pInvItems.current_quantity) as current, pii.sku_code")
        ->from('procurement_delivery_challan_items pInvItems')
        ->join('procurement_itemmaster_items pii', 'pii.id = pInvItems.proc_im_items_id')
        ->join('procurement_itemmaster_subcategory pis', 'pis.id = pii.proc_im_subcategory_id')
        ->join('procurement_itemmaster_category pic', 'pic.id = pis.proc_im_category_id')
        ->where_in('pInvItems.id', $invoices_arr)
        ->group_by('pInvItems.proc_im_items_id')
        ->order_by('pic.category_name, pis.subcategory_name, pii.item_name')
        ->get()->result();

      if($below_threshold_only == 'threshold_only' && !empty($threshold)) {
        foreach($threshold as $key => $val) {
          if($val->threshold_quantity < $val->current) {
            unset($threshold[$key]);
          }
        }
      }
    }
    return $threshold;
  }

  public function upload_category_image_path_byId($subCatId, $path){
    $this->db->where('id',$subCatId);
    $this->db->update('procurement_itemmaster_subcategory',array('picture_url'=>$path));

    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $logged_in_staff_id)
        ->get('staff_master')->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }

     $subc= $this->db->select("subcategory_name")->where('id', $subCatId)->get('procurement_itemmaster_subcategory')->row();
    // History insert
      $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Subcategory',
        'source_id' => $subCatId,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Image Uploaded',
        'action_description' => "A category image has been uploaded for subcategory: $subc->subcategory_name by $staff_name",
        'action_by' => $logged_in_staff_id
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);


    return true;
  }
  public function get_stock_report() {
    $input= $this->input->post();
    $category_id= isset($input['category_id']) ? $input['category_id'] : 0;
    $sub_category_id= isset($input['sub_category_id']) ? $input['sub_category_id'] : 0;
    $this->db_readonly->select("pii.id as id, pii.item_name, pis.subcategory_name, pic.category_name")
      ->from('procurement_itemmaster_category pic')
      ->join('procurement_itemmaster_subcategory pis', 'pis.proc_im_category_id = pic.id')
      ->join('procurement_itemmaster_items pii', 'pii.proc_im_subcategory_id = pis.id');
    if($category_id != 0) {
      $this->db_readonly->where('pic.id', $category_id);
    }
    if($sub_category_id != 0) {
      $this->db_readonly->where('pis.id', $sub_category_id);
    }
    $this->db_readonly->order_by('pic.id, pis.id, pii.id');
    $all_items= $this->db_readonly->get()->result();

    // foreach($all_items as $ikey => $item_id) {
    //   if($item_id->id != '1643') {
    //     unset($all_items[$ikey]);
    //   }
    // }
    if(!empty($all_items)) {
      foreach($all_items as $ikey => $item_id) {

    // OB transaction status
    $invoices = $this->db_readonly->select("item.initial_quantity quantity, item.price, ifnull(item.cgst, 0) as cgst, ifnull(item.sgst, 0) as sgst, ifnull(item.selling_price, 0) as selling_price, pim.dc_type")
    ->join('procurement_delivery_challan_master pim', 'pim.id= item.invoice_master_id')
    ->where('item.proc_im_items_id', $item_id->id)
    ->where('pim.sales_year_id', $input['sales_year_id'])
    ->where_in('pim.dc_type', ['Opening Balance', 'Normal Purchase'])
    ->get('procurement_delivery_challan_items item')->result();

    $ob = 0;
    $ob_costPrice = 0;
    $ob_sellingPrice = 0;
    $np = 0;
    $np_costPrice = 0;
    $np_sellingPrice = 0;
    $isInvoicesAdded= false;
    $ob_unit_cost_price= 0;
    $ob_unit_selleing_price= 0;
    $np_unit_cost_price= 0;
    $np_unit_selleing_price= 0;
    if(!empty($invoices)) {
      $isInvoicesAdded= true;
      foreach ($invoices as $key => $val) {
        if($val->dc_type == 'Opening Balance') {
          $ob_unit_cost_price= $val->price + ($val->price*$val->cgst) / 100 + ($val->price*$val->sgst) / 100;
          $ob_unit_selleing_price= $val->selling_price;
          $ob += $val->quantity;
          $ob_sellingPrice += $val->selling_price;
          $ob_costPrice += $val->quantity * ($ob_unit_cost_price);
        } else {
          $np_unit_cost_price= $val->price + ($val->price*$val->cgst) / 100 + ($val->price*$val->sgst) / 100;
          $np_unit_selleing_price= $val->selling_price;
          $np += $val->quantity;
          $np_sellingPrice += $val->quantity;
          $np_costPrice += $val->quantity * ($np_unit_cost_price);
        }
      }
    }   

// Allocated to staff transaction status
            $allocated = $this->db_readonly->select("vs.allocated_quantity as quantity")
            ->where('vs.proc_im_items_id', $item_id->id)
            ->where('vs.sales_year_id', $input['sales_year_id'])
            ->get('procurement_item_allocations_staff vs')->result();

            $allocatedTotal = 0;
              if(!empty($allocated)) {
              foreach ($allocated as $key => $val) {
              $allocatedTotal += $val->quantity;
              }
            }

// Collected from staff transaction status
              $collected = $this->db_readonly->select("pics.returned_quantity as quantity, pics.return_type as is_stockable")
              ->from('procurement_item_collections_staff pics')
              ->join('procurement_item_allocations_staff pias','pics.item_allocations_staff_id=pias.id')
              ->where('pias.sales_year_id', $input['sales_year_id'])
              ->where('pias.proc_im_items_id', $item_id->id)
              ->get()->result();

              // echo '<pre>'; print_r($this->db_readonly->last_query($collected)); die();

              $collectedTotal = 0;
              $damaged= 0;
              $not_stockaable= 0;
              if(!empty($collected)) {
              foreach ($collected as $key => $val) {
                if($val->is_stockable == '1') {
                  $collectedTotal += $val->quantity;
                } else if($val->is_stockable == '2') {
                  // $collectedTotal += $val->quantity;
                  $damaged ++;
                } else {
                  $not_stockaable ++;
                }
              }
              }

// Sold to student transaction status
              $sold = $this->db_readonly->select("pst.quantity")
                        ->where('pst.proc_im_items_id', $item_id->id)
                        ->where('pst.sales_year_id', $input['sales_year_id'])
                        ->get('procurement_sales_transactions pst')->result();

              $soldTotal = 0;
              if(!empty($sold)) {
                foreach ($sold as $key => $val) {
                  $soldTotal += $val->quantity;
                }
              }

// Sold to student transaction which is stoked by returned item status (If returned items also get sold)
              $returnedStokeSold = $this->db_readonly->select("pst.quantity")
                        ->join('procurement_delivery_challan_items pii', 'pii.id = pst.proc_invoice_items_id')
                        ->join('procurement_delivery_challan_master pim', 'pim.id = pii.invoice_master_id')
                        ->where('pst.proc_im_items_id', $item_id->id)
                        ->where('pst.sales_year_id', $input['sales_year_id'])
                        ->where_in('pim.dc_type', ['Normal Delete', 'Normal Return'])
                        ->get('procurement_sales_transactions pst')->result();

              $returnedStokeSoldQty = 0;
              if(!empty($returnedStokeSold)) {
                foreach ($returnedStokeSold as $key => $val) {
                  $returnedStokeSoldQty += $val->quantity;
                }
              }

// Item returned transaction status
              $return= $this->db_readonly->select("psr.return_quantity as quantity")
              ->where('psr.proc_im_items_id', $item_id->id)
              ->where('psr.sales_year_id', $input['sales_year_id'])
              ->get('procurement_sales_return psr')->result();

              $returnedTotal = 0;
              if(!empty($return)) {
                foreach ($return as $key => $val) {
                  $returnedTotal += $val->quantity;
                }
              }

// Soft deleted / cancelled receipt's item status
              $soft_deletes= $this->db_readonly->select("pst.quantity as quantity")
              ->from('procurement_sales_transactions pst')
              ->join('procurement_sales_master psm', 'psm.id= pst.sales_master_id')
              ->where('pst.sales_year_id', $input['sales_year_id'])
              ->where('pst.proc_im_items_id', $item_id->id)
              ->where('psm.soft_delete', 1)
              ->get()->result();

             

              $softDeletedItems = 0;
              if(!empty($soft_deletes)) {
                foreach ($soft_deletes as $key => $val) {
                  $softDeletedItems += $val->quantity;
                }
              }

// Vendor returns item status
              $vendor_return= $this->db_readonly->select("pvri.return_quantity as quantity")
              ->from('procurement_delivery_challan_master psm')
              ->join('procurement_delivery_challan_items pInvI', 'psm.id= pInvI.invoice_master_id')
              ->join('procurement_delivery_challan_return_items pvri', 'pvri.proc_invoice_items_id= pInvI.id')
              ->where('psm.sales_year_id', $input['sales_year_id'])
              ->where('pInvI.proc_im_items_id', $item_id->id)
              // ->where('psm.soft_delete', 1)
              ->get()->result();

             

              $returnsByVendor = 0;
              if(!empty($vendor_return)) {
                foreach ($vendor_return as $key => $val) {
                  $returnsByVendor += $val->quantity;
                }
              }

             

// End of getting statuses
                                  
              $item_id->total_quantity= intval($ob) + intval($np);
              $item_id->allocatedTotal= intval($allocatedTotal);
              $item_id->collectedTotal= intval($collectedTotal);
              $item_id->soldTotal= intval($soldTotal);
              $item_id->returnedTotal= intval($returnedTotal);
              $item_id->softDeletedItems= intval($softDeletedItems);
              $item_id->damaged= intval($damaged);
              $item_id->not_stockaable= intval($not_stockaable);
              $item_id->returnsByVendor= intval($returnsByVendor);
            $calculated_cur_qty= intval($ob) + intval($np) - intval($allocatedTotal) + intval($collectedTotal) + intval($damaged) - intval($soldTotal) +intval($returnedTotal) + intval($softDeletedItems) - intval($returnsByVendor);

            // $ob_unit_cost_price= 0;
            // $ob_unit_selleing_price= 0;
            // $np_unit_cost_price= 0;
            // $np_unit_selleing_price= 0;

              $item_id->stock= intval($calculated_cur_qty);
              $item_id->newstock= intval($np);
              $item_id->obstock= intval($ob);
              $item_id->newstock_price = $np_costPrice;
              $item_id->obstock_price = $ob_costPrice;
              $item_id->returnedStokeSoldQty = $returnedStokeSoldQty;
              $item_id->totalCost = $np_costPrice + $ob_costPrice;

              $item_id->ob_unit_cost_price = $ob_unit_cost_price;
              $item_id->ob_unit_selleing_price = $ob_unit_selleing_price;
              $item_id->np_unit_cost_price = $np_unit_cost_price;
              $item_id->np_unit_selleing_price = $np_unit_selleing_price;

              // // Current qty_in Database
              // $this->db_readonly->select('pdci.proc_im_items_id, sum(pdci.initial_quantity) as initial_quantity, sum(pdci.current_quantity) as current_quantity, group_concat(pdci.id) as challan_items_ids, group_concat(pdcm.dc_type) as dc_types');
              // $this->db_readonly->from('procurement_delivery_challan_master pdcm');
              // $this->db_readonly->join('procurement_delivery_challan_items pdci', 'pdci.invoice_master_id = pdcm.id');
              // $this->db_readonly->where('pdcm.sales_year_id', (1*$input['sales_year_id'] + 1));
              // $this->db_readonly->where('pdci.proc_im_items_id', $item_id->id);
              // $this->db_readonly->where('pdcm.dc_type', 'Opening Balance');
              // $this->db_readonly->group_by('pdci.proc_im_items_id');
              // $currectQtyInDB = $this->db_readonly->get()->row();

              // // echo '<pre>'; print_r($currectQtyInDB); die();

              // $item_id->current_quantity_in_database = 0;
              // $item_id->item_id = $item_id->id;
              // if(!empty($currectQtyInDB)) {
              //   $item_id->current_quantity_in_database = $currectQtyInDB->initial_quantity;
              // }


              if(!$isInvoicesAdded) {
                unset($all_items[$ikey]);
              }

      }

      return $all_items;
    }
    return [];

  }

  public function get_category_items(){
    $this->db_readonly->select('pic.id,pic.category_name');
    $this->db_readonly->from('procurement_parent_sales_order_details ppsod');
    $this->db_readonly->join('procurement_itemmaster_items pii','pii.id=ppsod.proccurment_item_id');
    $this->db_readonly->join('procurement_itemmaster_subcategory pis','pis.id=pii.proc_im_subcategory_id');
    $this->db_readonly->join('procurement_itemmaster_category pic','pic.id=pis.proc_im_category_id');
    $this->db_readonly->group_by('pic.id');
    return $this->db_readonly->get()->result();
  }

  public function get_ordered_items($input){
    $fromDate = date('Y-m-d',strtotime($input['from_date']));
    $toDate =date('Y-m-d',strtotime($input['to_date']));
    $this->db_readonly->select("ppsod.id,CONCAT(IFNULL(pis.subcategory_name, ''), ' - ', IFNULL(pii.item_name, '')) AS item_name,ppsod.quantity,sa.admission_no,ifnull(concat(sa.first_name, ' ',ifnull(sa.last_name,'')), '') as student_name,ifnull(concat(c.class_name, '',ifnull(cs.section_name,'')), '') as class_name,ppsod.price");
    $this->db_readonly->from('procurement_parent_sales_order_details ppsod');
    $this->db_readonly->join('procurement_itemmaster_items pii','pii.id=ppsod.proccurment_item_id');
    $this->db_readonly->join('procurement_itemmaster_subcategory pis','pis.id=pii.proc_im_subcategory_id');
    $this->db_readonly->join('procurement_itemmaster_category pic','pic.id=pis.proc_im_category_id');
    $this->db_readonly->join('procurement_parent_sales_orders ppso','ppso.id=ppsod.procurement_parent_sales_order_id');
    $this->db_readonly->join('student_admission sa','sa.id=ppso.student_admission_id');
    $this->db_readonly->join('student_year sy','sy.student_admission_id=sa.id and sy.acad_year_id=24');
    $this->db_readonly->join('class_section cs','cs.id=sy.class_section_id');
    $this->db_readonly->join('class c','c.id=cs.class_id');
    $this->db_readonly->where('ppso.status','submitted');
    if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(ppso.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate. '"');
    }
    if($input['category_id']){
    $this->db_readonly->where('pic.id', $input['category_id']);
    }
    $this->db_readonly->order_by('ppso.id','desc');
    return $this->db_readonly->get()->result();
  }

  public function get_parent_ordered_items($input){
    $fromDate = date('Y-m-d',strtotime($input['from_date']));
    $toDate =date('Y-m-d',strtotime($input['to_date']));
    $this->db_readonly->select("pii.id,CONCAT(IFNULL(pis.subcategory_name, ''), ' - ', IFNULL(pii.item_name, '')) AS item_name,SUM(quantity) AS total_quantity");  
    $this->db_readonly->from('procurement_itemmaster_subcategory pis');  
    $this->db_readonly->join('procurement_itemmaster_items pii','pii.proc_im_subcategory_id=pis.id');  
    $this->db_readonly->join('procurement_parent_sales_order_details ppsod','ppsod.proccurment_item_id=pii.id');  
    $this->db_readonly->join('procurement_parent_sales_orders ppso','ppso.id=ppsod.procurement_parent_sales_order_id');  
    $this->db_readonly->where('ppso.status','submitted');
    if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(ppso.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate. '"');
    }
    if($input['category_id']){
    $this->db_readonly->where('pis.proc_im_category_id', $input['category_id']);
    }
    $this->db_readonly->group_by('pii.id');
    return $this->db_readonly->get()->result();
  }

  public function make_sub_category_visible_to_parents($sub_category_id,$button_status){
    $this->db->set('is_item_visible_for_parent',$button_status);
    $this->db->where('id', $sub_category_id);
    $status= $this->db->update('procurement_itemmaster_subcategory');

    // History insert
      $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Subcategory',
        'source_id' => $sub_category_id,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Parent Visibility for Subcategory',
        'action_description' => "Subcategory visibility for parents has been changed to ".($button_status ? 'Visible' : 'Hidden'),
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);
      return $status;
  }

  public function enable_disable_custom_name($sub_category_id,$button_status){
    $this->db->set('custom_name_flag',$button_status);
    $this->db->where('id', $sub_category_id);
    $status= $this->db->update('procurement_itemmaster_subcategory');

    // History insert
      $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Subcategory',
        'source_id' => $sub_category_id,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Custom Name Updated',
        'action_description' => "Custom name flag has been changed to ".($button_status ? 'Enabled' : 'Disabled')." for subcategory id: $sub_category_id",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);
      return $status;
  }

  public function getExpenseSubCategories(){
    $subCategories=$this->db_readonly->select("ex.id, ex.sub_category as name, ec.category_name")
    ->from("expense_sub_category ex")
    ->join('expense_category ec','ec.id=ex.cat_id')
    ->get()->result();

    return $subCategories=!empty($subCategories) ? $subCategories : [];
  }

  public function getStaffForIndentApproval($payload){
    $procImCatId=$payload["procImCatId"];
    $staffAlreadyAssigned=$this->getFinancialApprover($procImCatId);
    $staffList=$this->getApproversList($staffAlreadyAssigned);

    return $staffList = empty($staffList) ? [] : $staffList;
  }

  public function getStaffForFinancialApproval($payload){
    $procImCatId=$payload["procImCatId"];
    $staffAlreadyAssigned=$this->getIndentApprovers($procImCatId);
    $staffList=$this->getApproversList($staffAlreadyAssigned);
    return $staffList = empty($staffList) ? [] : $staffList;
  }

  private function getApproversList($staffAlreadyAssigned){
    if (empty($staffAlreadyAssigned)) {
      $staffList = $this->db_readonly->select("id, TRIM(CONCAT(ifnull(first_name, ''), ' ', ifnull(last_name, ''))) as staffName")
        ->from("staff_master sm")
        ->where('status', 2)
        ->where('is_primary_instance', 1)
        ->get()->result();
    } else {
      $staffList = $this->db_readonly->select("id, TRIM(CONCAT(ifnull(first_name, ''), ' ', ifnull(last_name, ''))) as staffName")
        ->from("staff_master sm")
        ->where('status', 2)
        ->where('is_primary_instance', 1)
        ->where_not_in("id", $staffAlreadyAssigned)
        ->get()->result();
    }
    return $staffList;
  }

  private function getIndentApprovers($procImCatId){
    $staffAlreadyAssigned = [];
    $indentApproverDetails = $this->db_readonly->select('bom_approver_1,bom_approver_2,bom_approver_3')
      ->from("procurement_itemmaster_category")
      ->where("id", $procImCatId)
      ->get()->row();

    if (!empty($indentApproverDetails)) {
      if (!empty($indentApproverDetails->bom_approver_1)) {
        $staffAlreadyAssigned["bom_approver_1"] = $indentApproverDetails->bom_approver_1;
      }

      if (!empty($indentApproverDetails->bom_approver_2)) {
        $staffAlreadyAssigned["bom_approver_2"] = $indentApproverDetails->bom_approver_2;
      }

      if (!empty($indentApproverDetails->bom_approver_3)) {
        $staffAlreadyAssigned["bom_approver_3"] = $indentApproverDetails->bom_approver_3;
      }
    }
    return $staffAlreadyAssigned;
  }

  private function getFinancialApprover($procImCatId){
    $staffAlreadyAssigned = [];
    $financialApproverDetails = $this->db_readonly->select('financial_approver')
      ->from("procurement_itemmaster_category")
      ->where("id", $procImCatId)
      ->where("financial_approver>=", 0)
      ->get()->row();

    if (!empty($financialApproverDetails)) {
      $staffAlreadyAssigned["financial_approver"] = $financialApproverDetails->financial_approver;
    }
    return $staffAlreadyAssigned;
  }

  public function getIndentAndFinancialApprover($procImCatId){
    $indentApprovers = $this->getIndentApprovers($procImCatId);
    $financialApprover=$this->getFinancialApprover($procImCatId);
    
    $approversList=array_merge($indentApprovers,$financialApprover);
    return $approversList;
  }

  function onclick_for_final_reconcile() {
    $input= $this->input->post();
    $item_id= $input['item_id'];
    $sales_year_id= $input['sales_year_id'];

    $invoiceItemsIds= $this->db_readonly->select("pii.id, pii.initial_quantity, pii.current_quantity, pim.dc_type, pim.invoice_no, pim.bill_no")
        ->from('procurement_delivery_challan_master pim')
        ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
        ->where('pim.sales_year_id', $sales_year_id)
        ->where('pii.proc_im_items_id', $item_id)
        ->get()->result();

      $invItemWiseSold= [];
      if(!empty($invoiceItemsIds)) {
        foreach($invoiceItemsIds as $invKey => $invVal) {
          $sold= $this->db_readonly->select("sum(ifnull(quantity, 0)) as quantity")
            ->where('proc_invoice_items_id', $invVal->id)
            ->where('proc_im_items_id', $item_id)
            ->where('sales_year_id', $sales_year_id)
            ->get('procurement_sales_transactions')->row();
          $allocated= $this->db_readonly->select("sum(ifnull(allocated_quantity, 0)) as quantity")
            ->where('proc_im_items_id', $item_id)
            ->where('sales_year_id', $sales_year_id)
            ->where('proc_invoice_items_id', $invVal->id)
            ->get('procurement_item_allocations_staff')->row();
          $vendor_return= $this->db_readonly->select("sum(ifnull(return_quantity, 0)) as quantity")
            ->where('proc_invoice_items_id', $invVal->id)
            ->get('procurement_delivery_challan_return_items')->row();
          $invItemWiseSold[$invVal->id]= array(
            'id' => $invVal->id,
            'dc_type' => $invVal->dc_type,
            'invoice_no' => $invVal->invoice_no,
            'bill_no' => $invVal->bill_no,
            'initial_quantity' => $invVal->initial_quantity,
            'current_quantity' => $invVal->current_quantity,
            'sold' => $sold->quantity,
            'allocated' => $allocated->quantity,
            'vendor_return' => $vendor_return->quantity
          );

          if($invVal->initial_quantity - $sold->quantity - $allocated->quantity - $vendor_return->quantity >= 0) {
            $updateItem[]= array(
              'id' => $invVal->id,
              'current_quantity' => 1*($invVal->initial_quantity - $sold->quantity - $allocated->quantity - $vendor_return->quantity)
            );

          }
        }

        if(!empty($updateItem)) {
          $this->db->trans_start();
          $this->db->update_batch('procurement_delivery_challan_items', $updateItem, 'id');
          $this->db->trans_complete();
        }

      }

    echo '<pre>'; print_r($invItemWiseSold); die();
  }

  public function allocate_products() {
    $input = $this->input->post();
    // echo '<pre>input: '; print_r($input); die();

    $selected_staffs= $input['selected_staffs'];
    $allocate_date= $input['allocate_date'];
    $sales_year_id= $input['sales_year_id'];
    $purpose= $input['purpose'];
    $categories= $input['categories'];
    $sub_category_id= $input['sub_category_id'];
    $item_invItem_id= $input['items_id'];
    $quantityArr= $input['quantity'];

    $status= true;
    $history_details= '';
    $this->db->trans_start();
    if(!empty($selected_staffs)) {
      foreach($selected_staffs as $staffKey => $staff_id) {
        foreach ($quantityArr as $qtyKey => $quantity) {
          $itemInvoiceItemIds= explode('___', $item_invItem_id[$qtyKey]);
          $itemMasterId = $itemInvoiceItemIds[0];
          $invoiceItemsId = $itemInvoiceItemIds[1];
          $data = array(
            'collected_by' => $staff_id,
            'proc_im_items_id' => $itemMasterId,
            'proc_invoice_items_id' => $invoiceItemsId,
            'allocated_quantity' => $quantity,
            'purpose' => $purpose,
            'sales_year_id' => $sales_year_id,
            'allocate_date' => date('Y-m-d',strtotime($input['allocate_date'])),
            'given_by' => $this->authorization->getAvatarStakeHolderId()
          );
  // 
  $staff= $this->db->select("id, TRIM(CONCAT(first_name, ' ', ifnull(last_name, ''))) as staff_name")
        ->where('id', $staff_id)
        ->get('staff_master')->row();
    $staff_name= "Admin";
    if(!empty($staff)) {
      $staff_name= $staff->staff_name;
    }

     $item= $this->db->select("item_name")->where('id', $itemMasterId)->get('procurement_itemmaster_items')->row();
        $history_details .= "Staff: $staff_name | Item: " . $item->item_name . " | Quantity: " . $quantity . "\n";

    // 
          $exist_qty_in_invoices= $this->db->select("current_quantity")->where('id', $invoiceItemsId)->get('procurement_delivery_challan_items')->row()->current_quantity;
          if($exist_qty_in_invoices >= $quantity) {
// upadte current quantity in invoice items
            $currentQuantity= $exist_qty_in_invoices - $quantity;
            $invItemUpdate = array(
              'current_quantity' => $currentQuantity
            );
            $this->db->where('id', $invoiceItemsId)->update('procurement_delivery_challan_items', $invItemUpdate);
// updating allocated quantity in procurement_item_allocations_staff
            $this->db->insert('procurement_item_allocations_staff', $data);
          } else {
            $status= false;
          }
  
        }
      }
    }

    // History insert
      $currentSalesYear= $this->get_current_sales_year_data();
      $sales_year_id= $currentSalesYear->id;
      $history_insert= array(
        'source_type' => 'Allocation',
        'source_id' => NULL,
        'sales_year_id' => $sales_year_id,
        'action_type' => 'Item Allocated',
        'action_description' => "Some items have been allocated to staff members. Details:\n$history_details",
        'action_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_itemmaster_history', $history_insert);

    
    $this->db->trans_complete();
    if ($this->db->trans_status() === FALSE || $status === FALSE) {
        $this->db->trans_rollback();
        return 0;
    } else {
        $this->db->trans_commit();
        return 1;
    }
  }

  function return_collect_reconcile() {
    $input= $this->input->post();
    $item_id= $input['proc_im_items_id']; 
    $sales_year_id= $input['sales_year_id'];

    $invoices= $this->db->select("pii.id, pii.initial_quantity, pii.current_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where_in('pim.dc_type', ['Normal Return', 'Normal Delete', 'Normal Collect'])
      ->where('pii.is_closed', 1)
      ->order_by('pii.id', 'asc')
      ->get()->result();

      // echo '<pre>'; print_r($this->db->last_query($invoices));
      // die();

      $invoicesPurchasedOBReturDelete= $this->db->select("pii.id, pii.initial_quantity, pii.current_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where_in('pim.dc_type', ['Normal Return', 'Normal Delete', 'Normal Collect', 'Normal Purchase', 'Opening Balance'])
      ->order_by('pii.id', 'asc')
      ->get()->result();

      $invoicesPurchasedOBReturDelete_arr= [0];
      if(!empty($invoicesPurchasedOBReturDelete)) {
        foreach($invoicesPurchasedOBReturDelete as $k => $v) {
          $invoicesPurchasedOBReturDelete_arr[]= $v->id;
        }
      }

      // collected items with purchased and OB so that we can find that which invItem has been collected
      $collectedItems= $this->db->select("pias.id allo_id, pics.id as coll_id, ifnull(pias.collected_quantity, 0) as collected_quantity")
      ->join('procurement_item_collections_staff pics', 'pics.item_allocations_staff_id = pias.id')
      ->where_in('proc_invoice_items_id', $invoicesPurchasedOBReturDelete_arr)
      ->where("pias.collected_quantity is not null and pias.collected_quantity > 0")
      ->where('pics.sales_year_id', $sales_year_id)
      ->get('procurement_item_allocations_staff pias')->result();

      // echo '<pre>'; print_r($collectedItems);
      //   die();

    if(!empty($invoices)) {
      foreach($invoices as $key => $val) {
        $allocated= $this->db->select("ifnull(sum(ifnull(allocated_quantity, 0)), 0) as quantity")
          ->where('proc_invoice_items_id', $val->id)
          ->where('proc_im_items_id', $item_id)
          ->where('sales_year_id', $sales_year_id)
          ->get('procurement_item_allocations_staff')->row();
        $sold= $this->db->select("ifnull(sum(ifnull(quantity, 0)), 0) as quantity")
          ->where('proc_invoice_items_id', $val->id)
          ->where('proc_im_items_id', $item_id)
          ->where('sales_year_id', $sales_year_id)
          ->get('procurement_sales_transactions')->row();
        $vendor_return= $this->db->select("ifnull(sum(ifnull(return_quantity, 0)), 0) as quantity")
          ->where('proc_invoice_items_id', $val->id)
          ->get('procurement_delivery_challan_return_items')->row();
          ////////////////////////////////////////////////////////////////
        $return= $this->db->select("ifnull(sum(ifnull(psr.return_quantity, 0)), 0) as quantity")
          ->from ('procurement_sales_return psr')
          ->join('procurement_delivery_challan_master pim', 'pim.id = psr.proc_invoice_master_id_if_returned')
          ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
          ->where('pii.id', $val->id)
          ->where('psr.proc_im_items_id', $item_id)
          ->where('pii.proc_im_items_id', $item_id)
          ->where('pim.dc_type', 'Normal Return')
          ->where('pim.sales_year_id', $sales_year_id)
          ->get()->row();

        $collect= 0;
        if(!empty($collectedItems) && isset($collectedItems[$key])) {
          $collect= $collectedItems[$key]->collected_quantity;
        }

        $totalInitial= 1*($return->quantity + $collect);

        $allocated= $allocated->quantity;
        $sold= $sold->quantity;
        $vendor_return= $vendor_return->quantity;

        $total_used= 1*($allocated + $sold + $vendor_return);

        if($totalInitial > 0) {
          $updateInvItem[]= array(
            'id' => $val->id,
            'current_quantity' => 1*($totalInitial - $total_used),
            'initial_quantity' => $totalInitial
          );
        } else if($total_used > 0) {
          $updateInvItem[]= array(
            'id' => $val->id,
            'current_quantity' => 0,
            'initial_quantity' => $total_used
          );
        } else {
          $updateInvItem[]= array(
            'id' => $val->id,
            'current_quantity' => 1,
            'initial_quantity' => 1
          );
        }
      }
      // die();

      $this->db->trans_start();
      if(!empty($updateInvItem)) {
        $this->db->update_batch('procurement_delivery_challan_items', $updateInvItem, 'id');
      }
      $this->db->trans_complete();
    }

    return $this->db->trans_status();
  }

  function return_collect_and_delete_reconcile() {
    $input= $this->input->post();
    $item_id= $input['proc_im_items_id']; 
    $sales_year_id= $input['sales_year_id'];

    $invoicesReturn= $this->db->select("pii.id, pii.initial_quantity, pii.current_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where_in('pim.dc_type', ['Normal Return']) 
      ->order_by('pii.id', 'asc')
      ->get()->result();
    $returnMasters= $this->db->select("psr.id, psr.return_quantity, psr.proc_invoice_items_id")
      ->from('procurement_sales_master psm')
      ->join('procurement_sales_transactions pst', 'pst.sales_master_id = psm.id')
      ->where('pst.sales_year_id', $sales_year_id)
      ->where('pst.proc_im_items_id', $item_id)
      ->join('procurement_sales_return psr', 'psr.proc_sales_master_id = psm.id')
      ->where('psr.proc_im_items_id', $item_id) 
      ->order_by('id', 'asc')
      ->get()->result();
      // DO not match procImItemsId
      if(!empty($invoicesReturn)) {
        foreach($invoicesReturn as $key => $val) {
          if(!empty($returnMasters) && isset($returnMasters[$key])) {
            $update_invoice_items[]= array(
              'id' => $val->id,
              'initial_quantity' => $returnMasters[$key]->return_quantity,
              'current_quantity' => $returnMasters[$key]->return_quantity
            );
          }
        }
      }







    $invoicesDelete= $this->db->select("pii.id, pii.initial_quantity, pii.current_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where_in('pim.dc_type', ['Normal Delete']) 
      ->order_by('pii.id', 'asc')
      ->get()->result();
    $deleteMasters= $this->db->select("pst.id, pst.quantity, pst.proc_invoice_items_id")
      ->from('procurement_sales_master psm')
      ->join('procurement_sales_transactions pst', 'pst.sales_master_id = psm.id')
      ->where('psm.soft_delete', 1) 
      ->where('pst.proc_im_items_id', $item_id) 
      ->where('pst.sales_year_id', $sales_year_id)
      ->order_by('pst.id', 'asc')
      ->get()->result();
      // DO not match procImItemsId
      if(!empty($invoicesReturn)) {
        foreach($invoicesReturn as $key => $val) {
          if(!empty($deleteMasters) && isset($deleteMasters[$key])) {
            $update_invoice_items[]= array(
              'id' => $val->id,
              'initial_quantity' => $deleteMasters[$key]->quantity,
              'current_quantity' => $deleteMasters[$key]->quantity
            );
          }
        }
      }








    $invoicesCollect= $this->db->select("pii.id, pii.initial_quantity, pii.current_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where_in('pim.dc_type', ['Normal Collect']) 
      ->where('pii.is_closed', 1)
      ->order_by('pii.id', 'asc')
      ->get()->result();

      $matchAllocatedAndCollected= $this->db->select("pias.id, pias.allocated_quantity, pias.proc_invoice_items_id, pias.collected_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->join('procurement_item_allocations_staff pias', 'pias.proc_invoice_items_id = pii.id')
      ->where('pias.proc_im_items_id', $item_id)
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->order_by('pias.id', 'asc')
      ->get()->result();
    if(!empty($matchAllocatedAndCollected)) {
      foreach($matchAllocatedAndCollected as $key => $val) {
        if($val->allocated_quantity < $val->collected_quantity) {
          $macthAlloCollInStaffAllocation[]= array(
            'id' => $val->id,
            'allocated_quantity' => $val->collected_quantity
          );
        }
      }
    }


      // echo '<pre>'; print_r($invoicesCollect); die();  
      $ccollectMasters= $this->db->select("pias.id, pias.allocated_quantity, pias.proc_invoice_items_id, pias.collected_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->join('procurement_item_allocations_staff pias', 'pias.proc_invoice_items_id = pii.id')
      ->where('pias.proc_im_items_id', $item_id)
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where('pias.collected_quantity is not null and pias.collected_quantity > 0')
      ->order_by('pias.id', 'asc')
      ->get()->result();

      
      // DO not match procImItemsId
      if(!empty($invoicesReturn)) {
        foreach($invoicesReturn as $key => $val) {
          if(!empty($ccollectMasters) && isset($ccollectMasters[$key])) {
            $update_invoice_items[]= array(
              'id' => $val->id,
              'initial_quantity' => $ccollectMasters[$key]->collected_quantity,
              'current_quantity' => $ccollectMasters[$key]->collected_quantity
            );
          }
        }
      }

      
      $this->db->trans_start();
      if(!empty($update_invoice_items)) {
        $this->db->update_batch('procurement_delivery_challan_items', $update_invoice_items, 'id');
      }
      if(!empty($macthAlloCollInStaffAllocation)) {
        $this->db->update_batch('procurement_item_allocations_staff', $macthAlloCollInStaffAllocation, 'id');
      }
      $this->db->trans_complete();





    return $this->db->trans_status();
  }

  function getItemsSalesYearWise() {
    $sales_yearId= $this->input->post('sales_year_id');
    return $this->db_readonly->select("piis.proc_im_items_id, pic.category_name, pis.subcategory_name, pii.item_name, pii.status_sales_year_1, ifnull(pii.status_sales_year_2, 'Not completed') as status_sales_year_2")
    ->from('procurement_delivery_challan_master pim')
    ->join('procurement_delivery_challan_items piis','piis.invoice_master_id=pim.id')
    ->join('procurement_itemmaster_subcategory pis','pis.id=piis.proc_im_subcategory_id')
    ->join('procurement_itemmaster_category pic','pic.id=piis.proc_im_category_id')
    ->join('procurement_itemmaster_items pii','pii.id=piis.proc_im_items_id')
    ->where('pim.sales_year_id', $sales_yearId)
    ->group_by('piis.proc_im_items_id')
    ->get()->result();
  }

  function getReconciliationStatus() {
    $input= $this->input->post();
    $sales_year_id= $input['sales_year_id'];
    $itemsIds= $input['itemsIds'];
    $chunkedItems= $input['chunkedItems'];
    $chunkedSubCats= $input['chunkedSubCats'];
    $chunkedCats= $input['chunkedCats'];
    $chunkedreconciliationStatus= $input['chunkedreconciliationStatus'];

    
    $finalArr= [];
    if(!empty($itemsIds)) {
      foreach($itemsIds as $key => $item_id) {
        $obj= new stdClass();

        $obj->item_id= $item_id;
        $obj->item_name= $chunkedItems[$key];
        $obj->subcategory_name= $chunkedSubCats[$key];
        $obj->category_name= $chunkedCats[$key];
        $obj->reconciledStatus= $chunkedreconciliationStatus[$key];

        $calculationData= $this->get_total_current_sold_return_vendorReturn_allocated_collected_and_deleted_quantity($item_id, $sales_year_id);

        $obj->total= intval($calculationData['Initial']) + intval($calculationData['Purchased']);
        $obj->sold= intval($calculationData['soldTotal']);
        $obj->allocated= intval($calculationData['allocatedTotal']);
        $obj->collected= intval($calculationData['collectedTotal']) + intval($calculationData['collectedDamagedTotal']);
        $obj->vendorReturns= intval($calculationData['vendorReturnedItems']);
        $obj->salesReturn= intval($calculationData['returnedTotal']);
        $obj->softDeletedItems= intval($calculationData['softDeletedItems']);

        // echo '<pre>'; print_r($calculationData); die();

        // $uniqueInvoiceItemsId= $this->db_readonly->select('pii.id')
        //     ->from('procurement_delivery_challan_master pim')
        //     ->join('procurement_delivery_challan_items pii','pii.invoice_master_id=pim.id')
        //     ->where('pim.sales_year_id', $sales_year_id)
        //     ->where('pii.proc_im_items_id', $item_id)
        //     ->get()->result();
        // $unique_invoice_items_ids= [];
        // if(!empty($uniqueInvoiceItemsId)) {
        //   foreach($uniqueInvoiceItemsId as $key => $val) {
        //     $unique_invoice_items_ids[]= $val->id;
        //   }
        // }

        // $total= $this->db_readonly->select("ifnull(sum(ifnull(initial_quantity, 0)), 0) as total")
        //     ->from('procurement_delivery_challan_master pim')
        //     ->join('procurement_delivery_challan_items pii','pii.invoice_master_id=pim.id')
        //     ->where('pim.sales_year_id', $sales_year_id)
        //     ->where('pii.proc_im_items_id', $item_id)
        //     ->where_in('pim.dc_type', ['Normal Purchase', 'Opening Balance', 'PO'])
        //     ->get()->row();
        // $obj->total= $total->total;
        $current= $this->db_readonly->select("sum(ifnull(current_quantity, 0)) as total, group_concat(ifnull(pii.id, 0)) as proc_invoice_iems_ids, pii.proc_im_items_id, pim.sales_year_id")
            ->from('procurement_delivery_challan_master pim')
            ->join('procurement_delivery_challan_items pii','pii.invoice_master_id=pim.id')
            ->where('pim.sales_year_id', $sales_year_id)
            ->where('pii.proc_im_items_id', $item_id)
            ->group_by('pii.proc_im_items_id')
            ->get()->row();
        $obj->current= $current->total;

        // echo '<pre>'; print_r($this->db_readonly->last_query($current)); die();

        // $sold= $this->db_readonly->select("ifnull(sum(ifnull(quantity, 0)), 0) as total")
        //     ->from('procurement_sales_transactions pst')
        //     ->where('pst.sales_year_id', $sales_year_id)
        //     ->where('pst.proc_im_items_id', $item_id)
        //     ->get()->row();
        // $obj->sold= $sold->total;
        // // $proc_invoice_iems_ids= $current->proc_invoice_iems_ids;
        // $allocated= $this->db_readonly->select("ifnull(sum(ifnull(allocated_quantity, 0)), 0) as total, ifnull(sum(ifnull(collected_quantity, 0)), 0) as total_collected_quantity")
        //     ->from('procurement_item_allocations_staff')
        //     ->where_in("proc_invoice_items_id", $unique_invoice_items_ids)
        //     ->where('proc_im_items_id', $item_id)
        //     ->get()->row();
        // $obj->allocated= $allocated->total;
        // $obj->collected= $allocated->total_collected_quantity;
        // $vendorReturns= $this->db_readonly->select("ifnull(sum(ifnull(return_quantity, 0)), 0) as total")
        //     ->from('procurement_delivery_challan_return_items')
        //     ->where_in("proc_invoice_items_id", $unique_invoice_items_ids)
        //     ->get()->row();
        // $obj->vendorReturns= $vendorReturns->total;
        // $salesReturn= $this->db_readonly->select("ifnull(sum(ifnull(return_quantity, 0)), 0) as total")
        //     ->from('procurement_sales_return')
        //     ->where_in("proc_invoice_items_id", $unique_invoice_items_ids)
        //     ->where('proc_im_items_id', $item_id)
        //     ->get()->row();
        // $obj->salesReturn= $salesReturn->total;
        // $softDeletedItems= $this->db_readonly->select("ifnull(sum(ifnull(quantity, 0)), 0) as total")
        //     ->from('procurement_sales_master pim')
        //     ->join('procurement_sales_transactions pst','pim.id=pst.sales_master_id')
        //     ->where('pst.sales_year_id', $sales_year_id)
        //     ->where_in("pst.proc_invoice_items_id", $unique_invoice_items_ids)
        //     ->where('pim.soft_delete', 1)
        //     ->get()->row();
        // $obj->softDeletedItems= $softDeletedItems->total;

        $finalArr[]= $obj;
      }
    }
    
    //  // echo '<pre>'; print_r($finalArr); die();
   
    return $finalArr;
  }

  function finish_reconciliation() {
    $input= $this->input->post();
    $item_id= $input['item_id'];
    $sales_year_id= $input['sales_year_id'];

    $this->manage_initial_quantity_of_return_collect_and_delete_before_reconcile($item_id, $sales_year_id);

    $invoices= $this->db->select("pii.id, pii.proc_im_subcategory_id, pii.proc_im_category_id, pii.price, pii.cgst, pii.sgst, pii.selling_price, pii.initial_quantity, pii.current_quantity, pii.is_closed, pim.dc_type")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('sales_year_id', $sales_year_id)
      ->order_by('pii.id', 'asc')
      ->get()->result();

    $update_invoices= [];

    $invoices_arr= [0];
    if(!empty($invoices)) {
      foreach($invoices as $key => $val) {
        $val->temp_sold= 0;
        array_push($invoices_arr, $val->id);
        
        if($val->dc_type == 'Opening Balance' || $val->dc_type == 'Normal Purchase') { // Collect return items from this invoice can not change the current quantity because it is already sold OR no source update
          $val->current_quantity= $val->initial_quantity;
          $update_invoices[]= array(
            'id' => $val->id,
            'current_quantity' => $val->initial_quantity
          );
        } 
      }
    }

    if(!empty($update_invoices)) {
      $this->db->update_batch('procurement_delivery_challan_items', $update_invoices, 'id');
    }

    $transactions= $this->db->select("id, proc_invoice_items_id, quantity, amount, sales_master_id, proc_im_subcategory_id")
      ->where_in('proc_invoice_items_id', $invoices_arr)
      ->where_in('proc_im_items_id', $item_id)
      ->order_by('proc_invoice_items_id', 'asc')
      ->get('procurement_sales_transactions')->result();
      
    if(!empty($transactions)) {
      
      $update_tx_invoice_itemsId= [];
      $insert_tx_invoice_itemsId= [];
      foreach($transactions as $key => $val) {
        $update_or_insert= 'update';
        $insert_preset_variable= [];
        for($i = 0; $i< count($invoices); $i ++) {

          if($update_or_insert == 'update') {
            if($invoices[$i]->current_quantity >= $val->quantity) {
              $update_tx_invoice_itemsId[]= array(
                'id' => $val->id,
                'proc_invoice_items_id' => $invoices[$i]->id,
                'quantity' => $val->quantity,
                'amount' => $val->amount
              );
              $invoices[$i]->current_quantity = intval($invoices[$i]->current_quantity) - intval($val->quantity); // updating curr from invoices
              $invoices[$i]->temp_sold = intval($invoices[$i]->temp_sold) + intval($val->quantity);
              $update_or_insert= 'update';
              break;
            } else if($invoices[$i]->current_quantity > 0 && $invoices[$i]->current_quantity < $val->quantity) {
              $amtPerItem= abs($val->amount) / intval($val->quantity);
              $update_tx_invoice_itemsId[]= array(
                'id' => $val->id,
                'proc_invoice_items_id' => $invoices[$i]->id,
                'quantity' => $invoices[$i]->current_quantity,
                'amount' => $amtPerItem * intval($invoices[$i]->current_quantity)
              );
              
              $insert_preset_variable= array(
                'sales_master_id' => $val->sales_master_id,
                'proc_im_items_id' => $item_id,
                'proc_im_subcategory_id' => $val->proc_im_subcategory_id,
                'proc_invoice_items_id' => 0,
                'sales_year_id' => $sales_year_id,
                'quantity' => intval($val->quantity) - intval($invoices[$i]->current_quantity),
                'amount' => $amtPerItem * (intval($val->quantity) - intval($invoices[$i]->current_quantity))
              );
              $invoices[$i]->temp_sold = intval($invoices[$i]->temp_sold) + intval($invoices[$i]->current_quantity);
              $invoices[$i]->current_quantity = 0;
              $update_or_insert= 'insert';
            }
          } else {
            // Insert things
            if($invoices[$i]->current_quantity >= $insert_preset_variable['quantity']) {
              $insert_preset_variable['proc_invoice_items_id']= $invoices[$i]->id;

              $insert_tx_invoice_itemsId[]= $insert_preset_variable;

              $invoices[$i]->current_quantity = intval($invoices[$i]->current_quantity) - intval($insert_preset_variable['quantity']); // updating curr from invoices
              $invoices[$i]->temp_sold = intval($invoices[$i]->temp_sold) + intval($insert_preset_variable['quantity']);
              $update_or_insert= 'update';
              break;
            } else if($invoices[$i]->current_quantity > 0 && $invoices[$i]->current_quantity < $insert_preset_variable['quantity']) {
              $amtPerItem= abs($insert_preset_variable['quantity']) / intval($val->quantity);
              $insert_preset_variable['proc_invoice_items_id']= $invoices[$i]->id;
              $insert_preset_variable['quantity']= $invoices[$i]->current_quantity;
              $insert_preset_variable['amount']= $amtPerItem * intval($invoices[$i]->current_quantity);

              $insert_tx_invoice_itemsId[]= $insert_preset_variable;
              
              $insert_preset_variable= array(
                'sales_master_id' => $val->sales_master_id,
                'proc_im_items_id' => $item_id,
                'proc_im_subcategory_id' => $val->proc_im_subcategory_id,
                'proc_invoice_items_id' => 0,
                'sales_year_id' => $sales_year_id,
                'quantity' => intval($insert_preset_variable['quantity']) - intval($invoices[$i]->current_quantity),
                'amount' => $amtPerItem * (intval($insert_preset_variable['quantity']) - intval($invoices[$i]->current_quantity))
              );
              $invoices[$i]->temp_sold = intval($invoices[$i]->temp_sold) + ( intval($insert_preset_variable['quantity']) - intval($invoices[$i]->current_quantity) );
              $invoices[$i]->current_quantity = 0;
              $update_or_insert= 'insert';
            }
          }

        }
        
      }

      $update_invoices= [];
      $update_closed_sales_year_items= [];

      if(!empty($invoices)) {
        foreach($invoices as $key => $val) {
          $update_invoices[]= array(
            'id' => $val->id,
            'current_quantity' => $val->is_closed == '1' ? $val->current_quantity : 0
          );
        }
      }

      // Update and insert
      if(!empty($update_tx_invoice_itemsId)) {
        $this->db->update_batch('procurement_sales_transactions', $update_tx_invoice_itemsId, 'id');
      } 
      if(!empty($insert_tx_invoice_itemsId)) {
        $this->db->insert_batch('procurement_sales_transactions', $insert_tx_invoice_itemsId);
      }
      
      if(!empty($update_invoices)) {
        $this->db->update_batch('procurement_delivery_challan_items', $update_invoices, 'id');
      }

      if(!empty($update_closed_sales_year_items)) {
        $this->db->update_batch('procurement_delivery_challan_items', $update_closed_sales_year_items, 'id');
      }

    }

    $ststus_manage_allocation_and_vendor_return= $this->__manage_allocation_and_vendor_return_for_reconciliation($item_id, $sales_year_id, $invoices_arr);

    if($sales_year_id == '1') {
      $this->db->where('id', $item_id)->update('procurement_itemmaster_items', ['status_sales_year_1' => 'Complete']);
    } if($sales_year_id == '2') {
      $this->db->where('id', $item_id)->update('procurement_itemmaster_items', ['status_sales_year_2' => 'Complete']);
    }

    return true;

  }
  
  function __manage_allocation_and_vendor_return_for_reconciliation($item_id, $sales_year_id, $all_invoices_ids_arr) {
    array_shift($all_invoices_ids_arr);
    $invoices= $this->db->select("pii.id, pii.proc_im_subcategory_id, pii.proc_im_category_id, pii.price, pii.cgst, pii.sgst, pii.selling_price, pii.initial_quantity, pii.current_quantity, pii.is_closed")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where('pii.current_quantity > 0')
      ->order_by('pii.id', 'asc')
      ->get()->result();

    $invoices_obj= new stdClass();
    if(!empty($invoices)) {
      foreach($invoices as $key => $val) {
        $invoices_obj->{$val->id}= $val->current_quantity;
      }
    }

    // allocation
    $allocation= $this->db->select("id, proc_invoice_items_id, allocated_quantity")->where('proc_im_items_id', $item_id)->where_in('proc_invoice_items_id', $all_invoices_ids_arr)->get('procurement_item_allocations_staff')->result();

    if(!empty($allocation)) {
      $notSubtractedQtyFromInvoices= 0;
      foreach($allocation as $key => $val) {
          $remainingAllocation = $val->allocated_quantity; // total need to allocated
          
          foreach($invoices_obj as $invItemId => $invQty) {
              if($remainingAllocation <= 0) {
                  break; // We've fully allocated this item
              }
              
              if($invoices_obj->$invItemId > 0) {
                  $allocatedQty = min($invoices_obj->$invItemId, $remainingAllocation);
                  
                  $update_allocation[] = array(
                      'id' => $val->id,
                      'proc_invoice_items_id' => $invItemId,
                      // 'allocated_quantity' => $allocatedQty // No need to update the lessor quantity in the allocation table instead we can subtract it from the invoice table later
                  );
                  
                  $invoices_obj->$invItemId -= $allocatedQty;
                  $remainingAllocation -= $allocatedQty;

                  $notSubtractedQtyFromInvoices += $remainingAllocation;
                  break;
              }
          }
    }
    if($notSubtractedQtyFromInvoices > 0) { // If there are any remaining allocations that couldn't be satisfied by the invoices then we have to settle that by following way
      foreach($invoices_obj as $invItemId => $invQty) {
        if($invoices_obj->$invItemId > 0) {
          if($invoices_obj->$invItemId >= $notSubtractedQtyFromInvoices) {
            $invoices_obj->$invItemId= $invoices_obj->$invItemId - $notSubtractedQtyFromInvoices;
          } else if($invoices_obj->$invItemId < $notSubtractedQtyFromInvoices) {
            $notSubtractedQtyFromInvoices= $notSubtractedQtyFromInvoices - $invoices_obj->$invItemId;
            $invoices_obj->$invItemId= 0;
          }
        }

        if($notSubtractedQtyFromInvoices == 0) {
          break; // We've fully allocated this item
        }
      }
    }
  }

    if(!empty($update_allocation)) {
      $this->db->update_batch('procurement_item_allocations_staff', $update_allocation, 'id');
    }

    // vendor return
    $vendor_return= $this->db->select("id, proc_invoice_items_id, return_quantity")->where_in('proc_invoice_items_id', $all_invoices_ids_arr)->get('procurement_delivery_challan_return_items')->result();
    if(!empty($vendor_return)) {
      foreach($vendor_return as $key => $val) {

        $isSettled= false;
        foreach($invoices_obj as $invItemId => $invQty) {
          if($invoices_obj->$invItemId >= $val->return_quantity) {
            $isSettled= true;
            $update_vendor_return[]= array(
              'id' => $val->id,
              'proc_invoice_items_id' => $invItemId
            );
            $invoices_obj->$invItemId = $invoices_obj->$invItemId - $val->return_quantity;
            break;
          }
        }
        if(!$isSettled) {
          foreach($invoices_obj as $invItemId => $invQty) {
            if($invoices_obj->$invItemId > 0) {
              $isSettled= true;
              $update_vendor_return[]= array(
                'id' => $val->id,
                'proc_invoice_items_id' => $invItemId,
                'return_quantity' => $invoices_obj->$invItemId
              );
              $invoices_obj->$invItemId = 0;
              break;
            }
          }
        }

      }
    }
    if(!empty($update_vendor_return)) {
      $this->db->update_batch('procurement_delivery_challan_return_items', $update_vendor_return, 'id');
    }

    if(!empty($invoices_obj)) {
      foreach($invoices_obj as $key => $val) {
        $invoices_update[]= array(
          'id' => $key,
          'current_quantity' => $invoices_obj->$key
        );
      }
      if(!empty($invoices_update)) {
        $this->db->update_batch('procurement_delivery_challan_items', $invoices_update, 'id');
      }
    }
    
    return true;
  }

  function manage_initial_quantity_of_return_collect_and_delete_before_reconcile($item_id, $sales_year_id) {

    $invoicesReturn= $this->db->select("pii.id, pii.initial_quantity, pii.current_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where_in('pim.dc_type', ['Normal Return']) 
      ->order_by('pii.id', 'asc')
      ->get()->result();
    $returnMasters= $this->db->select("psr.id, psr.return_quantity, psr.proc_invoice_items_id")
      ->from('procurement_sales_master psm')
      ->join('procurement_sales_transactions pst', 'pst.sales_master_id = psm.id')
      ->where('pst.sales_year_id', $sales_year_id)
      ->where('pst.proc_im_items_id', $item_id)
      ->join('procurement_sales_return psr', 'psr.proc_sales_master_id = psm.id')
      ->where('psr.proc_im_items_id', $item_id) 
      ->order_by('id', 'asc')
      ->get()->result();
      // DO not match procImItemsId
      if(!empty($invoicesReturn)) {
        foreach($invoicesReturn as $key => $val) {
          if(!empty($returnMasters) && isset($returnMasters[$key])) {
            $update_invoice_items[]= array(
              'id' => $val->id,
              'initial_quantity' => $returnMasters[$key]->return_quantity,
              'current_quantity' => $returnMasters[$key]->return_quantity
            );
          }
        }
      }

    $invoicesDelete= $this->db->select("pii.id, pii.initial_quantity, pii.current_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where_in('pim.dc_type', ['Normal Delete']) 
      ->order_by('pii.id', 'asc')
      ->get()->result();
    $deleteMasters= $this->db->select("pst.id, pst.quantity, pst.proc_invoice_items_id")
      ->from('procurement_sales_master psm')
      ->join('procurement_sales_transactions pst', 'pst.sales_master_id = psm.id')
      ->where('psm.soft_delete', 1) 
      ->where('pst.proc_im_items_id', $item_id) 
      ->where('pst.sales_year_id', $sales_year_id)
      ->order_by('pst.id', 'asc')
      ->get()->result();
      // DO not match procImItemsId
      if(!empty($invoicesReturn)) {
        foreach($invoicesReturn as $key => $val) {
          if(!empty($deleteMasters) && isset($deleteMasters[$key])) {
            $update_invoice_items[]= array(
              'id' => $val->id,
              'initial_quantity' => $deleteMasters[$key]->quantity,
              'current_quantity' => $deleteMasters[$key]->quantity
            );
          }
        }
      }

    $invoicesCollect= $this->db->select("pii.id, pii.initial_quantity, pii.current_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where_in('pim.dc_type', ['Normal Collect']) 
      ->where('pii.is_closed', 1)
      ->order_by('pii.id', 'asc')
      ->get()->result();

      $matchAllocatedAndCollected= $this->db->select("pias.id, pias.allocated_quantity, pias.proc_invoice_items_id, pias.collected_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->join('procurement_item_allocations_staff pias', 'pias.proc_invoice_items_id = pii.id')
      ->where('pias.proc_im_items_id', $item_id)
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->order_by('pias.id', 'asc')
      ->get()->result();
    if(!empty($matchAllocatedAndCollected)) {
      foreach($matchAllocatedAndCollected as $key => $val) {
        if($val->allocated_quantity < $val->collected_quantity) {
          $macthAlloCollInStaffAllocation[]= array(
            'id' => $val->id,
            'allocated_quantity' => $val->collected_quantity
          );
        }
      }
    }

      // echo '<pre>'; print_r($invoicesCollect); die();  
      $ccollectMasters= $this->db->select("pias.id, pias.allocated_quantity, pias.proc_invoice_items_id, pias.collected_quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->join('procurement_item_allocations_staff pias', 'pias.proc_invoice_items_id = pii.id')
      ->where('pias.proc_im_items_id', $item_id)
      ->where('pii.proc_im_items_id', $item_id)
      ->where('pim.sales_year_id', $sales_year_id)
      ->where('pias.collected_quantity is not null and pias.collected_quantity > 0')
      ->order_by('pias.id', 'asc')
      ->get()->result();

      // DO not match procImItemsId
      if(!empty($invoicesReturn)) {
        foreach($invoicesReturn as $key => $val) {
          if(!empty($ccollectMasters) && isset($ccollectMasters[$key])) {
            $update_invoice_items[]= array(
              'id' => $val->id,
              'initial_quantity' => $ccollectMasters[$key]->collected_quantity,
              'current_quantity' => $ccollectMasters[$key]->collected_quantity
            );
          }
        }
      }

      $this->db->trans_start();
      if(!empty($update_invoice_items)) {
        $this->db->update_batch('procurement_delivery_challan_items', $update_invoice_items, 'id');
      }
      if(!empty($macthAlloCollInStaffAllocation)) {
        $this->db->update_batch('procurement_item_allocations_staff', $macthAlloCollInStaffAllocation, 'id');
      }
      $this->db->trans_complete();

    return $this->db->trans_status();
  }













  public function get_total_current_sold_return_vendorReturn_allocated_collected_and_deleted_quantity($variant_id, $sales_year_id) {

// Initial transaction status
    $added = $this->db_readonly->select("item.initial_quantity as quantity")
    ->from('procurement_itemmaster_items pv')
    ->join('procurement_delivery_challan_items item',"item.proc_im_items_id=pv.id")
    ->join('procurement_delivery_challan_master pInvMaster',"item.invoice_master_id=pInvMaster.id")
    ->where('pv.id', $variant_id)
    ->where('pInvMaster.dc_type', 'Opening Balance');
    
   if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
    $this->db_readonly->where('pInvMaster.sales_year_id', $sales_year_id);
  }
    $added = $this->db_readonly->get()->result();

    $data['Initial'] = 0;
    if(!empty($added)) {
      foreach ($added as $key => $val) {
        $data['Initial'] += $val->quantity;
      }
    }

// Purchased transaction status
    $this->db_readonly->select("item.initial_quantity as quantity")
              ->from('procurement_itemmaster_items pv')
              ->join('procurement_delivery_challan_items item','item.proc_im_items_id=pv.id')
              ->join('procurement_delivery_challan_master im', 'item.invoice_master_id=im.id')
              ->where('pv.id', $variant_id)
              ->where('im.dc_type =', 'Normal Purchase');
              
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('im.sales_year_id', $sales_year_id);
    }
    $invoices = $this->db_readonly->get()->result();

    $data['Purchased'] = 0;
    if(!empty($invoices)) {
      foreach ($invoices as $key => $val) {
        $data['Purchased'] += $val->quantity;
      }
    }

// Allocated to staff transaction status
    $this->db_readonly->select("vs.allocated_quantity as quantity")
              ->from('procurement_itemmaster_items pv')
              ->join('procurement_item_allocations_staff vs','vs.proc_im_items_id=pv.id')
              ->where('pv.id', $variant_id);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('vs.sales_year_id', $sales_year_id);
    }
    $allocated = $this->db_readonly->get()->result();

    $data['allocatedTotal'] = 0;
    if(!empty($allocated)) {
      foreach ($allocated as $key => $val) {
        $data['allocatedTotal'] += $val->quantity;
      }
    }

    // Collected from staff transaction status
    $this->db_readonly->select("pics.returned_quantity as quantity, pics.return_type as is_stockable")
              ->from('procurement_item_collections_staff pics')
              ->join('procurement_item_allocations_staff pias','pics.item_allocations_staff_id=pias.id')
              ->join('procurement_itemmaster_items pii', 'pii.id= pias.proc_im_items_id')
              ->where('pias.proc_im_items_id', $variant_id);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('pias.sales_year_id', $sales_year_id);
    }
    $collected = $this->db_readonly->get()->result();

    $data['collectedTotal'] = 0;
    $data['collectedDamagedTotal'] = 0;
    $data['collectedNonStockableTotal'] = 0;
    if(!empty($collected)) {
      foreach ($collected as $key => $val) {
        if($val->is_stockable == '1') {
          $data['collectedTotal'] += $val->quantity;
        } else if($val->is_stockable == '2') {
          $data['collectedDamagedTotal'] += $val->quantity;
        }
         else {
          $data['collectedNonStockableTotal'] += $val->quantity;
        }
      }
    }

// Sold to student transaction status
    // getting item name
    $this->db_readonly->select("pst.quantity, psm.student_id, '1' as is_stockable")
              ->from('procurement_sales_transactions pst')
              ->join('procurement_sales_master psm','psm.id= pst.sales_master_id')
              ->where('pst.proc_im_items_id', $variant_id);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('pst.sales_year_id', $sales_year_id);
    }
    $sold = $this->db_readonly->get()->result();


    $data['soldTotal'] = 0;
    if(!empty($sold)) {
      foreach ($sold as $key => $val) {
        $data['soldTotal'] += $val->quantity;
      }
    }

// Item returned transaction status
    $return= $this->db_readonly->select("return_quantity as quantity")
            ->where('proc_im_items_id', $variant_id);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('sales_year_id', $sales_year_id);
    }
    $return= $this->db_readonly->get('procurement_sales_return')->result();

    $data['returnedTotal'] = 0;
    if(!empty($return)) {
      foreach ($return as $key => $val) {
        $data['returnedTotal'] += $val->quantity;
      }
    }

// Soft deleted transaction status
    $softDeleted = $this->db_readonly->select("pst.quantity as quantity")
      ->from('procurement_sales_transactions pst')
      ->join('procurement_sales_master psm', 'psm.id= pst.sales_master_id')
      ->join('avatar a', 'a.id= psm.soft_delete_by')
      ->join('student_admission sa', 'sa.id= psm.student_id')
      ->where('pst.proc_im_items_id', $variant_id)
      ->where('psm.soft_delete', 1);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('pst.sales_year_id', $sales_year_id);
    }
    $softDeleted = $this->db_readonly->get()->result();

    $data['softDeletedItems'] = 0;
    if(!empty($softDeleted)) {
      foreach ($softDeleted as $key => $val) {
        $data['softDeletedItems'] += $val->quantity;
      }
    }

    // Vendor return transaction status
    $vendorReturned = $this->db_readonly->select("pvri.return_quantity as quantity")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pInvI', 'pim.id= pInvI.invoice_master_id')
      ->join('procurement_delivery_challan_return_items pvri', 'pvri.proc_invoice_items_id= pInvI.id')
      ->join('procurement_delivery_challan_return_master pvrm', 'pvrm.id= pvri.vendor_return_master_id')
      ->join('staff_master sm', 'sm.id= pvrm.created_by', 'left')
      ->where('pInvI.proc_im_items_id', $variant_id);
    if($sales_year_id && $sales_year_id != 0 && $sales_year_id != '') {
      $this->db_readonly->where('pim.sales_year_id', $sales_year_id);
    }
    $vendorReturned = $this->db_readonly->get()->result();

    $data['vendorReturnedItems'] = 0;
    if(!empty($vendorReturned)) {
      foreach ($vendorReturned as $key => $val) {
        $data['vendorReturnedItems'] += $val->quantity;
      }
    }

    return $data;
  }

  function getReconciliationStatusOfAnItem() {
    $input= $this->input->post();
    $sales_year_id= $input['sales_year_id'];
    $item_id= $input['item_id'];
    
        $obj= new stdClass();

        $obj->item_id= $item_id;

        $calculationData= $this->get_total_current_sold_return_vendorReturn_allocated_collected_and_deleted_quantity($item_id, $sales_year_id);

        $obj->total= intval($calculationData['Initial']) + intval($calculationData['Purchased']);
        $obj->sold= intval($calculationData['soldTotal']);
        $obj->allocated= intval($calculationData['allocatedTotal']);
        $obj->collected= intval($calculationData['collectedTotal']) + intval($calculationData['collectedDamagedTotal']);
        $obj->vendorReturns= intval($calculationData['vendorReturnedItems']);
        $obj->salesReturn= intval($calculationData['returnedTotal']);
        $obj->softDeletedItems= intval($calculationData['softDeletedItems']);
        
        $current= $this->db_readonly->select("ifnull(sum(ifnull(current_quantity, 0)), 0) as total, group_concat(ifnull(pii.id, 0)) as proc_invoice_iems_ids, pii.proc_im_items_id")
            ->from('procurement_delivery_challan_master pim')
            ->join('procurement_delivery_challan_items pii','pii.invoice_master_id=pim.id')
            ->where('pim.sales_year_id', $sales_year_id)
            ->where('pii.proc_im_items_id', $item_id)
            ->group_by('pii.proc_im_items_id')
            ->get()->row();
        $obj->current= $current->total;

        $obj->derived= intval($obj->total) - intval($obj->sold) - intval($obj->allocated) - intval($obj->vendorReturns) + intval($obj->collected) + intval($obj->softDeletedItems) + intval($obj->salesReturn); 

    return $obj;
  }

  function map_with_expense_subcategory($input) {
    $this->db->where('id', $input['subcategory_id']);
    $this->db->update('procurement_itemmaster_subcategory', ['expense_sub_category_id' => $input['expense_subcategory_id']]);
    return true;
  }

  function get_not_stockable_report() {
    $input= $this->input->post();
    $category_id= $input['category_id'];
    $sales_year_id= $input['sales_year_id'];
    $item_type= $input['item_type'];

    $this->db_readonly->select("pii.id, pii.item_name, pis.subcategory_name, pic.category_name, sum(returned_quantity) as returned_quantity");
    $this->db_readonly->from('procurement_item_collections_staff pics');
    $this->db_readonly->join('procurement_item_allocations_staff pias', 'pias.id = pics.item_allocations_staff_id');
    $this->db_readonly->join('procurement_itemmaster_items pii', 'pii.id = pias.proc_im_items_id');
    $this->db_readonly->join('procurement_itemmaster_subcategory pis', 'pis.id = pii.proc_im_subcategory_id');
    $this->db_readonly->join('procurement_itemmaster_category pic', 'pic.id = pis.proc_im_category_id');
    $this->db_readonly->where('pics.return_type', $item_type);
    if($sales_year_id != 'All') {
      $this->db_readonly->where('pics.sales_year_id', $sales_year_id);
    }
    if($category_id != 'All') {
      $this->db_readonly->where('pic.id', $category_id);
    }
    $this->db_readonly->group_by('pii.id, pii.item_name');
    $this->db_readonly->order_by('pic.category_name, pis.subcategory_name, pii.item_name');
   $data= $this->db_readonly->get()->result();

  //  echo '<pre>'; print_r($this->db_readonly->last_query($data)); die();
   return $data;
  }

  public function getItemMasterSummary($payload, $chunk_size = 1000, $offset = 0){
    // $fromDate = $payload['fromDate'];
    // $toDate = $payload['toDate'];

    $this->db_readonly->select("
    pic.category_name AS `item_category`,
    pis.subcategory_name AS `item_sub_category`,
    pii.item_name AS `item_name`,
    CASE
        WHEN pic.status = 0 OR pis.status = 0 OR pii.status = 0 THEN 'No'
        ELSE 'Yes'
    END AS `is_active`,
    CASE
        WHEN pii.sku_code IS NOT NULL
             AND TRIM(pii.sku_code) != ''
             AND pii.sku_code != 'Not Added'
        THEN pii.sku_code
        ELSE '-'
    END AS `sku_code`,
    CASE
        WHEN pii.unit_type IS NOT NULL
            AND TRIM(pii.unit_type) != ''
            AND pii.unit_type != 'Not Provided'
        THEN pii.unit_type
        ELSE 'Unitless'
    END AS `unit_type`,
    IFNULL(ec.category_name, '-') AS `expense_category`,
    IFNULL(es.sub_category, '-') AS `expense_sub_category`,
    IFNULL(sd.id, '-') AS `department_id`,
    IFNULL(sd.department, '-') AS `department_name`,
    IFNULL(sd.approver_1, '-') AS `approver_1`,
    IFNULL(sd.approver_2, '-') AS `approver_2`,
    IFNULL(sd.approver_3, '-') AS `approver_3`,
    case
      when sd.approval_algorithm = 1 then 'Sigle level approval with financial approver'
      when sd.approval_algorithm = 2 then 'Two level approval with financial approver'
      when sd.approval_algorithm = 3 then 'Three level approval with financial approver'
      when sd.approval_algorithm = 4 then 'Three level (Amount Based) approval with financial approver'
      else '-'
    end as approval_algorithm_name,
    ifnull(concat(sm1.first_name, ' ', ifnull(sm1.last_name, '')), '-') as approver_1_name,
    ifnull(concat(sm2.first_name, ' ', ifnull(sm2.last_name, '')), '-') as approver_2_name,
    ifnull(concat(sm3.first_name, ' ', ifnull(sm3.last_name, '')), '-') as approver_3_name
  ", FALSE) // FALSE to avoid escaping SQL keywords
    ->from('procurement_itemmaster_category AS pic')
    ->join('procurement_itemmaster_subcategory AS pis', 'pis.proc_im_category_id = pic.id', 'inner')
    ->join('procurement_itemmaster_items AS pii', 'pii.proc_im_subcategory_id = pis.id', 'inner')
    ->join('expense_sub_category AS es', 'es.id = pis.expense_sub_category_id', 'left')
    ->join('expense_category AS ec', 'ec.id = es.cat_id', 'left')
    ->join('staff_departments AS sd', 'ec.department_id = sd.id', 'left')
    ->join('staff_master AS sm1', 'sm1.id = sd.approver_1', 'left')
    ->join('staff_master AS sm2', 'sm2.id = sd.approver_2', 'left')
    ->join('staff_master AS sm3', 'sm3.id = sd.approver_3', 'left');

    $this->_applyItemMasterFilters($payload);

    $this->db_readonly->order_by('pii.id', 'DESC');

    // Add chunking
    $this->db_readonly->limit($chunk_size, $offset);

    $query = $this->db_readonly->get();
    return $query->result();
  }

  public function getItemMasterSummaryCount($payload) {
    $this->db_readonly->select('COUNT(*) as total_count')
      ->from('procurement_itemmaster_category AS pic')
      ->join('procurement_itemmaster_subcategory AS pis', 'pis.proc_im_category_id = pic.id', 'inner')
      ->join('procurement_itemmaster_items AS pii', 'pii.proc_im_subcategory_id = pis.id', 'inner')
      ->join('expense_sub_category AS es', 'es.id = pis.expense_sub_category_id', 'left')
      ->join('expense_category AS ec', 'ec.id = es.cat_id', 'left')
      ->join('staff_departments AS sd', 'ec.department_id = sd.id', 'left');

    $this->_applyItemMasterFilters($payload);

    $query = $this->db_readonly->get();
    $result = $query->row();
    return $result ? $result->total_count : 0;
  }

  private function _applyItemMasterFilters($payload) {
    $itemMasterCategory = $payload['itemMasterCategory'];
    $itemMasterSubCategory = $payload['itemMasterSubCategory'];
    $expenseCategory = $payload['expenseCategory'];
    $expenseSubCategory = $payload['expenseSubCategory'];

    // if ($fromDate && $toDate) {
    //   $this->db_readonly->where('date_format(pic.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate. '"');
    // }

    if ($itemMasterCategory && $itemMasterCategory != 'All') {
      $this->db_readonly->where('pic.id', $itemMasterCategory);
    }

    if ($itemMasterSubCategory && $itemMasterSubCategory != 'All') {
      $this->db_readonly->where('pis.id', $itemMasterSubCategory);
    }

    if ($expenseCategory && $expenseCategory != 'All') {
      $this->db_readonly->where('ec.id', $expenseCategory);
    }

    if ($expenseSubCategory && $expenseSubCategory != 'All') {
      $this->db_readonly->where('es.id', $expenseSubCategory);
    }
  }

  // get item master sub category
  public function getItemMasterSubCategory() {
    return $this->db_readonly->select("id, subcategory_name")
    ->from('procurement_itemmaster_subcategory')
    ->order_by('subcategory_name', 'ASC')
    ->get()->result();
  }

  // get item master sub categories by category
  public function getItemMasterSubCategoriesByCategory($category_id) {
    if (!$category_id || $category_id === 'All') {
      return $this->getItemMasterSubCategory();
    }

    return $this->db_readonly->select("id, subcategory_name")
    ->from('procurement_itemmaster_subcategory')
    ->where('proc_im_category_id', $category_id)
    ->order_by('subcategory_name', 'ASC')
    ->get()->result();
  }

  // get expense category
  public function getExpenseCategory() {
    return $this->db_readonly->select("id, category_name")
    ->from('expense_category')
    ->order_by('category_name', 'ASC')
    ->get()->result();
  }

  // get expense sub category
  public function getExpenseSubCategory() {
    return $this->db_readonly->select("id, sub_category")
    ->from('expense_sub_category')
    ->order_by('sub_category', 'ASC')
    ->get()->result();
  }

  // get expense sub categories by category
  public function getExpenseSubCategoriesByCategory($category_id) {
    if (!$category_id || $category_id === 'All') {
      return $this->getExpenseSubCategory();
    }

    return $this->db_readonly->select("id, sub_category")
    ->from('expense_sub_category')
    ->where('cat_id', $category_id)
    ->order_by('sub_category', 'ASC')
    ->get()->result();
  }

}
?>
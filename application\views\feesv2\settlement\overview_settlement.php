<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard') ?>">Fee Dashboard</a></li>
  <li class="active">Overivew Online Payment Settlement report</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
          <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
            <span class="fa fa-arrow-left"></span>
          </a> 
         Online Payment Settlement Report
        </h3>
        <!-- <div class="col-md-8 d-flex align-items-center justify-content-end" id="btns_display" style="display: none !important;">
          <button id="stu_print" class="new_circleShape_res" style="margin-left: 8px; background-color: #fe970a;" data-placement="top" data-toggle="tooltip" title="Print" data-original-title="Print" onClick="printProfile();"><span class="fa fa-print" aria-hidden="true"></span></button>
          <a onclick="exportToExcel()" class="new_circleShape_res mr-2" style="margin-left: 8px; background-color: #fe970a;cursor: pointer;" data-placement="top" data-toggle="tooltip" title="Export" data-original-title="Export"><i class="fa fa-file-excel-o"></i></a>
          <ul class="panel-controls loading-icon" style="display: none;"><i class="fa fa-spinner fa-spin" style="font-size: 24px;"></i></ul>
        </div>  -->
       </div>
     </div>
    </div>

    <div class="card-body">
      <div class="col-md-2 form-group">
        <p>Date Range</p>
        <div id="reportrange" class="dtrange" style="width: 100%">                                            
          <span></span>
            <input type="hidden" id="from_date">
            <input type="hidden" id="to_date">
        </div>
      </div>

      <div class="col-sm-2 col-md-2">
        <p style="margin-top: 2.5rem"></p>
        <input type="button" name="search" id="search" onclick="get_overview_settelment()" class="btn btn-primary" value="Get Report">
      </div>
    </div>

    <div class="card-body">
      <div class="text-center mt-2">
        <div style="display: none;" class="progress" id="progress">
          <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
        </div>
      </div>
      <div class="row" style="margin: 0px;display: block;">
        <div id="settle" class='panel-title'></div>
        <div id="datatable_controls" style="margin-bottom: 15px;"></div>
        <div id="settlement_table" style="max-height: 70vh; overflow-y: auto;"></div>
        <div id="datatable_pagination" style="margin-top: 15px;"></div>
      </div>

    </div>
  </div>

</div>

<!--- Verification Settlement Dialog box --->
<div id="_verify_settlement_modal" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-x1" style="width:40%;margin:auto;">
    <div class="modal-content" style="border-radius: 8px;overflow-x:auto;max-height: 40vh;">

      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title">Verify Settlement</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>

      <div class="modal-body">
        <div class="col-md-12">
          <label class="control-label">settlement id</label><h6 id='verification_settlement_id_title'>XXXX</h6>

          <input type="hidden" id="verification_settlement_id" name="verification_settlement_id" value=''>
          <label for="verification_comment" class="control-label">enter comments (if any)</label>
          <textarea rows=3 placeholder="Enter Comment" id="verification_comment" name="verification_comment" class="form-control"></textarea>
          <br>
        </div>
      </div>

      <div class="modal-footer">
        <div class="col-md-12">
          <button class="btn btn-primary" id="verify_settlement_button" type="button" onclick="submit_verification()">Verify Settlement</button>
        </div>
      </div>

    </div>
  </div>
</div>

<!--- Settlement Details Dialog box --->
<div id="_show_settlement_details" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-x1" style="width:75%;margin:auto;">
    <div class="modal-content" style="border-radius: 8px;">

      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader">Settlement Details of <span id='settlement_id_title'>XXXX</span></h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="action-buttons" style="position: sticky; top: 0; background: white; z-index: 1000; padding: 10px 0; border-bottom: 1px solid #eee;">
        <a class="control-primary pull-right" href="javascript:void(0)" id="printBtn" onclick="printProfile()">
          <span class="btn btn-warning" > Print </span>
        </a>
        <a class="control-primary pull-right" href="javascript:void(0)" id="printExcelBtn" onclick="exportToExcel_daily()">
          <span class="btn btn-info" > Excel </span>
        </a>
      </div>

      <div class="modal-body" style="border-radius: 8px;overflow-x:auto;max-height: 90vh;min-height: 75vh;">
       
        
        <div class="content-scrollable" style="max-height: calc(90vh - 80px); overflow-y: auto;">
          <div id='settlement_match_div'>
          </div>

          <div id='settlement_summary_div'>       
          </div>
          <br>
          
          <div class="card">
            <div class="card-header sticky-header" style="position: sticky; top: 0; background: white; z-index: 999; border-bottom: 2px solid #dee2e6;">
              <h5>Details of Receipts Raised</h5>
            </div>
            <div class="card-body" style="overflow-y:auto;height:55vh;">
              <div id='tx_split_summary_div'>
              </div>
              <div id='tx_table_div'>            
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<style type="text/css">
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}
</style>

<script>
  function submit_verification() {
    settlement_id = $('#verification_settlement_id').val();
    settlement_comment = $('#verification_comment').val();

    $.ajax({
      url:'<?php echo site_url('payment_controller/submit_settlement_verification') ?>',
      type:'post',
      data : {'settlement_id': settlement_id, 'settlement_comment': settlement_comment},
      success : function(data) {
        new PNotify({
          title: 'Success',
          text: 'Verification Noted',
          type: 'success',
        });
      },
      error: function(err) {
        console.log(err);
      },
      complete: function () {
        $("#_verify_settlement_modal").modal('hide');
        get_overview_settelment();
      }
    });
  }

</script>

<script type="text/javascript">
  $("#reportrange").daterangepicker({
    ranges: {
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'DD.MM.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().subtract(6, 'days'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));
</script>


<script type="text/javascript">

   $(document).ready(function() {
    $('#_verify_settlement_modal').on('show.bs.modal', function (event) {
      //Get the settlement_id
      var settlement_id = $(event.relatedTarget).data('settlement_id');

      //Initialize all the fields for creating a new subject
      $(this)
        .find("input[type=hidden],input[type=text],textarea")
        .val('')
        .end();

      $(this).find('.modal-body #verification_settlement_id').val(settlement_id);
      $(this).find('.modal-body #verification_settlement_id_title').html(settlement_id);
    });
  });

  function show_settlement_details(settlement_id, settlement_datetime, account_name, bank_reference, payout_amount) {
    var loading_icon_html = `<div class="col-12 text-center loading-icon" style="display: show;"><i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i></div>`;
    $('#settlement_id_title').html(settlement_id);
    $('#settlement_summary_div').html(loading_icon_html);
    $('#tx_table_div').html(loading_icon_html);
    $('#settlement_match_div').html('');
    $('#tx_split_summary_div').html('');

    $.ajax({
        url:'<?php echo site_url('payment_controller/get_settlement_details') ?>',
        type:'post',
        data : {'settlement_id': settlement_id},
        success : function(data){
          var data = JSON.parse(data);
          var transactions = data.tx_details_se;
          var vendor_data = data.vendor_data;
          $('#settlement_match_div').html(_build_settlement_match_summary(payout_amount, transactions));
          $('#settlement_summary_div').html(_build_settlement_summary(settlement_id, settlement_datetime, account_name, bank_reference, payout_amount));
          $('#tx_split_summary_div').html(_build_split_summary_table(vendor_data));
          $('#tx_table_div').html(_build_tx_table(transactions));
        },
        error: function(err) {
          console.log(err);
        }
      });
  }

  function _build_settlement_match_summary(payout_amount, transactions_data) {
    var sum_tx_amount = 0;
    transactions_data.forEach(tx => {
      //Add only if the receipt number is generated
      if (tx.receipt_number) {
        sum_tx_amount += parseInt(tx.amount_paid || 0);
      }
    });

    var difference = parseInt(payout_amount) - sum_tx_amount;
    var difference_html = '';
    if (difference == 0) {
      difference_html = `<font color="green">SETTLEMENTS MATCH!</font>`;
    } else if (difference < 0) {
      difference_html = `<font color="red">LESS AMOUNT SETTLED</font>`;
    } else {
      difference_html = `<font color="red">MORE AMOUNT SETTLED</font>`;
    }

    var output_html = `
      <table class="table table-bordered" style="font-size:16px;background:#dcdce6;">
        <thead>
          <tr>
            <th colspan=4>SETTLEMENT MATCH SUMMARY
            </th>
          </tr>
        </thead>
        <tr>
          <td>settlement amount</td><td><strong>${numberToCurrency(payout_amount)}</strong></td>
          <td>receipt amount</td><td><strong>${numberToCurrency(sum_tx_amount)}</strong></td>
        </tr>
        <tfoot>
          <tr>
            <td colspan=2><strong>Difference: ${numberToCurrency(difference)}</strong></td>
            <td colspan=2><strong>${difference_html}</strong></td>
          </tr>
        </tfoot>
      </table>
    `;

    return output_html;
  }

  function _build_split_summary_table(vendor_data) {
    if (vendor_data.length == 0) return '';
    var html_output = `
      <table class="table table-bordered" width="75%">
        <thead>
          <th colspan=3>Expected settlement based on Receipts</th>
        </thead>
        <thead>
          <th>Code</th>
          <th>Name</th>
          <th>Amount</th>
        </thead>
      `;

    vendor_data.forEach(vendor => {
      html_output += `
        <tr>
          <td>${vendor.vendor_code}</td>
          <td>${vendor.vendor_name}</td>
          <td>${numberToCurrency(vendor.vendor_amount)}</td>
        </tr>
      `;
    });

    html_output += `</table>`;

    return html_output;
  }

  function _build_settlement_summary(settlement_id, settlement_datetime, account_name, bank_reference, payout_amount) {
    var html = `
      <table class="table table-bordered" style="width:100%">
        <thead>
          <th colspan=6>Settlement Details</th>
        </thead>
        <tr>
          <td width="10%">settlement id</td>
          <td width="20%"><strong>${settlement_id}</strong></td>
          <td width="10%">settlement date</td>
          <td width="20%"><strong>${settlement_datetime}</strong></td>
          <td width="10%">account name</td>
          <td width="30%"><strong>${account_name}</strong></td>
        </tr>
        <tr>
          <td>bank reference</td>
          <td><strong>${(bank_reference)?bank_reference:'Not Processed'}</strong></td>
          <td colspan=2 style="background-color:#dcdce6">settlement amount</td>
          <td colspan=2 style="background-color:#dcdce6"><strong>${numberToCurrency(payout_amount)}</strong></td>
        </tr>
      </table>
    `;

    return html;
  }

  function _build_tx_table(transactions_data) {
    if (transactions_data.length == 0) {
      var html = `<h4>No Transactions</h4>`;
      return html;
    }

    var html = `
      <table class="table table-bordered">
        <thead>
          <th width="5%">#</th>
          <th width="10%">Payment Type</th>
          <th width="10%">Receipt Number</th>
          <th width="25%">Student Name</th>
          <th width="25%">Admission No.</th>
          <th width="25%">Enrollment No.</th>
          <th width="20%">Paid Date</th>
          <th width="15%">Amount</th>
          <th width="15%">Split JSON</th>
          <th width="25%">Order ID</th>
          <th width="25%">Tx ID</th>
          <th width="25%">Actions</th>
        </thead>`;

    var sl_no = 1;
    var sum_tx_amount = 0;
    transactions_data.forEach(tx => {
      var generate_receipt_btn_html = '';
      //Add only if the receipt number is generated
      if (tx.receipt_number) {
        var url = '<?php echo base_url() ?>feesv2/fees_collection/receipt_pdf_download/'+tx.trans_id;
        generate_receipt_btn_html = 'Receipt Generated <br><br><a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px;background-color: #31b0d5; color:white;" data-placement="top" data-toggle="tooltip" data-original-title="Download PDF " href="'+url+'"><i class="fa fa-cloud-download"></i></a>';
        sum_tx_amount += parseInt(tx.amount_paid || 0);
        
        var receipt_url = '<?php echo base_url() ?>feesv2/fees_collection/fee_reciept_viewv1/'+tx.trans_id;

      } else {
        generate_receipt_btn_html = `<button class="btn btn-info" onclick="generate_receipt('${tx.order_id}')">Generate Receipt</button>`;
      }

      html += `
        <tr>
          <td>${sl_no++}</td>
          <td>${tx.fees_type}</td>
          <td><a target="_blank" data-toggle="print receipt" data-original-title="Print Receipt" href="${receipt_url}">${tx.receipt_number}</a> </td>
          <td>${tx.student_name} (${tx.section_name})</td>
          <td>${tx.admission_no ? tx.admission_no : ''}</td>
          <td>${tx.enrollment_number ? tx.enrollment_number : ''}</td>
          <td>${tx.paid_datetime}</td>
          <td>${numberToCurrency(tx.amount_paid)}</td>
          <td>${_build_split_json_small_table(tx.split_objs)}</td>
          <td>${tx.order_id}</td>
          <td>${tx.tx_id}</td>
          <td>${generate_receipt_btn_html}</td>
        </tr>`;
    });

    html += `
      <tr>
      <td colspan=5 style="background-color:#dcdce6;font-weight:700;font-size=14;">total receipt collection</td>
      <td colspan=4 style="background-color:#dcdce6;font-weight:700;font-size=14;">${numberToCurrency(sum_tx_amount)}</td>
      </tr>
    `;

    html += `</table>`;

    return html;
  }

  function _build_split_json_small_table(split_objs) {
    if (!split_objs) return 'NA';

    var html = `
      <table class="table table-bordered">`;

    var sl_no = 1;
    var sum_tx_amount = 0;
    split_objs.forEach(obj => {
      html += `
        <tr>
          <td><small>${obj.vendor_name}</small></td>
          <td><small>${numberToCurrency(obj.vendor_amount)}</small></td>
        </tr>`;
    });

    html += `</table>`;

    return html;
  }

  function generate_receipt(order_id) {
    alert(order_id);
  }

  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      currency: 'INR',
    });
    return formatter.format(amount);
  }


 function get_overview_settelment(){
  $('#search').prop('disabled',true).val('Please wait...'); 
  $('#progress').show();
  $('#progress-ind').css('width', '50%').attr('aria-valuenow', 50); 
    $("#btns_display").hide();
    $('#settlement_table').html('');

    var fromdate = $("#from_date").val();
    var toDate = $("#to_date").val();

    // var input_array = input.split(",");
    // var date = input_array[0];
    // var verify = input_array[1];
    // var conformed_by = input_array[2];
    // var conformed_on = input_array[3];

    $('#trans').html('<h4><strong>Transactions on From ' + fromdate + ' to '+toDate+'</strong></h4>');
    $("#confirmBtn").hide();
    $("#confirmed").hide();
    $.ajax({
        url:'<?php echo site_url('payment_controller/get_overview_transactions') ?>',
        type:'post',
        data : {'fromdate':fromdate,'toDate':toDate},
        success : function(data){
            var data = JSON.parse(data);
            if(data.code == '1028') {
              $('#progress').hide();
              $('#search').prop('disabled',false).val('Get Report'); 
              $('#settle').html('<h4><strong>Settlement not started</strong></h4>');
              return false;
            }
            $("#btns_display").show();
            $('#progress-ind').css('width', '100%').attr('aria-valuenow', 100); 
            setTimeout(function() { $('#progress').hide(); }, 400);
            $('#search').prop('disabled',false).val('Get Report'); 
            var settlements = data.settlement;
            var settlement_amount = data.settlement_amount;
            var trans_amount = data.trans_amount;
            var not_settled_count = data.not_settled_count;
            var settled_count = data.settled_count;
            var isConfirmed = data.isConfirmed;

            var html  = '';
            var html1 = '<table class="table table-bordered dataTable" id="settlement_data_table">';
            if(settlements.length == 0 || data.code == '1028') {
                $('#settle').html('<h4><strong>Settlement not started</strong></h4>');
            } else {
              html1 +='<thead>';
              html1 +='<tr>';
              html1 +='<th>#</th>';
              html1 +='<th>Settlement ID</th>';
              html1 +='<th>Settlement Date</th>';
              html1 +='<th>Account Name</th>';
              html1 +='<th>Bank Reference</th>';
              html1 +='<th>Settlement Amount</th>';
              html1 +='<th>Transaction Amount</th>';
              html1 +='<th>Receipt Not Generated Amount</th>';
              html1 +='<th>Settlement Status</th>';
              html1 +='<th>Verification Status</th>';
              html1 +='<th>Actions</th>';
              html1 +='</tr>';
              html1 +='</thead>';
              html1 +='<tbody>';
              var totalTransAmount = 0;
              var totalNotGeneartedTransAmount = 0;
              for (var j = 0; j < settlements.length; j++) {
                totalTransAmount += parseFloat(settlements[j].amount_paid);
                totalNotGeneartedTransAmount += parseFloat(settlements[j].total_tax_not_generated_amount);
                var vericiation_status_style = '';
                var verification_button = '';
                var verification_details = '';
                if (settlements[j].verification_status == 'Not Verified') {
                  vericiation_status_style = "style='background-color:#fabbbb;font-weight: 700;font-size: 14px;'";
                  verification_button = `<button style="margin-top: 5px;" class="btn btn-info" data-settlement_id='${settlements[j].settlement_id}' data-target='#_verify_settlement_modal' data-toggle='modal'>Mark as Verified</button>`;
                } else {
                  vericiation_status_style = "style='background-color:#91cb91;font-weight: 700;font-size: 14px;'";
                  verification_details = `<br><small>${settlements[j].verified_by} on ${settlements[j].verified_on}</small>`;
                }
                var verification_comment = (settlements[j].remarks) ? `<br><small>(${settlements[j].remarks})</small>` : '';

                html1 += '<tr>';
                html1 += '<td>'+(j+1)+'</td>';
                html1 += '<td>'+settlements[j].settlement_id+'</td>';
                html1 += '<td>'+settlements[j].settlement_datetime+'</td>';
                html1 += '<td>'+settlements[j].account_name+'</td>';
                html1 += '<td>'+((settlements[j].bank_reference)?settlements[j].bank_reference:'Not Processed')+'</td>';
                html1 += '<td>'+settlements[j].payout_amount+'</td>';
                html1 += '<td>'+settlements[j].amount_paid+'</td>';
                html1 += '<td style="color:red">'+settlements[j].total_tax_not_generated_amount+'</td>';
                html1 += '<td>'+settlements[j].status+'</td>';
                html1 += `<td ${vericiation_status_style}>${settlements[j].verification_status}${verification_details}${verification_comment}</td>`;
                var bankReference = settlements[j].bank_reference.trim().replace(/\n/g, '').replace(/'/g, "\\'");
                html1 += `
                  <td>
                    <button class="btn btn-info" onclick="show_settlement_details('${settlements[j].settlement_id}', '${settlements[j].settlement_datetime}', '${settlements[j].account_name}', '${bankReference}', '${settlements[j].payout_amount}')" data-toggle="modal" data-target="#_show_settlement_details">View Details</button>
                    ${verification_button}
                  </td>`;
                html1 += '</tr>';
              }
              html1 +='</tbody>';
              html1 +='<tfoot>';
              html1 +='<tr>';
              html1 +='<th colspan="5">Total Amount</th>';
              html1 +='<th>'+settlement_amount+'</th>';
              html1 +='<th>'+totalTransAmount+'</th>';
              html1 +='<th>'+totalNotGeneartedTransAmount+'</th>';
              html1 +='<th></th>';
              html1 +='<th></th>';
              html1 +='<th></th>';
              html1 +='</tr>';
              html1 +='</tfoot>';
              html1 += '</table>';
              $('#settlement_table').html(html1);

              var table = $('#settlement_data_table').DataTable( {
                "language": {
                  "search": "",
                  "searchPlaceholder": "Enter Search..."
                },
                dom: 'rt',
                buttons: [
                  {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    filename: 'Settlement Report',
                    className: 'btn btn-warning',
                    exportOptions: {
                      columns: [0,1,2,3,4,5,6,7,8,9]
                    }
                  },
                  {
                    extend: 'print',
                    text: 'Print',
                    filename: 'Settlement Report',
                    autoPrint: true,
                    className: 'btn btn-info',
                    exportOptions: {
                      columns: [0,1,2,3,4,5,6,7,8,9]
                    }
                  },
                ]
              });

              // Create custom controls
              var controlsHtml = `
                <div class="row">
                  <div class="col-md-4">
                    <label>Show <select class="form-control" style="width: auto; display: inline-block;">
                      <option value="10">10</option>
                      <option value="25">25</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                    </select> entries</label>
                  </div>
                  <div class="col-md-8 text-right">
                    <div class="search-box" style="display:inline-block;">
                      <input type="search" class="input-search" placeholder="Enter Search...">
                    </div>
                    <button class="btn btn-info" style="margin-left:3px; margin-bottom: 10px; border-radius: 8px !important;" onclick="printProfile()">
                      <span class="fa fa-print" aria-hidden="true"></span> Print
                    </button>
                    <button class="btn btn-info" style="margin-left:3px; margin-bottom: 10px; border-radius: 8px !important;" onclick="exportToExcel_daily()">
                      <span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel
                    </button>
                  </div>
                </div>
              `;
              
              var paginationHtml = `
                <div class="row">
                  <div class="col-md-6">
                    <div class="dataTables_info">Showing 1 to 10 of 0 entries</div>
                  </div>
                  <div class="col-md-6 text-right">
                    <div class="dataTables_paginate paging_simple_numbers">
                      <ul class="pagination">
                        <li class="paginate_button previous disabled"><a href="#">Previous</a></li>
                        <li class="paginate_button active"><a href="#">1</a></li>
                        <li class="paginate_button next disabled"><a href="#">Next</a></li>
                      </ul>
                    </div>
                  </div>
                </div>
              `;

              $('#datatable_controls').html(controlsHtml);
              $('#datatable_pagination').html(paginationHtml);

              // Bind events to custom controls
              $('#datatable_controls select').on('change', function() {
                table.page.len($(this).val()).draw();
              });

              $('#datatable_controls input').on('keyup', function() {
                table.search($(this).val()).draw();
              });

              // Update pagination info
              table.on('draw', function() {
                var info = table.page.info();
                $('#datatable_pagination .dataTables_info').html(
                  'Showing ' + (info.start + 1) + ' to ' + info.end + ' of ' + info.recordsTotal + ' entries'
                );
              });
            }
        },
        error: function() {
            $('#progress').hide();
            $('#search').prop('disabled',false).val('Get Report'); // Reset button on error
        }
    });
}

function exportToExcel_daily() {
  var schoolName = '<?php echo $this->settings->getSetting('school_name') ?>';
  var reportTitle = 'Online Payment Settlement Report';
  var header = '<h3 style="text-align:center;">' + schoolName + '</h3><h4 style="text-align:center;">' + reportTitle + '</h4>';
  var currentDate = '<div style="text-align:center; font-size:12px; margin-bottom:10px;">Generated on: ' + new Date().toLocaleString() + '</div>';

  var tableHtml = $("#settlement_table").html();
  if (!tableHtml) {
    alert("No data to export!");
    return;
  }

  var tempDiv = document.createElement('div');
  tempDiv.innerHTML = tableHtml;
  var table = tempDiv.querySelector('table');
  if (table) {
    // Add colgroup for proper column widths
    var colgroup = document.createElement('colgroup');

    // Set widths in px to avoid ##### issues in Excel
    var widths = [
      '100px', // S.No or ID
      '150px', // Student Name or Payee
      '150px', // Payment Method
      '200px', // Transaction ID
      '150px', // Amount
      '150px', // Status
      '180px', // Settlement Date
      '120px', // Class
      '120px', // Section
      '150px'  // Any other relevant column
    ];

    for (var i = 0; i < widths.length; i++) {
      var col = document.createElement('col');
      col.style.width = widths[i];
      colgroup.appendChild(col);
    }
    table.insertBefore(colgroup, table.firstChild);

    // Apply nowrap and min-width to all table cells
    var cells = table.querySelectorAll('th, td');
    cells.forEach(cell => {
      cell.style.whiteSpace = 'nowrap';
      cell.style.minWidth = '80px';
    });

    // Remove last th from thead (Actions)
    var theads = table.querySelectorAll('thead tr');
    theads.forEach(tr => {
      if (tr.lastElementChild) tr.removeChild(tr.lastElementChild);
    });

    // Remove last td from tbody (Actions)
    var tbodys = table.querySelectorAll('tbody tr');
    tbodys.forEach(tr => {
      if (tr.lastElementChild) tr.removeChild(tr.lastElementChild);
    });

    // Remove last td from tfoot (Actions) if present
    var tfoot = table.querySelector('tfoot');
    if (tfoot) {
      Array.from(tfoot.rows).forEach(row => {
        if (row.lastElementChild) row.removeChild(row.lastElementChild);
      });
    }
  }

  // Construct full HTML
  var htmls =
    '<div>' + header + currentDate + '</div>' +
    (table ? '<div>' + table.outerHTML + '</div>' : '');

  // Excel template and download logic
  var uri = 'data:application/vnd.ms-excel;base64,';
  var template =
    '<html xmlns:o="urn:schemas-microsoft-com:office:office" ' +
    'xmlns:x="urn:schemas-microsoft-com:office:excel" ' +
    'xmlns="http://www.w3.org/TR/REC-html40">' +
    '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet>' +
    '<x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>' +
    '</x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->' +
    '<meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body>{table}</body></html>';

  var base64 = function (s) {
    return window.btoa(unescape(encodeURIComponent(s)));
  };

  var format = function (s, c) {
    return s.replace(/{(\w+)}/g, function (m, p) {
      return c[p];
    });
  };

  var ctx = {
    worksheet: 'Settlement Report',
    table: htmls
  };

  var link = document.createElement("a");
  link.download = "Settlement Report.xls";
  link.href = uri + base64(format(template, ctx));
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}


function printProfile() {
    const mainTableDiv = document.getElementById('settlement_table');
    if (!mainTableDiv) return;
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = mainTableDiv.innerHTML;
    const table = tempDiv.querySelector('table');
    if (table) {
        const theads = table.querySelectorAll('thead tr');
        theads.forEach(tr => {
            if (tr.lastElementChild) tr.removeChild(tr.lastElementChild);
        });
        const tbodys = table.querySelectorAll('tbody tr');
        tbodys.forEach(tr => {
            if (tr.lastElementChild) tr.removeChild(tr.lastElementChild);
        });
        const tfoot = table.querySelector('tfoot');
        if (tfoot) {
            const tbody = table.querySelector('tbody');
            if (tbody) {
                Array.from(tfoot.rows).forEach(row => {
                    const clonedRow = row.cloneNode(true);
                    if (clonedRow.lastElementChild) {
                        clonedRow.removeChild(clonedRow.lastElementChild);
                    }
                    tbody.appendChild(clonedRow);
                });
            }
            tfoot.parentNode.removeChild(tfoot);
        }
    }

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>Settlement Overview Report</title>
            <style>
                body {
                    font-family: 'Poppins', sans-serif;
                    padding: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    font-size: 12px;
                }
                h3 { margin: 15px 0; }
                @media print {
                    table { page-break-inside: auto }
                    tr { page-break-inside: avoid }
                }
            </style>
        </head>
        <body>
            <center><h2><?php echo $this->settings->getSetting('school_name'); ?></h2></center>
            ${tempDiv.innerHTML}
            <script>
                window.onload = function() {
                    window.print();
                };
                window.onafterprint = function() {
                    window.close();
                };
            <\/script>
        </body>
        </html>
    `);

    printWindow.document.close();
}
</script>

<style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

    table {
      font-family: 'Poppins', sans-serif !important;
    }

    #settlement_data_table {
      width: 100%;
      border-collapse: collapse;
      background-color: #ffffff;
      border-radius: 1.5rem;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
      opacity: 1 !important;
      transition: none !important;
    }

    #settlement_data_table thead th {
      position: sticky !important;
      top: 0;
      background-color: #f1f5f9;
      color: #111827;
      font-size: 11px;
      font-weight: 500;
      z-index: 10;
      text-align: left;
      padding: 12px 16px;
    }

    #settlement_data_table th,
    #settlement_data_table td {
      padding: 10px 14px;
      border-bottom: 1px solid #e5e7eb;
      font-size: 11px;
      font-weight: 400;
    }

    #settlement_data_table tbody tr:nth-child(even) {
      background-color: #f9fafb;
    }

    #settlement_data_table tbody tr:hover {
      background-color: #f1f5f9;
    }

    #settlement_data_table tfoot tr {
      position: sticky;
      bottom: 0;
      background: #f3f4f6;
      z-index: 11;
      font-weight: 500;
    }
    #settlement_data_table tfoot th,
    #settlement_data_table tfoot td {
      background: #f3f4f6;
    }

    .dataTables_scrollBody {
      margin-top: -13px;
    }

    .dataTables_filter input {
      background-color: #f2f2f2;
      border: 1px solid #ccc;
      border-radius: 4px;
      margin-right: 3vh;
    }

    .dataTables_wrapper .dt-buttons {
      float: right;
    }

    .dataTables_filter input {
      background-color: #f2f2f2;
      border: 1px solid #ccc;
      border-radius: 4px;
      margin-right: 5px;
    }

    .dataTables_wrapper .dataTables_filter {
      float: none;
      text-align: left;
      width: unset;
    }

    #datatable_controls .form-control {
      display: inline-block;
      width: auto;
    }

    #datatable_controls .btn {
      margin-left: 5px;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dt-buttons,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
      display: none !important;
    }

    .content-scrollable::-webkit-scrollbar {
      width: 12px;
    }

    .content-scrollable::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 6px;
    }

    .content-scrollable::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 6px;
    }

    .content-scrollable::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    .card-body::-webkit-scrollbar {
      width: 12px;
    }

    .card-body::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 6px;
    }

    .card-body::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 6px;
    }

    .card-body::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    .table thead th {
      position: sticky;
      top: 0;
      background: white;
      z-index: 10;
      border-bottom: 2px solid #dee2e6;
    }

    .modal-body::-webkit-scrollbar {
      width: 12px;
    }

    .modal-body::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 6px;
    }

    .modal-body::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 6px;
    }

    .modal-body::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    .content-scrollable {
      scrollbar-width: thick;
      scrollbar-color: #888 #f1f1f1;
    }

    .card-body {
      scrollbar-width: thick;
      scrollbar-color: #888 #f1f1f1;
    }

    .modal-body {
      scrollbar-width: thick;
      scrollbar-color: #888 #f1f1f1;
    }

    .search-box {
      display: inline-block;
      position: relative;
      margin-right: 2px;
      vertical-align: middle;
    }

    .input-search {
      line-height: 1.5;
      padding: 5px 10px;
      display: inline;
      width: 177px;
      height: 27px;
      background-color: #f2f2f2 !important;
      border: 1px solid #ccc !important;
      border-radius: 4px !important;
      margin-right: 0 !important;
      font-size: 14px;
      color: #495057;
      outline: none;
      margin-bottom: 10px;
    }

    .input-search::placeholder {
      color: rgba(73, 80, 87, 0.5);
      font-size: 14px;
      font-weight: 300;
    }
  </style>
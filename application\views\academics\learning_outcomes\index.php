<ul class="breadcrumb">
    <li><a href="<?php echo base_url('avatars'); ?>">Dashboard</a></li>
    <li><a href="<?php echo base_url('academics/academics_menu'); ?>">Academics</a></li>
    <li class="active">Manage Learning Outcomes</li>
</ul>

<div class="col-md-12 col_new_padding">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-6 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu') ?>"
                            class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Learning Outcomes
                    </h3>
                </div>
                <div class="col-md-6 d-flex justify-content-end align-items-center">
                    <button onclick="loadLearningOutcomes()" class="btn btn-info" id="refreshButton" title="Refresh Data" disabled style="padding: 9px 12px; border-radius: 50%"><i class="fa fa-refresh mr-0"></i></button>
                    <a href="" class="new_circleShape_res ml-2" style="background-color: #fe970a;float: right;" data-toggle="modal" data-target="#addNewLearningOutcomeModal" onclick="loadModalData()" title="Add New Learning Outcome">
                        <span class="fa fa-plus" style="font-size: 19px;"></span>
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body pt-1">
            <div class="col-md-12 d-flex align-items-center">
                <div class="col-md-2 pl-0">
                    <div class="form-group">
                        <label for="class_master_id" class="mb-0">Class</label>
                        <select class="form-control" id="class_master_id" name="class_master_id" onchange="clearSearch()">
                            <option value="">Select Class</option>
                        </select>
                        <div style="position: absolute; right: 25px; top: 66%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 pl-0">
                    <div class="form-group">
                        <label for="subject_master_id" class="mb-0">Subject</label>
                        <select class="form-control" id="subject_master_id" name="subject_master_id" onchange="clearSearch()">
                            <option value="">Select Subject</option>
                        </select>
                        <div style="position: absolute; right: 25px; top: 66%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 pl-0">
                    <div class="form-group">
                        <label for="status" class="mb-0">Status</label>
                        <select class="form-control" id="status" name="status" onchange="clearSearch()">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                            <option value="-1">All</option>
                        </select>
                        <div style="position: absolute; right: 25px; top: 66%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 pl-0">
                    <label for="searchLearningOutcomes" class="mb-0">Search Learning Outcomes</label>
                    <div class="input-group">
                        <input type="text" id="searchLearningOutcomes" class="form-control" placeholder="Search Learning Outcome Name">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" id="resetSearch" title="Clear Search">
                                <i class="fa fa-times mr-0"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 pl-0">
                    <button class="btn btn-primary" onclick="loadLearningOutcomes()" id="getLearningOutcomesBtn" style="margin-top: 9% !important;" title="Get Learning Outcomes Data">Get Data</button>
                </div>
            </div>
            <div class="col-md-12 mt-3" id="learningOutcomesDiv">
                <!-- <div class="no-data-display">Click On Get Button</div> -->
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addNewLearningOutcomeModal" tabindex="-1" role="dialog" aria-labelledby="addNewLearningOutcomeModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top: 2% !important; margin: auto; width: 50%;">
            <div class="modal-header">
                <h5 class="modal-title" id="addNewLearningOutcomeModalLabel">Add New Learning Outcome</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="add_class_master_id" class="mt-2 mb-0">Class <font color="red">*</font></label>
                    <select class="form-control" id="add_class_master_id" name="add_class_master_id" required>
                        <option value="">Select Class</option>
                    </select>
                    <div style="position: absolute; right: 25px; top: 16%; transform: translateY(-50%);">
                        <i class="fa fa-caret-down"></i>
                    </div>
                    <label for="add_subject_master_id" class="mt-2 mb-0">Subject <font color="red">*</font></label>
                    <select class="form-control" id="add_subject_master_id" name="add_subject_master_id" required>
                        <option value="">Select Subject</option>
                    </select>
                    <div style="position: absolute; right: 25px; top: 33%; transform: translateY(-50%);">
                        <i class="fa fa-caret-down"></i>
                    </div>
                    <label for="add_learning_outcome" class="mt-2 mb-0">Learning Outcome <font color="red">*</font></label>
                    <input type="text" class="form-control" id="add_learning_outcome" name="add_learning_outcome" required placeholder="Enter Learning Outcome" autocomplete="off">
                    <span class="help-block text-secondary mt-0" id="learningOutcomeErrorMessage">Max 150 characters allowed.</span>
                    <label for="add_description" class="mt-2 mb-0">Description</label>
                    <textarea class="form-control" id="add_description" name="add_description" required placeholder="Enter Description" maxlength="250" style="height: 75px; resize: none;"></textarea>
                    <span class="help-block text-secondary mt-0" id="descriptionErrorMessage">Max 250 characters allowed.</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary mt-0" onclick="addLearningOutcome(this)">Add</button>
            </div>
        </div>
    </div>
</div>

<!-- Topic Mapping Modal -->
<div class="modal fade" id="topicMappingModal" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="topicMappingModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top: 1% !important; margin: auto;border-radius: .75rem; width: 50%;">
            <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
                <div>
                    <h4 class="modal-title" id="topicMappingModalLabel">
                        <i class="fa fa-link"></i> Map Topics to Learning Outcome
                    </h4>
                    <div id="topicMappingClassSubject"></div>
                </div>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Current Mappings Section -->
                <!-- <div class="card"> -->
                    <div class="col-md-12 mb-2 pr-0">
                        <div class="d-flex justify-content-between align-items-center pull-right">
                            <div id="currentMappingsActions" style="display: none;">
                                <button type="button" class="btn btn-sm btn-light" onclick="selectAllCurrentMappings(true)">
                                    <i class="fa fa-check-square-o"></i> Select All
                                </button>
                                <button type="button" class="btn btn-sm btn-light ml-1" onclick="selectAllCurrentMappings(false)">
                                    <i class="fa fa-square-o"></i> Deselect All
                                </button>
                                <button type="button" class="btn btn-sm btn-danger ml-2" onclick="removeSelectedMappings()" id="removeSelectedBtn" disabled>
                                    <i class="fa fa-trash-o"></i> Remove Selected (<span id="selectedMappingsCount">0</span>)
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="card-body"> -->
                        <div id="currentMappedTopics">
                            <div class="text-center">
                                <i class="fa fa-spinner fa-spin"></i> Loading mapped topics...
                            </div>
                        </div>
                    <!-- </div> -->
                <!-- </div> -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Close
                </button>
                <button type="button" class="btn btn-primary mt-0" onclick="showAddMoreTopicsModal()">
                    <i class="fa fa-plus"></i> Add More Topics
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add More Topics Modal -->
<div class="modal fade" id="addMoreTopicsModal" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="addMoreTopicsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="margin-top: 1% !important; margin: auto; border-radius: .75rem; width: 50%;">
            <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
                <h4 class="modal-title" id="addMoreTopicsModalLabel">
                    <i class="fa fa-plus"></i> Add More Topics
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- <div class="card"> -->
                    <div class="col-md-12 mb-2 pr-0">
                        <div class="d-flex justify-content-between align-items-center pull-right">
                            <div>
                                <button type="button" class="btn btn-sm btn-light" onclick="selectAllTopics(true)">
                                    <i class="fa fa-check-square-o"></i> Select All
                                </button>
                                <button type="button" class="btn btn-sm btn-light ml-1" onclick="selectAllTopics(false)">
                                    <i class="fa fa-square-o"></i> Deselect All
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="card-body"> -->
                        <div id="availableTopicsList">
                            <div class="text-center">
                                <i class="fa fa-spinner fa-spin"></i> Loading available topics...
                            </div>
                        </div>
                    <!-- </div> -->
                <!-- </div> -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Close
                </button>
                <button type="button" class="btn btn-success mt-0" onclick="mapSelectedTopics()" id="mapSelectedBtn" disabled>
                    <i class="fa fa-plus"></i> Map Selected Topics (<span id="selectedCount">0</span>)
                </button>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
    .new_circleShape_res {
        padding: 8px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 22px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }

    .new_circleShape_res1 {
        padding: 5px 8px;
        border-radius: 50% !important;
        font-size: 16px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 0rem !important;
    }

    .ellipsis{
        display:none;
    }

    .dt-buttons{
        float: right;
        margin-left: 5px;
        margin-bottom: 5px;
    }

    .description-cell {
        max-width: 250px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    @keyframes blink {
        0%, 100% { opacity: 1; }
        50% { opacity: 0; }
    }

    .blink {
        animation: blink 0.8s ease-in-out 3;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    let isSuperAdmin = '<?php echo $this->authorization->isSuperAdmin() ?>';
    let staffMapping = <?php echo json_encode($staffMapping); ?>;
    let tableColumnMapping = {
        'class_master_id': 'Class',
        'subject_master_id': 'Subject',
        'learning_outcome_name': 'Learning Outcome Name',
        'description': 'Learning Outcome Description',
        'created_by': 'Created By',
        'created_on': 'Created On',
        'updated_by': 'Updated By',
        'updated_on': 'Updated On',
        'approved_by': 'Approved By',
        'approved_on': 'Approved On',
        'history': 'History',
        'is_active': 'Status'
    };

    function loadClassMasterDataOnLoad(){
        $.ajax({
            url: '<?php echo base_url('academics/Learning_outcomes/getClassMasterData'); ?>',
            type: 'POST',
            success: function(data) {
                let parseData = JSON.parse(data);
                if(parseData.length <= 0){
                    $('#class_master_id').html('<option value="">Select Class</option>');
                }else{
                    $('#class_master_id').html('<option>Loading Classes...</option>')
                    let classOptions = '';
                    classOptions += '<option value="">Select Class</option>';
                    parseData.forEach(function(item) {
                        classOptions += '<option value="' + item.class_master_id + '">' + item.class_name + '</option>';
                    });
                    $('#class_master_id').html(classOptions);
                }
            },
            error: function(error) {
                console.error(error);
                $('#class_master_id').html('<option value="">Select Class</option>');
                $('#subject_master_id').html('<option value="">Select Subject</option>');
            }
        });
    }

    function loadSubjectMasterDataOnLoad(){
        $('#searchLearningOutcomes').val('');
        $.ajax({
            url: '<?php echo base_url('academics/Learning_outcomes/getSubjectMasterData'); ?>',
            type: 'POST',
            success: function(data) {
                let parseData = JSON.parse(data);
                if(parseData.length <= 0){
                    $('#subject_master_id').html('<option value="">Select Subject</option>');
                }else{
                    $('#subject_master_id').html('<option>Loading Subjects...</option>')
                    let subjectOptions = '';
                    subjectOptions += '<option value="">Select Subject</option>';
                    parseData.forEach(function(item) {
                        subjectOptions += '<option value="' + item.subject_master_id + '">' + item.subject_name + '</option>';
                    });
                    $('#subject_master_id').html(subjectOptions);
                }
            },
            error: function(error) {
                console.error(error);
                $('#subject_master_id').html('<option value="">Some Error Occurred</option>');
            }
        });
    }

    function clearSearch(){
        $('#searchLearningOutcomes').val('');
        $('#learningOutcomesDiv').html('<div class="no-data-display">Click On Get Button</div>');
    }

    function loadModalData(){
        loadClassMasterDataForModal();
        loadSubjectMasterDataForModal();
    }

    function loadClassMasterDataForModal(){
        $('#add_learning_outcome').val('');
        $('#add_description').val('');
        $.ajax({
            url: '<?php echo base_url('academics/Learning_outcomes/getClassMasterData'); ?>',
            type: 'POST',
            success: function(data) {
                let parseData = JSON.parse(data);
                if(parseData.length <= 0){
                    $('#add_class_master_id').html('<option value="">Select Class</option>');
                }else{
                    $('#add_class_master_id').html('<option>Loading Classes...</option>')
                    let classOptions = '';
                    parseData.forEach(function(item) {
                        classOptions += '<option value="' + item.class_master_id + '">' + item.class_name + '</option>';
                    });
                    $('#add_class_master_id').html(classOptions);
                }
            },
            error: function(error) {
                console.error(error);
                $('#add_class_master_id').html('<option value="">Select Class</option>');
                $('#add_subject_master_id').html('<option value="">Select Subject</option>');
            }
        });
    }

    function loadSubjectMasterDataForModal(){
        $.ajax({
            url: '<?php echo base_url('academics/Learning_outcomes/getSubjectMasterData'); ?>',
            type: 'POST',
            success: function(data) {
                let parseData = JSON.parse(data);
                if(parseData.length <= 0){
                    $('#add_subject_master_id').html('<option value="">Select Subject</option>');
                }else{
                    $('#add_subject_master_id').html('<option>Loading Subjects...</option>')
                    let subjectOptions = '';
                    parseData.forEach(function(item) {
                        subjectOptions += '<option value="' + item.subject_master_id + '">' + item.subject_name + '</option>';
                    });
                    $('#add_subject_master_id').html(subjectOptions);
                }
            },
            error: function(error) {
                console.error(error);
                $('#add_subject_master_id').html('<option value="">Some Error Occurred</option>');
            }
        });
    }

    $(document).ready(function() {
        loadClassMasterDataOnLoad();
        loadSubjectMasterDataOnLoad();
        loadLearningOutcomes();
        $('#add_learning_outcome').on('input', function () {
            let raw = $(this).val();
            let cleaned = removeEmojis(raw);

            if (cleaned.length > 150) {
                const $msg = $('#learningOutcomeErrorMessage');
                $msg.removeClass('text-secondary').addClass('text-danger').text('Learning Outcome must be 150 characters or less!');
                
                // Trigger blink animation by re-adding the class
                $msg.removeClass('blink'); // Reset
                void $msg[0].offsetWidth;  // Force reflow to restart animation
                $msg.addClass('blink');

                cleaned = cleaned.substring(0, 150);
            } else {
                $('#learningOutcomeErrorMessage').removeClass('text-danger blink').addClass('text-secondary').text('Max 150 characters allowed.');
            }

            $(this).val(cleaned);  // allow trailing space during typing
        });

        $('#add_description').on('input', function () {
            let raw = $(this).val();
            let cleaned = removeEmojis(raw);

            if (cleaned.length > 250) {
                const $msg = $('#descriptionErrorMessage');
                $msg.removeClass('text-secondary').addClass('text-danger').text('Description must be 250 characters or less!');
                
                // Trigger blink animation by re-adding the class
                $msg.removeClass('blink'); // Reset
                void $msg[0].offsetWidth;  // Force reflow to restart animation
                $msg.addClass('blink');
                cleaned = cleaned.substring(0, 250); // Just cut, don't trim
            } else {
                $('#descriptionErrorMessage').removeClass('text-danger blink').addClass('text-secondary').text('Max 250 characters allowed.'); // Reset color if valid
            }

            $(this).val(cleaned);  // keep user experience smooth
        });
        $('#resetSearch').on('click', function () {
            if($('#searchLearningOutcomes').val() == ''){
                return;
            }
            $('#searchLearningOutcomes').val('').trigger('input'); // Clear and trigger input event
            $('#learningOutcomesDiv').html('<div class="no-data-display">Click On Get Button</div>');
        });
        $('#searchLearningOutcomes').on('blur', function () {
            if($('#searchLearningOutcomes').val() == ''){
                return;
            }
            $('#learningOutcomesDiv').html('<div class="no-data-display">Click On Get Button</div>');
        })
    });

    function loadLearningOutcomes() {
        $('#refreshButton').prop('disabled', true).html('<i class="fa fa-spin fa-spinner mr-0"></i>');
        $('#getLearningOutcomesBtn').prop('disabled', true).html('Please Wait...');
        $('#learningOutcomesDiv').html('<div class="no-data-display">Loading...</div>');
        $.ajax({
            url: '<?php echo base_url('academics/Learning_outcomes/get_all'); ?>',
            type: 'POST',
            data: {
                class_master_id: $('#class_master_id').val(),
                subject_master_id: $('#subject_master_id').val(),
                search: $('#searchLearningOutcomes').val(),
                status: $('#status').val(),
            },
            success: function(response) {
                $('#refreshButton').prop('disabled', true).html('<i class="fa fa-refresh mr-0"></i>');
                $('#getLearningOutcomesBtn').prop('disabled', false).html('Get Data');
                response = JSON.parse(response);
                if (response.status) {
                    if(response.data.length <= 0){
                        $('#learningOutcomesDiv').html('<div class="no-data-display">No Learning Outcomes Found</div>');
                        return;
                    }
                    $('#learningOutcomesDiv').html(constructLearningOutcomes(response.data));
                    const now = new Date();

                    const pad = (n) => n.toString().padStart(2, '0');

                    const year = now.getFullYear();
                    const month = pad(now.getMonth() + 1); // 0-based index
                    const day = pad(now.getDate());
                    const hour = pad(now.getHours());
                    const minute = pad(now.getMinutes());
                    const second = pad(now.getSeconds());
                    let fileName = `Learning Outcomes ${year}-${month}-${day} ${hour}-${minute}-${second}`;
                    $('#learningOutcomesTable').DataTable({
                        "dom": 'Bfrtip',
                        "destroy": true,
                        "paging": false,
                        "lengthChange": false,
                        "searching": false,
                        "ordering": false,
                        "info": true,
                        "autoWidth": false,
                        "responsive": true,
                        "language": {
                            "search": "Search:",
                            "emptyTable": "No Learning Outcomes available"
                        },
                        "buttons": [
                            // {
                            //     "extend": "csv",
                            //     "className": "btn btn-primary",
                            //     "title": 'Learning Outcomes',
                            //     "text": "CSV",
                            //     "filename": `${fileName}`,
                            //     "exportOptions": {
                            //         "columns": [0, 1, 2, 3, 4, 5, 6],
                            //     },
                            //     customize: function (csv) {
                            //         return '\uFEFF' + csv;
                            //     }
                            // },
                            {
                                "extend": "excelHtml5",
                                "className": "btn btn-primary",
                                "title": 'Learning Outcomes',
                                "text": "Excel",
                                "filename": `${fileName}`,
                                "exportOptions": {
                                    "columns": [0, 1, 2, 3, 4, 5, 6],
                                },
                                customize: function (csv) {
                                    // Add UTF-8 BOM
                                    return '\uFEFF' + csv;
                                }
                            },
                        ]
                    });
                } else {
                    $('#learningOutcomesDiv').html('<div class="no-data-display">No Learning Outcomes Found</div>');
                }
            },
            error: function() {
                $('#learningOutcomesDiv').html('<div class="no-data-display">Error loading Learning Outcomes</div>');
            }
        });
    }

    function constructLearningOutcomes(learningOutcomes) {
        let html = `
            <div class="table-responsive">
                <table class="table table-bordered table-striped align-middle" id="learningOutcomesTable">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>Class</th>
                            <th>Subject</th>
                            <th>Learning Outcome</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        learningOutcomes.forEach(function(item, index) {
            const toggleButton = item.is_active == 1
                ? `<button class="btn btn-sm btn-outline-danger mr-2" onclick="toggleLearningOutcomeStatus(${item.learning_outcome_id}, 0)" title="Deactivate Learning Outcome">
                        <i class="fa fa-times mr-0" style="padding: 2px 0px !important;"></i>
                    </button>`
                : `<button class="btn btn-sm btn-outline-success mr-2" onclick="toggleLearningOutcomeStatus(${item.learning_outcome_id}, 1)" title="Activate Learning Outcome">
                        <i class="fa fa-check mr-0" style="padding: 2px 0px !important;"></i>
                    </button>`;

            const statusBadge = item.is_active == 1
                ? '<span class="badge bg-success">Active</span>'
                : '<span class="badge bg-danger">Inactive</span>';

            const createdInfo = `${item.created_by || '-'} <br>(<small>${item.created_on || '-'}</small>)`;
            const updatedInfo = item.updated_by && item.updated_on
                ? `${item.updated_by} <br>(<small>${item.updated_on}</small>)`
                : '-';
            const approvedInfo = item.approved_by && item.approved_on
                ? `${item.approved_by} <br>(<small>${item.approved_on}</small>)`
                : '-';

            // Check if learning outcome has topic mappings to determine if deactivate should be hidden
            const hasMappings = item.mapped_topics_count && item.mapped_topics_count > 0;

            // Map to Topics button - always show for active learning outcomes
            const mapToTopicsButton = item.is_active == 1
                ? `<button class="btn btn-sm btn-outline-info mr-2" title="Map to Topics" onclick="showTopicMappingModal(${item.learning_outcome_id}, '${item.learning_outcome_name}', '${item.class_name}', '${item.subject_name}')">
                        <i class="fa fa-link mr-0" style="padding: 5px 0px !important;"></i>
                    </button>`
                : '';

            // Toggle button - hide deactivate if learning outcome has mappings
            const toggleButtonToShow = item.is_active == 1
                ? (hasMappings
                    ? '' // Don't show deactivate button if has mappings
                    : `<button class="btn btn-sm btn-outline-danger mr-2" onclick="toggleLearningOutcomeStatus(${item.learning_outcome_id}, 0)" title="Deactivate Learning Outcome">
                            <i class="fa fa-times mr-0" style="padding: 2px 0px !important;"></i>
                        </button>`)
                : `<button class="btn btn-sm btn-outline-success mr-2" onclick="toggleLearningOutcomeStatus(${item.learning_outcome_id}, 1)" title="Activate Learning Outcome">
                        <i class="fa fa-check mr-0" style="padding: 2px 0px !important;"></i>
                    </button>`;

            let actions = item.is_active == 1
                    ? `<button class="btn btn-sm btn-outline-primary mr-2" title="Edit Learning Outcome" onclick="editLearningOutcome(${item.learning_outcome_id})">
                            <i class="fa fa-edit mr-0" style="padding: 5px 0px !important;"></i>
                        </button>
                        ${mapToTopicsButton}
                        ${toggleButtonToShow}`
                    : toggleButtonToShow;

            const historyButton = item.history && item.history.length > 0
                ? `<button class="btn btn-sm btn-outline-info" onclick='viewLearningOutcomeHistory(${JSON.stringify(item.history)}, "${item.learning_outcome_name}")' title="View History">
                        <i class="fa fa-history mr-0" style="padding: 5px 0px !important;"></i>
                    </button>`
                : '';

            if(isSuperAdmin){
                actions += historyButton;
            }

            html += `
                <tr id="learning_outcome_row_${item.learning_outcome_id}">
                    <td>${index + 1}</td>
                    <td>${item.class_name}</td>
                    <td>${item.subject_name}</td>
                    <td>${item.learning_outcome_name}</td>
                    <td title="${item.learning_outcome_description}">
                        <div class="description-cell">${item.learning_outcome_description}</div>
                    </td>
                    <td>${statusBadge}</td>
                    <td>${createdInfo}</td>
                    <td id="learning_outcome_actions_${item.learning_outcome_id}"><div class="d-flex">${actions}</div></td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
        return html;
    }

    function formatDate(datetime) {
        const date = new Date(datetime);
        return date.toLocaleString('en-IN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
            second: 'numeric',
            hour12: true,
        });
    }

    function removeEmojis(str) {
        return str.replace(
            /([\u203C-\u3299]|\u00A9|\u00AE|[\u2000-\u3300]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|\uD83E[\uDD00-\uDFFF])/g,
            ''
        );
    }

    function viewLearningOutcomeHistory(historyData, learningOutcomeName) {
        // Parse if it's a string
        if (typeof historyData === 'string') {
            try {
                historyData = JSON.parse(historyData);
            } catch (e) {
                Swal.fire('Error', 'Invalid history format', 'error');
                return;
            }
        }

        if (!Array.isArray(historyData) || historyData.length === 0) {
            Swal.fire('No History', 'No changes were made to this learning outcome.', 'info');
            return;
        }

        let tableRows = '';
        historyData.reverse().forEach((entry, index) => {
            const changeTime = formatDate(entry.changed_at); // Optional formatting
            const changedBy = staffMapping[entry.changed_by]; // You can customize this
            const changes = Object.entries(entry.changes).map(([key, value]) => {
                return `<b>${tableColumnMapping[key]}</b>: ${value.from} → ${value.to}`;
            }).join('<br>');

            tableRows += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${changeTime}</td>
                    <td>${changedBy}</td>
                    <td>${changes}</td>
                </tr>`;
        });

        const htmlTable = `
            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                <table class="table table-bordered small">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Changed At</th>
                            <th>Changed By</th>
                            <th>Changes</th>
                        </tr>
                    </thead>
                    <tbody>${tableRows}</tbody>
                </table>
            </div>`;

        Swal.fire({
            title: `Change History - ${learningOutcomeName}`,
            html: htmlTable,
            width: '60%',
            confirmButtonText: 'Close',
        });
    }

    function addLearningOutcome(addBtn){
        $(addBtn).prop('disabled', true);
        $(addBtn).html('Please wait...');
        let classId = $('#add_class_master_id').val();
        let subjectId = $('#add_subject_master_id').val();
        let learningOutcome = $('#add_learning_outcome').val();
        let description = $('#add_description').val();
        $.ajax({
            url: '<?php echo base_url('academics/Learning_outcomes/create'); ?>',
            type: 'POST',
            data: {
                class_master_id: classId,
                subject_master_id: subjectId,
                learning_outcome_name: learningOutcome,
                learning_outcome_description: description
            },
            success: function(response) {
                response = JSON.parse(response);
                $(addBtn).prop('disabled', false);
                $(addBtn).html('Add');
                if (response.status) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: response.message,
                        showCancelButton: true,
                        confirmButtonText: 'Continue Adding',
                        cancelButtonText: 'Close',
                        reverseButtons: true,
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        allowEnterKey: false,
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Keep modal open, just clear inputs
                            $('#add_learning_outcome').val('');
                            $('#add_description').val('');
                        } else {
                            // Close modal, clear inputs, reload table
                            $('#add_learning_outcome').val('');
                            $('#add_description').val('');
                            $('#addNewLearningOutcomeModal').modal('hide');
                            loadLearningOutcomes();
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.message
                    });
                }
            },
            error: function() {
                $(addBtn).prop('disabled', false);
                $(addBtn).html('Add');
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Error adding Learning Outcome'
                });
            }
        });
    }

    function markAsRefreshPending(id) {
        const actionTd = document.getElementById(`learning_outcome_actions_${id}`);
        if (actionTd) {
            actionTd.innerHTML = `<span class="text-primary">Refresh Pending</span>`;
        }
        $('#refreshButton').prop('disabled', false);
    }

    function editLearningOutcome(id) {
        $.ajax({
            url: '<?= base_url("academics/Learning_outcomes/get_by_id") ?>',
            type: 'POST',
            data: { id: id },
            success: function (data) {
                let parseData = JSON.parse(data);
                if (!parseData.status) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Learning outcome not found.'
                    });
                    return;
                }
                displayEditSwal(parseData.data);
            },
            error: function () {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to fetch learning outcome.'
                });
            }
        });
    }

    function displayEditSwal(learningOutcomeData){
        Swal.fire({
            title: 'Edit Learning Outcome',
            html: `
                <div class="form-group" style="height: 150px">
                    <label for="edit_learning_outcome" class="pull-left">Learning Outcome <font color="red">*</font></label>
                    <input type="text" class="form-control" id="edit_learning_outcome" name="edit_learning_outcome" value="${learningOutcomeData.learning_outcome_name}" autocomplete="off" required placeholder="Enter Learning Outcome">
                    <span id="edit_learning_outcome_error" class="help-block mt-0 text-secondary pull-left">Max 150 characters allowed.</span><br>
                    <label for="edit_description" class="pull-left mt-2 mb-0">Description</label>
                    <textarea class="form-control" id="edit_description" style="height: 75px; resize: none;" name="edit_description" placeholder="Enter Description">${learningOutcomeData.learning_outcome_description}</textarea>
                    <span id="edit_description_error" class="help-block mt-0 text-secondary pull-left">Max 250 characters allowed.</span>
                </div>`,
            showCancelButton: true,
            confirmButtonText: 'Save',
            confirmButtonColor: '#3085d6',
            cancelButtonText: 'Cancel',
            reverseButtons: true,
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            width: '60%',
            didOpen: () => {
                const $nameInput = $('#edit_learning_outcome');
                const $descInput = $('#edit_description');

                const originalName = learningOutcomeData.learning_outcome_name.trim();
                const originalDesc = learningOutcomeData.learning_outcome_description.trim();

                Swal.getConfirmButton().disabled = true;

                function validateInputs() {
                    const nameRaw = $nameInput.val();
                    const descRaw = $descInput.val();

                    const cleanedName = removeEmojis(nameRaw).trim();
                    const cleanedDesc = removeEmojis(descRaw).trim();

                    let isValid = true;

                    // Length validations
                    if (cleanedName.length > 150) {
                        Swal.showValidationMessage('Learning Outcome must be 150 characters or less!');
                        isValid = false;
                    } else if (cleanedDesc.length > 250) {
                        Swal.showValidationMessage('Description must be 250 characters or less!');
                        isValid = false;
                    } else {
                        Swal.resetValidationMessage();
                    }

                    // Both must be non-empty
                    const bothNonEmpty = cleanedName !== '' && cleanedDesc !== '';

                    // At least one field must be changed
                    const nameChanged = cleanedName !== originalName;
                    const descChanged = cleanedDesc !== originalDesc;

                    Swal.getConfirmButton().disabled = !(isValid && bothNonEmpty && (nameChanged || descChanged));
                }

                $nameInput.on('input', function () {
                    const cleaned = removeEmojis($(this).val()).substring(0, 150);
                    $(this).val(cleaned);
                    validateInputs();
                });

                $descInput.on('input', function () {
                    const cleaned = removeEmojis($(this).val()).substring(0, 250);
                    $(this).val(cleaned);
                    validateInputs();
                });
            },
            preConfirm: () => {
                const name = $('#edit_learning_outcome').val().trim();
                const description = $('#edit_description').val().trim();

                if (!name) {
                    Swal.showValidationMessage('Learning outcome name is required');
                    return false;
                }

                return { name, description };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url("academics/Learning_outcomes/update") ?>',
                    type: 'POST',
                    data: {
                        id: learningOutcomeData.learning_outcome_id,
                        learning_outcome_name: result.value.name,
                        learning_outcome_description: result.value.description
                    },
                    success: function (response) {
                        let parseData = JSON.parse(response);
                        if (parseData.status) {
                            markAsRefreshPending(learningOutcomeData.learning_outcome_id);
                            Swal.fire({
                                icon: 'success',
                                title: 'Success',
                                text: parseData.message
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: parseData.message
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Action failed.'
                        });
                    }
                });
            }
        });
    }

    function toggleLearningOutcomeStatus(id, status) {
        const action = status === 1 ? 'activate' : 'deactivate';
        Swal.fire({
            icon: 'question',
            title: 'Confirm Action',
            html: `Are you sure you want to <b>${action}</b> this learning outcome?`,
            showCancelButton: true,
            confirmButtonText: 'Yes',
            cancelButtonText: 'No',
            reverseButtons: true,
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
        }).then((result) => {
            if (result.isConfirmed) {
                // User confirmed, proceed with AJAX request
                $.ajax({
                    url: '<?= base_url("academics/Learning_outcomes/toggle_active") ?>',
                    type: 'POST',
                    data: {
                        id: id,
                        status: status
                    },
                    success: function (response) {
                        let parseData = JSON.parse(response);
                        if (parseData.status) {
                            markAsRefreshPending(id);
                            Swal.fire({
                                icon: 'success',
                                title: 'Success',
                                text: parseData.message
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: parseData.message
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Action failed.'
                        });
                    }
                });
            }
        });
    }

    // ==================== REVERSE MAPPING FUNCTIONS ====================

    let currentLearningOutcomeId = null;
    let currentLearningOutcomeName = null;

    function showTopicMappingModal(learningOutcomeId, learningOutcomeName, className, subjectName) {
        currentLearningOutcomeId = learningOutcomeId;
        currentLearningOutcomeName = learningOutcomeName;

        // Update modal title
        $('#topicMappingModalLabel').html(`Map Topics to Learning Outcome: <strong>${learningOutcomeName}</strong>`);
        $('#topicMappingClassSubject').html(`<small class="text-muted">Class: ${className} | Subject: ${subjectName}</small>`);

        // Load current mappings
        loadCurrentTopicMappings(learningOutcomeId);

        // Show modal
        $('#topicMappingModal').modal('show');
    }

    function loadCurrentTopicMappings(learningOutcomeId) {
        $.ajax({
            url: '<?php echo site_url('academics/learning_outcomes/get_mapped_topics'); ?>',
            type: 'POST',
            data: {
                learning_outcome_id: learningOutcomeId
            },
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    displayCurrentMappings(response.data);
                } else {
                    $('#currentMappedTopics').html('<p class="text-muted">No topics mapped to this learning outcome</p>');
                }
            },
            error: function() {
                $('#currentMappedTopics').html('<p class="text-danger">Error loading mapped topics</p>');
            }
        });
    }

    function displayCurrentMappings(mappings) {
        const container = $('#currentMappedTopics');

        if (mappings.length === 0) {
            container.html('<p class="text-muted">No topics mapped to this learning outcome</p>');
            $('#currentMappingsActions').hide();
            return;
        }

        // Show mass action controls
        $('#currentMappingsActions').show();

        let html = `
            <table class="table table-sm table-bordered">
                <thead class="thead-light">
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAllCurrentMappingsCheckbox" onchange="selectAllCurrentMappings(this.checked)">
                        </th>
                        <th>Class</th>
                        <th>Subject</th>
                        <th>Lesson</th>
                        <th>Topic</th>
                        <th>Mapped On</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
        `;

        mappings.forEach(function(mapping) {
            const mappedDate = new Date(mapping.created_on);
            const formattedDate = mappedDate.toLocaleDateString('en-IN', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });

            html += `
                <tr>
                    <td>
                        <input type="checkbox" class="mapping-checkbox" value="${mapping.mapping_id}" onchange="updateSelectedMappingsCount()">
                    </td>
                    <td>${mapping.class_name}</td>
                    <td>${mapping.subject_name}</td>
                    <td>${mapping.lesson_name}</td>
                    <td>${mapping.sub_topic_name}</td>
                    <td><small class="text-muted">${formattedDate}</small></td>
                    <td>
                        <button class="btn btn-sm btn-danger"
                                onclick="removeLearningOutcomeTopicMapping(${mapping.mapping_id})"
                                title="Remove Mapping">
                            <i class="fa fa-trash-o mr-0"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        container.html(html);
        updateSelectedMappingsCount();
    }

    function showAddMoreTopicsModal() {
        // Load available topics for mapping
        $.ajax({
            url: '<?php echo site_url('academics/learning_outcomes/get_available_topics_for_mapping'); ?>',
            type: 'POST',
            data: {
                learning_outcome_id: currentLearningOutcomeId
            },
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    if (response.topics && response.topics.length > 0) {
                        displayAvailableTopics(response.topics);
                        $('#addMoreTopicsModal').modal('show');
                    } else {
                        Swal.fire({
                            icon: 'info',
                            title: 'No Topics Available',
                            text: `No topics found under the class "${response.learning_outcome.class_name}" and subject "${response.learning_outcome.subject_name}" that are available for mapping.`,
                            confirmButtonText: 'OK'
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.message || 'Failed to load available topics'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load available topics'
                });
            }
        });
    }

    function displayAvailableTopics(topics) {
        if (topics.length === 0) {
            $('#availableTopicsList').html('<p class="text-muted text-center">No topics available for mapping</p>');
            return;
        }

        let html = `
            <table class="table table-sm table-bordered">
                <thead class="thead-light">
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAllCheckbox" onchange="selectAllTopics(this.checked)">
                        </th>
                        <th>Subject</th>
                        <th>Lesson</th>
                        <th>Topic</th>
                    </tr>
                </thead>
                <tbody>
        `;

        topics.forEach(function(topic) {
            html += `
                <tr>
                    <td>
                        <input type="checkbox" class="topic-checkbox" value="${topic.topic_id}" onchange="updateSelectedCount()">
                    </td>
                    <td>${topic.subject_name}</td>
                    <td>${topic.lesson_name}</td>
                    <td>${topic.sub_topic_name}</td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        $('#availableTopicsList').html(html);
        updateSelectedCount();
    }

    function selectAllTopics(checked) {
        $('.topic-checkbox').prop('checked', checked);
        $('#selectAllCheckbox').prop('checked', checked);
        updateSelectedCount();
    }

    function updateSelectedCount() {
        const selectedCount = $('.topic-checkbox:checked').length;
        const totalCount = $('.topic-checkbox').length;

        $('#selectedCount').text(selectedCount);
        $('#mapSelectedBtn').prop('disabled', selectedCount === 0);

        // Update select all checkbox state
        if (selectedCount === 0) {
            $('#selectAllCheckbox').prop('indeterminate', false).prop('checked', false);
        } else if (selectedCount === totalCount) {
            $('#selectAllCheckbox').prop('indeterminate', false).prop('checked', true);
        } else {
            $('#selectAllCheckbox').prop('indeterminate', true);
        }
    }

    // ==================== CURRENT MAPPINGS MASS SELECTION ====================

    function selectAllCurrentMappings(checked) {
        $('.mapping-checkbox').prop('checked', checked);
        $('#selectAllCurrentMappingsCheckbox').prop('checked', checked);
        updateSelectedMappingsCount();
    }

    function updateSelectedMappingsCount() {
        const selectedCount = $('.mapping-checkbox:checked').length;
        const totalCount = $('.mapping-checkbox').length;

        $('#selectedMappingsCount').text(selectedCount);
        $('#removeSelectedBtn').prop('disabled', selectedCount === 0);

        // Update select all checkbox state
        if (selectedCount === 0) {
            $('#selectAllCurrentMappingsCheckbox').prop('indeterminate', false).prop('checked', false);
        } else if (selectedCount === totalCount) {
            $('#selectAllCurrentMappingsCheckbox').prop('indeterminate', false).prop('checked', true);
        } else {
            $('#selectAllCurrentMappingsCheckbox').prop('indeterminate', true);
        }
    }

    function mapSelectedTopics() {
        const selectedTopicIds = $('.topic-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedTopicIds.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'No Topics Selected',
                text: 'Please select at least one topic to map.'
            });
            return;
        }

        // Show confirmation
        Swal.fire({
            title: 'Confirm Mapping',
            text: `Are you sure you want to map ${selectedTopicIds.length} topic(s) to this learning outcome?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, map them!',
            reverseButtons: true,
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
        }).then((result) => {
            if (result.isConfirmed) {
                performMassMapping(selectedTopicIds);
            }
        });
    }

    function performMassMapping(topicIds) {
        // Show progress
        Swal.fire({
            title: 'Mapping Topics...',
            html: `Processing <b>${topicIds.length}</b> topics...`,
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Use mass mapping endpoint for better performance
        $.ajax({
            url: '<?php echo site_url('academics/learning_outcomes/create_mass_learning_outcome_topic_mapping'); ?>',
            type: 'POST',
            data: {
                learning_outcome_id: currentLearningOutcomeId,
                topic_ids: topicIds
            },
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    showMappingResults(response.success_count, response.error_count, response.errors);
                } else {
                    showMappingResults(0, topicIds.length, [response.message]);
                }
            },
            error: function() {
                showMappingResults(0, topicIds.length, ['Network error occurred while mapping topics']);
            }
        });
    }

    function showMappingResults(successCount, errorCount, errors) {
        let message = '';
        let icon = 'success';

        if (errorCount === 0) {
            message = `Successfully mapped ${successCount} topic(s) to the learning outcome.`;
            icon = 'success';
        } else if (successCount === 0) {
            message = `Failed to map all ${errorCount} topic(s). ${errors.join(', ')}`;
            icon = 'error';
        } else {
            message = `Mapped ${successCount} topic(s) successfully. ${errorCount} failed: ${errors.join(', ')}`;
            icon = 'warning';
        }

        Swal.fire({
            icon: icon,
            title: 'Mapping Complete',
            text: message,
            confirmButtonText: 'OK',
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
        }).then(() => {
            // Close add more modal and refresh current mappings
            $('#addMoreTopicsModal').modal('hide');
            loadCurrentTopicMappings(currentLearningOutcomeId);
            // Mark row as refresh pending instead of full reload
            markAsRefreshPending(currentLearningOutcomeId);
        });
    }

    function removeLearningOutcomeTopicMapping(mappingId) {
        Swal.fire({
            title: 'Are you sure?',
            text: 'This will remove the topic mapping from this learning outcome.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, remove it!',
            reverseButtons: true,
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url('academics/learning_outcomes/remove_learning_outcome_topic_mapping'); ?>',
                    type: 'POST',
                    data: {
                        mapping_id: mappingId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Success',
                                text: response.message
                            });
                            loadCurrentTopicMappings(currentLearningOutcomeId);
                            // Mark row as refresh pending instead of full reload
                            markAsRefreshPending(currentLearningOutcomeId);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: response.message
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Failed to remove mapping'
                        });
                    }
                });
            }
        });
    }

    // ==================== MASS REMOVAL FUNCTIONS ====================

    function removeSelectedMappings() {
        const selectedMappingIds = $('.mapping-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedMappingIds.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'No Mappings Selected',
                text: 'Please select at least one mapping to remove.'
            });
            return;
        }

        // Show confirmation
        Swal.fire({
            title: 'Confirm Removal',
            text: `Are you sure you want to remove ${selectedMappingIds.length} topic mapping(s) from this learning outcome?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, remove them!',
            reverseButtons: true,
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
        }).then((result) => {
            if (result.isConfirmed) {
                performMassRemoval(selectedMappingIds);
            }
        });
    }

    function performMassRemoval(mappingIds) {
        // Show progress
        Swal.fire({
            title: 'Removing Mappings...',
            html: `Processing <b>${mappingIds.length}</b> mappings...`,
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Use mass removal endpoint for better performance
        $.ajax({
            url: '<?php echo site_url('academics/learning_outcomes/remove_mass_learning_outcome_topic_mapping'); ?>',
            type: 'POST',
            data: {
                mapping_ids: mappingIds
            },
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    showRemovalResults(response.success_count, response.error_count, response.errors);
                } else {
                    showRemovalResults(0, mappingIds.length, [response.message]);
                }
            },
            error: function() {
                showRemovalResults(0, mappingIds.length, ['Network error occurred while removing mappings']);
            }
        });
    }

    function showRemovalResults(successCount, errorCount, errors) {
        let message = '';
        let icon = 'success';

        if (errorCount === 0) {
            message = `Successfully removed ${successCount} topic mapping(s) from the learning outcome.`;
            icon = 'success';
        } else if (successCount === 0) {
            message = `Failed to remove all ${errorCount} mapping(s). ${errors.join(', ')}`;
            icon = 'error';
        } else {
            message = `Removed ${successCount} mapping(s) successfully. ${errorCount} failed: ${errors.join(', ')}`;
            icon = 'warning';
        }

        Swal.fire({
            icon: icon,
            title: 'Removal Complete',
            text: message,
            confirmButtonText: 'OK',
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
        }).then(() => {
            // Refresh current mappings
            loadCurrentTopicMappings(currentLearningOutcomeId);
            // Mark row as refresh pending instead of full reload
            markAsRefreshPending(currentLearningOutcomeId);
        });
    }
</script>
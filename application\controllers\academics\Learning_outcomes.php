<?php

class Learning_outcomes extends CI_Controller {
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('academics/Learning_outcomes_model');
    }

    public function index() {
        $data['staffMapping'] = $this->Learning_outcomes_model->getStaffMapping();
        $data['title'] = 'Manage Learning Outcomes';
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        } else if ($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        } else {
            $data['main_content'] = 'academics/learning_outcomes/index';
        }
        $this->load->view('inc/template', $data);
    }

    public function getClassMasterData(){
        $classes = $this->Learning_outcomes_model->getClassMasterData();
        echo json_encode($classes);
    }

    public function getSubjectMasterData(){
        $subjects = $this->Learning_outcomes_model->getSubjectMasterData();
        echo json_encode($subjects);
    }

    public function create() {
        $input = $this->input->post();
        $result = $this->Learning_outcomes_model->create($input);
        echo json_encode($result);
    }

    public function update() {
        $input = $this->input->post();
        $result = $this->Learning_outcomes_model->update_with_history($input['id'], $input);

        echo json_encode($result);
    }

    public function get_all() {
        $class_master_id = $this->input->post('class_master_id');
        $subject_master_id = $this->input->post('subject_master_id');
        $search = $this->input->post('search');
        $status = $this->input->post('status');
        $data = $this->Learning_outcomes_model->get_all($class_master_id, $subject_master_id, $search, $status);
        echo json_encode(['status' => true, 'data' => $data]);
    }

    public function get_by_id() {
        $id = $this->input->post('id');
        $data = $this->Learning_outcomes_model->get_by_id($id);
        if ($data) {
            echo json_encode(['status' => true, 'data' => $data]);
        } else {
            echo json_encode(['status' => false, 'message' => 'Learning outcome not found']);
        }
    }

    public function toggle_active() {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        $result = $this->Learning_outcomes_model->toggle_active($id, $status);
        echo json_encode($result);
    }

    public function toggle_locked($id) {
        $status = $this->input->post('status');
        $result = $this->Learning_outcomes_model->toggle_locked($id, $status);

        echo json_encode($result);
    }

    public function get_mapped_topics() {
        $learning_outcome_id = $this->input->post('learning_outcome_id');

        if (empty($learning_outcome_id)) {
            echo json_encode(['status' => false, 'message' => 'Learning outcome ID is required']);
            return;
        }

        $mapped_topics = $this->Learning_outcomes_model->get_mapped_topics($learning_outcome_id);
        echo json_encode(['status' => true, 'data' => $mapped_topics]);
    }

    public function get_available_topics_for_mapping() {
        $learning_outcome_id = $this->input->post('learning_outcome_id');

        if (empty($learning_outcome_id)) {
            echo json_encode(['status' => false, 'message' => 'Learning outcome ID is required']);
            return;
        }

        $result = $this->Learning_outcomes_model->get_available_topics_for_mapping($learning_outcome_id);
        echo json_encode($result);
    }

    public function create_learning_outcome_topic_mapping() {
        $learning_outcome_id = $this->input->post('learning_outcome_id');
        $topic_id = $this->input->post('topic_id');

        if (empty($learning_outcome_id) || empty($topic_id)) {
            echo json_encode(['status' => false, 'message' => 'Learning outcome ID and topic ID are required']);
            return;
        }

        $result = $this->Learning_outcomes_model->create_learning_outcome_topic_mapping($learning_outcome_id, $topic_id);
        echo json_encode($result);
    }

    public function create_mass_learning_outcome_topic_mapping() {
        $learning_outcome_id = $this->input->post('learning_outcome_id');
        $topic_ids = $this->input->post('topic_ids'); // Array of topic IDs

        if (empty($learning_outcome_id) || empty($topic_ids) || !is_array($topic_ids)) {
            echo json_encode(['status' => false, 'message' => 'Learning outcome ID and topic IDs array are required']);
            return;
        }

        $results = [];
        $success_count = 0;
        $error_count = 0;
        $errors = [];

        foreach ($topic_ids as $topic_id) {
            $result = $this->Learning_outcomes_model->create_learning_outcome_topic_mapping($learning_outcome_id, $topic_id);
            $results[] = $result;

            if ($result['status']) {
                $success_count++;
            } else {
                $error_count++;
                $errors[] = $result['message'];
            }
        }

        echo json_encode([
            'status' => $success_count > 0,
            'success_count' => $success_count,
            'error_count' => $error_count,
            'errors' => $errors,
            'message' => $success_count > 0
                ? "Successfully mapped {$success_count} topic(s)" . ($error_count > 0 ? " ({$error_count} failed)" : "")
                : "Failed to map all topics"
        ]);
    }

    public function remove_learning_outcome_topic_mapping() {
        $mapping_id = $this->input->post('mapping_id');

        if (empty($mapping_id)) {
            echo json_encode(['status' => false, 'message' => 'Mapping ID is required']);
            return;
        }

        $result = $this->Learning_outcomes_model->remove_learning_outcome_topic_mapping($mapping_id);
        echo json_encode($result);
    }

    public function remove_mass_learning_outcome_topic_mapping() {
        $mapping_ids = $this->input->post('mapping_ids'); // Array of mapping IDs

        if (empty($mapping_ids) || !is_array($mapping_ids)) {
            echo json_encode(['status' => false, 'message' => 'Mapping IDs array is required']);
            return;
        }

        $results = [];
        $success_count = 0;
        $error_count = 0;
        $errors = [];

        foreach ($mapping_ids as $mapping_id) {
            $result = $this->Learning_outcomes_model->remove_learning_outcome_topic_mapping($mapping_id);
            $results[] = $result;

            if ($result['status']) {
                $success_count++;
            } else {
                $error_count++;
                $errors[] = $result['message'];
            }
        }

        echo json_encode([
            'status' => $success_count > 0,
            'success_count' => $success_count,
            'error_count' => $error_count,
            'errors' => $errors,
            'message' => $success_count > 0
                ? "Successfully removed {$success_count} mapping(s)" . ($error_count > 0 ? " ({$error_count} failed)" : "")
                : "Failed to remove all mappings"
        ]);
    }
}
<ul class="breadcrumb" id="parent_breadcums">
  <?php if(isset($callFrom) && $callFrom == ''){?>
        <?php if(!empty($previous)){ ?>
          <li><a href="<?php echo site_url('previous_year_data') ?>">Dashboard</a></li>
        <?php }else{ ?>
          <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
        <?php } ?>
    <?php } else {?>
        <li><a href="<?php echo site_url('admissionflowv2/index') ?>">Onboarding Page</a></li>
    <?php }?>
  <li>Profile</li>
</ul>
<hr>
  <?php 
    /**
     * If isNextYearStudent is not set in cache, then, we assume that he is a old student.
     * For new students, we will display only FEES and PROFILE. Other features are hidden.
     */
    $isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent)?$this->parentcache->getParentCache()->isNextYearStudent:'0';
  ?>
<!-- Desktop view only -->
<div class="col-md-12" style="margin-top: 1%">
  <div class="panel-group">
    <div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">
      <div class="card-body">
        <?php if($this->settings->getSetting('enable_enquiry_student_referral_url') == 1) { ?>
          <h4 style="color: #101087;font-weight: 700;" >Get Referral Link
          <a class="btn btn-md btn-warning pull-right" onclick="generate_enquiry_referal_link('<?php echo $student_id ?>')" id="">Referral Link</a>
        </h4>
        <br>
        <?php } ?>
      <?php if ($studentData->profile_status == 'Unlock' ) { ?>
        <h4 style="color: #101087;font-weight: 700;" >Click the Confirm button if the below information is correct.
          <a class="btn btn-md btn-warning pull-right" onclick="update_profile_confirmedbyuser('<?php echo $studentData->stdYearId ?>')" id="confirm_profile">Confirm</a>
        </h4>
      <?php }else{ ?>
        <?php if ($studentData->profile_confirmed == 'Yes' ) { ?>
          <?php if ($studentData->profile_confirmed_date) { ?>
        <h5 style="line-height: 26px;">You have confirmed profile information on Date : <?php echo date('d-M-Y',strtotime($studentData->profile_confirmed_date)) ?> <i class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right">   </span></h5>
        <?php }else{ ?>
        <h5 style="line-height: 26px;">You have confirmed profile information <i class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right">   </span></h5>
          <?php } ?>
        <?php }else{ ?>
          <?php if ($studentData->profile_status_changed_date) { ?>
          <h5 style="line-height: 26px;">The profile has been locked to prevent further edits on this date : <?php echo date('d-M-Y',strtotime($studentData->profile_status_changed_date)) ?> <i class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right">   </span></h5>
          <?php }else{ ?>
          <h5 style="line-height: 26px;">The profile has been locked to prevent further edits <i class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right">   </span></h5>
          <?php } ?>
        <?php } ?>
      <?php } ?>
      </div>
    </div>
    <?php $this->load->view('parent/profile/blocks/_student_desktop.php') ?>
      <?php if ($this->settings->isProfile_profile_enabled('school_name')) : ?>
    <?php $this->load->view('parent/profile/blocks/_previous_school_details.php') ?>
    <?php endif ?>
      <?php if ($this->settings->isProfile_profile_enabled('FATHER_NAME')) : ?>
        <?php $this->load->view('parent/profile/blocks/_father_desktop.php') ?>
      <?php endif ?>
      <?php if ($this->settings->isProfile_profile_enabled('MOTHER_NAME')) : ?>
        <?php $this->load->view('parent/profile/blocks/_mother_desktop.php') ?>
      <?php endif ?>
    <?php 
      if($show_guardian) {
        $this->load->view('parent/profile/blocks/_guardian_desktop.php');
      }
    ?>

<?php if ($this->settings->isProfile_profile_enabled('FAMILY_PHOTO')) : ?>
      <div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">

        <?php   
          $family_pic = $this->config->item('s3_base_url').'/nextelement-common/Staff and Admin icons 64px/father.png';
          if($family_picture_url != '') {
            $family_pic = $this->filemanager->getFilePath($family_picture_url);
          }
        ?>

      <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
        <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
        <h3 class="card-title">
          <strong> Family Photo  </strong>
          
        </h3>
        </div>
      </div>

      <div class="card-body">
        <div class="row" style="margin: 0px;">
          <div class="col-md-12">
            <div class="col-md-2">
              <img id="previewing" class="img-responsive" src="<?php echo $family_pic; ?>"/> 
            </div>

            <div class="col-md-8">

            </div>
          </div> 
        </div>
      </div> 
      
    </div> 
  <?php endif ?>

  <?php if (($this->settings->isProfile_profile_enabled('STUDENT_REMARKS'))) : ?>
      <div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">

      <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
        <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
        <h3 class="card-title">
          <strong> Remarks  </strong>
          
        </h3>
        </div>
      </div>

      <div class="card-body">
        <div class="row" style="margin: 0px;">
          <div class="col-md-12">

            <h5><?php if(!empty($studentData->student_remarks)){
              echo $studentData->student_remarks;
            }else{
              echo '-';
            }  ?></h5>
          </div> 
        </div>
      </div> 
      
    </div> 
  <?php endif ?>
  
  <?php if ($this->settings->isProfile_profile_enabled('ELECTIVES')) : ?>
    <?php if(!$isNewStudent && !empty($electives)){ ?>
      <?php $this->load->view('parent/profile/blocks/_electives_desktop.php') ?>
    <?php } ?>
  <?php endif ?>


    <?php $staff_handling = $this->settings->getSetting('show_staff_handling_in_parent_profile')  ?>
  
    <?php 
    
      if ($staff_handling == TRUE && !$isNewStudent) {
        $this->load->view('parent/profile/blocks/_cls_teacher_sub_desktop.php');
      }
      ?>

    <?php if ($canteenModuleEnabled && !$isNewStudent) $this->load->view('parent/profile/blocks/_canteen_desktop.php') ?>
  </div>
</div>


<!-- <div class="visible-xs visible-sm">
  <a href="<?php //echo site_url('dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div> -->

<script type="text/javascript">
  function update_profile_confirmedbyuser(stdYearId) {
    bootbox.confirm({
      title : "Confirm",  
      message: "Are you sure that the profile information is correct ?",
      className: "medium",
      buttons: {
        confirm: {
            label: 'Yes',
            className: 'btn-success'
        },
        cancel: {
            label: 'No',
            className: 'btn-danger'
        }
      },
      callback: function (result) {
        if(result) { 
          $.ajax({
            url: '<?php echo site_url('parent_controller/update_profile_confirmed'); ?>',
            type: 'post',
            data: {'stdYearId' : stdYearId},
            success:function(data){
              var response = JSON.parse(data);
              // console.log(response);
              if (response == 1) {
                location.reload();
              }else{
                mandatory_fields_display_in_popup(response);
              }
            }
          });    
        }
      }
    });
  }

  function mandatory_fields_display_in_popup(response) {
    var html = '';
    html +='<h6>The following fields needs to be entered to \'Confirm\' - </h6>';
    for (var i = 0; i < response.length; i++) {
      var field =  response[i].replace(/_/g,' ');
      html +='<div class="col-md-12">';
      html +='<span class="label label-form" style="color:#000" >'+(i+1)+'. '+field+'</span>';
      html +='</div>';
    }
    html +='<h6 style="margin-top:9rem">Kindly add the fields and \'Confirm\' the data.</h6>';
    bootbox.alert({
      title:'Confirmation message.',
      message: html,
      className:'medium',
      backdrop: true
    });
  }

  function generate_enquiry_referal_link(student_id){
    $.ajax({
          url: '<?php echo site_url('parent_controller/get_referal_link'); ?>',
          type: 'post',
          data: {'student_id' : student_id},
          success:function(data){
            var response = JSON.parse(data);
            console.log(response)
            if (response) {
              bootbox.alert({
                title:'Referral Link',
                message: response+`<button onclick="copyToClipboard('${response}',this)" style="margin-left: 10px; cursor: pointer;" id="copyBtn">Copy</button>`,
                className:'link_medium',
                backdrop: true
              });
            }
          }
    });   
  }

  function copyToClipboard(text, button) {
    navigator.clipboard.writeText(text).then(() => {
        button.textContent = 'Copied';
        button.style.backgroundColor = '#4caf50'; // Optional: Change button color to indicate success
        button.style.color = '#fff';
        setTimeout(() => {
            button.textContent = 'Copy';
            button.style.backgroundColor = ''; // Reset to default
            button.style.color = ''; // Reset to default
        }, 2000); // Reset button text after 2 seconds
    }).catch(err => {
        console.error('Could not copy text: ', err);
    });
}
</script>
<style>

@media (min-width: 992px) {  
  .medium {
    width: 450px;
    margin: auto;
  } 
}

@media (min-width: 1200px) {  
  .medium {
    width: 450px;
    margin: auto;
  } 
}


  .panel.panel-primary {
      border:none;
  }

  .panel-group .panel {
      margin-bottom : 0;
      border-radius : 8px;
  }
  .control-label
    {
      color : #4165a2;
    }
    .form-horizontal .control-label {
    padding-top: 5px;
    margin-bottom: 0;
     text-align: left; 
}
.link_medium{
  width: 50%;
  margin: auto;
}
</style>
   


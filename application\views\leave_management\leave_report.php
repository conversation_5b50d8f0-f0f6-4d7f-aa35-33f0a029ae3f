<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('leave_controller');?>">Leave</a></li>
    <li class="active">Leave Report</li>
</ul>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-12 pl-0">
                     <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('leave_controller');?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Leave Report - Students on Leave
                    </h3>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Filters -->
            <div class="row mb-3">
                <div class="col-md-4 form-group">
                    <label for="report_date">Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="report_date" name="report_date" value="<?php echo date('Y-m-d'); ?>" required>
                </div>

                <div class="col-md-4 form-group">
                    <label for="class_section_filter">Class & Section</label>
                    <select class="form-control" id="class_section_filter" name="class_section_filter">
                        <option value="">All Classes & Sections</option>
                        <?php foreach($getclasssections as $classsection): ?>
                            <option value="<?php echo $classsection->id; ?>"><?php echo $classsection->class_name; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-4 form-group">
                    <label>&nbsp;</label><br>
                    <button type="button" class="btn btn-primary" onclick="loadLeaveReport()">
                         Generate Report
                    </button>
                </div>
            </div>

           
          

            <!-- Results Table -->
            <div class="hidden-xs" id="tableContainer" style="display: none;">
                <table class="table table-bordered" id="leaveReportTable">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Student Name (Roll No.)</th>
                            <th>Class</th>
                            <th>Section</th>
                            <th>Admission No</th>
                            <th>Enrollment No</th>
                            <th>From Date</th>
                            <th>To Date</th>
                            <th>Leave Type</th>
                            <th>Reason</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="leaveReportData">
                    </tbody>
                </table>
            </div>

            <!-- Mobile View -->
            <div class="visible-xs" id="mobileLeaveReportData">
            </div>

           
        </div>
    </div>
</div>

<script>
$(document).ready(function () {
    // DataTable will be initialized when data is loaded
});

function loadLeaveReport() {
    var date = $('#report_date').val();
    var classSectionId = $('#class_section_filter').val();

    if (!date) {
        alert('Please select a date');
        return;
    }

    var mobile = 0;
    if($(window).width() < 760) {
        mobile = 1;
    }

    // Hide initial message and show loading
    $('#initialMessage').hide();

    $.ajax({
        url: '<?php echo site_url('leave_controller/get_leave_report_data'); ?>',
        type: 'POST',
        data: {
            'date': date,
            'class_section_id': classSectionId,
            'mobile': mobile
        },
        beforeSend: function() {
            if(mobile) {
                $("#mobileLeaveReportData").html('<p class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</p>');
            } else {
                $('#tableContainer').show();
                $("#leaveReportData").html('<tr><td colspan="11" class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</td></tr>');
            }
        },
        success: function (data) {
            var leaveData = JSON.parse(data);
            var html = '';

            if(mobile) {
                html = buildMobileLeaveData(leaveData);
                $("#mobileLeaveReportData").html(html);
            } else {
                html = buildTableLeaveData(leaveData);
                $("#leaveReportData").html(html);

                // Initialize or reinitialize DataTable
                if ($.fn.DataTable.isDataTable('#leaveReportTable')) {
                    $('#leaveReportTable').DataTable().destroy();
                }
                $('#leaveReportTable').DataTable({
                    paging: true,
                    ordering: true,
                    info: true,
                    searching: true,
                    pageLength: 25,
                    order: [[1, 'asc']]
                });
            }
        },
        error: function (err) {
            console.log(err);
            if(mobile) {
                $("#mobileLeaveReportData").html('<p class="text-center text-danger">Error loading data. Please try again.</p>');
            } else {
                $('#tableContainer').show();
                $("#leaveReportData").html('<tr><td colspan="11" class="text-center text-danger">Error loading data. Please try again.</td></tr>');
            }
        }
    });
}

function buildTableLeaveData(data) {
    if(data.length == 0) {
        return '<tr><td colspan="11" class="text-center"><strong>No student leaves found for the selected date and class & section.</strong></td></tr>';
    }

    var html = '';
    for (var i = 0; i < data.length; i++) {
        html += '<tr>';
        html += '<td>'+(i+1)+'</td>';
        html += '<td>'+data[i].student_name_with_roll+'</td>';
        html += '<td>'+data[i].grade+'</td>';
        html += '<td>'+data[i].section+'</td>';
        html += '<td>'+data[i].admission_no+'</td>';
        html += '<td>'+data[i].enrollment_no+'</td>';
        html += '<td>'+data[i].from_date+'</td>';
        html += '<td>'+data[i].to_date+'</td>';
        html += '<td>'+data[i].leave_type+'</td>';
        html += '<td>'+data[i].reason+'</td>';
        html += '<td>'+data[i].status+'</td>';
        html += '</tr>';
    }
    return html;
}

function buildMobileLeaveData(data) {
    if(data.length == 0) {
        return '<h4>No student leaves found for the selected date and class & section.</h4>';
    }

    var html = '';
    for (var i = 0; i < data.length; i++) {
        html += '<div class="panel panel-default">';
        html += '<div class="panel-body">';
        html += '<p><strong>Student: </strong>'+data[i].student_name_with_roll+'</p>';
        html += '<p><strong>Grade/Section: </strong>'+data[i].grade+' - '+data[i].section+'</p>';
        html += '<p><strong>Admission No: </strong>'+data[i].admission_no+'</p>';
        html += '<p><strong>Enrollment No: </strong>'+data[i].enrollment_no+'</p>';
        html += '<p><strong>Leave Period: </strong>'+data[i].from_date+' to '+data[i].to_date+'</p>';
        html += '<p><strong>Leave Type: </strong>'+data[i].leave_type+'</p>';
        html += '<p><strong>Reason: </strong>'+data[i].reason+'</p>';
        html += '<p><strong>Status: </strong>'+data[i].status+'</p>';
        html += '</div>';
        html += '</div>';
    }
    return html;
}
</script>

<style type="text/css">
.mb-3 {
    margin-bottom: 1rem;
}
</style>

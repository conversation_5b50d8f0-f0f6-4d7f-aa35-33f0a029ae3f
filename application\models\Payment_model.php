<?php

class Payment_model extends CI_Model {
    

    // public function storeHash($order_id, $hash){
    //     $data =  array('order_id'=>$order_id, 'hash'=>$hash);
    //     $this->db->insert('payment_hash_values',$data);
    // }

    public function __construct() {
        parent::__construct();

        // $this->yearId = $this->acad_year->getAcadYearId();
    }

    public function getHash($order_id){
        $this->db->select('hash');
        $this->db->where('order_id', $order_id);
        $returnHash = $this->db->get('payment_hash_values')->row_array();
    }

    public function saveTransaction($input){
        $data = array(
            'transaction_id'=> $input['transaction_id'],
            'payment_mode'=> $input['payment_mode'],
            'payment_channel'=> $input['payment_channel'],
            'payment_datetime'=> $input['payment_datetime'],
            'response_code'=> $input['response_code'],
            'response_message'=> $input['response_message'],
            'error_desc'=> $input['error_desc'],
            'order_id'=> $input['order_id'],
            'amount'=> $input['amount'],
            'currency'=> $input['currency'],
            'description'=> $input['description'],
            'name'=> $input['name'],
            'email'=> $input['email'],
            'phone'=> $input['phone'],
            'address_line_1'=> $input['address_line_1'],
            'address_line_2'=> $input['address_line_2'],
            'city'=> $input['city'],
            'state'=> $input['state'],
            'country'=> $input['country'],
            'zip_code'=> $input['zip_code'],
            'cardmasked'=> $input['cardmasked']
            
                    );
                    //Return Hash not storing 
                    $this->db->insert('payment_transactions', $data);
                    
    }

    public function get_trans_dates() {
        $result = $this->db->select('date_format(tx_date_time, "%d-%b-%Y") as tx_date, settlement_status, settlement_confirmed_by, settlement_confirmed_on')
            ->where('tx_response_code', '0')
            ->group_by('tx_date')
            ->order_by('tx_date_time', 'desc')
            ->get('online_payment_master')->result();

        return $result; 
    }

    public function get_tx_details_from_order_id($order_id_arr) {
        if(empty($order_id_arr)){
            return $result = [];
        }
        //Get the transactions from Fee Payments
        $acad_year = $this->acad_year->getAcadYearId();
        $result = $this->db_readonly->select('concat(ifnull(sa.first_name,""), " ", ifnull(sa.last_name, "")) as student_name, ft.receipt_number, opm.tx_id, opm.split_json, date_format(ft.paid_datetime, "%d-%b-%Y %H:%i") as paid_datetime, opm.amount as amount_paid, concat(cs.class_name, cs.section_name) as section_name, "School Fee" as fees_type, opm.order_id,ft.receipt_pdf_link, ft.pdf_status, ft.id as trans_id, ifnull(sa.admission_no,"") as admission_no, ifnull(sa.enrollment_number,"") as enrollment_number')
            ->from('online_payment_master opm')
            ->join('feev2_transaction ft', 'ft.id=opm.source_id', 'left')
            ->join('student_admission sa', 'sa.id=ft.student_id', 'left')
            ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year", 'left')
            ->join('class_section cs', 'sy.class_section_id=cs.id', 'left')
            ->where_in('opm.order_id', $order_id_arr)
            ->order_by('ft.receipt_number')
            ->get()->result();

        //Get the transactions from Application Payments
        $acad_year = $this->acad_year->getAcadYearId();
        $result1 = $this->db_readonly->select('af.std_name as student_name, af.receipt_number, opm.tx_id, opm.order_id, "" as split_json, date_format(opm.tx_date_time, "%d-%b-%Y %H:%i") as paid_datetime, opm.amount as amount_paid, "-" as section_name, "Application Fee" as fees_type')
            ->from('admission_forms af')
            ->join('online_application_fee_payment_master opm', 'opm.source_id=af.id', 'left')
            ->where_in('opm.order_id', $order_id_arr)
            ->order_by('af.receipt_number')
            ->get()->result();
        
        $itari_app = $this->db_readonly->select('af.name as student_name, "0" as receipt_number, opm.tx_id, opm.order_id, "" as split_json, date_format(opm.tx_date_time, "%d-%b-%Y %H:%i") as paid_datetime, opm.amount as amount_paid, af.course as section_name, "Itari Application Fee" as fees_type')
            ->from('itari_admissions_forms af')
            ->join('online_application_fee_payment_master opm', 'opm.source_id=af.id', 'left')
            ->where_in('opm.order_id', $order_id_arr)
            ->get()->result();

        //Get the transactions from Online challan Payments
        $challan = $this->db_readonly->select('(case when opm.source_id != 0 then concat(ifnull(sa.first_name,""), " ", ifnull(sa.last_name, "")) else opm.customer_name  end)  as student_name, ifnull(ft.receipt_number, "") as receipt_number, opm.transaction_id as tx_id, "" as split_json,   (case when opm.source_id != 0 then date_format(ft.paid_datetime, "%d-%b-%Y %H:%i") else date_format(opm.payment_datetime, "%d-%b-%Y %H:%i") end)  as paid_datetime, opm.amount_orig as amount_paid, concat(ifnull(cs.class_name,""), ifnull(cs.section_name,"")) as section_name, "Challan / School Fee" as fees_type, opm.order_id, ifnull(sa.admission_no,"") as admission_no, ifnull(sa.enrollment_number,"") as enrollment_number')
        ->from('feev2_online_challan_payments opm')
        ->join('feev2_transaction ft', 'ft.id=opm.source_id', 'left')
        ->join('student_admission sa', 'sa.id=ft.student_id', 'left')
        ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year", 'left')
        ->join('class_section cs', 'sy.class_section_id=cs.id', 'left')
        ->where_in('opm.order_id', $order_id_arr)
        ->order_by('ft.receipt_number')
        ->get()->result();
        $result = array_merge($result, $result1,$challan, $itari_app);
        return $result;
    }

    public function get_tx_details_from_order_accountwise_id($order_id_vendorarray){
      
        if(empty($order_id_vendorarray)){
            return [];
        }
        
        $orderIds = [];
        $allSourceIds = [];
        $vendoraccountId = 0;
        $results = []; // Initialize array to store all results
        
        foreach ($order_id_vendorarray as $accountId => $order_id) {
            $vendoraccountId = $accountId;
            $paymentMaster = $this->db->select('source_id')
                ->from('online_payment_master opm')
                ->where_in('order_id', $order_id)
                ->get()->result();
                
            foreach ($paymentMaster as $item) {
                if (strpos($item->source_id, '[') !== false) {
                    $ids = trim($item->source_id, '[]');
                    $idArray = explode(',', $ids);
                    $allSourceIds = array_merge($allSourceIds, $idArray);
                } else {
                    $allSourceIds[] = $item->source_id;
                }
            }
        }
        
        $acad_year = $this->acad_year->getAcadYearId();
        $allSourceIds = array_filter(array_unique($allSourceIds)); // Remove duplicates and empty values
        if (!empty($allSourceIds)) {
            $results = $this->db_readonly->select('concat(ifnull(sa.first_name,""), " ", ifnull(sa.last_name, "")) as student_name, 
            ft.receipt_number, 
            opm.tx_id, 
            SUBSTRING_INDEX(
                SUBSTRING_INDEX(
                    SUBSTRING_INDEX(
                        opm.split_json, 
                        CONCAT(\'"\', "vendor_code", \'":"\', "'.$vendoraccountId.'", \'","split_amount_fixed":\'), 
                        -1
                    ), 
                    \'}"\', 
                    1
                ), 
                \',\', 
                1
            ) as split_amount,
            date_format(ft.paid_datetime, "%d-%b-%Y %H:%i") as paid_datetime, 
            ft.amount_paid as amount_paid, 
            concat(cs.class_name, cs.section_name) as section_name, 
            "School Fee" as fees_type, 
            opm.order_id,
            ft.receipt_pdf_link, 
            ft.pdf_status, 
            ft.id as trans_id, ifnull(sa.admission_no,"") as admission_no, ifnull(sa.enrollment_number,"") as enrollment_number')
            ->from('online_payment_master opm')
            ->join('feev2_transaction ft', 'FIND_IN_SET(ft.id, REPLACE(REPLACE(opm.source_id, "[", ""), "]", ""))')
            ->join('feev2_transaction_installment_component ftic', 'ft.id=ftic.fee_transaction_id')
            ->join('feev2_blueprint_components fbc', 'ftic.blueprint_component_id=fbc.id')
            ->where('fbc.vendor_code', $vendoraccountId)
            ->join('student_admission sa', 'sa.id=ft.student_id', 'left')
            ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year", 'left')
            ->join('class_section cs', 'sy.class_section_id=cs.id', 'left')
            ->where_in('ft.id', $allSourceIds)
            ->group_by('ft.id')
            ->order_by('ft.receipt_number')
            ->get()->result();
        }
        return $results;
      
    }

    public function get_settlement_verification_data($settlement_id_arr) {
        if (empty($settlement_id_arr)) {
            return [];
        }
        //Taking this from db instead of db_readonly as we read it immediately after insert
        $result = $this->db->select('settlement_id, verification_status, remarks, concat(ifnull(sm.first_name, ""), " ", ifnull(sm.last_name, "")) as staff_name, date_format(verified_on, "%d-%m-%Y") as verified_on')
            ->from('feev2_online_settlement_verification fosv')
            ->join('staff_master sm', 'fosv.verified_by=sm.id', 'left')
            ->where_in('settlement_id', $settlement_id_arr)
            ->get()->result();

        return $result;
    }

    public function submit_settlement_verification($settlement_id, $settlement_comment) {
        $verification_obj = [
            'remarks' => $settlement_comment, 
            'verification_status' => 'Verified',
            'verified_by' => $this->authorization->getAvatarStakeHolderId()
        ];
        return $this->db->where('settlement_id', $settlement_id)->update('feev2_online_settlement_verification', $verification_obj);
    }

    public function get_daily_transactions_model($date){
        /*$this->db->select("opm.id, opm.amount,opm.settlement_status, opm.source, opm.tx_id, opm.tx_payment_mode, opm.tx_date_time, concat(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as parent_name, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as stdName, concat(cs.class_name,'',cs.section_name) as classSection");
        $this->db->join('feev2_transaction ft', 'ft.id=opm.source_id');
        $this->db->join('avatar a', 'a.id=opm.transaction_by and avatar_type=2', 'left');
        $this->db->join('parent p', 'a.stakeholder_id=p.id', 'left');
        $this->db->join('student_admission sa', 'sa.id=p.student_id');
        $this->db->join('student_year sy', 'sa.id=sy.student_admission_id');
        $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
        $this->db->where("DATE_FORMAT(tx_date_time,'%Y-%m-%d')",  $date);
        //$this->db->where("execution_mode",  "TEST");  // CHange it to Live
        $this->db->where("payment_to",  "SCHOOL");
        $this->db->where("hash_match",  "1");
        $this->db->where("tx_response_code",  "0");
        // $this->db->where("recon_status",  "");
        $res =  $this->db->get('online_payment_master opm')->result_array(); 

        echo "<pre>"; print_r($res); */
        $acad_year = $this->acad_year->getAcadYearId();

        $sourceIds = $this->db->select('source_id')
        ->from('online_payment_master opm')
        ->where("DATE_FORMAT(opm.tx_date_time,'%Y-%m-%d')", $date)
        ->where("tx_response_code", "0")
        ->where("payment_to",  "SCHOOL")
        ->where("hash_match",  "1")
        ->get()->result();

        $allSourceIds = [];
        foreach ($sourceIds as $item) {
            $ids = trim($item->source_id, '[]');
            $idArray = explode(',', $ids);
            $allSourceIds = array_merge($allSourceIds, $idArray);
        }

        $allSourceIds = array_filter(array_unique($allSourceIds));


        $this->db->select("
            opm.id, 
            opm.amount,
            opm.settlement_status, 
            opm.source, 
            opm.tx_id, 
            opm.tx_payment_mode, 
            opm.tx_date_time, 
            CONCAT(IFNULL(p.first_name, ''), ' ', IFNULL(p.last_name, '')) as parent_name, 
            CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) as stdName, 
            sa.admission_no, 
            CONCAT(cs.class_name, '', cs.section_name) as classSection,
            fb.name as blueprint_name, 
            fi.name as installment_name, 
            fbc.name as component_name, 
            ftic.amount_paid as component_amount_paid, 
            ft.concession_amount, 
            IFNULL(ft.fine_amount, '0') as fine_amount, 
            ft.adjustment_amount, 
            ftic.blueprint_installments_id as insId, 
            ay.id as acad_year_id, 
            ay.acad_year as acad_year
        ");

        $this->db->from('online_payment_master opm')
            ->join('avatar a', 'a.id=opm.transaction_by AND avatar_type=2', 'left')
            ->join('parent p', 'a.stakeholder_id=p.id', 'left')
            ->join('student_admission sa', 'sa.id=p.student_id')
            ->join('student_year sy', "sa.id=sy.student_admission_id AND sy.acad_year_id=$acad_year")
            ->join('class_section cs', 'sy.class_section_id=cs.id', 'left');

        // Handle source_id join condition
        if (!empty($allSourceIds)) {
            $joinConditions = [];
            foreach ($allSourceIds as $id) {
                $joinConditions[] = "FIND_IN_SET(ft.id, REPLACE(REPLACE(opm.source_id, '[', ''), ']', ''))";
            }
            $joinCondition = '(' . implode(' OR ', $joinConditions) . ')';
        } else {
            $joinCondition = 'opm.source_id = ft.id';
        }
        $this->db->join('feev2_transaction ft', $joinCondition);

        // Add remaining joins
        $this->db->join('feev2_student_schedule fss', 'ft.fee_student_schedule_id=fss.id')
            ->join('feev2_blueprint_installment_types fbit', 'fss.feev2_blueprint_installment_types_id=fbit.id')
            ->join('feev2_blueprint fb', 'fb.id=fbit.feev2_blueprint_id')
            ->join('academic_year ay', 'fb.acad_year_id=ay.id')
            ->join('feev2_transaction_installment_component ftic', 'ft.id=ftic.fee_transaction_id')
            ->join('feev2_installments fi', 'ftic.blueprint_installments_id=fi.id')
            ->join('feev2_blueprint_components fbc', 'ftic.blueprint_component_id=fbc.id');

        // Add where conditions
        $this->db->where("DATE_FORMAT(opm.tx_date_time,'%Y-%m-%d')", $date)
            ->where("tx_response_code", "0")
            ->where("ft.status", 'SUCCESS');

        $result = $this->db->get()->result();
        
        if (empty($result)) {
            return [];
        }
        
        return $result;
        // echo "<pre>"; print_r($result); die();
    }

    public function get_online_transaction_data () {
        $acad_year = $this->acad_year->getAcadYearId();

        $result = $this->db->select("opm.id as opm_id, opm.order_id, opm.tx_response_code as response_code, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode, concat(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as parent_name, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, concat(cs.class_name, cs.section_name) as csname, opm.status, opm.tx_id")
            ->join('avatar a', 'a.id=opm.transaction_by and avatar_type=2', 'left')
            ->join('parent p', 'a.stakeholder_id=p.id', 'left')
            ->join('student_admission sa', 'p.student_id=sa.id', 'left')
            ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year", 'left')
            ->join('class_section cs', "sy.class_section_id=cs.id", 'left')
            ->order_by('opm.init_date_time', 'DESC')
            ->get('online_payment_master opm')->result();

        foreach ($result as &$tx) {
            $tx->response_message = $this->payment->get_payment_status_from_error_code($tx->response_code);
            switch ($tx->source) {
                case 'PARENT_FEE':
                    $tx->source_desc = 'Student Fee';
                    break;
                default:
                    $tx->source_desc = 'Unknown';
            }
        }

        return $result;
    }

    public function get_transaction_detail ($opm_id) {
        $acad_year = $this->acad_year->getAcadYearId();
        
        $tx = $this->db->select("opm.*, concat(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as parent_name, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, concat(cs.class_name, cs.section_name) as csname")
            ->join('avatar a', 'a.id=opm.transaction_by and avatar_type=2', 'left')
            ->join('parent p', 'a.stakeholder_id=p.id', 'left')
            ->join('student_admission sa', 'p.student_id=sa.id', 'left')
            ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year", 'left')
            ->join('class_section cs', "sy.class_section_id=cs.id", 'left')
            ->where('opm.id', $opm_id)
            ->get('online_payment_master opm')->row();
        // echo '<pre>';print_r($tx);die();

        $tx->response_message = $this->payment->get_payment_status_from_error_code($tx->tx_response_code);
        switch ($tx->source) {
            case 'PARENT_FEE':
                $tx->source_desc = 'Student Fee';
                break;
            default:
                $tx->source_desc = 'Unknown';
        }

        return $tx;
    }

    public function confirmSettlement() {
        $input = $this->input->post();
        $transIds = explode(",", $input['trans_ids']);
        $settlementIds = explode(",", $input['settlement_ids']);
        $settle = [];
        foreach ($settlementIds as $id) {
            $settle[] = array('id' => $id);
        }
        $settle_json = json_encode($settle);
        $confirmBy = $this->authorization->getAvatarId();
        $confirmOn = date('Y-m-d H:i:s');
        foreach ($transIds as $transId) {
            $data[] = array(
                'id' => $transId,
                'settlement_status' => 'SETTLED',
                'settlement_id_json' => $settle_json,
                'settlement_confirmed_by' => $confirmBy,
                'settlement_confirmed_on' => $confirmOn
            );
        }
        $this->db->where_in('id', $transIds);
        return $this->db->update_batch('online_payment_master', $data, 'id');
    }

    public function getTransactionDetailsByStudent($name){
        $acad_year = $this->acad_year->getAcadYearId();

        $result = $this->db->select("opm.id as opm_id, opm.order_id, opm.tx_response_code as response_code, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode, concat(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as parent_name, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, concat(cs.class_name, cs.section_name) as csname, opm.status, opm.tx_id, opm.split_json, sa.id as student_id")
            ->join('avatar a', 'a.id=opm.transaction_by and avatar_type=2', 'left')
            ->join('parent p', 'a.stakeholder_id=p.id', 'left')
            ->join('student_admission sa', 'p.student_id=sa.id', 'left')
            ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year", 'left')
            ->join('class_section cs', "sy.class_section_id=cs.id", 'left')
            ->where("(LOWER(sa.first_name) like '%$name%' OR (LOWER(sa.last_name) like '%$name%'))")
            ->order_by('opm.init_date_time', 'DESC')
            ->get('online_payment_master opm')->result();

        foreach ($result as &$tx) {
            $tx->response_message = $this->payment->get_payment_status_from_error_code($tx->response_code);
            switch ($tx->source) {
                case 'PARENT_FEE':
                    $tx->source_desc = 'Student Fee';
                    break;
                default:
                    $tx->source_desc = 'Unknown';
            }
        }

        return $result;
    }

    public function getTransactionDetailsByOrderId($order){
        $acad_year = $this->acad_year->getAcadYearId();

        $result = $this->db->select("opm.id as opm_id, opm.order_id, opm.tx_response_code as response_code, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode, concat(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as parent_name, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, concat(cs.class_name, cs.section_name) as csname, opm.status, opm.tx_id, opm.split_json, sa.id as student_id")
            ->join('avatar a', 'a.id=opm.transaction_by and avatar_type=2', 'left')
            ->join('parent p', 'a.stakeholder_id=p.id', 'left')
            ->join('student_admission sa', 'p.student_id=sa.id', 'left')
            ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year", 'left')
            ->join('class_section cs', "sy.class_section_id=cs.id", 'left')
            ->where("(opm.order_id) like '%$order%'")
            ->order_by('opm.init_date_time', 'DESC')
            ->get('online_payment_master opm')->result();

        foreach ($result as &$tx) {
            $tx->response_message = $this->payment->get_payment_status_from_error_code($tx->response_code);
            $tx->trans_check = $this->check_paymen_tx_success_in_trans_table($tx->opm_id);
            switch ($tx->source) {
                case 'PARENT_FEE':
                    $tx->source_desc = 'Student Fee';
                    break;
                default:
                    $tx->source_desc = 'Unknown';
            }
        }
        // echo "<pre>";print_r($result);die();

        return $result;
    }

    public function getTransactionDetailsByTransactionId($transaction){
        $acad_year = $this->acad_year->getAcadYearId();

        $result = $this->db->select("opm.id as opm_id, opm.order_id, opm.tx_response_code as response_code, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode, concat(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as parent_name, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, concat(cs.class_name, cs.section_name) as csname, opm.status, opm.tx_id, opm.split_json, sa.id as student_id, opm.source_id")
            ->join('avatar a', 'a.id=opm.transaction_by and avatar_type=2', 'left')
            ->join('parent p', 'a.stakeholder_id=p.id', 'left')
            ->join('student_admission sa', 'p.student_id=sa.id', 'left')
            ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year", 'left')
            ->join('class_section cs', "sy.class_section_id=cs.id", 'left')
            ->where("(opm.tx_id) like '%$transaction%'")
            ->order_by('opm.init_date_time', 'DESC')
            ->get('online_payment_master opm')->result();

        foreach ($result as &$tx) {
            $tx->response_message = $this->payment->get_payment_status_from_error_code($tx->response_code);
            $tx->trans_check = $this->check_paymen_tx_success_in_trans_table($tx->opm_id);
            switch ($tx->source) {
                case 'PARENT_FEE':
                    $tx->source_desc = 'Student Fee';
                    break;
                default:
                    $tx->source_desc = 'Unknown';
            }
        }
        // echo "<pre>";print_r($result);die();

        return $result;
    }

    public function check_paymen_tx_success_in_trans_table($opm_id){

        $result = $this->db->select('opm.id')
        ->from('online_payment_master opm')
        ->where('opm.source_id in (select ft.id from feev2_transaction ft where ft.status ="INITIATED" or ft.status ="FAILED")')
        ->where('opm.tx_response_code',0)
        ->where('opm.id',$opm_id)
        ->get()->row();
        
        if(!empty($result)){
            return 1;
        }else{
            return 0;
        }

    }
    public function getTransactionDetailsByDate($date){
        $acad_year = $this->acad_year->getAcadYearId();

        $result = $this->db->select("opm.id as opm_id, opm.order_id, opm.tx_response_code as response_code, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode, concat(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as parent_name, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, concat(cs.class_name, cs.section_name) as csname, opm.status, opm.tx_id, opm.split_json, sa.id as student_id")
            ->join('avatar a', 'a.id=opm.transaction_by and avatar_type=2', 'left')
            ->join('parent p', 'a.stakeholder_id=p.id', 'left')
            ->join('student_admission sa', 'p.student_id=sa.id', 'left')
            ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year", 'left')
            ->join('class_section cs', "sy.class_section_id=cs.id", 'left')
            ->where("DATE_FORMAT(opm.init_date_time, '%Y-%m-%d') like '%$date%'" )
            // ->where("DATE_FORMAT('opm.init_date_time', '%Y-%m-%d') ", "=" ,    "%$date%" ) 
            // ->where("(opm.init_date_time) like '%$date%'")
        //    ->where("DATE_FORMAT(from_unixtime(channel_titles.entry_date), '%Y-%m-%d')", $yesterday, false)
            ->order_by('opm.init_date_time', 'DESC')
            ->get('online_payment_master opm')->result();

            // echo "<pre>";print_r($this->db->last_query());die();

        foreach ($result as &$tx) {
            $tx->response_message = $this->payment->get_payment_status_from_error_code($tx->response_code);
            switch ($tx->source) {
                case 'PARENT_FEE':
                    $tx->source_desc = 'Student Fee';
                    break;
                default:
                    $tx->source_desc = 'Unknown';
            }
        }
        // echo "<pre>";print_r($result);die();

        return $result;
    }

    public function getChallanTransactionDetails($from_date, $to_date) {
        $fromDate = date('Y-m-d',strtotime($from_date));
    	$toDate =date('Y-m-d',strtotime($to_date));
        $this->db_readonly->select('*, date_format(payment_datetime, "%d-%b-%Y %H:%i") as payment_datetime');
        $this->db_readonly->from('feev2_online_challan_payments');
        $this->db_readonly->where('date_format(payment_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
        return $this->db_readonly->get()->result();
    }

    public function get_online_transaction_report_all_data($from_date, $to_date){
        $fromDate = date('Y-m-d',strtotime($from_date));
    	$toDate =date('Y-m-d',strtotime($to_date));
        $result =  $this->db_readonly->select('opm.id')
        ->from('online_payment_master opm')
        ->where('opm.source_id not in ( select ft.id from feev2_transaction ft where ft.status ="SUCCESS")')
        // ->where('hash_match',1)
        ->where('date_format(opm.init_date_time,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"')
        ->get()->result();
        $opmids = [];
        foreach ($result as $key => $val) {
            array_push($opmids, $val->id);
        }
        return $opmids;
    }

    public function get_receipt_not_generated_online_transaction_report($onlineOpmIds){

        $acad_year = $this->acad_year->getAcadYearId();
        $txorderIds = [];
        foreach ($onlineOpmIds as $key => $omp_id) {
            $tx_status =  $this->payment->get_transaction_status($omp_id);
            $decode = json_decode($tx_status->data);
            if(!empty($decode->data[0])){
                if ($decode->data[0]->response_code == 0) {
                    array_push($txorderIds, $decode->data[0]->order_id);
                }
            }
        }
        $onlinetx = [];
        if(!empty($txorderIds)){
            $onlinetx = $this->db_readonly->select("opm.id as opm_id, opm.order_id, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode,  opm.status, opm.tx_id")
            ->from('online_payment_master opm')
            ->where_in("opm.order_id",$txorderIds)
            ->where('opm.source_id in ( select ft.id from feev2_transaction ft where ft.status ="INITIATED" or ft.status ="FAILED")')
            ->get()->result();
        }
        return $onlinetx;
    }
    

    public function get_online_refund_transaction_data($mode, $getValue){
        
        // $fromDate = date('Y-m-d',strtotime($from_date));
    	// $toDate =date('Y-m-d',strtotime($to_date));

        // $result =  $this->db_readonly->select('opm.tx_id')
        // ->from('online_payment_master opm')
        // ->where('opm.tx_response_code',0)
        // ->where('date_format(opm.init_date_time,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"')
        // ->get()->result();FEUPIIFEA34F91CE8
        $acad_year = $this->acad_year->getAcadYearId();
        $this->db_readonly->select("opm.init_date_time, opm.tx_payment_mode, concat(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as parent_name, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, concat(cs.class_name, cs.section_name) as csname, opm.tx_id");
        $this->db_readonly->join('avatar a', 'a.id=opm.transaction_by and avatar_type=2', 'left');
        $this->db_readonly->join('parent p', 'a.stakeholder_id=p.id', 'left');
        $this->db_readonly->join('student_admission sa', 'p.student_id=sa.id', 'left');
        $this->db_readonly ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year", 'left');
        $this->db_readonly->join('class_section cs', "sy.class_section_id=cs.id", 'left');
        if($mode == 'student_name'){
            $this->db_readonly->where("(LOWER(sa.first_name) like '%$getValue%' OR (LOWER(sa.last_name) like '%$getValue%'))");
        }
        if($mode == 'tx_date'){
            $date  = date('Y-m-d', strtotime($getValue));
            $this->db_readonly->where("DATE_FORMAT(opm.init_date_time, '%Y-%m-%d') like '%$date%'" );
        }
        if($mode == 'tx_id'){
            $this->db_readonly->where("opm.tx_id",$getValue);
        }
        $this->db_readonly->order_by('opm.init_date_time', 'DESC');
        $result = $this->db_readonly->get('online_payment_master opm')->result();
        $refundData = [];
        foreach ($result as $key => $val) {
            $refund_status = $this->payment->get_refund_transaction_by_tx_id($val->tx_id);
            if (isset($refund_status->error) && $refund_status->error->code == '1050') {
                continue; 
            }
            if (!empty($refund_status->data) && isset($refund_status->data[0]->refund_details[0])) {
                $refund = $refund_status->data[0];
                $refundDetails = $refund->refund_details[0];
                $refundData[] = array(
                    'student_name'=> $val->student_name,
                    'csname'=> $val->csname,
                    'tx_date'=> date('d-m-Y h:i A',strtotime($val->init_date_time)),
                    'parent_name'=> $val->parent_name,
                    'transaction_id' => $refund->transaction_id,
                    'transaction_amount' => $refund->transaction_amount,
                    'refund_amount' => $refund->refund_amount,
                    'refund_id' => $refundDetails->refund_id,
                    'refund_status' => $refundDetails->refund_status,
                    'date' => date('d-m-Y h:i A',strtotime($refundDetails->date)),
                );
            }
        }
        return $refundData;
    }
}
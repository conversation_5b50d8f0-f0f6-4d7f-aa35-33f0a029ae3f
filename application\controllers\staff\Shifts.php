<?php

/**
 * Description of Student_menu
 *
 * <AUTHOR>
 */
class Shifts extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('staff/shifts_model');
  }

  public function index() {
    $data['shifts'] = $this->shifts_model->getShifts();
    // echo "<pre>"; print_r($data['shifts']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/shifts/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/shifts/index_mobile';
    }else{
      $data['main_content'] = 'staff/shifts/index';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function delete_staff_shift(){
    if($_POST['action']==0)
      $is_shift_assigned=$this->shifts_model->get_shift_assigned_staff($_POST['shiftId'],$_POST['action']);
    else
      $is_shift_assigned=0;

    if(empty($is_shift_assigned)){
      echo $this->shifts_model->delete_staff_shift($_POST['shiftId'],$_POST['action']);
    }else{
      echo -1;
    }
  }

  public function staff_shifts() {
    $data['shifts'] = $this->shifts_model->getShifts();

    $current_date = date('Y-m-d');
    $current_month = date('M-Y');

    //Manjukiran: Hard-coding to facilitate entry of shifts to previous months
    $current_date = '2025-05-01';
    $current_month = 'May-2025';
    $data['months'] = array($current_month);
    for($i=1; $i<12; $i++) {
      $mon = date('M', strtotime($current_date));
      if($mon == 'Jan') {
        $data['months'][] = 'Feb-'.date('Y', strtotime("+".$i." months", strtotime($current_date)));
        $current_date = date('Y', strtotime($current_date)).'-02-01';
      } else {
        $data['months'][] = date('M-Y', strtotime("+31 days", strtotime($current_date)));
        $current_date = date('Y-m-d', strtotime("+31 days", strtotime($current_date)));
      }
    }
    // echo "<pre>"; print_r($data); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/shifts/staff_shifts_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/shifts/staff_shifts_mobile';
    }else{
      $data['main_content'] = 'staff/shifts/staff_shifts';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function getStaffList() {
    $staff = $this->shifts_model->getStaffList($_POST);
    echo json_encode($staff);
  }

  public function getAllStaffMonthShiftsStatus() {
    $staff_ids = $_POST['staff_ids'];
    $month = $_POST['month'];
    $staff_assigned = $this->shifts_model->getAllStaffMonthShiftsStatus($staff_ids, $month);
    echo json_encode($staff_assigned);
  }

  public function getSingleStaffMonthShift() {
    if(!isset($_POST['staff_id'])){
      echo json_encode([]);
      return;
    }
    
    $staff_id = $_POST['staff_id'];
    $month = $_POST['month'];
    $data["shift_data"] = $this->shifts_model->getSingleStaffMonthShift($staff_id, $month);
    // bring all the Holidays for this particular month
    $data["holiday_list"]= $this->shifts_model->getListOfHolidaysFromCalender($staff_id,$month);
    echo json_encode($data);
  }

  public function saveStaffMonthShift() {
    // echo "<pre>"; print_r($_POST); die();
    $status = $this->shifts_model->saveStaffMonthShift();
    echo json_encode($status);
  }

  public function saveShift() {
    $input = $_POST;
    $input['start_time'] = ($input['start_time'] == '')?NULL:gmt_time(date('d-m-Y').' '.$input['start_time'], 'H:i:s');
    $input['end_time'] = ($input['end_time'] == '')?NULL:gmt_time(date('d-m-Y').' '.$input['end_time'], 'H:i:s');
    $input['created_by'] = $this->authorization->getAvatarId();
    $input['last_modified_by'] = $input['created_by'];
    echo $this->shifts_model->saveShift($input);
  }

  public function getStaffShifts() {
    $data = $this->shifts_model->getStaffShifts();
    echo json_encode($data);
  }

  public function saveStaffShift() {
    echo $this->shifts_model->saveStaffShift();
  }

  public function remove_staff_shift(){
    echo $this->shifts_model->remove_staff_shift($_POST);
  }

  public function check_is_shift_exits(){
    echo $this->shifts_model->check_is_shift_exits($_POST);
  }
  
}
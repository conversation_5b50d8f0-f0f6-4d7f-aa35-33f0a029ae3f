<div class="card" style="box-shadow: none;border:none;">
    <div class="card-body" style="padding-bottom: 0;">
        <div class="jHead d-flex justify-content-between align-items-center" style="padding: 15px 20px;">
            <h4 class="mb-0">
              <strong>Previous Schooling Information</strong>
            </h4>
            <?php if ($this->settings->isProfile_edit_enabled('year_id') ||
                      $this->settings->isProfile_edit_enabled('school_name') ||
                      $this->settings->isProfile_edit_enabled('class') ||
                      $this->settings->isProfile_edit_enabled('board') ||
                      $this->settings->isProfile_edit_enabled('medium_of_instruction') ||
                      $this->settings->isProfile_edit_enabled('school_address') ||
                      $this->settings->isProfile_edit_enabled('university') ||
                      $this->settings->isProfile_edit_enabled('period') ||
                      $this->settings->isProfile_edit_enabled('subjects')) : ?>
            <button type="button" class="btn btn-light btn-sm mobile-add-btn" onclick="addPreviousSchoolDetailsMobile()" style="border-radius: 8px; padding: 8px 16px; background: white; color: #007bff; border: 1px solid #007bff; font-weight: 500;">
                <span class="fa fa-plus"></span> Add
            </button>
            <?php endif; ?>
        </div>
        <!-- Loading state -->
        <div id="prev_school_loading_mobile" class="text-center" style="display: none; padding: 40px 20px;">
            <div class="spinner-border text-primary" role="status" style="width: 2.5rem; height: 2.5rem;">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="mt-3" style="color: #666; font-size: 0.9rem;">Loading previous schooling details...</p>
        </div>

        <!-- Content container for AJAX data -->
        <div id="prev_school_content_mobile">
            <!-- Fallback to PHP data if AJAX is not used -->

        </div>
    </div>
</div>

<!-- Responsive Modal for Add/Edit Previous School Details -->
<div class="modal fade" id="previousSchoolModalMobile" tabindex="-1" role="dialog" aria-labelledby="previousSchoolModalMobileLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-responsive" role="document">
        <div class="modal-content modal-content-responsive">
            <div class="modal-header mobile-modal-header" style="color: white; padding: 20px; border: none;">
                <h5 class="modal-title" id="previousSchoolModalMobileLabel" style="font-weight: 600; font-size: 1.1rem;">Add Previous School Details</h5>
                <button type="button" class="btn-close btn-close-white" data-dismiss="modal" aria-label="Close" style="background: none; border: none; color: white; font-size: 1.5rem;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body responsive-modal-body" style="padding: 20px; overflow-y: auto;">
                <form id="previousSchoolFormMobile" class="responsive-form">
                    <input type="hidden" id="school_id_mobile" name="school_id" value="">

                    <!-- Row 1: Academic Year and School Name -->
                    <div class="form-row responsive-form-row">
                        <!-- Academic Year Field -->
                        <?php if ($this->settings->isProfile_edit_enabled('year_id')) : ?>
                        <div class="form-group responsive-form-group">
                            <label for="year_id_mobile" class="form-label mobile-label">Academic Year <span class="text-danger">*</span></label>
                            <div class="position-relative">
                                 <select name="year_id_mobile" id="year_id_mobile" class="form-control form-select mobile-select" required>
                                    <option value="">Select Year</option>
                                    <?php foreach ($academic_years as $ay) {
                                        echo '<option value="' . $ay->acad_year . '">' . $ay->acad_year . '</option>';
                                    }   ?>
                                </select>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- School Name Field -->
                        <?php if ($this->settings->isProfile_edit_enabled('school_name')) : ?>
                        <div class="form-group responsive-form-group">
                            <label for="school_name_mobile" class="form-label mobile-label">School/College Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control mobile-input" id="school_name_mobile" name="school_name" data-parsley-pattern="^[a-zA-Z. ]+$" placeholder="Enter school or college name" required style="padding: 12px; border-radius: 8px; border: 2px solid #e9ecef; font-size: 1rem;">
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Row 2: Class and Board -->
                    <div class="form-row responsive-form-row">
                        <!-- Class Field -->
                        <?php if ($this->settings->isProfile_edit_enabled('class')) : ?>
                        <div class="form-group responsive-form-group">
                            <label for="class_mobile" class="form-label mobile-label">Class/Degree <span class="text-danger">*</span></label>
                            <input type="text" class="form-control mobile-input" id="class_mobile" name="class" placeholder="e.g., Grade 10, Bachelor's" required style="padding: 12px; border-radius: 8px; border: 2px solid #e9ecef; font-size: 1rem;">
                        </div>
                        <?php endif; ?>

                        <!-- Board Field -->
                        <?php if ($this->settings->isProfile_edit_enabled('board')) : ?>
                        <div class="form-group responsive-form-group">
                            <label for="board_mobile" class="form-label mobile-label">Board <span class="text-danger">*</span></label>
                            <select name="board_mobile" id="board_mobile" class="form-control form-select mobile-select" required>
                                <option value="">Select Board</option>
                                <option value="CBSE">CBSE</option>
                                <option value="ICSE">ICSE</option>
                                <option value="STATE">STATE</option>
                                <option value="Home School">Home School</option>
                                <option value="IGCSE">IGCSE</option>
                                <option value="IBDP">IBDP</option>
                                <option value="NIOS">NIOS</option>
                                <option value="PU Board">PU Board</option>
                                <option value="ITI Board">ITI Board</option>
                                <option value="KSEEB">KSEEB</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Medium of Instruction Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('medium_of_instruction')) : ?>
                    <div class="form-group mb-4">
                        <label for="medium_of_instruction_mobile" class="form-label mobile-label">Medium of Instruction</label>
                        <select name="medium_of_instruction" id="medium_of_instruction" class="form-control form-select mobile-select">
                            <option value="">Select Medium of Instruction</option>
                            <option value="English">English</option>
                            <option value="Kannada">Kannada</option>
                        </select>
                    </div>
                    <?php endif; ?>

                    <!-- School Address Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('school_address')) : ?>
                    <div class="form-group mb-4">
                        <label for="school_address_mobile" class="form-label mobile-label">School/College Address</label>
                        <textarea class="form-control mobile-textarea" id="school_address_mobile" name="school_address" rows="3" placeholder="Enter complete address" style="padding: 12px; border-radius: 8px; border: 2px solid #e9ecef; font-size: 1rem; resize: vertical;"></textarea>
                    </div>
                    <?php endif; ?>

                    <!-- University Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('university')) : ?>
                    <div class="form-group mb-4">
                        <label for="university_mobile" class="form-label mobile-label">University</label>
                        <input type="text" class="form-control mobile-input" id="university_mobile" name="university" placeholder="Enter university name" style="padding: 12px; border-radius: 8px; border: 2px solid #e9ecef; font-size: 1rem;">
                    </div>
                    <?php endif; ?>

                    <!-- Period Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('period')) : ?>
                    <div class="form-group mb-4">
                        <label class="form-label mobile-label">Period</label>
                        <div class="row">
                            <div class="col-6">
                                <label for="period_from_mobile" class="form-label mobile-label" style="font-size: 0.85rem; margin-bottom: 5px;">From Date</label>
                                <input type="date" class="form-control mobile-input" id="period_from_mobile" name="period_from" style="padding: 12px; border-radius: 8px; border: 2px solid #e9ecef; font-size: 1rem;">
                            </div>
                            <div class="col-6">
                                <label for="period_to_mobile" class="form-label mobile-label" style="font-size: 0.85rem; margin-bottom: 5px;">To Date</label>
                                <input type="date" class="form-control mobile-input" id="period_to_mobile" name="period_to" style="padding: 12px; border-radius: 8px; border: 2px solid #e9ecef; font-size: 1rem;">
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Subjects Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('subjects')) : ?>
                    <div class="form-group mb-4">
                        <label for="subjects_mobile" class="form-label mobile-label">Subjects</label>
                        <textarea class="form-control mobile-textarea" id="subjects_mobile" name="subjects" rows="3" placeholder="Enter subjects studied (comma separated)" style="padding: 12px; border-radius: 8px; border: 2px solid #e9ecef; font-size: 1rem; resize: vertical;"></textarea>
                    </div>
                    <?php endif; ?>

                    <!-- Registration Number Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('registration_no')) : ?>
                    <div class="form-group mb-4">
                        <label for="registration_number_mobile" class="form-label mobile-label">Registration Number</label>
                        <input type="text" class="form-control mobile-input" id="registration_number_mobile" name="registration_number" placeholder="Enter registration number" style="padding: 12px; border-radius: 8px; border: 2px solid #e9ecef; font-size: 1rem;">
                    </div>
                    <?php endif; ?>

                    <!-- Marks Fields -->
                    <div class="form-row responsive-form-row">
                        <!-- Total Marks Field -->
                        <?php if ($this->settings->isProfile_edit_enabled('total_marks')) : ?>
                        <div class="form-group responsive-form-group">
                            <label for="total_marks_mobile" class="form-label mobile-label">Total Marks</label>
                            <input type="number" class="form-control mobile-input" id="total_marks_mobile" name="total_marks" placeholder="Enter total marks" min="0" style="padding: 12px; border-radius: 8px; border: 2px solid #e9ecef; font-size: 1rem;">
                        </div>
                        <?php endif; ?>

                        <!-- Total Marks Obtained Field -->
                        <?php if ($this->settings->isProfile_edit_enabled('total_marks_scored')) : ?>
                        <div class="form-group responsive-form-group">
                            <label for="total_marks_obtained_mobile" class="form-label mobile-label">Total Marks Obtained</label>
                            <input type="number" class="form-control mobile-input" id="total_marks_obtained_mobile" name="total_marks_obtained" placeholder="Enter marks obtained" min="0" style="padding: 12px; border-radius: 8px; border: 2px solid #e9ecef; font-size: 1rem;">
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Report Card Field -->
                    <?php if ($this->settings->isProfile_edit_enabled('report_card')) : ?>
                    <div class="form-group mb-4">
                        <label for="report_card_mobile" class="form-label mobile-label">Report Card</label>
                        <input type="hidden" id="report_card_path_mobile" name="report_card" value="">
                        <div class="file-upload-container" style="border: 2px dashed #e9ecef; border-radius: 8px; padding: 20px; text-align: center; background: #f8f9fa;">
                            <input type="file" class="form-control-file" id="report_card_mobile" accept="image/*,.pdf" style="display: none;">
                            <div class="upload-area" onclick="document.getElementById('report_card_mobile').click()" style="cursor: pointer;">
                                <div class="upload-icon mb-2" style="font-size: 2rem; color: #6c757d;">
                                    <i class="fa fa-cloud-upload"></i>
                                </div>
                                <div class="upload-text" style="color: #6c757d; font-size: 0.9rem;">
                                    <strong>Click to upload report card</strong><br>
                                    <small>Supports: JPG, PNG, PDF (Max 5MB)</small>
                                </div>
                            </div>
                            <div class="file-preview mt-2" id="report_card_preview_mobile" style="display: none;">
                                <div class="file-info" style="background: white; padding: 10px; border-radius: 6px; border: 1px solid #dee2e6;">
                                    <span class="file-name" style="font-weight: 500;"></span>
                                    <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="removeReportCard('mobile')" style="padding: 2px 8px;">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="upload-progress mt-2" id="report_card_progress_mobile" style="display: none;">
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="text-muted">Uploading...</small>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
            <div class="modal-footer mobile-modal-footer" style="padding: 20px; border: none; background: #f8f9fa;">
                <button type="button" class="btn btn-secondary mobile-btn-cancel" data-dismiss="modal" style="flex: 1; margin-right: 10px; padding: 12px; border-radius: 8px; font-weight: 500;">
                    Cancel
                </button>
                <button type="button" class="btn btn-primary mobile-btn-save" onclick="savePreviousSchoolDetailsMobile()" style="flex: 1; padding: 12px; border-radius: 8px; font-weight: 500;">
                    Save
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Mobile-specific JavaScript functions for Previous School Details

// Function to load previous schooling details via AJAX for mobile
function loadPreviousSchoolingDetailsMobile() {
    // Show loading state
    $('#prev_school_loading_mobile').show();
    $('#prev_school_content_mobile').hide();

    $.ajax({
        url: '<?php echo site_url("parent_controller/get_prev_schooling_details"); ?>',
        type: 'POST',
        dataType: 'json',
        success: function(data) {
            // Hide loading state
            $('#prev_school_loading_mobile').hide();
            $('#prev_school_content_mobile').show();

            // Build the mobile cards HTML
            var html = buildPreviousSchoolingMobileCards(data);
            $('#prev_school_content_mobile').html(html);
        },
        error: function(xhr, status, error) {
            // Hide loading state
            $('#prev_school_loading_mobile').hide();
            $('#prev_school_content_mobile').show();

            // Show error message
            $('#prev_school_content_mobile').html(
                '<div class="text-center mobile-error" style="padding: 40px 20px;">' +
                    '<div class="error-icon mb-3" style="font-size: 3rem; color: #dc3545;">' +
                        '<i class="fa fa-exclamation-triangle"></i>' +
                    '</div>' +
                    '<div class="error-text" style="color: #dc3545; font-size: 1rem;">Error loading data. Please try again.</div>' +
                    '<button class="btn btn-primary mt-3" onclick="loadPreviousSchoolingDetailsMobile()" style="border-radius: 8px;">' +
                        '<i class="fa fa-refresh"></i> Retry' +
                    '</button>' +
                '</div>'
            );
            console.error('Error loading previous schooling details:', error);
        }
    });
}

// Function to build mobile cards HTML
function buildPreviousSchoolingMobileCards(data) {
    if (!data || data.length === 0) {
        var noDataHtml = '<div class="text-center mobile-no-data" style="padding: 60px 20px;">' +
                           '<div class="no-data-icon mb-3" style="font-size: 3rem; color: #ddd;">' +
                               '<i class="fa fa-graduation-cap"></i>' +
                           '</div>' +
                           '<div class="no-data-text" style="color: #999; font-size: 1rem;">No previous schooling data available</div>';

        // Only show Add button if user has edit permissions for at least one field
        <?php if ($this->settings->isProfile_edit_enabled('year_id') ||
                  $this->settings->isProfile_edit_enabled('school_name') ||
                  $this->settings->isProfile_edit_enabled('class') ||
                  $this->settings->isProfile_edit_enabled('board') ||
                  $this->settings->isProfile_edit_enabled('medium_of_instruction') ||
                  $this->settings->isProfile_edit_enabled('school_address') ||
                  $this->settings->isProfile_edit_enabled('university') ||
                  $this->settings->isProfile_edit_enabled('period') ||
                  $this->settings->isProfile_edit_enabled('subjects')) : ?>
        noDataHtml += '<button class="btn btn-primary mt-3" onclick="addPreviousSchoolDetailsMobile()" style="border-radius: 8px;">' +
                         '<span class="fa fa-plus"></span> Add First Record' +
                     '</button>';
        <?php endif; ?>

        noDataHtml += '</div>';
        return noDataHtml;
    }

    var html = '<div class="mobile-school-list">';

    data.forEach(function(item, index) {
        html += '<div class="mobile-school-card" style="background: #f8f9fa; border-radius: 12px; padding: 16px; margin-bottom: 16px; border: 1px solid #e9ecef;">';
        html += '    <div class="d-flex justify-content-between align-items-start mb-3">';
        html += '        <div class="school-year-badge" style="background: #3a6ca1ff; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 500;">';
        html += '            ' + escapeHtmlMobile(item.year_id || '-');
        html += '        </div>';
        html += '        <div class="mobile-actions">';

        // Only show edit button if user has edit permissions for at least one field
        <?php if ($this->settings->isProfile_edit_enabled('year_id') ||
                  $this->settings->isProfile_edit_enabled('school_name') ||
                  $this->settings->isProfile_edit_enabled('class') ||
                  $this->settings->isProfile_edit_enabled('board') ||
                  $this->settings->isProfile_edit_enabled('medium_of_instruction') ||
                  $this->settings->isProfile_edit_enabled('school_address') ||
                  $this->settings->isProfile_edit_enabled('university') ||
                  $this->settings->isProfile_edit_enabled('period') ||
                  $this->settings->isProfile_edit_enabled('subjects')) : ?>
        html += '            <button class="btn btn-sm btn-outline-primary me-2" onclick="editPreviousSchoolDetailsMobile(' + (item.id || index) + ')" style="border-radius: 6px; padding: 6px 12px;">';
        html += `               <?php $this->load->view('svg_icons/edit_icon.svg'); ?>`;
        html += '            </button>';
        <?php endif; ?>

        // Only show delete button if user has edit permissions (delete is also an edit operation)
        <?php if ($this->settings->isProfile_edit_enabled('year_id') ||
                  $this->settings->isProfile_edit_enabled('school_name') ||
                  $this->settings->isProfile_edit_enabled('class') ||
                  $this->settings->isProfile_edit_enabled('board') ||
                  $this->settings->isProfile_edit_enabled('medium_of_instruction') ||
                  $this->settings->isProfile_edit_enabled('school_address') ||
                  $this->settings->isProfile_edit_enabled('university') ||
                  $this->settings->isProfile_edit_enabled('period') ||
                  $this->settings->isProfile_edit_enabled('subjects')) : ?>
        html += '            <button class="btn btn-sm btn-outline-danger" onclick="deletePreviousSchoolDetailsMobile(' + (item.id || index) + ')" style="border-radius: 6px; padding: 6px 12px;">';
        html += `                <?php $this->load->view('svg_icons/delete_icon.svg'); ?>`;
        html += '            </button>';
        <?php endif; ?>

        html += '        </div>';
        html += '    </div>';

        html += '    <div class="school-info">';
        html += '        <h6 class="school-name mb-2" style="font-weight: 600; color: #333; font-size: 1rem;">';
        html += '            ' + escapeHtmlMobile(item.school_name || 'School Name Not Available');
        html += '        </h6>';

        html += '        <div class="info-grid responsive-info-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 12px; font-size: 0.85rem;">';

        // Class field
        <?php if ($this->settings->isProfile_profile_enabled('class')) : ?>
        html += '            <div class="info-item">';
        html += '                <span class="info-label" style="color: #666; font-weight: 500;">Class/Degree:</span>';
        html += '                <span class="info-value" style="color: #333;">' + escapeHtmlMobile(item.class || '-') + '</span>';
        html += '            </div>';
        <?php endif; ?>

        // Board field
        <?php if ($this->settings->isProfile_profile_enabled('board')) : ?>
        html += '            <div class="info-item">';
        html += '                <span class="info-label" style="color: #666; font-weight: 500;">Board:</span>';
        html += '                <span class="info-value" style="color: #333;">' + escapeHtmlMobile(item.board || '-') + '</span>';
        html += '            </div>';
        <?php endif; ?>

        // Medium of Instruction field
        <?php if ($this->settings->isProfile_profile_enabled('medium_of_instruction')) : ?>
        html += '            <div class="info-item">';
        html += '                <span class="info-label" style="color: #666; font-weight: 500;">Medium:</span>';
        html += '                <span class="info-value" style="color: #333;">' + escapeHtmlMobile(item.medium_of_instruction || '-') + '</span>';
        html += '            </div>';
        <?php endif; ?>

        // University field
        <?php if ($this->settings->isProfile_profile_enabled('university')) : ?>
        html += '            <div class="info-item">';
        html += '                <span class="info-label" style="color: #666; font-weight: 500;">University:</span>';
        html += '                <span class="info-value" style="color: #333;">' + escapeHtmlMobile(item.university_name || '-') + '</span>';
        html += '            </div>';
        <?php endif; ?>

        html += '        </div>';

        // School Address field
        <?php if ($this->settings->isProfile_profile_enabled('school_address')) : ?>
        if (item.school_address) {
            html += '        <div class="info-item">';
            html += '            <span class="info-label" style="color: #666; font-weight: 500;">Address:</span>';
            html += '            <span class="info-value" style="color: #333;">' + escapeHtmlMobile(item.school_address) + '</span>';
            html += '        </div>';
        }
        <?php endif; ?>

        // Period field
        <?php if ($this->settings->isProfile_profile_enabled('period')) : ?>
        if (item.period) {
            html += '        <div class="info-item">';
            html += '            <span class="info-label" style="color: #666; font-weight: 500;">Period:</span>';
            html += '            <span class="info-value" style="color: #333;">' + escapeHtmlMobile(item.period) + '</span>';
            html += '        </div>';
        }
        <?php endif; ?>

        // Subjects field
        <?php if ($this->settings->isProfile_profile_enabled('subjects')) : ?>
        if (item.subjects) {
            html += '          <div class="info-item">';
            html += '                <span class="info-label" style="color: #666; font-weight: 500; font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.5px;">SUBJECTS:</span>';
            html += '                <span class="info-value" style="color: #333; font-weight: 500;">' + escapeHtmlMobile(item.subjects) + '</span>';
            html += '            </div>';
        }
        <?php endif; ?>

        // Registration Number field
        <?php if ($this->settings->isProfile_profile_enabled('registration_no')) : ?>
        if (item.registration_number) {
            html += '           <div class="info-item">';
            html += '                <span class="info-label" style="color: #666; font-weight: 500; font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.5px;">REGISTRATION NUMBER:</span>';
            html += '                <span class="info-value" style="color: #333; font-weight: 500;">' + escapeHtmlMobile(item.registration_no) + '</span>';
            html += '            </div>';
        }
        <?php endif; ?>

        // Marks fields
        var hasMarks = false;
        <?php if ($this->settings->isProfile_profile_enabled('total_marks')) : ?>
        hasMarks = hasMarks || item.total_marks;
        <?php endif; ?>
        <?php if ($this->settings->isProfile_profile_enabled('total_marks_scored')) : ?>
        hasMarks = hasMarks || item.total_marks_scored;
        <?php endif; ?>

        if (hasMarks) {

            <?php if ($this->settings->isProfile_profile_enabled('total_marks')) : ?>
            if (item.total_marks) {
                html += '               <div class="info-item">';
                html += '                    <span class="info-label" style="color: #666; font-weight: 500; font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.5px;">TOTAL MARKS:</span>';
                html += '                    <span class="info-value" style="color: #333; font-weight: 500;">' + escapeHtmlMobile(item.total_marks) + '</span>';
                html += '                </div>';
            }
            <?php endif; ?>

            <?php if ($this->settings->isProfile_profile_enabled('total_marks_scored')) : ?>
            if (item.total_marks_scored) {
                html += '               <div class="info-item">';
                html += '                    <span class="info-label" style="color: #666; font-weight: 500; font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.5px;">MARKS OBTAINED:</span>';
                html += '                    <span class="info-value" style="color: #333; font-weight: 500;">' + escapeHtmlMobile(item.total_marks_scored) + '</span>';
                html += '                </div>';
            }
            <?php endif; ?>

        }

        // Report Card field
        <?php if ($this->settings->isProfile_profile_enabled('report_card')) : ?>
        if (item.report_card) {
            html += '           <div class="info-item">';
            html += '                <span class="info-label" style="color: #666; font-weight: 500; font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.5px;">REPORT CARD:</span>';
            html += '                <div class="report-card-link" style="margin-top: 5px;">';

            // Check if it's an image or PDF
            var fileExtension = item.report_card.split('.').pop().toLowerCase();
            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                html += '                    <a href="' + item.report_card + '" target="_blank" class="btn btn-sm btn-outline-primary" style="padding: 4px 8px; font-size: 0.8rem;">';
                html += '                        <i class="fa fa-image"></i> View Image';
                html += '                    </a>';
            } else if (fileExtension === 'pdf') {
                html += '                    <a href="' + item.report_card + '" target="_blank" class="btn btn-sm btn-outline-danger" style="padding: 4px 8px; font-size: 0.8rem;">';
                html += '                        <i class="fa fa-file-pdf-o"></i> View PDF';
                html += '                    </a>';
            } else {
                html += '                    <a href="' + item.report_card + '" target="_blank" class="btn btn-sm btn-outline-secondary" style="padding: 4px 8px; font-size: 0.8rem;">';
                html += '                        <i class="fa fa-file"></i> View File';
                html += '                    </a>';
            }

            html += '                </div>';
            html += '            </div>';
        }
        <?php endif; ?>

        html += '    </div>';
        html += '</div>';
    });

    html += '</div>';

    return html;
}

// Handle report card file selection for mobile
$(document).on('change', '#report_card_mobile', function() {
    var file = this.files[0];
    if (file) {
        // Validate file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB');
            this.value = '';
            return;
        }

        // Validate file type
        var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid image (JPG, PNG) or PDF file');
            this.value = '';
            return;
        }

        // Upload file to S3
        uploadReportCardToS3Mobile(file);
    }
});

// Upload report card to S3 for mobile
function uploadReportCardToS3Mobile(file) {
    // Show progress
    $('#report_card_progress_mobile').show();
    $('#report_card_preview_mobile').hide();

    // Get signed URL from server
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
        },
        success: function(response) {
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;

            // Upload file to S3
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        if (e.lengthComputable) {
                            var percentComplete = (e.loaded / e.total) * 100;
                            $('#report_card_progress_mobile .progress-bar').css('width', percentComplete + '%');
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    // Store path in hidden input
                    $('#report_card_path_mobile').val(path);

                    // Hide progress and show preview
                    $('#report_card_progress_mobile').hide();
                    $('#report_card_preview_mobile').show();
                    $('#report_card_preview_mobile .file-name').text(file.name);

                    console.log('Report card uploaded successfully to:', path);
                },
                error: function(xhr, status, error) {
                    $('#report_card_progress_mobile').hide();
                    alert('Failed to upload report card. Please try again.');
                    console.error('S3 upload error:', error);
                }
            });
        },
        error: function(xhr, status, error) {
            $('#report_card_progress_mobile').hide();
            alert('Failed to get upload URL. Please try again.');
            console.error('Signed URL error:', error);
        }
    });
}

// Remove report card file for mobile
function removeReportCard(type) {
    if (type === 'mobile') {
        $('#report_card_mobile').val('');
        $('#report_card_path_mobile').val('');
        $('#report_card_preview_mobile').hide();
        $('#report_card_progress_mobile').hide();
    } else {
        $('#report_card').val('');
        $('#report_card_path').val('');
        $('#report_card_preview').hide();
        $('#report_card_progress').hide();
    }
}

// Utility function to escape HTML characters for mobile
function escapeHtmlMobile(text) {
    if (text === null || text === undefined) {
        return '';
    }
    var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.toString().replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Function to populate academic year dropdown for mobile
function populateAcademicYearDropdownMobile() {
    // Generate academic years (current year and previous 15 years)
    var currentYear = new Date().getFullYear();
    var yearOptions = '<option value="">Select Academic Year</option>';

    // Add future year (next academic year)
    var futureStartYear = currentYear;
    var futureEndYear = futureStartYear + 1;
    var futureAcademicYear = futureStartYear + '-' + futureEndYear;
    yearOptions += '<option value="' + futureAcademicYear + '">' + futureAcademicYear + '</option>';

    // Add current and previous years
    for (var i = 0; i <= 15; i++) {
        var startYear = currentYear - 1 - i;
        var endYear = startYear + 1;
        var academicYear = startYear + '-' + endYear;
        yearOptions += '<option value="' + academicYear + '">' + academicYear + '</option>';
    }

    $('#year_id_mobile').html(yearOptions);
}

// Function to add new previous school details for mobile
function addPreviousSchoolDetailsMobile() {
    // Reset form and modal title
    $('#previousSchoolFormMobile')[0].reset();
    $('#school_id_mobile').val('');
    $('#previousSchoolModalMobileLabel').text('Add Previous School Details');

    // Populate academic year dropdown
    populateAcademicYearDropdownMobile();

    // Ensure save button has correct text
    $('.mobile-btn-save').text('Save');

    // Show the modal
    $('#previousSchoolModalMobile').modal('show');
}

// Function to edit previous school details for mobile
function editPreviousSchoolDetailsMobile(schoolId) {
    console.log('Edit previous school details clicked for ID:', schoolId);

    // Set modal title for editing
    $('#previousSchoolModalMobileLabel').text('Edit Previous School Details');
    $('#school_id_mobile').val(schoolId);

    // Populate academic year dropdown first
    populateAcademicYearDropdownMobile();

    // Load existing data for editing
    loadSchoolDetailsForEditMobile(schoolId);

    // Ensure save button has correct text
    $('.mobile-btn-save').text('Save');

    // Show the modal
    $('#previousSchoolModalMobile').modal('show');
}

// Function to load school details for editing (mobile)
function loadSchoolDetailsForEditMobile(schoolId) {
    // You can implement AJAX call to get specific school details
    // For now, we'll show a placeholder implementation

    // Example AJAX call:
    
    $.ajax({
        url: '<?php echo site_url("parent_controller/get_previous_school_details_by_id"); ?>',
        type: 'POST',
        data: { school_id: schoolId },
        dataType: 'json',
        success: function(data) {
            if (data) {
                <?php if ($this->settings->isProfile_edit_enabled('year_id')) : ?>
                $('#year_id_mobile').val(data.year_id);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('school_name')) : ?>
                $('#school_name_mobile').val(data.school_name);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('class')) : ?>
                $('#class_mobile').val(data.class);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('board')) : ?>
                $('#board_mobile').val(data.board);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('school_address')) : ?>
                $('#school_address_mobile').val(data.school_address);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('medium_of_instruction')) : ?>
                $('#medium_of_instruction_mobile').val(data.medium_of_instruction);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('university')) : ?>
                $('#university_mobile').val(data.university_name);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('period')) : ?>
                // Parse period data if it exists (format: "YYYY-MM-DD to YYYY-MM-DD")
                if (data.period) {
                    var periodParts = data.period.split(' to ');
                    if (periodParts.length === 2) {
                        $('#period_from_mobile').val(periodParts[0]);
                        $('#period_to_mobile').val(periodParts[1]);
                    } else {
                        // Handle legacy format or single date
                        $('#period_from_mobile').val('');
                        $('#period_to_mobile').val('');
                    }
                } else {
                    $('#period_from_mobile').val('');
                    $('#period_to_mobile').val('');
                }
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('subjects')) : ?>
                $('#subjects_mobile').val(data.subjects);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('registration_no')) : ?>
                $('#registration_number_mobile').val(data.registration_number);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('total_marks')) : ?>
                $('#total_marks_mobile').val(data.total_marks);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('total_marks_scored')) : ?>
                $('#total_marks_obtained_mobile').val(data.total_marks_scored);
                <?php endif; ?>

                <?php if ($this->settings->isProfile_edit_enabled('report_card')) : ?>
                // Handle report card file display
                if (data.report_card) {
                    $('#report_card_path_mobile').val(data.report_card);
                    $('#report_card_preview_mobile').show();
                    $('#report_card_preview_mobile .file-name').text(data.report_card.split('/').pop());
                } else {
                    $('#report_card_path_mobile').val('');
                    $('#report_card_preview_mobile').hide();
                }
                <?php endif; ?>
            }
        },
        error: function() {
            bootbox.alert({
                title: "Error",
                message: "Error loading school details for editing",
                className: "bootbox-error"
            });
        }
    });
}

// Function to delete previous school details for mobile
function deletePreviousSchoolDetailsMobile(schoolId) {
    // Use Bootbox for confirmation dialog
    bootbox.confirm({
        title: "Delete Previous School Record",
        message: "Are you sure you want to delete this previous school record? This action cannot be undone.",
        buttons: {
            confirm: {
                label: '<i class="fa fa-trash"></i> Delete',
                className: 'btn-danger'
            },
            cancel: {
                label: '<i class="fa fa-times"></i> Cancel',
                className: 'btn-secondary'
            }
        },
        callback: function (result) {
            if (result) {
                // Show loading dialog
                var loadingDialog = bootbox.dialog({
                    title: "Deleting Record",
                    message: '<div class="text-center"><i class="fa fa-spinner fa-spin fa-2x"></i><br><br>Please wait while we delete the record...</div>',
                    closeButton: false
                });

                $.ajax({
                    url: '<?php echo site_url("parent_controller/delete_previous_school"); ?>',
                    type: 'POST',
                    data: { school_id: schoolId },
                    dataType: 'json',
                    success: function(response) {
                        loadingDialog.modal('hide');

                        if (response) {
                            // Show success message
                            bootbox.alert({
                                title: "Success",
                                message: "Previous school record has been deleted successfully!",
                                className: "bootbox-success",
                                callback: function() {
                                    // Reload the mobile cards
                                    loadPreviousSchoolingDetailsMobile();
                                }
                            });
                        } else {
                            // Show error message
                            bootbox.alert({
                                title: "Error",
                                message: response.message || "Failed to delete the record. Please try again.",
                                className: "bootbox-error"
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        loadingDialog.modal('hide');

                        // Show error message
                        bootbox.alert({
                            title: "Error",
                            message: "An error occurred while deleting the record. Please try again.",
                            className: "bootbox-error"
                        });
                        console.error('Delete error:', error);
                    }
                });
            }
        }
    });
}

// Function to save previous school details for mobile (add or edit)
function savePreviousSchoolDetailsMobile() {
    // Get form data
    var formData = {
        school_id: $('#school_id_mobile').val()
    };

    // Add fields based on edit settings
    <?php if ($this->settings->isProfile_edit_enabled('year_id')) : ?>
    formData.year_id = $('#year_id_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('school_name')) : ?>
    formData.school_name = $('#school_name_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('class')) : ?>
    formData.class = $('#class_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('board')) : ?>
    formData.board = $('#board_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('school_address')) : ?>
    formData.school_address = $('#school_address_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('medium_of_instruction')) : ?>
    formData.medium_of_instruction = $('#medium_of_instruction_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('university')) : ?>
    formData.university = $('#university_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('period')) : ?>
    // Merge period from and to dates
    var periodFrom = $('#period_from_mobile').val();
    var periodTo = $('#period_to_mobile').val();
    if (periodFrom && periodTo) {
        formData.period = periodFrom + ' to ' + periodTo;
    } else if (periodFrom) {
        formData.period = periodFrom + ' to Present';
    } else if (periodTo) {
        formData.period = 'Unknown to ' + periodTo;
    } else {
        formData.period = '';
    }
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('subjects')) : ?>
    formData.subjects = $('#subjects_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('registration_no')) : ?>
    formData.registration_number = $('#registration_number_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('total_marks')) : ?>
    formData.total_marks = $('#total_marks_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('total_marks_scored')) : ?>
    formData.total_marks_obtained = $('#total_marks_obtained_mobile').val();
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('report_card')) : ?>
    // Handle report card path from S3 upload
    formData.report_card = $('#report_card_path_mobile').val();
    <?php endif; ?>

    // Basic validation for edit-enabled fields
    var validationErrors = [];

    <?php if ($this->settings->isProfile_edit_enabled('year_id')) : ?>
    if (!formData.year_id) {
        validationErrors.push('Academic Year is required');
    }
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('school_name')) : ?>
    if (!formData.school_name) {
        validationErrors.push('School/College Name is required');
    }
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('class')) : ?>
    if (!formData.class) {
        validationErrors.push('Class/Degree is required');
    }
    <?php endif; ?>

    <?php if ($this->settings->isProfile_edit_enabled('board')) : ?>
    if (!formData.board) {
        validationErrors.push('Board is required');
    }
    <?php endif; ?>

    if (validationErrors.length > 0) {
        bootbox.alert({
            title: "Validation Error",
            message: "Please fill in all required fields:<br><ul><li>" + validationErrors.join('</li><li>') + "</li></ul>",
            className: "bootbox-warning"
        });
        return;
    }

    // Determine if this is add or edit
    var isEdit = formData.school_id && formData.school_id !== '';
    var url = isEdit ?
        '<?php echo site_url("parent_controller/update_previous_school"); ?>' :
        '<?php echo site_url("parent_controller/add_previous_school_details"); ?>';

    // Show loading state
    var saveBtn = $('.mobile-btn-save');
    saveBtn.prop('disabled', true).text('Saving...');

    // AJAX call to save data
    $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response) {
                // Close modal
                $('#previousSchoolModalMobile').modal('hide');

                // Show success message
                bootbox.alert({
                    title: "Success",
                    message: isEdit ?
                        "Previous school details updated successfully!" :
                        "Previous school details added successfully!",
                    className: "bootbox-success",
                    callback: function() {
                        // Reload the mobile cards
                        loadPreviousSchoolingDetailsMobile();
                    }
                });
            } else {
                bootbox.alert({
                    title: "Error",
                    message: (response && response.message) || 'Failed to save previous school details. Please try again.',
                    className: "bootbox-error"
                });
            }
        },
        error: function(xhr, status, error) {
            bootbox.alert({
                title: "Error",
                message: "An error occurred while saving the details. Please check your connection and try again.",
                className: "bootbox-error"
            });
            console.error('Save error:', error);
        },
        complete: function() {
            // Restore button to original state
            saveBtn.prop('disabled', false).text('Save');
        }
    });
}

// Auto-load on page ready (optional - remove if you want manual loading)
$(document).ready(function() {
    // Uncomment the line below to auto-load via AJAX on page load
    loadPreviousSchoolingDetailsMobile();
});
</script>

<style>
/* Mobile-specific styles for Previous School Details */

/* jHead styling for mobile */
.jHead {
    margin: 0 !important;
}

.jHead h4 {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

.jHead .mobile-add-btn {
    transition: all 0.3s ease !important;
    font-size: 0.85rem !important;
    background: white !important;
    color: #007bff !important;
    border: 1px solid #007bff !important;
    font-weight: 500 !important;
}

.jHead .mobile-add-btn:hover {
    transform: translateY(-1px) !important;
    background: #007bff !important;
    color: white !important;
    border-color: #007bff !important;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3) !important;
}

/* Mobile card styling */
.mobile-card {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    border: 1px solid #e9ecef !important;
}

.mobile-header {
    color: black !important;
    border-bottom: none !important;
}

.mobile-title {
    color: white !important;
}

.mobile-add-btn {
    background: rgba(255,255,255,0.2) !important;
    border: 1px solid rgba(255,255,255,0.3) !important;
    color: white !important;
    transition: all 0.3s ease;
}

.mobile-add-btn:hover {
    background: rgba(255,255,255,0.3) !important;
    transform: translateY(-1px);
}

/* Mobile school card styling */
.mobile-school-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef !important;
}

.mobile-school-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.school-year-badge {
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.mobile-actions .btn {
    transition: all 0.2s ease;
}

.mobile-actions .btn:hover {
    transform: scale(1.05);
}

.info-grid {
    gap: 8px !important;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.info-label {
    font-size: 0.75rem !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-weight: 500 !important;
}

/* Responsive modal styling */
.modal-dialog-responsive {
    transition: all 0.3s ease !important;
}

.modal-content-responsive {
    transition: all 0.3s ease !important;
}

.mobile-modal-header {
    position: sticky;
    top: 0;
    z-index: 1000;
}

.mobile-modal-body {
    background: #f8f9fa;
}

.mobile-label {
    font-weight: 600 !important;
    color: #333 !important;
    margin-bottom: 8px !important;
    font-size: 0.95rem !important;
}

.mobile-input, .mobile-select, .mobile-textarea {
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: white !important;
}

.mobile-input:focus, .mobile-select:focus, .mobile-textarea:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    outline: 0 !important;
}

.mobile-modal-footer {
    position: sticky;
    bottom: 0;
    z-index: 1000;
    display: flex !important;
    gap: 10px;
}

.mobile-btn-cancel, .mobile-btn-save {
    border-radius: 8px !important;
    font-weight: 500 !important;
    padding: 12px 20px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.mobile-btn-cancel {
    background: #6c757d !important;
    border-color: #6c757d !important;
}

.mobile-btn-cancel:hover {
    background: #5a6268 !important;
    transform: translateY(-1px);
}

.mobile-btn-save:hover {
    background: #0056b3 !important;
    transform: translateY(-1px);
}

/* No data state styling */
.mobile-no-data {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.no-data-icon {
    opacity: 0.5;
}

.mobile-error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-radius: 12px;
    border: 2px dashed #f5c6cb;
}

/* Loading state styling */
#prev_school_loading_mobile {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    border: 2px dashed #90caf9;
}
.form-control {
    height: 3rem;
}

/* Responsive form layout */
.responsive-form {
    width: 100%;
}

.responsive-form-row {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.responsive-form-group {
    flex: 1;
    min-width: 0;
}

/* Tablet form layout */
@media (min-width: 768px) {
    .responsive-form-row {
        flex-direction: row;
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .responsive-form-group {
        flex: 1;
    }
}

/* Enhanced Responsive Design */

/* Modal responsive behavior */
.modal-dialog-responsive {
    width: 100vw !important;
    max-width: 100vw !important;
    margin: 0 !important;
    height: 100vh !important;
}

.modal-content-responsive {
    border-radius: 0 !important;
    height: 100vh !important;
}

.responsive-modal-body {
    background: #f8f9fa !important;
    height: calc(100vh - 140px) !important;
}

/* Tablet optimizations (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    .modal-dialog-responsive {
        width: 90vw !important;
        max-width: 800px !important;
        margin: 2rem auto !important;
        height: auto !important;
    }

    .modal-content-responsive {
        border-radius: 12px !important;
        height: auto !important;
        max-height: 90vh !important;
    }

    .responsive-modal-body {
        height: auto !important;
        max-height: 70vh !important;
        padding: 30px !important;
    }

    .jHead {
        padding: 20px 25px !important;
    }

    .jHead h4 {
        font-size: 1.3rem !important;
    }

    .jHead .mobile-add-btn {
        padding: 10px 20px !important;
        font-size: 0.95rem !important;
    }

    .mobile-school-list {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
        gap: 20px !important;
    }

    .mobile-school-card {
        padding: 20px !important;
        margin-bottom: 0 !important;
    }

    .responsive-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)) !important;
        gap: 15px !important;
    }

    .mobile-modal-footer {
        padding: 25px 30px !important;
        gap: 15px !important;
    }

    .mobile-btn-cancel, .mobile-btn-save {
        padding: 14px 24px !important;
        font-size: 1.05rem !important;
        min-width: 120px !important;
    }

    /* Form layout improvements for tablets */
    .form-group {
        margin-bottom: 1.5rem !important;
    }

    .mobile-input, .mobile-select, .mobile-textarea {
        padding: 14px !important;
        font-size: 1.05rem !important;
    }

    .mobile-label {
        font-size: 1rem !important;
        margin-bottom: 10px !important;
    }
}

/* Large tablet and small desktop (1025px - 1200px) */
@media (min-width: 1025px) and (max-width: 1200px) {
    .modal-dialog-responsive {
        width: 80vw !important;
        max-width: 900px !important;
        margin: 3rem auto !important;
    }

    .mobile-school-list {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
        gap: 25px !important;
    }

    .responsive-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    }
}

/* Small mobile devices (max-width: 576px) */
@media (max-width: 576px) {
    .jHead {
        padding: 12px 15px !important;
        flex-direction: row !important;
        align-items: center !important;
    }

    .jHead h4 {
        font-size: 1rem !important;
    }

    .jHead .mobile-add-btn {
        padding: 6px 12px !important;
        font-size: 0.8rem !important;
    }

    .mobile-header {
        padding: 15px !important;
    }

    .mobile-title {
        font-size: 1rem !important;
    }

    .mobile-add-btn {
        padding: 6px 12px !important;
        font-size: 0.85rem !important;
    }

    .mobile-school-card {
        padding: 12px !important;
        margin-bottom: 12px !important;
    }

    .school-year-badge {
        font-size: 0.75rem !important;
        padding: 3px 8px !important;
    }

    .mobile-actions .btn {
        padding: 4px 8px !important;
        font-size: 0.8rem !important;
    }

    .responsive-info-grid {
        grid-template-columns: 1fr !important;
        gap: 6px !important;
    }

    .info-item {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .responsive-modal-body {
        padding: 15px !important;
    }

    .mobile-input, .mobile-select, .mobile-textarea {
        padding: 10px !important;
        font-size: 0.95rem !important;
    }

    .mobile-modal-footer {
        padding: 15px !important;
    }

    .mobile-btn-cancel, .mobile-btn-save {
        padding: 10px 16px !important;
        font-size: 0.95rem !important;
    }
}

/* Medium mobile devices (577px - 767px) */
@media (min-width: 577px) and (max-width: 767px) {
    .jHead {
        padding: 15px 20px !important;
    }

    .jHead h4 {
        font-size: 1.15rem !important;
    }

    .jHead .mobile-add-btn {
        padding: 8px 16px !important;
        font-size: 0.9rem !important;
    }

    .mobile-school-card {
        padding: 16px !important;
    }

    .responsive-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)) !important;
        gap: 10px !important;
    }

    .responsive-modal-body {
        padding: 20px !important;
    }

    .mobile-input, .mobile-select, .mobile-textarea {
        padding: 12px !important;
        font-size: 1rem !important;
    }
}

/* Bootbox mobile adjustments */
@media (max-width: 768px) {
    .bootbox .modal-dialog {
        width: 95% !important;
        max-width: 95% !important;
        margin: 10px auto !important;
    }

    .bootbox .modal-body {
        padding: 1.25rem !important;
        font-size: 0.95rem !important;
    }

    .bootbox .btn {
        font-size: 0.9rem !important;
        padding: 0.5rem 1rem !important;
    }
}
</style>
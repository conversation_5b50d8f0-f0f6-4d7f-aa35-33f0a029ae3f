<?php

class Attendance_model extends CI_Model {
    private $yearId;
    private $current_branch;
    public function __construct() {
        parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearId();
        $this->current_branch = $this->authorization->getCurrentBranch();
    }

    public function checkAttendanceTaken() {
        $date = date('Y-m-d', strtotime($_POST['date']));
        $section_id = $_POST['section_id'];
        $type_id = $_POST['type_id'];
        $type = ($_POST['type'] == 'subject_wise')?0:1;
        $att_data = $this->db->select("am.id,am.period_no, (case when am.taken_by=0 then 'Super Admin' else CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) end) as taken_by, am.taken_on, SUM(case when att.status=1 then 1 else 0 end) as present, SUM(case when att.status=2 then 1 else 0 end) as absent, SUM(case when att.status=0 then 1 else 0 end) as not_taken, SUM(case when att.status=3 then 1 else 0 end) as late,SUM(case when att.status=4 then 1 else 0 end) as absent_with_permission, am.notified_at")->from('attendance_v2_master am')->join('attendance_v2_student att', 'att.attendance_v2_master_id=am.id')->join('staff_master sm', 'sm.id=am.taken_by', 'left')->where('am.date', $date)->where('am.class_section_id', $section_id)->where('am.type_id', $type_id)->where('am.type', $type)->group_by('am.id')->get()->result();
        // echo '<pre>'; print_r($this->db->last_query()); die();
        foreach ($att_data as $key => $val) {
            $att_data[$key]->taken_on = local_time($val->taken_on, 'Y-m-d H:i:s');
        }
        return $att_data;
    }

    public function getStudentsAttendanceById($attendance_master_id) {
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sa.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sa.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'sy.alpha_rollnum';
        }else if ($prefix_order_by == "registration_no") {
            $order_by = 'sa.registration_no';
        }

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else if ($prefix_student_name == "registration_no") {
            $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }

        $sql = "select sa.id as student_id, $std_name, avs.status, avs.id as student_attendance_id, ifnull(avs.duration,'-') as attended_duration, ifnull(avm.duration,'-') as duration, ifnull(sa.first_name,'') as first_name, sa.admission_no as admission_number, sy.roll_no as roll_number, ifnull(sa.enrollment_number,0) as enrollment_number, ifnull(sy.alpha_rollnum,0) as alpha_rollnum, ifnull(sa.registration_no,0) as  registration_no 
            from student_admission sa 
            join attendance_v2_student avs on avs.student_admission_id=sa.id 
            join attendance_v2_master avm on avm.id=avs.attendance_v2_master_id 
            join student_year sy on sa.id=sy.student_admission_id  and sy.acad_year_id=$this->yearId
            where avm.id=$attendance_master_id
            order by $order_by";
        return $this->db->query($sql)->result();
    }

    public function getStudentsBySubjectSection($input) {
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name,sa.admission_no';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sa.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sa.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'sy.alpha_rollnum';
        }else if ($prefix_order_by == "registration_no") {
            $order_by = 'sa.registration_no';
        }else if ($prefix_order_by == "first_name") {
            $order_by = 'sa.first_name';
        }

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else if ($prefix_student_name == "registration_no") {
            $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }

        $acad_year_id = $this->acad_year->getAcadYearId();
        $section_id = $input['section_id'];
        $attendance_type = $input['attendance_type']; //subject_wise OR session_wise
        $attendance_type_id = $input['attendance_type_id'];//subject_id OR session_id
        if($attendance_type == 'subject_wise') {
            $class = $this->db_readonly->select("id, class_master_id")->where("id in (select class_id from class_section where id=$section_id)")->get('class')->row();
            $subject_master_id = $attendance_type_id;

            $elective = $this->db_readonly->query("SELECT id FROM elective_master_group_subjects WHERE subject_master_id=$subject_master_id")->result();

            /*$elective = $this->db_readonly->query("SELECT id FROM ex_elective_group_subjects WHERE class_subject_id in (SELECT id FROM lp_subjects WHERE class_master_id=$class->class_master_id AND subject_master_id=$subject_master_id)")->row();*/
            //check if subject is elective
            if(!empty($elective)) {
                $ids = [];
                foreach ($elective as $el) {
                    $ids[] = $el->id;
                }
                $elective_ids = implode(",", $ids);
                $students = $this->db_readonly->query("select student_admission_id as id from elective_student_master where elective_master_group_subject_id in ($elective_ids)")->result();
                $ids = [];
                foreach ($students as $std) {
                    $ids[] = $std->id;
                }
                $std_ids = implode(",", $ids);
                $sql = "select sa.id as student_id, $std_name, sa.email, ifnull(sa.first_name,'') as first_name, sa.admission_no as admission_number, sy.roll_no as roll_number, ifnull(sa.enrollment_number,0) as enrollment_number, ifnull(sy.alpha_rollnum,0) as alpha_rollnum, ifnull(sa.registration_no,0) as  registration_no
                        from student_admission sa 
                        join student_year sy on sy.student_admission_id=sa.id 
                        where sy.class_section_id=$section_id 
                        and sa.id in ($std_ids) 
                        and sa.admission_status=2 
                        and sy.promotion_status!=4 
                        and sy.promotion_status!=5 
                        and sy.promotion_status!='JOINED'
                        group by sa.id 
                        order by $order_by";
                return $this->db_readonly->query($sql)->result();
                /*$elective_group_subject_id = $elective->id;
                $sql = "select sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name 
                        from student_admission sa 
                        join student_year sy on sy.student_admission_id=sa.id 
                        join ex_elective_student se on se.student_admission_id=sa.id 
                        where sy.class_section_id=$section_id and sa.admission_status=2 
                        and sy.promotion_status!=4 and sy.promotion_status!=5 
                        and se.elective_group_subject_id=$elective_group_subject_id 
                        group by sa.id 
                        order by sa.first_name";
                return $this->db_readonly->query($sql)->result();*/
            }
        }
        $sql = "select sa.id as student_id, $std_name, sa.email, ifnull(sa.first_name,'') as first_name, sa.admission_no as admission_number, sy.roll_no as roll_number, ifnull(sa.enrollment_number,0) as enrollment_number, ifnull(sy.alpha_rollnum,0) as alpha_rollnum, ifnull(sa.registration_no,0) as  registration_no
            from student_admission sa 
            join student_year sy on sy.student_admission_id=sa.id 
            where sy.class_section_id=$section_id and sa.admission_status=2 
            and sy.promotion_status!=4 and sy.promotion_status!=5 and sy.promotion_status!='JOINED'
            and sy.acad_year_id=$acad_year_id 
            order by $order_by";
        return $this->db_readonly->query($sql)->result();

    }

    public function getSubjectNameById($subject_id) {
        return $this->db->select('subject_name')
                  ->from('subject_master')
                  ->where('id', $subject_id)
                  ->get()->row()->subject_name;
    }

    public function save_attndance_data() {
        $input = $_POST;
        $taken_by = $this->authorization->getAvatarStakeHolderId();
        $attendance_master_id = $input['attendance_master_id'];
        $new_status = $input['new_status'];
        $old_status = $input['old_status'];
        $student_data = [];
        $student_ids = [];
        $this->db->trans_start();
        if($input['is_edit']) {
            //edit existing
            $master_data = array(
                'date' => date('Y-m-d', strtotime($input['date'])),
                'class_section_id' => $input['section_id'],
                'type' => ($input['attendance_type']=='subject_wise')?0:1,
                'type_id' => $input['attendance_type_id'],
                'last_modified_by' => $taken_by,
                'period_no' => isset($input['period_no']) ? $input['period_no'] : null
            );
            $this->db->where('id', $attendance_master_id)->update('attendance_v2_master', $master_data);
            foreach ($new_status as $student_id => $status) {
                if($old_status[$student_id] != $status) {
                    $student_data[] = array(
                        'student_admission_id' => $student_id,
                        'status' => $status
                    );
                    $student_ids[] = $student_id;
                }
            }
            if(!empty($student_data)) {
                $this->db->where('attendance_v2_master_id', $attendance_master_id)->update_batch('attendance_v2_student', $student_data, 'student_admission_id');
            }

        } else {
            //new entry
            $master_data = array(
                'date' => date('Y-m-d', strtotime($input['date'])),
                'class_section_id' => $input['section_id'],
                'type' => ($input['attendance_type']=='subject_wise')?0:1,
                'type_id' => $input['attendance_type_id'],
                'taken_by' => $taken_by,
                'last_modified_by' => $taken_by,
                'duration' => (!isset($input['class_duration']) || $input['class_duration']=='')?NULL:$input['class_duration'],
                'source' => $input['source'],
                'period_no' => isset($input['period_no']) ? $input['period_no'] : null,
                'ttp_id' => isset($input['timetable_id']) ? $input['timetable_id'] : null
            );

            $this->db->insert('attendance_v2_master', $master_data);
            $attendance_master_id = $this->db->insert_id();

            foreach ($new_status as $student_id => $status) {
                $duration = isset($input['minimum_duration'])?$input['minimum_duration'][$student_id]:'';
                $student_data[] = array(
                    'attendance_v2_master_id' => $attendance_master_id,
                    'student_admission_id' => $student_id,
                    'status' => $status,
                    'duration' => ($duration=='')?NULL:$duration
                );
                $student_ids[] = $student_id;
            }
            if(!empty($student_data)) {
                //save individual student status
                $this->db->insert_batch('attendance_v2_student', $student_data);
            }
        }

        if(!empty($student_data)) {
            $student_audit_data = [];
            //save audit data
            $std_data = $this->db->select('id, student_admission_id as std_id, status')->where('attendance_v2_master_id', $attendance_master_id)->where_in('student_admission_id', $student_ids)->get('attendance_v2_student')->result();
            foreach ($std_data as $key => $std) {
                $student_audit_data[] = array(
                    'attendance_v2_student_id' => $std->id,
                    'staff_id' => $taken_by,
                    'status' => $new_status[$std->std_id],
                    'reason' => ''
                );
            }
            if(!empty($student_audit_data)) {
                $this->db->insert_batch('attendance_v2_student_audit', $student_audit_data);
            }
        }

        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return $attendance_master_id;
    }

    public function getStudentAttendanceAudit($student_attendance_id) {
        $sql = "select (case when asa.staff_id=0 then 'Super Admin' else CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) end) as action_by, asa.created_on, asa.status, asa.reason 
                from attendance_v2_student_audit asa 
                left join staff_master sm on sm.id=asa.staff_id 
                where attendance_v2_student_id=$student_attendance_id";
        $data = $this->db->query($sql)->result();
        foreach ($data as $key => $val) {
            $data[$key]->action_on = local_time($val->created_on, 'd-M h:i a');
        }
        return $data;
    }

    public function getSectionSubjets($section_id) {
        $class = $this->db_readonly->query("select class_master_id from class c join class_section cs on cs.class_id=c.id where cs.id=$section_id")->row();
        if(empty($class)) return [];
        $class_master_id = $class->class_master_id;
        if($this->settings->getSetting('is_semester_scheme')) {
            // $active_semester = 'odd';
            // if($this->settings->getSetting('active_semester')) {
            //     $active_semester = strtolower($this->settings->getSetting('active_semester'));
            // }
            $sql = "select sub.class_master_id,sub.subject_master_id as id, sub.subject_name 
                from lp_subjects sub 
                join class_master_semester cms on cms.sem_id=sub.semester_id 
                join semester s on s.id=sub.semester_id 
                where sub.class_master_id=$class_master_id 
                and cms.class_master_id=$class_master_id and sub.acad_year_id=$this->yearId 
                order by sub.subject_name";
        } else {
            $sql = "select sub.subject_master_id as id, sub.subject_name,sub.class_master_id
                    from lp_subjects sub 
                    where sub.class_master_id=$class_master_id and sub.acad_year_id=$this->yearId 
                    order by sub.subject_name";    
        }
        $return = $this->db_readonly->query($sql)->result();
        return $return;
        // echo "<pre>"; 
    }

    public function getTakenPeriodLists($section_id,$date) {
        $result=$result["period_lists"]=$this->db_readonly->select("*")
        ->from("attendance_v2_master ")
        ->where("period_no Is Not Null",NULL,FALSE)
        ->where("class_section_id",$section_id)
        ->where("date",$date)
        ->get()->result();
        
        foreach($result as $key => $value){
            $value->elective= $this->isElective($value->type_id);
            $value->take_on_local = local_time($value->taken_on,'d-M h:i a'); 
            $value->login_staff_id=$this->authorization->getAvatarStakeHolderId();
            $result[$key]->taken_by_name=$this->get_staff_name_from_staff_id($value->taken_by);
        }

        // foreach($result as $val) {
        // }
        
        return $result;
    }

    public function get_staff_name_from_staff_id($staff_id) {
        $staff_obj = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staff_name')
            ->from('staff_master sm')
            ->where('sm.id', $staff_id)        
            ->get()->row();
        if (!empty($staff_obj)) {
          return $staff_obj->staff_name;
        }else{
          return 'Admin';
        }
      }

    public function isElective($type_id){
        return $this->db_readonly->select("*")
                ->where("subject_master_id",$type_id)
                ->get("elective_master_group_subjects")->result();
    }

    public function getElectiveSubjects($elective_master_group_id,$class_master_id,$period_no,$class_section_id,$date,$ttp_id,$class_semester_id){
        // echo $elective_master_group_id;
        // echo $class_master_id;
        // echo $period_no;
        // // echo $class_section_id; die();

        $show_all_electives=$this->settings->getSetting('show_all_electives');
        $is_semester_scheme=$this->settings->getSetting('is_semester_scheme');
        $loggedin_staff_id=$this->authorization->getAvatarStakeHolderId();

        if($show_all_electives==1){
            $query="select * from lp_subjects          
            where subject_master_id in (select subject_master_id from elective_master_group_subjects) and class_master_id=$class_master_id and subject_master_id not in (select type_id from attendance_v2_master
            where period_no='$period_no' and class_section_id=$class_section_id and date='$date' and ttp_id=$ttp_id)";
        }else{
            $query="select * from lp_subjects
            join lp_subjects_section_staff lsss on lsss.lp_subjects_id=lp_subjects.id
            where lsss.staff_id=$loggedin_staff_id and subject_master_id in (select subject_master_id from elective_master_group_subjects where elective_master_group_id=$elective_master_group_id) and class_master_id=$class_master_id and subject_master_id not in (select type_id from attendance_v2_master
            where period_no='$period_no' and class_section_id=$class_section_id and date='$date' and ttp_id=$ttp_id)";
        }
        
        if ($is_semester_scheme) {
            $query.= " and semester_id=$class_semester_id";
        }
            
        return $this->db->query($query)->result();
    }

      public function getActualPeriodLists($data){
          return $this->db_readonly->select("ttp.id,ttp.start_time,ttp.end_time,ttp.short_name,ttp.period_seq_excl_break")
          ->from("timetable_template_class_section ttcs")
          ->join("timetable_template tt","tt.id=ttcs.timetable_template_id")
          ->join("timetable_template_periods ttp","tt.id=ttp.ttt_id")
          ->where("ttcs.class_section_id",$data["section_id"])
          ->where("ttp.week_day",$data["weekDay"])
          ->order_by('ttp.period_seq')
          ->get()->result();
      }
    
    public function getSectionWise(){
        $from_date = date('Y-m-d', strtotime($_POST['from_date']));
        $to_date = date('Y-m-d', strtotime($_POST['to_date']));
        $section_id = $_POST['section_id'];
        $type_id = $_POST['type_id'];
        $attendance_type = $_POST['type'];

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sa.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sa.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'sy.alpha_rollnum';
        }

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }


        $std_sql = "select sa.id as student_id, $std_name, sa.admission_status, sy.promotion_status from student_admission sa join student_year sy on sy.student_admission_id=sa.id where sy.class_section_id=$section_id order by sa.first_name";
        if($attendance_type == 'subject_wise') {
            $class = $this->db_readonly->select("id, class_master_id")->where("id in (select class_id from class_section where id=$section_id)")->get('class')->row();
            $elective = $this->db_readonly->query("SELECT id FROM ex_elective_group_subjects WHERE class_subject_id in (SELECT id FROM lp_subjects WHERE class_master_id=$class->class_master_id AND subject_master_id=$type_id)")->row();
            if(!empty($elective)) {
                $std_sql = "SELECT sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, sa.admission_status, sy.promotion_status 
                        from student_admission sa 
                        join student_year sy on sy.student_admission_id=sa.id 
                        join ex_elective_student se on se.student_admission_id=sa.id 
                        where sy.class_section_id=$section_id 
                        and se.elective_group_subject_id=$elective->id 
                        order by $order_by";
            }
        }
        $students = $this->db_readonly->query($std_sql)->result();
        $student_ids = [];
        foreach ($students as $std) {
            $student_ids[] = $std->student_id;
        }
        $std_ids = empty($student_ids)?0:implode(",", $student_ids);
        $sql = '';
        if($attendance_type == 'subject_wise') {
            $sql = "select am.date, am.id as att_id, ats.student_admission_id as student_id, ats.status 
                    from attendance_v2_master am 
                    join attendance_v2_student ats on ats.attendance_v2_master_id=am.id 
                    where (am.date>='$from_date' and am.date<='$to_date') 
                    and ats.student_admission_id in ($std_ids) 
                    and am.type=0 and am.type_id=$type_id";
        } else if($attendance_type == 'session_wise') {
            //todo
        }
        $result = [];
        if($sql != '') {
            $result = $this->db_readonly->query($sql)->result();
        }

        $dates = [];
        $attendance = [];
        foreach ($result as $res) {
            if(!array_key_exists($res->date, $dates)) {
                $dates[$res->date] = [];
            }
            if(!in_array($res->att_id, $dates[$res->date])) {
                $dates[$res->date][] = $res->att_id;
            }
            if(!array_key_exists($res->student_id, $attendance)) {
                $attendance[$res->student_id] = [];
            }
            $attendance[$res->student_id][$res->date][$res->att_id] = $res->status;
        }

        foreach ($students as $key => $student) {
            $students[$key]->attendance = [];
            if(array_key_exists($student->student_id, $attendance)) {
                $students[$key]->attendance = $attendance[$student->student_id];
            }
        }

        return array('dates' => $dates, 'students' => $students);
    }

    private function getTimeTableTime($ttp_id) {
        return $timings=$this->db_readonly->query("select start_time, end_time from timetable_template_periods where id=$ttp_id")->row();
    }

    private function get_students_absent_with_permission($att_master_id){
        return $this->db_readonly->select("count(status) as students_absent_with_permission_count")
        ->from("attendance_v2_student")
        ->where("attendance_v2_master_id",$att_master_id)->where("status",4)->get()->row()->students_absent_with_permission_count;
    }
    public function get_period_wise_attendance_report($inputData){
        $from_date = $inputData['from_date'];
        $to_date = $inputData['to_date'];
        $class_section_id = $inputData['class_section_id'];

        $attendance_data_query="select avm.id as att_master_id, concat(c.class_name, cs.section_name) as class_section, avm.period_no, avm.type_id as subject_id,
        sum(case 
        when avs.status=0 then 1
        else 0
        end) as toal_not_taken,
        sum(case 
        when avs.status=1 then 1
        else 0
        end) as total_present,
        sum(case 
        when avs.status=2 then 1
        else 0
        end) as total_absent, avm.taken_by,
        count(avs.student_admission_id) as total_student,
        date_format(avm.taken_on,'%D %b %Y, %r') as att_taken_time,
        date_format(avm.date,'%D %b %Y') as date,
        concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as teacher_name, subject_name, sub_master.short_name, ttp_id
        from attendance_v2_master avm
        join attendance_v2_student avs on avs.attendance_v2_master_id=avm.id
        join class_section cs on cs.id=avm.class_section_id
        join class c on c.id=cs.class_id
        join staff_master sm on sm.id=avm.taken_by
        join subject_master sub_master on sub_master.id=avm.type_id
        where avm.period_no is not null and date(avm.taken_on) between cast('$from_date' AS DATE) and cast('$to_date' AS DATE) ";

        if (!empty($class_section_id)) {
            $attendance_data_query .= " and avm.class_section_id in (" . implode(', ', $class_section_id) . ") ";
        }

        $attendance_data_query .= " group by avm.class_section_id, avm.period_no order by class_section,period_no";

        $attendance_data = $this->db_readonly->query($attendance_data_query)->result();

        foreach($attendance_data as $key => $val){
            $val->att_taken_time = date("l, jS M Y h:i:s A", strtotime($val->att_taken_time));
            $val->date=date("jS M Y",strtotime($val->date));
            $timings=$this->getTimeTableTime($val->ttp_id);
            $val->start_time= date("h:i A",strtotime($timings->start_time));
            $val->end_time = date("h:i A", strtotime($timings->end_time));
            $val->students_absent_with_permission=$this->get_students_absent_with_permission($val->att_master_id);
        }
        
        return $attendance_data;
    }

    public function getSectionSubjectData() {
        $date = date('Y-m-d', strtotime($_POST['date']));
        $section_id = $_POST['section_id'];
        $attendance_type = $_POST['type'];

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }

        //get students of section
        $std_sql = "SELECT sa.id as student_id, $std_name, sa.admission_status, sy.promotion_status from student_admission sa join student_year sy on sy.student_admission_id=sa.id where sy.class_section_id=$section_id and sa.admission_status=2 and promotion_status!=4 and promotion_status!=5 order by sa.first_name";
        $students = $this->db_readonly->query($std_sql)->result();
        $student_ids = [];
        foreach ($students as $std) {
            $student_ids[] = $std->student_id;
        }
        $std_ids = empty($student_ids)?0:implode(",", $student_ids);
        $sql = "";
        if($attendance_type == 'subject_wise') {
            $sql = "SELECT am.type_id,am.period_no,am.taken_on, ats.student_admission_id as student_id, ats.status, am.id as am_id, cs.class_id, c.class_master_id, lp_sub.subject_name
                    from attendance_v2_master am 
                    join attendance_v2_student ats on ats.attendance_v2_master_id=am.id 
                    join class_section cs on cs.id=am.class_section_id
                    join class c on c.id=cs.class_id
                    join lp_subjects lp_sub on c.class_master_id=lp_sub.class_master_id and lp_sub.subject_master_id=am.type_id
                    where am.date='$date' 
                    and am.type=0 
                    and am.class_section_id=$section_id
                    order by am.period_no Asc";
        } else if($attendance_type == 'session_wise') {
            //todo
        }

        $result = [];
        if($sql != '') {
            $result = $this->db_readonly->query($sql)->result();
        }

        $attendance = [];
        $type_ids = [];
        $subjects_or_sessions = [];
        foreach ($result as $key => $res) {
            $k = $res->type_id.'_'.$res->am_id;
            if(!in_array($k, $type_ids)) {
                $type_ids[] = $k;
                $subjects_or_sessions[] = array('id' => $k, 'subject_name' => $res->subject_name,'period_no' => $res->period_no,'taken_on' => $res->taken_on);
            } 
            if(!array_key_exists($res->student_id, $attendance)) {
                $attendance[$res->student_id] = array();
            }
            
            $attendance[$res->student_id][$res->type_id.'_'.$res->am_id] = $res->status;
        }

        foreach ($students as $key => $student) {
            $students[$key]->attendance = [];
            if(array_key_exists($student->student_id, $attendance)) {
                $students[$key]->attendance = $attendance[$student->student_id];
            }
        }
        /*$subjects_or_sessions = [];
        if(!empty($type_ids)) {
            if($attendance_type == 'subject_wise') {
                $ids = implode(",", $type_ids);
                $subjects_or_sessions = $this->db_readonly->query("SELECT id, subject_name from subject_master where id in ($ids) order by subject_name")->result();
            } else if($attendance_type == 'session_wise'){
                //todo
            }
        }*/
        // echo "<pre>"; print_r($subjects_or_sessions); die();
        return array('students' => $students, 'subjects_or_sessions' => $subjects_or_sessions);
    }

    public function get_overall_period_wise_attendance($data){
        $date=$_POST['date'];
        $class_section_id=$_POST['class_section_id'];

        $attendance_query="select concat(c.class_name, cs.section_name) as class_section, avm.period_no, avm.type_id as subject_id,
        sum(case 
        when avs.status=0 then 1
        else 0
        end) as toal_not_taken,
        sum(case 
        when avs.status=1 then 1
        else 0
        end) as total_present,
        sum(case 
        when avs.status=2 then 1
        else 0
        end) as total_absent, avm.taken_by,
        count(avs.student_admission_id) as total_student,
        date_format(avm.taken_on,'%d-%m-%Y %r') as att_taken_time,
        concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as teacher_name
        from attendance_v2_master avm
        join attendance_v2_student avs on avs.attendance_v2_master_id=avm.id
        join class_section cs on cs.id=avm.class_section_id
        join class c on c.id=cs.class_id
        join staff_master sm on sm.id=avm.taken_by
        where avm.period_no is not null and date(avm.taken_on) = cast('$date' AS DATE) ";

        if(!empty($class_section_id)){
            $attendance_query.=" and avm.class_section_id in (".implode(', ', $class_section_id).")";
        }

        $attendance_query.=" group by avm.class_section_id, avm.period_no";

        $attendance=$this->db_readonly->query($attendance_query)->result();

        foreach($attendance as $key => $val){
            $val->att_taken_time=local_time($val->att_taken_time);
        }

        return $attendance;
    }

    public function get_subject_month_wise_attendance($data){
        $section_id = $_POST['section_id'];

        $subject_id = '';
        if(!empty($_POST['subject_id'])){
            $subject_id =  implode(',',$_POST['subject_id']);
        }
        $attendance_type = $_POST['type'];
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else {
          $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sa.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sa.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'sy.alpha_rollnum';
        }

        $std_sql = "SELECT sa.id as student_id, $std_name, c.class_name,  sa.admission_status, sy.promotion_status from student_admission sa join student_year sy on sy.student_admission_id=sa.id join class c on sy.class_id = c.id where sy.class_section_id=$section_id and sa.admission_status=2 and promotion_status!=4 and promotion_status!=5 order by $order_by";

        $students = $this->db_readonly->query($std_sql)->result();
        $student_ids = [];
        foreach ($students as $std) {
            $student_ids[] = $std->student_id;
        }
        $std_ids = empty($student_ids)?0:implode(",", $student_ids);

        $fromDate = date('Y-m-d', strtotime($data['from_date']));
        $toDate = date('Y-m-d', strtotime($data['to_date']));
        $sql = "";
        if($attendance_type == 'subject_wise') {
            $sql = "SELECT am.type_id,am.period_no,am.date,am.taken_on, ats.student_admission_id as student_id, ats.status, am.id as am_id, cs.class_id, c.class_master_id,lp_sub.id as subject_id, lp_sub.subject_name, count(lp_sub.id) as total, 
            SUM(case when ats.status=1 then 1 else 0 end) as present, SUM(case when ats.status=2 then 1 else 0 end) as absent, SUM(case when ats.status=0 then 1 else 0 end) as not_taken
            from attendance_v2_master am 
            join attendance_v2_student ats on ats.attendance_v2_master_id=am.id 
            join class_section cs on cs.id=am.class_section_id
            join class c on c.id=cs.class_id
            join lp_subjects lp_sub on lp_sub.subject_master_id=am.type_id
            and am.type=0 
            and ats.student_admission_id in ($std_ids)";
            // $subjectIds='';
            // for($i=0;$i<count($subject_id);$i++){
            //     if($i==count($subject_id)-1){
            //         $subjectIds.=$subject_id[$i];
            //     }else{
            //         $subjectIds.=$subject_id[$i].",";
            //     }
            // }

            if($subject_id){
                $sql.= " and am.type_id IN ($subject_id)";
            }

            $sql.= " and am.date between '$fromDate' and '$toDate'
            group by student_id, subject_id
            order by am.period_no Asc;";

            $subjects = "SELECT am.type_id,am.period_no,am.date,am.taken_on, am.id as am_id, cs.class_id, c.class_master_id,lp_sub.id as subject_id, lp_sub.subject_name, count(lp_sub.id) as total
            from attendance_v2_master am 
            join attendance_v2_student ats on ats.attendance_v2_master_id=am.id 
            join class_section cs on cs.id=am.class_section_id
            join class c on c.id=cs.class_id
            join lp_subjects lp_sub on lp_sub.subject_master_id=am.type_id
            and am.type=0 
            and ats.student_admission_id in ($std_ids)";
            // and am.class_section_id=$section_id";

            // $subjectIds='';
            // for($i=0;$i<count($subject_id);$i++){
            //     if($i==count($subject_id)-1){
            //         $subjectIds.=$subject_id[$i];
            //     }else{
            //         $subjectIds.=$subject_id[$i].",";
            //     }
            // }
            if($subject_id){
                $subjects.= " and am.type_id IN ($subject_id)";
            }
            $subjects.= " and am.date between '$fromDate' and '$toDate'
            group by am.type_id
            order by am.period_no Asc;";

        } else if($attendance_type == 'session_wise') {
            //todo
        }

        $result = [];
        if($sql != '') {
            $result = $this->db_readonly->query($sql)->result();
        }
        $subjects_total = [];
        if($sql != '') {
            $subjects_total = $this->db_readonly->query($subjects)->result();
        }

        $attendance = [];
        $type_ids = [];
        $subjects_or_sessions = [];
        foreach ($result as $key => $res) {
            if(!array_key_exists($res->student_id, $attendance)) {
                $attendance[$res->student_id][$res->type_id] = array();
            }
            $attendance[$res->student_id][$res->type_id] = $res;
        }
        // foreach ($subjects_total as $key => $sub) {
        //     if(!in_array($sub->type_id, $type_ids)) {
        //         $subjects_or_sessions[] = array('type_id'=>$sub->type_id,'subject_name' => $sub->subject_name,'total'=>count($subjects_total));
        //     } 
        // }
        // foreach ($students as $key => $student) {
        //     $students[$key]->attendance = [];
        //     if(array_key_exists($student->student_id, $attendance)) {
        //         $students[$key]->attendance = $attendance[$student->student_id];
        //     }
        // }
        return array('students' => $students, 'subjects_or_sessions' => $subjects_total,'attendance_data'=>$attendance);
    }

    public function get_subjects_by_class_section($data){
        $class_section_id = $data['class_section_id'];

        return $subjects=$this->getSectionSemesterSubjets($class_section_id);
        
        // echo "<pre>"; print_r($subjects); die();
        
        // $sql="select am.type_id, lp_sub.subject_name
        // from attendance_v2_master am 
        // join class_section cs on cs.id=am.class_section_id
        // join class c on c.id=cs.class_id
        // join lp_subjects lp_sub on c.class_master_id=lp_sub.class_master_id and lp_sub.subject_master_id=am.type_id
        // and am.type=0 
        // and am.class_section_id=$class_section_id
        // group by lp_sub.subject_name";
        
        // $subjects=$this->db_readonly->query($sql)->result();
        // return $subjects;
    }

    public function getSectionStudentNames($section_id) {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		}
        $std_sql = "SELECT sa.id as student_id, $std_name, sa.admission_status, sy.promotion_status from student_admission sa join student_year sy on sy.student_admission_id=sa.id where sy.class_section_id=$section_id and sa.admission_status=2 and promotion_status!=4 and promotion_status!=5 order by sa.first_name";
        return $this->db_readonly->query($std_sql)->result();
    }

    public function getAttendanceDataByDateRange($from_date, $to_date, $student_id) {
        $attendance_type = $this->settings->getSetting('student_attendance_type');
        $attendance = [];
        if($attendance_type == '' || $attendance_type == 'subject_wise') {
            $sql = "SELECT m.date,m.period_no, sub.subject_name, s.status, m.taken_on, s.duration 
                    FROM attendance_v2_master m 
                    JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
                    JOIN subject_master sub ON m.type_id=sub.id 
                    WHERE m.date>='$from_date' AND m.date<='$to_date' 
                    AND s.student_admission_id=$student_id 
                    AND m.type=0 AND s.status!=0 
                    ORDER BY m.taken_on Asc, m.period_no Asc";
            $attendance = $this->db_readonly->query($sql)->result();
        }
        $data = [];
        foreach($attendance as $key => $value) {
            if(!array_key_exists($value->date, $data)) {
                $data[$value->date] = array();
                $data[$value->date]['present'] = 0;
                $data[$value->date]['absent'] = 0;
                $data[$value->date]['late'] = 0;
            }
            $value->taken_on = local_time($value->taken_on, 'Y-m-d H:i:s');
            $data[$value->date]['date'] = $value->date;
            $data[$value->date]['duration'] = $value->duration;
            if($value->status == 1) {
                $attendance[$key]->att_status = 'Present';
                $data[$value->date]['present']++;
            } else if($value->status == 2) {
                $attendance[$key]->att_status = 'Absent';
                $data[$value->date]['absent']++;
            } else if($value->status == 3) {
                $attendance[$key]->att_status = 'Late';
                $data[$value->date]['late']++;
            }
            $data[$value->date]['attendance'][] = $value;
        }
        $attendance = [];
        foreach ($data as $d) {
            $attendance[] = $d;
        }
        return $attendance;
        // echo "<pre>"; print_r($attendance); die();
    }

    public function getSectionSummaryByDateRange($from_date, $to_date, $section_id) {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else {
          $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sa.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sa.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'sy.alpha_rollnum';
        }

        $std_sql = "SELECT sa.id as student_id, $std_name, sa.admission_status, sy.promotion_status from student_admission sa join student_year sy on sy.student_admission_id=sa.id where sy.class_section_id=$section_id and sa.admission_status=2 and promotion_status!=4 and promotion_status!=5 order by $order_by";
        $students = $this->db_readonly->query($std_sql)->result();
        $student_ids = [];
        foreach ($students as $std) {
            $student_ids[] = $std->student_id;
        }
        $std_ids = empty($student_ids)?0:implode(",", $student_ids);

        $sql = "SELECT m.date, SUM(case when s.status=1 then 1 else 0 end) as present, SUM(case when s.status=2 then 1 else 0 end) as absent, SUM(case when s.status=3 then 1 else 0 end) as late, s.student_admission_id as student_id 
                FROM attendance_v2_master m 
                JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
                WHERE m.date>='$from_date' AND m.date<='$to_date' 
                AND s.student_admission_id in ($std_ids) 
                AND s.status!=0 
                GROUP BY m.date, s.student_admission_id 
                ORDER BY m.date ASC";
        $result = $this->db_readonly->query($sql)->result();

        $dates = [];
        $attendance = [];
        foreach ($result as $res) {
            if(!in_array($res->date, $dates)) {
                $dates[] = $res->date;
            }
            if(!array_key_exists($res->student_id, $attendance)) {
                $attendance[$res->student_id][$res->date] = [];
            }
            $attendance[$res->student_id][$res->date] = $res;
        }

        foreach ($students as $key => $student) {
            $students[$key]->attendance = [];
            if(array_key_exists($student->student_id, $attendance)) {
                $students[$key]->attendance = $attendance[$student->student_id];
            }
        }

        return array('dates' => $dates, 'students' => $students);
    }

    public function getSummaryReport($from_date, $to_date) {
        $acad_year_id = $this->acad_year->getAcadYearId();
        $section_sql = "SELECT cs.id as section_id, cs.section_name, cs.class_name 
                        FROM class_section cs 
                        JOIN class c ON c.id=cs.class_id 
                        WHERE c.acad_year_id=$acad_year_id 
                        AND c.is_placeholder=0 
                        AND cs.is_placeholder=0
                        ORDER BY cs.display_order";
        $sections = $this->db_readonly->query($section_sql)->result();

        $section_ids = [];
        foreach ($sections as $sec) {
            $section_ids[] = $sec->section_id;
        }
        $sec_ids = empty($section_ids)?0:implode(",", $section_ids);

        $std_sql = "SELECT count(sa.id) as total_students, sy.class_section_id as section_id, sa.gender 
                    FROM student_admission sa 
                    JOIN student_year sy ON sy.student_admission_id=sa.id 
                    WHERE sy.class_section_id IN ($sec_ids) 
                    AND sa.admission_status=2 
                    AND promotion_status!=4 
                    AND promotion_status!=5 
                    GROUP BY sy.class_section_id, sa.gender";
        $std_data = $this->db_readonly->query($std_sql)->result();

        $students = [];
        foreach ($std_data as $std) {
            $students[$std->section_id][$std->gender] = $std->total_students;
        }

        $sql = "SELECT class_section_id, COUNT(id) as total 
                FROM attendance_v2_master 
                WHERE date>='$from_date' AND date<='$to_date' 
                GROUP BY class_section_id";
        $master_data = $this->db_readonly->query($sql)->result();
        $att_master_data = [];
        foreach ($master_data as $key => $m_data) {
            $att_master_data[$m_data->class_section_id] = $m_data;
        }

        $sql = "SELECT m.class_section_id, SUM(case when s.status=1 then 1 else 0 end) as present, SUM(case when s.status=2 then 1 else 0 end) as absent, SUM(case when s.status=3 then 1 else 0 end) as late, sa.gender 
                FROM attendance_v2_master m 
                JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
                JOIN student_admission sa ON sa.id=s.student_admission_id 
                WHERE m.date>='$from_date' AND m.date<='$to_date' 
                AND s.status!=0 
                GROUP BY m.class_section_id, sa.gender";
        $status_data = $this->db_readonly->query($sql)->result();
        $att_data = [];
        foreach ($status_data as $key => $status) {
            $att_data[$status->class_section_id][$status->gender] = $status;
        }

        //Manjukiran: For filling gaps
        foreach ($sections as $section) {
            if (!isset($att_data[$section->section_id]['M'])) {
                $obj = new stdClass();
                $obj->class_section_id = $section->section_id;
                $obj->present = 0;
                $obj->absent = 0;
                $obj->late = 0;
                $obj->gender = 'M';
                $att_data[$section->section_id]['M'] =  $obj;
            }
            if (!isset($att_data[$section->section_id]['F'])) {
                $obj = new stdClass();
                $obj->class_section_id = $section->section_id;
                $obj->present = 0;
                $obj->absent = 0;
                $obj->late = 0;
                $obj->gender = 'F';
                $att_data[$section->section_id]['F'] =  $obj;
            }

            if (!isset($students[$section->section_id])) {
                $students[$section->section_id]['M'] = 0;
                $students[$section->section_id]['F'] = 0;
            }
            if (!isset($students[$section->section_id]['M'])) {
                $students[$section->section_id]['M'] = 0;
            }
            if (!isset($students[$section->section_id]['F'])) {
                $students[$section->section_id]['F'] = 0;
            }
        }

        foreach ($sections as $k => $sec) {
            $sections[$k]->total_classes = 0;
            $sections[$k]->present = ['boys' => 0, 'girls' => 0];
            $sections[$k]->absent = ['boys' => 0, 'girls' => 0];
            $sections[$k]->total = ['boys' => 0, 'girls' => 0];
            $sections[$k]->percentage = ['boys' => 0, 'girls' => 0];
            $sections[$k]->total_students = ['boys' => 0, 'girls' => 0];
            $sections[$k]->average_present = ['boys' => '-', 'girls' => '-'];

            if(array_key_exists($sec->section_id, $att_master_data)) {
                $sections[$k]->total_classes = $att_master_data[$sec->section_id]->total;
            }

            if(array_key_exists($sec->section_id, $att_data)) {
                $sections[$k]->present['boys'] = $att_data[$sec->section_id]['M']->present+$att_data[$sec->section_id]['M']->late;
                $sections[$k]->absent['boys'] = $att_data[$sec->section_id]['M']->absent;
                $sections[$k]->total['boys'] = $att_data[$sec->section_id]['M']->present + $att_data[$sec->section_id]['M']->absent +$att_data[$sec->section_id]['M']->late;
                if ($sections[$k]->total['boys'] != 0)
                    $percentage_boys = ($sections[$k]->present['boys']/$sections[$k]->total['boys'])*100;
                else 
                    $percentage_boys = 0;

                $sections[$k]->percentage['boys'] = round($percentage_boys);
                $sections[$k]->total_students['boys'] = $students[$sec->section_id]['M'];
                $sections[$k]->average_present['boys'] = round($percentage_boys * 0.01 * $sections[$k]->total_students['boys']);
                
                $sections[$k]->present['girls'] = $att_data[$sec->section_id]['F']->present+$att_data[$sec->section_id]['F']->late;
                $sections[$k]->absent['girls'] = $att_data[$sec->section_id]['F']->absent;
                $sections[$k]->total['girls'] = $att_data[$sec->section_id]['F']->present + $att_data[$sec->section_id]['F']->absent +$att_data[$sec->section_id]['F']->late;

                if ($sections[$k]->total['girls'] != 0)
                    $percentage_girls = ($sections[$k]->present['girls']/$sections[$k]->total['girls'])*100;
                else
                    $percentage_girls = 0;
                $sections[$k]->percentage['girls'] = round($percentage_girls);
                $sections[$k]->total_students['girls'] = $students[$sec->section_id]['F'];
                $sections[$k]->average_present['girls'] = round($percentage_girls * 0.01 * $sections[$k]->total_students['girls']);
            }
        }

        return $sections;
    }

    public function getDayWise(){
        $from_date = date('Y-m-d', strtotime($_POST['from_date']));
        $to_date = date('Y-m-d', strtotime($_POST['to_date']));
        $section_id = $_POST['section_id'];

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else {
          $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sa.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sa.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'sy.alpha_rollnum';
        }

        $std_sql = "select sa.id as student_id, $std_name, sa.admission_status, sy.promotion_status from student_admission sa join student_year sy on sy.student_admission_id=sa.id where sy.class_section_id=$section_id order by $order_by";
        $students = $this->db_readonly->query($std_sql)->result();
        $student_ids = [];
        foreach ($students as $std) {
            $student_ids[] = $std->student_id;
        }
        $std_ids = empty($student_ids)?0:implode(",", $student_ids);
        $sql = "SELECT am.date, am.id as att_id, ats.student_admission_id as student_id, sum(case when ats.status=1 then 1 else 0 end) as present, sum(case when ats.status=2 then 1 else 0 end) as absent, sum(case when ats.status=3 then 1 else 0 end) as late, sum(case when ats.status=0 then 1 else 0 end) as not_taken, count(ats.status=1) as total, ats.status 
                    from attendance_v2_master am 
                    join attendance_v2_student ats on ats.attendance_v2_master_id=am.id 
                    where (am.date>='$from_date' and am.date<='$to_date') 
                    and am.type_id!=0 
                    and ats.student_admission_id in ($std_ids) 
                    group by am.date, ats.student_admission_id";
        $result = [];
        if($sql != '') {
            $result = $this->db_readonly->query($sql)->result();
        }

        $dates = [];
        $attendance = [];
        foreach ($result as $res) {
            if(!in_array($res->date, $dates)) {
                $dates[] = $res->date;
            }
            if(!array_key_exists($res->student_id, $attendance)) {
                $attendance[$res->student_id] = [];
            }
            $attendance[$res->student_id][$res->date] = (($res->present||$res->late)?1:($res->absent?2:0));
        }

        foreach ($students as $key => $student) {
            $students[$key]->attendance = [];
            if(array_key_exists($student->student_id, $attendance)) {
                $students[$key]->attendance = $attendance[$student->student_id];
            }
        }

        return array('dates' => $dates, 'students' => $students);
    }

    public function getAllSemesters(){
        // $active_semester = strtolower($this->settings->getSetting('active_semester'));
        return $this->db_readonly->select("s.id as semester_id, sem_name, type")
        ->from("semester s")
        ->get()->result();
    }

    public function getAllClassSectionSemester($placeholder=0) {
        $active_semester = strtolower($this->settings->getSetting('active_semester'));
        $this->db_readonly->select('cs.id, section_name, cs.class_name, class_id, sem.id as sem_id, sem.sem_name')
        ->join('class c', 'c.id=cs.class_id')
        ->join('class_master_semester cms', 'cms.class_master_id=c.class_master_id', 'left')
        ->join('semester sem', 'sem.id=cms.sem_id', 'left')
        ->where('sem.type',$active_semester)
        ->where("c.acad_year_id",$this->yearId);
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if(!$placeholder) {
          $this->db_readonly->where('cs.is_placeholder', 0);
        }
        $this->db_readonly->order_by('cs.display_order');
        return $this->db_readonly->get('class_section cs')->result();
    }

    public function getSectionSemesterSubjets($section_id) {
        $class = $this->db_readonly->query("select class_master_id from class c join class_section cs on cs.class_id=c.id where cs.id=$section_id")->row();
        if(empty($class)) return [];
        $class_master_id = $class->class_master_id;
        if($this->settings->getSetting('is_semester_scheme')) {
            $active_semester = 'odd';
            if($this->settings->getSetting('active_semester')) {
                $active_semester = strtolower($this->settings->getSetting('active_semester'));
            }
            $sql = "select sub.subject_master_id as id, sub.subject_name, s.sem_name 
                from lp_subjects sub 
                join class_master_semester cms on cms.sem_id=sub.semester_id 
                join semester s on s.id=sub.semester_id 
                where sub.class_master_id=$class_master_id 
                and cms.class_master_id=$class_master_id 
                and sub.acad_year_id= $this->yearId
                order by s.sem_name, sub.subject_name";
        } else {
            $sql = "select sub.subject_master_id as id, sub.subject_name 
                    from lp_subjects sub 
                    where sub.class_master_id=$class_master_id 
                    and sub.acad_year_id= $this->yearId
                    order by sub.subject_name";    
        }
        $return = $this->db_readonly->query($sql)->result();
        return $return;
        // echo "<pre>"; 
    }

    public function get_absent_late_data() {
        $from_date = date('Y-m-d', strtotime($_POST['from_date']));
        $to_date = date('Y-m-d', strtotime($_POST['to_date']));
        $class_ids = implode(',',$_POST['class_ids']);

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		}

        $sql = "SELECT m.date, SUM(case when s.status=1 then 1 else 0 end) as present, SUM(case when s.status=2 then 1 else 0 end) as absent, SUM(case when s.status=3 then 1 else 0 end) as late, s.student_admission_id as student_id,$std_name, cs.class_name, cs.section_name, sa.preferred_contact_no
                FROM attendance_v2_master m 
                JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
                JOIN student_admission sa ON sa.id=s.student_admission_id 
                JOIN student_year sy ON sa.id=sy.student_admission_id 
                JOIN class_section cs ON cs.id=sy.class_section_id 
                WHERE m.date>='$from_date' AND m.date<='$to_date' 
                AND sy.acad_year_id=$this->yearId 
                AND cs.class_id in ($class_ids) AND s.status!=0 
                GROUP BY s.student_admission_id";
        $result = $this->db_readonly->query($sql)->result();

        $data = [];

        foreach ($result as $key => $res) {
            if($res->absent || $res->late) {
                $res->total = $res->present + $res->absent + $res->late;
                $data[] = $res;
            }
        }

        // bringing parents phone numbers
        $parents_phone_nums=$this->db_readonly->select("student_id,mobile_no, sr.relation_type")
        ->from("parent p")
        ->join('student_relation sr','p.id = sr.relation_id')
        ->get()->result();
        

        $parents_phone_nums_array=[];
        foreach($parents_phone_nums as $key => $val){
            $parents_phone_nums_array[$val->student_id][]=$val;
        }

        foreach($data as $key => $val) {
            $val->parent_nums = $parents_phone_nums_array[$val->student_id];
        }

        return $data;
        // echo "<pre>"; print_r($data); die();
    }

    public function get_absentees_latecomers($attendance_master_id) {
        $sql = "SELECT s.status, sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, cs.class_name, cs.section_name 
                FROM attendance_v2_master m 
                JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
                JOIN student_admission sa ON sa.id=s.student_admission_id 
                JOIN student_year sy ON sa.id=sy.student_admission_id 
                JOIN class_section cs ON cs.id=sy.class_section_id 
                WHERE (s.status=2 or s.status=3) and sy.acad_year_id=$this->yearId and m.id=$attendance_master_id";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
        // echo "<pre>"; print_r($result); die();
    }

    public function get_day_attendance_absentees_latecomers($attendance_session_id,$selectedDate) {
        $sql = "select distinct at_m.id as attendance_master_id, sa.id as student_id, concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as student_name, concat(cs.class_name, ' ', cs.section_name) as class_Section, at_s.status, date_format(at_session.day,'%b %D %Y') as attendance_taken_date
                from attendance_master at_m
                join attendance_session at_session on at_session.attendance_master_id=at_m.id
                join attendance_student at_s on at_s.attendance_session_id=at_session.id
                JOIN student_admission sa ON sa.id=at_s.student_admission_id 
                JOIN student_year sy ON sa.id=sy.student_admission_id 
                JOIN class_section cs ON cs.id=sy.class_section_id 
                WHERE at_s.status=0 and sy.acad_year_id=$this->yearId and at_session.id=$attendance_session_id and date(at_session.attendance_time)=cast('$selectedDate' AS date) ";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
    }

    public function get_attendence_students_names($attendance_master_id,$status) {
        $sql = "SELECT s.status, sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, cs.class_name, cs.section_name 
                FROM attendance_v2_master m 
                JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
                JOIN student_admission sa ON sa.id=s.student_admission_id 
                JOIN student_year sy ON sa.id=sy.student_admission_id 
                JOIN class_section cs ON cs.id=sy.class_section_id 
                WHERE s.status=$status and sy.acad_year_id=$this->yearId and m.id=$attendance_master_id";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
    }

    public function update_notified_at($attendance_master_id) {
        return $this->db->where('id', $attendance_master_id)->update('attendance_v2_master', ['notified_at' => date('Y-m-d H:i:s')]);
    }

    public function getAttendanceStatus($data){
        return $this->db_readonly->select("sum(case when att.status = 0 then 1 else 0 end) as not_taken,sum(case when att.status = 1 then 1 else 0 end) as present,sum(case when att.status = 2 then 1 else 0 end) as absent,sum(case when att.status = 3 then 1 else 0 end) as late,sum(case when att.status = 4 then 1 else 0 end) as absent_by_permission")
        ->from("attendance_v2_student att")
        ->join("attendance_v2_master am","am.id=att.attendance_v2_master_id")
        ->where("class_section_id",$data["classSectionId"])
        ->where("type_id",$data["subjectId"])
        ->where("am.period_no",$data["period_no"])
        ->where("am.date",$data["date"])
        ->where("period_no is not null",NULL,FALSE)
        ->group_by("class_section_id")
        ->get()->result();

    }

    public function checkPeriodExists($data){
        return $this->db_readonly->select("*")
        ->where("period_no",$data["period_no"])
        ->where("class_section_id",$data["class_section_id"])
        ->where("date",$data["date"])
        ->where("ttp_id",$data["timetable_id"])
        ->get("attendance_v2_master")
        ->result();
    }

    public function remove_attendance($data){
        $attendance=$this->db->delete("attendance_v2_master",["id"=>$data["attendanceMasterId"]]);
        $student_records=$this->db->delete("attendance_v2_student",["attendance_v2_master_id"=>$data["attendanceMasterId"]]);
        return $attendance==$student_records && 1 || 0;
    }

    public function checkSectionEnability($class_section_id) {
        $staff_id= $this->authorization->getAvatarStakeHolderId();
        $a= $this->db_readonly->where('staff_type', 'section')->where('staff_id', $staff_id)->where('class_section_id', $class_section_id)->get('lp_subjects_section_staff');
        if($a->num_rows() > 0) {
            return 1;
        }
        return 0;
    }

    public function checkSubjectEnability($section_id, $subject_master_id) {
        $staff_id= $this->authorization->getAvatarStakeHolderId();
        $a= $this->db_readonly
        ->from('lp_subjects_section_staff lsss')
        ->join('lp_subjects ls', "ls.id= lsss.lp_subjects_id")
        ->where('ls.subject_master_id', $subject_master_id)
        ->where('lsss.staff_id', $staff_id)
        ->where('class_section_id', $section_id)
        ->where('lsss.staff_type', 'section')
        ->get();
        if($a->num_rows() > 0) {
            return 1;
        }
        return 0;
    }

    public function callGetSubjects(){
        $class_master_id= $this->db_readonly->select('cm.id')
            ->from('class_master cm')
            ->join('class c', "c.class_master_id= cm.id")
            ->join('class_section cs', "cs.class_id=c.id")
            ->where('cs.id', $_POST['class_master_id'])
            ->get()->row()->id;
        $semester_main_screen_id = $_POST['semester_main_screen_id'];
         
        $this->db_readonly->distinct()->select('s.*, s.id as subject_id,count(l.id) as TotalLessons, sem.sem_name')
         ->from('lp_subjects s')
         ->join('lp_lessons l','l.lp_subject_id=s.id','left')
         ->join('semester sem', 'sem.id=s.semester_id', 'left')
         ->where('s.class_master_id',$class_master_id)
         ->where('s.acad_year_id',$this->yearId);
     
        if ($this->settings->getSetting('is_semester_scheme')) {
          $this->db_readonly->where('s.semester_id',$semester_main_screen_id);
        }
    
        $result = $this->db_readonly->group_by('s.id')
         ->get()->result();
    
         return $result;
    }

    public function get_class_semesters($class_section_id) {
        $class_master_id= $this->db_readonly->select('cm.id')
            ->from('class_master cm')
            ->join('class c', "c.class_master_id= cm.id")
            ->join('class_section cs', "cs.class_id=c.id")
            ->where('cs.id', $class_section_id)
            ->get()->row()->id;
        return $this->db_readonly->select('s.id, s.sem_name')->from('semester s')->join('class_master_semester cms', 'cms.sem_id=s.id')->where('cms.class_master_id', $class_master_id)->get()->result();
    }

    public function get_late_absentees_students($data) {
        $requestDate = date('Y-m-d', strtotime($data['requestDate']));
        $class_ids = implode(',',$data['classIds']);
        $action_type=$data["actionType"];
        
        $attendance_status="(2,3,4)";
        if($action_type == "absentees"){
            $attendance_status = "(2,4)";
        } else if ($action_type == "late") {
            $attendance_status = "(3)";
        }

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		}

        $sql = "SELECT m.date, SUM(case when s.status=1 then 1 else 0 end) as present, SUM(case when s.status=2 then 1 else 0 end) as absent, SUM(case when s.status=3 then 1 else 0 end) as late, s.student_admission_id as student_id,$std_name, cs.class_name, cs.section_name, sa.preferred_contact_no,group_concat(sm.subject_name) as absent_and_late_subjects,
        group_concat(case when s.status=1 then substring(sm.subject_name,1,4) end) as present_subjects,
        group_concat(case when s.status=2 or s.status=4 then substring(sm.subject_name,1,4) end) as absent_subjects,
        group_concat(case when s.status=3 then substring(sm.subject_name,1,4) end) as late_subjects
                FROM attendance_v2_master m 
                JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
                JOIN student_admission sa ON sa.id=s.student_admission_id 
                JOIN student_year sy ON sa.id=sy.student_admission_id 
                JOIN class_section cs ON cs.id=sy.class_section_id
                JOIN subject_master sm on sm.id=m.type_id
                WHERE m.date='$requestDate' 
                AND sy.acad_year_id=$this->yearId 
                AND cs.class_id in ($class_ids) AND s.status in $attendance_status 
                GROUP BY s.student_admission_id";
        $result = $this->db_readonly->query($sql)->result();

        $data = [];

        foreach ($result as $key => $res) {
            $res->date=date('d.m.Y',strtotime($res->date));
            if($res->absent || $res->late) {
                $res->total = $res->present + $res->absent + $res->late;
                $data[] = $res;
            }
        }

        // bringing parents phone numbers
        $parents_phone_nums=$this->db_readonly->select("student_id,mobile_no, sr.relation_type")
        ->from("parent p")
        ->join('student_relation sr','p.id = sr.relation_id')
        ->get()->result();
        

        $parents_phone_nums_array=[];
        foreach($parents_phone_nums as $key => $val){
            $parents_phone_nums_array[$val->student_id][]=$val;
        }

        foreach($data as $key => $val) {
            $val->parent_nums = $parents_phone_nums_array[$val->student_id];
        }

        return $data;
        // echo "<pre>"; print_r($data); die();
    }
}
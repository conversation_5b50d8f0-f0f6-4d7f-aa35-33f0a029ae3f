<?php 
    $bloodGroup = array(
        'A+'=> 'A +ve',
        'B+'=> 'B +ve',
        'O+'=> 'O +ve',
        'AB+'=> 'AB +ve',
        'A-'=> 'A -ve',
        'B-'=> 'B -ve',
        'O-'=> 'O -ve',
        'AB-'=> 'AB -ve',
        'Unknown'=>'Unknown'
    );
?>

<div class="row">

    <?php if(!in_array('allergy', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="allergy">Food/Medicine Allergy &nbsp;
            <?php if($health_required_fields['allergy']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea <?php echo $health_required_fields['allergy']['required'] ?> placeholder="Food/Medicine Allergy"
            id="allergy" name="allergy" type="text" rows="4"
            class="form-control input-md"><?php if(!empty($admission_medical->allergy)) echo $admission_medical->allergy ?></textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('family_history', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="f_first_name">Family Medical History &nbsp;
            <?php if($health_required_fields['family_history']['required']=='required') echo'<font color="red">*</font>' ?>
        </label>
        <textarea <?php echo $health_required_fields['family_history']['required'] ?>
            placeholder="Family Medical History" id="family_history" name="family_history" class="form-control input-md"
            rows="4"><?php if(!empty($admission_medical->family_history)) echo $admission_medical->family_history ?></textarea>
    </div>
    <?php endif ?>
    <?php if(!in_array('anaemia', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4" style="">
        <label class="form-label" for="anaemia">Anaemia &nbsp;
            <?php if($health_required_fields['anaemia']['required']=='required') echo'<font color="red">*</font>' ?>
        </label>
        <select id="anaemia" <?php echo $health_required_fields['anaemia']['required'] ?> name="anaemia"
            class="form-control input-md ">
            <?php 
                    $AbsentSelected = '';
                    $MildSelected = '';
                    $ModerateSelected = '';
                    $SevereSelected = '';
                    if(!empty($admission_medical->anaemia)){
                        if($admission_medical->anaemia == 'Absent'){
                            $AbsentSelected = 'selected';
                        }
                        if($admission_medical->anaemia == 'Mild'){
                            $MildSelected = 'selected';
                        }
                        if($admission_medical->anaemia == 'Moderate'){
                            $ModerateSelected = 'selected';
                        }
                        if($admission_medical->anaemia == 'Severe'){
                            $SevereSelected = 'selected';
                        }
                    } ?>
            <option value="">Select</option>
            <option <?php echo $AbsentSelected ?> value="Absent">Absent</option>
            <option <?php echo $MildSelected ?> value="Mild">Mild</option>
            <option <?php echo $ModerateSelected ?> value="Moderate">Moderate</option>
            <option <?php echo $SevereSelected ?> value="Severe">Severe</option>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('head_injury', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4" style="">
        <label class="form-label" for="head_injury">Child Has Head Injury &nbsp;
            <?php if($health_required_fields['head_injury']['required']=='required') echo'<font color="red">*</font>' ?>
        </label>
        <select id="head_injury" <?php echo $health_required_fields['head_injury']['required'] ?> name="head_injury"
            type="text" class="form-control input-md " data-parsley-group="block1">
            <?php 
                        $noSelected = '';
                        $yesSelected = '';
                        if(!empty($admission_medical->head_injury)){
                            if($admission_medical->head_injury == 'No'){
                                $noSelected = 'selected';
                            }
                            if($admission_medical->head_injury == 'Yes'){
                                $yesSelected = 'selected';
                            }
                        } 
                    ?>
            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('ear_impartement', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4" style="">
        <label class="form-label" for="ear_impartement">Child Has Ear Impairment? &nbsp;
            <?php if($health_required_fields['ear_impartement']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="ear_impartement" <?php echo $health_required_fields['ear_impartement']['required'] ?>
            name="ear_impartement" type="text" class="form-control input-md " data-parsley-group="block1">
            <?php 
                        $noSelected = '';
                        $yesSelected = '';
                        if(!empty($admission_medical->ear_impartement)){
                            if($admission_medical->ear_impartement == 'No'){
                                $noSelected = 'selected';
                            }
                            if($admission_medical->ear_impartement == 'Yes'){
                                $yesSelected = 'selected';
                            }
                        } 
                    ?>
            <option value=""><?php echo "Select"?></option>
            <option <?php echo $noSelected ?> value="No"><?php echo "No"?></option>
            <option <?php echo $yesSelected ?> value="Yes"><?php echo "Yes" ?></option>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('difficulty_in_breathing', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4" style="">
        <label class="form-label" for="difficulty_in_breathing"> Child Has Difficulty In Breathing? &nbsp;
            <?php if($health_required_fields['difficulty_in_breathing']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="difficulty_in_breathing"
            <?php echo $health_required_fields['difficulty_in_breathing']['required'] ?> name="difficulty_in_breathing"
            type="text" class="form-control input-md " data-parsley-group="block1">
            <?php 
                        $noSelected = '';
                        $yesSelected = '';
                        if(!empty($admission_medical->difficulty_in_breathing)){
                            if($admission_medical->difficulty_in_breathing == 'No'){
                                $noSelected = 'selected';
                            }
                            if($admission_medical->difficulty_in_breathing == 'Yes'){
                                $yesSelected = 'selected';
                            }
                        } 
                    ?>

            <option value=""><?php echo "Select"?></option>
            <option <?php echo $noSelected ?> value="No"><?php echo "No"?></option>
            <option <?php echo $yesSelected ?> value="Yes"><?php echo "Yes" ?></option>

        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('child_has_join_pain', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="child_has_join_pain"> Child Has Recurrent Pain In Joints? &nbsp;
            <?php if($health_required_fields['child_has_join_pain']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="child_has_join_pain" <?php echo $health_required_fields['child_has_join_pain']['required'] ?>
            name="child_has_join_pain" type="text" class="form-control input-md " data-parsley-group="block1">
            <?php 
                        $noSelected = '';
                        $yesSelected = '';
                        if(!empty($admission_medical->child_has_join_pain)){
                            if($admission_medical->child_has_join_pain == 'No'){
                                $noSelected = 'selected';
                            }
                            if($admission_medical->child_has_join_pain == 'Yes'){
                                $yesSelected = 'selected';
                            }
                        } 
                    ?>
            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>

        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('fit_to_participate', $health_disabled_fields)) :  ?>

    <div class="col-md-6 mb-4">
        <label class="form-label" for="fitblock">Fit to participate in age specific physical activity? &nbsp;
            <?php if($health_required_fields['fit_to_participate']['required']=='required') echo'<font color="red">*</font>' ?></label>

        <select id="" name="fit_to_participate" class="form-control" required <?= $health_required_fields['fit_to_participate']['required'] ?>>
            <option value="">Select an option</option>
            <option value="Fit" <?= (!empty($admission_medical->fit_to_participate) && $admission_medical->fit_to_participate == 'Fit') ? 'selected' : '' ?>>Fit</option>
            <option value="Fit with precautions" <?= (!empty($admission_medical->fit_to_participate) && $admission_medical->fit_to_participate == 'Fit with precautions') ? 'selected' : '' ?>>Fit with precautions</option>
            <option value="Not fit" <?= (!empty($final_preview->fit_to_participate) && $admission_medical->fit_to_participate == 'Not fit') ? 'selected' : '' ?>>Not Fit</option>
        </select>
    </div>
    <?php endif ?>
    <?php if(!in_array('foodytpe', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label d-block" for="foody_type">
            Child Food Habits &nbsp;
            <?php if($health_required_fields['foodytpe']['required'] == 'required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php 
            $food_type = $admission_medical->foodytpe ?? '';
        ?>

        <select id=""
          name="foodytpe"
          class="form-control"
          required <?= $health_required_fields['foodytpe']['required'] ?>>
            <option value="">Select food type</option>
            <option value="Vegetarian" <?= ($food_type === 'Vegetarian') ? 'selected' : '' ?>>Vegetarian</option>
            <option value="Eggitarian" <?= ($food_type === 'Eggitarian') ? 'selected' : '' ?>>Eggitarian</option>
            <option value="Non Vegetarian" <?= ($food_type === 'Non Vegetarian') ? 'selected' : '' ?>>Non Vegetarian</option>
        </select>
         <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('child_past_hsitory_sea', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="child_past_hsitory_sea"> Child Has Past History Of Sea/Air/Motion
            sickness? &nbsp;
            <?php if($health_required_fields['child_past_hsitory_sea']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="child_past_hsitory_sea" <?php echo $health_required_fields['child_past_hsitory_sea']['required'] ?>
            name="child_past_hsitory_sea" type="text" class="form-control input-md " data-parsley-group="block1">
            <?php 
                        $noSelected = '';
                        $yesSelected = '';
                        if(!empty($admission_medical->child_past_hsitory_sea)){
                            if($admission_medical->child_past_hsitory_sea == 'No'){
                                $noSelected = 'selected';
                            }
                            if($admission_medical->child_past_hsitory_sea == 'Yes'){
                                $yesSelected = 'selected';
                            }
                        } 
                    ?>

            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>

        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('child_nervous_breakdown', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="child_nervous_breakdown">Child Suffers From Nervous
            Breakdown/Depression? &nbsp;
            <?php if($health_required_fields['child_nervous_breakdown']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="child_nervous_breakdown"
            <?php echo $health_required_fields['child_nervous_breakdown']['required'] ?> name="child_nervous_breakdown"
            type="text" class="form-control input-md " data-parsley-group="block1">
            <?php 
                        $noSelected = '';
                        $yesSelected = '';
                        if(!empty($admission_medical->child_nervous_breakdown)){
                            if($admission_medical->child_nervous_breakdown == 'No'){
                                $noSelected = 'selected';
                            }
                            if($admission_medical->child_nervous_breakdown == 'Yes'){
                                $yesSelected = 'selected';
                            }
                        } 
                    ?>
            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>

        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('child_color_blindness', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="child_color_blindness">Child Suffers From Color Blindness? &nbsp;
            <?php if($health_required_fields['child_color_blindness']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="child_color_blindness" <?php echo $health_required_fields['child_color_blindness']['required'] ?>
            name="child_color_blindness" type="text" class="form-control input-md " data-parsley-group="block1">
            <?php 
                        $noSelected = '';
                        $yesSelected = '';
                        if(!empty($admission_medical->child_color_blindness)){
                            if($admission_medical->child_color_blindness == 'No'){
                                $noSelected = 'selected';
                            }
                            if($admission_medical->child_color_blindness == 'Yes'){
                                $yesSelected = 'selected';
                            }
                        } 
                    ?>

            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>

        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('child_diabities', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="child_diabities">Child Suffers From Juvenile Diabetes? &nbsp;
            <?php if($health_required_fields['child_diabities']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="child_diabities" <?php echo $health_required_fields['child_diabities']['required'] ?>
            name="child_diabities" type="text" class="form-control input-md " data-parsley-group="block1">
            <?php 
                        $noSelected = '';
                        $yesSelected = '';
                        if(!empty($admission_medical->child_diabities)){
                            if($admission_medical->child_diabities == 'No'){
                                $noSelected = 'selected';
                            }
                            if($admission_medical->child_diabities == 'Yes'){
                                $yesSelected = 'selected';
                            }
                        } 
                    ?>
            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('congenital_heart_disease', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="congenital_heart_disease">Child Suffers From Congenital Heart
            Disease? &nbsp;
            <?php if($health_required_fields['congenital_heart_disease']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="congenital_heart_disease"
            <?php echo $health_required_fields['congenital_heart_disease']['required'] ?>
            name="congenital_heart_disease" type="text" class="form-control input-md " data-parsley-group="block1">
            <?php 
                        $noSelected = '';
                        $yesSelected = '';
                        if(!empty($admission_medical->congenital_heart_disease)){
                            if($admission_medical->congenital_heart_disease == 'No'){
                                $noSelected = 'selected';
                            }
                            if($admission_medical->congenital_heart_disease == 'Yes'){
                                $yesSelected = 'selected';
                            }
                        } 
                    ?>
            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>

        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('blood_group', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" for="blood_group">Child Blood Group &nbsp;
            <?php if($health_required_fields['blood_group']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="blood_group" <?php echo $health_required_fields['blood_group']['required'] ?> name="blood_group"
            type="text" class="form-control input-md " data-parsley-group="block1">
            <option value=""><?php echo "Select Blood Group" ?></option>
            <?php 
                                foreach ($bloodGroup as $key => $val) { 
                                    $bldSelected = '';
                                    if(!empty($admission_medical->blood_group)){
                                        if($admission_medical->blood_group == $key){
                                            $bldSelected = 'selected';
                                        }
                                    }
                                ?>
            <option <?php echo $bldSelected ?> value="<?php echo $key ?>"><?php echo $val ?></option>
            <?php }
                            ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>

    <?php if(!in_array('father_bld_group', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="father_bld_group"> Father's Blood Group &nbsp;
            <?php if($health_required_fields['father_bld_group']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="father_bld_group" <?php echo $health_required_fields['father_bld_group']['required'] ?>
            name="father_bld_group" type="text" class="form-control input-md " data-parsley-group="block1">
            <option value=""><?php echo "Select Blood Group" ?></option>
            <?php 
                            foreach ($bloodGroup as $key => $val) { 
                                $bldSelectedf = '';
                                if(!empty($admission_medical->father_bld_group)){
                                    if($admission_medical->father_bld_group == $key){
                                        $bldSelectedf = 'selected';
                                    }
                                }
                            ?>
            <option <?php echo $bldSelectedf ?> value="<?php echo $key ?>"><?php echo $val ?></option>
            <?php }
                        ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('mother_bld_group', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="mother_bld_group"> Mother's Blood Group &nbsp;
            <?php if($health_required_fields['mother_bld_group']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select id="mother_bld_group" <?php echo $health_required_fields['mother_bld_group']['required'] ?>
            name="mother_bld_group" type="text" class="form-control input-md" data-parsley-group="block1">
            <option value="">Select Blood group</option>
            <?php 
                        foreach ($bloodGroup as $key => $val) { 
                            $bldSelectedm = '';
                            if(!empty($admission_medical->mother_bld_group)){
                                if($admission_medical->mother_bld_group == $key){
                                    $bldSelectedm = 'selected';
                                }
                            }
                        ?>
            <option <?php echo $bldSelectedm ?> value="<?php echo $key ?>"><?php echo $val ?></option>
            <?php }
                    ?>
        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('height', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="height">Student Height &nbsp;
            <?php if($health_required_fields['height']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Height (in Centimeter)" <?php echo $health_required_fields['height']['required'] ?>
            <?php if(!empty($admission_medical->height)) echo 'value="'.$admission_medical->height.'"' ?> value=""
            id="height" name="height" type="text" class="form-control input-md">
    </div>
    <?php endif ?>

    <?php if(!in_array('weight', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" for="weight">Student Weight &nbsp;
            <?php if($health_required_fields['weight']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Weight (in Kilogram)" <?php echo $health_required_fields['weight']['required'] ?>
            <?php if(!empty($admission_medical->weight)) echo 'value="'.$admission_medical->weight.'"' ?> id="weight"
            value="" name="weight" type="text" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('hair', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="hair">Hair &nbsp;
            <?php if($health_required_fields['hair']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Hair" <?php echo $health_required_fields['hair']['required'] ?> id="hair"
            <?php if(!empty($admission_medical->hair)) echo 'value="'.$admission_medical->hair.'"' ?> name="hair"
            value="" type="text" class="form-control input-md">
    </div>
    <?php endif ?>

    <?php if(!in_array('special_attention_from_school', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="throat">Need Any Special Attention From
            School
            &nbsp;<?php if($health_required_fields['special_attention_from_school']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Need Special Attention"
            <?php echo $health_required_fields['special_attention_from_school']['required'] ?>
            <?php if(!empty($admission_medical->special_attention_from_school)) echo 'value="'.$admission_medical->special_attention_from_school.'"' ?>
            id="special_attention_from_school" value="" name="special_attention_from_school" type="text"
            class="form-control input-md">
    </div>
    <?php endif ?>

    <?php if(!in_array('child_sleeping_time', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="throat">Child Sleep Pattern & Timings During Day and
            Night
            &nbsp;<?php if($health_required_fields['child_sleeping_time']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea placeholder="Sleep Pattern" <?php echo $health_required_fields['child_sleeping_time']['required'] ?>
            id="child_sleeping_time" value="" name="child_sleeping_time" type="text"
            class="form-control input-md"><?php if(!empty($admission_medical->child_sleeping_time)) echo $admission_medical->child_sleeping_time ?> </textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('does_express_touse_toilet', $health_disabled_fields)) {  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label d-block" for="does_express_touse_toilet">
            Indicates toilet needs? &nbsp;
            <?php if($health_required_fields['does_express_touse_toilet']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>

        <?php $toilet_express = $admission_medical->does_express_touse_toilet ?? ''; ?>

        <select  name="does_express_touse_toilet" class="form-control" required <?= $health_required_fields['does_express_touse_toilet']['required'] ?>
          onchange="show_toilet_express(this.value)">
            <option value="" >Select an option</option>
            <option value="Yes" <?= ($toilet_express === 'Yes') ? 'selected' : '' ?>>Yes</option>
            <option value="No"  <?= ($toilet_express === 'No') ? 'selected' : '' ?>>No</option>
        </select>

    </div>
    <div class="col-md-6 mb-4 toilet_express_text" style="<?= ($toilet_express == 'Yes') ? 'display:block;' : 'display:none;' ?>">
        <label class="form-label d-block" for="does_express_touse_toilet">
            Which word he/she expresses <font style="color:red">*</font>
            <textarea class="form-control" name="toilet_express_text" id="toilet_express_text" rows="3"
            placeholder="Which word he/she expresses?"><?php if(!empty($admission_medical->what_does_word_express_for_toilet)) echo $admission_medical->what_does_word_express_for_toilet ?></textarea>
        </label>
    </div>
    <?php } ?>

    <?php if(!in_array('skin', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="skin">Skin &nbsp;
            <?php if($health_required_fields['skin']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Skin" <?php echo $health_required_fields['skin']['required'] ?> id="skin"
            <?php if(!empty($admission_medical->skin)) echo 'value="'.$admission_medical->skin.'"' ?> name="skin"
            type="text" value="" class="form-control input-md">
    </div>
    <?php endif ?>

    <?php if(!in_array('ear', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" for="ear">Ear &nbsp;
            <?php if($health_required_fields['ear']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Ear" <?php echo $health_required_fields['ear']['required'] ?> id="ear"
            <?php if(!empty($admission_medical->ear)) echo 'value="'.$admission_medical->ear.'"' ?> name="ear"
            type="text" value="" class="form-control input-md">
    </div>
    <?php endif ?>
    <?php if(!in_array('nose', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" for="nose">Nose &nbsp;
            <?php if($health_required_fields['nose']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Nose" <?php echo $health_required_fields['nose']['required'] ?> id="nose"
            <?php if(!empty($admission_medical->nose)) echo 'value="'.$admission_medical->nose.'"' ?> name="nose"
            type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('throat', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" for="throat">Throat &nbsp;
            <?php if($health_required_fields['throat']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Throat" <?php echo $health_required_fields['throat']['required'] ?> id="throat"
            <?php if(!empty($admission_medical->throat)) echo 'value="'.$admission_medical->throat.'"' ?> name="throat"
            type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('neck', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" for="neck">Neck &nbsp;
            <?php if($health_required_fields['neck']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Neck" <?php echo $health_required_fields['neck']['required'] ?> id="neck"
            <?php if(!empty($admission_medical->neck)) echo 'value="'.$admission_medical->neck.'"' ?> name="neck"
            type="text" value="" class="form-control input-md">


    </div>
    <?php endif ?>


    <?php if(!in_array('respiratory', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="respiratory">Respiration &nbsp;
            <?php if($health_required_fields['respiratory']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Respiration" <?php echo $health_required_fields['respiratory']['required'] ?>
            id="respiratory" name="respiratory"
            <?php if(!empty($admission_medical->respiratory)) echo 'value="'.$admission_medical->respiratory.'"' ?>
            type="text" value="" class="form-control input-md">
    </div>
    <?php endif ?>

    <?php if(!in_array('renal_issues', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="respiratory">Renal Issues &nbsp;
            <?php if($health_required_fields['renal_issues']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea placeholder="Renal Issues" <?php echo $health_required_fields['renal_issues']['required'] ?>
            <?php if(!empty($admission_medical->renal_issues)) echo 'value="'.$admission_medical->renal_issues.'"' ?>
            id="renal_issues" rows="4" name="renal_issues" type="text"
            class="form-control input-md"><?php if(!empty($admission_medical->renal_issues)) echo $admission_medical->renal_issues ?></textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('skin_issues', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="skin_issues">Skin Issues &nbsp;
            <?php if($health_required_fields['skin_issues']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea placeholder="Skin Issues" <?php echo $health_required_fields['skin_issues']['required'] ?>
            id="skin_issues" rows="4" name="skin_issues" type="text"
            class="form-control input-md"><?php if(!empty($admission_medical->skin_issues)) echo $admission_medical->skin_issues ?></textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('physical_disability', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="phyDisability">Does the child have any physical disability? &nbsp;
            <?php if($health_required_fields['physical_disability']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="position-relative">
        <select <?php echo $health_required_fields['physical_disability']['required'] ?> id="phyDisability"
            name="phyDisability" class="form-control input-md " onchange="showPhyDisabilityBox()">
            <?php 
                    $noSelected = '';
                    $yesSelected = '';
                    if(!empty($admission_medical->physical_disability)){
                        if($admission_medical->physical_disability == 'No'){
                            $noSelected = 'selected';
                        }
                        if($admission_medical->physical_disability == 'Yes'){
                            $yesSelected = 'selected';
                        }
                    } 
                ?>

            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>
        </select>
        <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
        </div>
    </div>
    <div class="col-md-6 mb-4" id="phyDisabilityResaon" style="display:block; display:none;">
        <label class="form-label" for="phyDisabilityResaon">Reason for physical disability &nbsp;
            <?php if($health_required_fields['physical_disability']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea id="physical_disability_reason" class="form-control" placeholder="Reason..."
            name="phyDisabilityResaon"
            rows="3"><?php if(!empty($admission_medical->physical_disability_reason)) echo $admission_medical->physical_disability_reason ?></textarea>
    </div>
    <?php endif ?>
    <?php if(!in_array('learning_disability', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="leaDisability">Does the child have any learning disability? &nbsp;
            <?php if($health_required_fields['learning_disability']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <select <?php echo $health_required_fields['learning_disability']['required'] ?> id="leaDisability"
            name="leaDisability" class="form-control input-md " onchange="showLeaDisabilityBox()">
            <?php 
                    $noSelected = '';
                    $yesSelected = '';
                    if(!empty($admission_medical->learning_disability)){
                        if($admission_medical->learning_disability == 'No'){
                            $noSelected = 'selected';
                        }
                        if($admission_medical->learning_disability == 'Yes'){
                            $yesSelected = 'selected';
                        }
                    } 
                ?>

            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>

        </select>
        <div style="position: absolute; right: 50px; top: 65%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
    </div>
    <div class="col-md-6 mb-4" id="leaDisabilityReason" style="display:block; display:none;">
        <label class="form-label" for="leaDisabilityReason">Reason for learning disability &nbsp;
            <?php if($health_required_fields['learning_disability_reason']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea id="learning_disability_reason" placeholder="Reason..." class="form-control"
            name="leaDisabilityReason"
            rows="3"><?php if(!empty($admission_medical->learning_disability_reason)) echo $admission_medical->learning_disability_reason ?></textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('child_past_hsitory_fracture', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="child_past_hsitory_fracture"> Child has past history of fractures? &nbsp;
            <?php if($health_required_fields['child_past_hsitory_fracture']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="position-relative">
        <select id="child_past_hsitory_fracture"
            <?php echo $health_required_fields['child_past_hsitory_fracture']['required'] ?>
            name="child_past_hsitory_fracture" type="text" class="form-control input-md " data-parsley-group="block1"
            onchange="showpastfracturehistory()">
            <?php 
                    $noSelected = '';
                    $yesSelected = '';
                    if(!empty($admission_medical->child_past_hsitory_fracture)){
                        if($admission_medical->child_past_hsitory_fracture == 'No'){
                            $noSelected = 'selected';
                        }
                        if($admission_medical->child_past_hsitory_fracture == 'Yes'){
                            $yesSelected = 'selected';
                        }
                    } 
                ?>

            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>

        </select>
        <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
        </div>
    </div>
    <div class="col-md-6 mb-4" id="fracture_types_div" style="display:block; display:none;">
        <label class="form-label" for="fracture_types"> Select type of fracture &nbsp;
            <?php if($health_required_fields['fracture']['required']=='required') echo'<font color="red">*</font>' ?></label>
            <?php
                $fractureValue = !empty($admission_medical->fracture) ? $admission_medical->fracture : '';
            ?>
        <select id="fracture_select"
          name="fracture"
          class="form-control"
          <?= $health_required_fields['fracture']['required'] ?> >
            <option value="" disabled hidden>Select fracture location</option>
            <option value="Spine" <?= $fractureValue === 'Spine' ? 'selected' : '' ?>>Spine</option>
            <option value="Upper Limb" <?= $fractureValue === 'Upper Limb' ? 'selected' : '' ?>>Upper Limb</option>
            <option value="Lower Limb" <?= $fractureValue === 'Lower Limb' ? 'selected' : '' ?>>Lower Limb</option>
        </select>
    </div>
    <?php endif ?>

    <?php if(!in_array('more_than_month_disease', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="more_than_month_disease">Child has taken medicine > 1 month ? If yes, specify. &nbsp;
            <?php if($health_required_fields['more_than_month_disease']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="position-relative">
        <select id="more_than_month_disease"
            <?php echo $health_required_fields['more_than_month_disease']['required'] ?> name="more_than_month_disease"
            type="text" class="form-control input-md " data-parsley-group="block1" onchange="morethanmonthspecify()">
            <?php 
                    $noSelected = '';
                    $yesSelected = '';
                    if(!empty($admission_medical->more_than_month_disease)){
                        if($admission_medical->more_than_month_disease == 'No'){
                            $noSelected = 'selected';
                        }
                        if($admission_medical->more_than_month_disease == 'Yes'){
                            $yesSelected = 'selected';
                        }
                    } 
                ?>

            <option value="">Select</option>
            <option <?php echo $noSelected ?> value="No">No</option>
            <option <?php echo $yesSelected ?> value="Yes">Yes</option>

        </select>
        <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
        </div>
    </div>
    <div class="col-md-6 mb-4" id="medicine_name_for_month_div" style="display:block; display:none;">
        <label class="form-label" for="medicine_name_for_month">Mention medicine names &nbsp;
            <?php if($health_required_fields['medicine_name_for_month']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea id="medicine_names_reason"
            <?php echo $health_required_fields['medicine_name_for_month']['required'] ?>
            placeholder="Medicines names...." id="medicine_name_for_month" class="form-control"
            name="medicine_name_for_month"
            rows="3"><?php if(!empty($admission_medical->medicine_name_for_month)) echo $admission_medical->medicine_name_for_month ?></textarea>
    </div>
    <?php endif ?>
    <?php if(!in_array('any_other_medical_treatment', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4" id="any_other_medical_treatment">
        <label class="form-label" for="any_other_medical_treatment">Other treatments/ailments? &nbsp;
            <?php if($health_required_fields['any_other_medical_treatment']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea placeholder="Medicines Treatment"
            <?php echo $health_required_fields['any_other_medical_treatment']['required'] ?> class="form-control"
            name="any_other_medical_treatment"
            rows="3"><?php if(!empty($admission_medical->any_other_medical_treatment)) echo $admission_medical->any_other_medical_treatment ?></textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('mental_issues', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="mental_issues">Mental Health
            Issues
            &nbsp;<?php if($health_required_fields['mental_issues']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea placeholder="Mental Health Issues" <?php echo $health_required_fields['mental_issues']['required'] ?>
            value="" id="mental_issues" rows="4" name="mental_issues" type="text"
            class="form-control input-md"><?php if(!empty($admission_medical->mental_issues)) echo $admission_medical->mental_issues ?></textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('daily_medication', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label d-block" for="daily_medication">
             Daily medication? &nbsp;
            <?php if($health_required_fields['daily_medication']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>

            <?php
                $daily_medication_text = !empty($admission_medical->daily_medication) ? $admission_medical->daily_medication : '';
                $showMedication = !empty($daily_medication_text);
            ?>

        <select id="daily_medication_select" name="m_btn" class="form-control"
                required <?= $health_required_fields['daily_medication']['required'] ?>
                onchange="show_medication_box(this.value)">
            <option value="">Select</option>
            <option value="1" <?= $showMedication ? 'selected' : '' ?>>Yes</option>
            <option value="0" <?= !$showMedication ? 'selected' : '' ?>>No</option>
        </select>


    </div>
    <div class="col-md-6 mb-4 mt-3 medication" style="<?= $showMedication ? 'display:block;' : 'display:none;' ?>">
        <textarea class="form-control" name="daily_medication" id="medication_text" rows="3"
            placeholder="Please specify the medication"><?= $daily_medication_text ?></textarea>
    </div>
    <?php endif ?>

    <?php if(!in_array('cardio_vascular', $health_disabled_fields)) :  ?>

    <div class="col-md-6 mb-4">
        <label class="form-label" for="throat">Cardio-Vascular &nbsp;
            <?php if($health_required_fields['cardio_vascular']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Cardio-Vascular" <?php echo $health_required_fields['cardio_vascular']['required'] ?>
            <?php if(!empty($admission_medical->cardio_vascular)) echo 'value="'.$admission_medical->cardio_vascular.'"' ?>
            id="cardio_vascular" value="" name="cardio_vascular" type="text" class="form-control input-md">

    </div>
    <?php endif ?>


    <?php if(!in_array('abdomen', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" for="abdomen">Abdomen &nbsp;
            <?php if($health_required_fields['abdomen']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Abdomen" <?php echo $health_required_fields['abdomen']['required'] ?> id="abdomen"
            <?php if(!empty($admission_medical->abdomen)) echo 'value="'.$admission_medical->abdomen.'"' ?>
            name="abdomen" type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('nervous_system', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" for="nervous_system">Nervous System &nbsp;
            <?php if($health_required_fields['nervous_system']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Nervous System" <?php echo $health_required_fields['nervous_system']['required'] ?>
            id="nervous" <?php if(!empty($admission_medical->nervous_system)) echo 'value="'.$admission_medical->nervous_system.'"' ?>
            name="nervous" type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>

    <?php if(!in_array('left_eye', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" for="left_eye">Left Eye &nbsp;
            <?php if($health_required_fields['left_eye']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Left Eye" <?php echo $health_required_fields['left_eye']['required'] ?> id="left_eye"
            <?php if(!empty($admission_medical->left_eye)) echo 'value="'.$admission_medical->left_eye.'"' ?>
            name="left_eye" type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('right_eye', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" for="right_eye">Right Eye &nbsp;
            <?php if($health_required_fields['right_eye']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Right Eye" <?php echo $health_required_fields['right_eye']['required'] ?> id="right_eye"
            <?php if(!empty($admission_medical->right_eye)) echo 'value="'.$admission_medical->right_eye.'"' ?>
            name="right_eye" type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('extra_oral', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label class="form-label" id="dental_examination" for="extra_oral">Dental Examination Extra Oral &nbsp;
            <?php if($health_required_fields['extra_oral']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea class="form-control input-md" <?php echo $health_required_fields['extra_oral']['required'] ?>
            placeholder="Extra Oral" id="extra_oral" name="extra_oral"
            rows="3"><?php if(!empty($admission_medical->extra_oral)) echo $admission_medical->extra_oral ?></textarea>

    </div>
    <?php endif ?>

    <?php if(!in_array('intra_oral', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">


        <label id="intra_oral" class="form-label" for="intra_oral">Dental examination - Intra oral &nbsp;
            <?php if($health_required_fields['intra_oral']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <textarea class="form-control input-md" <?php echo $health_required_fields['intra_oral']['required'] ?>
            placeholder="Intra Oral" id="intra_oral" name="intra_oral"
            rows="3"><?php if(!empty($admission_medical->intra_oral)) echo $admission_medical->intra_oral ?></textarea>


    </div>
    <?php endif ?>
    <?php if(!in_array('tooth_cavity', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label id="tooth_cavity" class="form-label" for="tooth_cavity">Tooth cavity &nbsp;
            <?php if($health_required_fields['tooth_cavity']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Tooth Cavity" <?php echo $health_required_fields['tooth_cavity']['required'] ?>
            id="tooth_cavity" name="tooth_cavity"
            <?php if(!empty($admission_medical->tooth_cavity)) echo 'value="'.$admission_medical->tooth_cavity.'"' ?>
            type="text" value="" class="form-control input-md">


    </div>
    <?php endif ?>
    <?php if(!in_array('plaque', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label id="plaque" class="form-label" for="plaque">Plaque &nbsp;
            <?php if($health_required_fields['plaque']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Plaque" <?php echo $health_required_fields['plaque']['required'] ?> id="plaque"
            <?php if(!empty($admission_medical->plaque)) echo 'value="'.$admission_medical->plaque.'"' ?> name="plaque"
            type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('gum_bleeding',$health_disabled_fields)) : ?>
    <div class="col-md-6 mb-4">

        <label id="gum_bleeding" class="form-label" for="gum_bleeding">Gum bleeding &nbsp;
            <?php if($health_required_fields['gum_bleeding']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Gum Bleeding" <?php echo $health_required_fields['gum_bleeding']['required'] ?>
            id="gum_bleeding" name="gum_bleeding"
            <?php if(!empty($admission_medical->gum_bleeding)) echo 'value="'.$admission_medical->gum_bleeding.'"' ?>
            type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>

    <?php if(!in_array('gum_inflamation', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label id="gum_inflamation" class="form-label" for="gum_inflamation">Gum inflamation &nbsp;
            <?php if($health_required_fields['gum_inflamation']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Gum Inflamation" <?php echo $health_required_fields['gum_inflamation']['required'] ?>
            id="gum_inflamation" name="gum_inflamation" type="text"
            <?php if(!empty($admission_medical->gum_inflamation)) echo 'value="'.$admission_medical->gum_inflamation.'"' ?>
            value="" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('stains', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label id="stains" class="form-label" for="stains">Stains &nbsp;
            <?php if($health_required_fields['stains']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Stains" <?php echo $health_required_fields['stains']['required'] ?> id="stains"
            name="stains" <?php if(!empty($admission_medical->stains)) echo 'value="'.$admission_medical->stains.'"' ?>
            type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>

    <?php if(!in_array('bad_breath', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label id="bad_breath" class="form-label" for="bad_breath">Bad breath &nbsp;
            <?php if($health_required_fields['bad_breath']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Bad Breath" <?php echo $health_required_fields['bad_breath']['required'] ?> id="bad_breath"
            <?php if(!empty($admission_medical->bad_breath)) echo 'value="'.$admission_medical->bad_breath.'"' ?>
            name="bad_breath" type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('soft_tissue', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label id="soft_tissue" class="form-label" for="soft_tissue">Soft tissue &nbsp;
            <?php if($health_required_fields['soft_tissue']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Soft Tissue" <?php echo $health_required_fields['soft_tissue']['required'] ?>
            id="soft_tissue"
            <?php if(!empty($admission_medical->soft_tissue)) echo 'value="'.$admission_medical->soft_tissue.'"' ?>
            name="soft_tissue" type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>

    <?php if(!in_array('vitaminb12', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label id="vitaminb12" class="form-label" for="vitaminb12">Vitamin B12 &nbsp;
            <?php if($health_required_fields['vitaminb12']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Vitamin B12" <?php echo $health_required_fields['vitaminb12']['required'] ?> id="vitaminb12"
            <?php if(!empty($admission_medical->vitaminb12)) echo 'value="'.$admission_medical->vitaminb12.'"' ?>
            name="vitaminb12" type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('vitamind', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">

        <label id="vitamiind" class="form-label" for="vitamind">Vitamin D &nbsp;
            <?php if($health_required_fields['vitamind']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Vitamin D" <?php echo $health_required_fields['vitamind']['required'] ?> id="vitamind"
            <?php if(!empty($admission_medical->vitamind)) echo 'value="'.$admission_medical->vitamind.'"' ?>
            name="vitamind" type="text" value="" class="form-control input-md">

    </div>
    <?php endif ?>

    <?php if(!in_array('iron', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label id="iron" class="form-label" for="iron">Iron &nbsp;
            <?php if($health_required_fields['iron']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Iron" <?php echo $health_required_fields['iron']['required'] ?> id="iron" name="iron"
            <?php if(!empty($admission_medical->iron)) echo 'value="'.$admission_medical->iron.'"' ?> type="text"
            value="" class="form-control input-md">

    </div>
    <?php endif ?>
    <?php if(!in_array('calcium', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label id="calcium" class="form-label" for="calcium">Calcium &nbsp;
            <?php if($health_required_fields['calcium']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Calcium" <?php echo $health_required_fields['calcium']['required'] ?> id="calcium"
            <?php if(!empty($admission_medical->calcium)) echo 'value="'.$admission_medical->calcium.'"' ?>
            name="calcium" value="" type="text" class="form-control input-md">
    </div>
    <?php endif ?>
    <?php if(!in_array('family_doctor_name', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="family_doctor_name">Family Doctor Name &nbsp;
            <?php if($health_required_fields['family_doctor_name']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Family Doctor Name" <?php echo $health_required_fields['family_doctor_name']['required'] ?>
            id="family_doctor_name"
            <?php if(!empty($admission_medical->family_doctor_name)) echo 'value="'.$admission_medical->family_doctor_name.'"' ?>
            name="family_doctor_name" type="text" class="form-control input-md">
    </div>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="family_doctor_contact_number">Family Doctor Contact Number &nbsp;
            <?php if($health_required_fields['family_doctor_contact_number']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Family Doctor Number"
            <?php echo $health_required_fields['family_doctor_contact_number']['required'] ?>
            id="family_doctor_contact_number"
            <?php if(!empty($admission_medical->family_doctor_contact_number)) echo 'value="'.$admission_medical->family_doctor_contact_number.'"' ?>
            name="family_doctor_contact_number" type="text" class="form-control input-md"
            data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]">
    </div>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="family_doctor_city">Family Doctor City
            Name
            &nbsp;<?php if($health_required_fields['family_doctor_city']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Family Doctor City Name"
            <?php echo $health_required_fields['family_doctor_city']['required'] ?> id="family_doctor_city"
            <?php if(!empty($admission_medical->family_doctor_city)) echo 'value="'.$admission_medical->family_doctor_city.'"' ?>
            name="family_doctor_city" type="text" class="form-control input-md">
    </div>
    <?php endif ?>
    <?php if(!in_array('number_of_sons', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="number_of_sons">Total Number Of
            Sons
            &nbsp;<?php if($health_required_fields['number_of_sons']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Total Number Of Sons" <?php echo $health_required_fields['number_of_sons']['required'] ?>
            id="number_of_sons"
            <?php if(!empty($admission_medical->number_of_sons)) echo 'value="'.$admission_medical->number_of_sons.'"' ?>
            name="number_of_sons" type="number" class="form-control input-md">
    </div>
    <?php endif ?>
    <?php if(!in_array('number_of_daughters', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label" for="number_of_daughters">Total Number Of
            Daughters
            &nbsp;<?php if($health_required_fields['number_of_daughters']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <input placeholder="Total Number Of Daughters"
            <?php echo $health_required_fields['number_of_daughters']['required'] ?> id="number_of_daughters"
            <?php if(!empty($admission_medical->number_of_daughters)) echo 'value="'.$admission_medical->number_of_daughters.'"' ?>
            name="number_of_daughters" type="number" class="form-control input-md">
    </div>
    <?php endif ?>
    <?php if(!in_array('does_child_suffering_from_headaches', $health_disabled_fields)) :  ?>
    <div class="col-md-6 mb-4">
        <label class="form-label d-block" for="does_child_suffering_from_headaches">
            Does your child suffer from headache? &nbsp;
            <?php if($health_required_fields['does_child_suffering_from_headaches']['required']=='required') echo '<font color="red">*</font>'; ?>
        </label>

            <?php
                $headacheYes = (!empty($admission_medical->does_child_suffering_from_headaches) && $admission_medical->does_child_suffering_from_headaches == 'Yes');
                $headacheNo = !$headacheYes;
            ?>

        <div class="position-relative">
        <select
          name="does_child_suffering_from_headaches"
          class="form-control"
          required <?= $health_required_fields['does_child_suffering_from_headaches']['required'] ?>>
            <option value="">Select an option</option>
            <option value="Yes" <?= $headacheYes ? 'selected' : '' ?>>Yes</option>
            <option value="No"  <?= $headacheNo  ? 'selected' : '' ?>>No</option>
        </select>
        <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); pointer-events: none;">
            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
        </div>
        </div>
    </div>
    <?php endif ?>
</div>

<?php if($this->settings->getSetting('enabled_hospitalization_details_in_admissions') == 1) {?>
<div class="row" style="margin-bottom: 10px;">
    <div class="col-md-12" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 14px;padding: 15px 5px;">
        <h4 style="margin: 0;">
            <span>Hospitalization Details</span>
        </h4>
        <button type="button" onclick="open_hospitalization_modal()"
            style="background: #623CE7; color: white; border: none; padding: 10px 16px; border-radius: 8px; font-size: 16px"
            id="document_submit" class="btn save-step3">Add Hospital data</button>
    </div>

    <div class="col-md-12" id="hospital_details" style="margin-top:10px">

    </div>
</div>
<?php  } ?>

<!-- ans -->
<?php if($this->settings->getSetting('enabled_vaccination_details_in_admissions') == 1) { ?>
<style>
/* Mobile-specific styling for vaccination radio buttons */
@media (max-width: 768px) {
    .vaccination-radio-group {
        gap: 12px !important;
        justify-content: flex-start !important;
    }

    .radio-option {
        gap: 8px !important;
        min-width: 60px;
    }

    .radio-option input[type="radio"] {
        margin: 0 !important;
        transform: scale(1.1);
    }

    .radio-option label {
        font-size: 13px !important;
        font-weight: 500;
        white-space: nowrap;
    }

    #vaccination_table_in_admission td {
        padding: 8px 4px !important;
        vertical-align: middle;
    }

    #vaccination_table_in_admission .vaccination-radio-group {
        min-height: 40px;
        align-items: center;
    }

    .vaccination-details-btn {
        width: 100% !important;
        max-width: 120px;
        font-size: 11px !important;
        padding: 6px 8px !important;
        margin-top: 5px !important;
    }
}

/* General improvements for all screen sizes */
.vaccination-radio-group {
    min-height: 35px;
}

.radio-option input[type="radio"]:focus {
    outline: 2px solid #623CE7;
    outline-offset: 2px;
}
</style>

<div class="row">
    <h4 style="margin-bottom: 14px; height: 50px; width: 100%; padding: 15px 5px;" class="">
        <span style="padding: auto;">Vaccination Details</span>
    </h4>
    <div class=""
        style="width: <?php if($this->mobile_detect->isMobile() || $this->mobile_detect->isTablet()) { echo "auto";} else {echo "100%";} ?>;">
        <table class="table table-responsive table-bordered" id="vaccination_table_in_admission">
            <thead>
                <tr>
                    <th style="max-width: 25px; width: 25px;">#</th>
                    <th style="max-width: 110px; width: 110px;">Name</th>
                    <th style="max-width: 140px; width: 140px;">Is Vaccinated?</th>
                    <th style="max-width: 120px; width: 120px;">Date</th>
                    <th style="max-width: 150px; width: 150px;">Remarks</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $sn = 1;
                $vac_master = $this->config->item('vaccine_master');
                $chunkVac = array_chunk($vac_master, 2);

                foreach($chunkVac as $vaccine) {
                    foreach ($vaccine as $val) {
                        $vaccName = strtolower(str_replace(' ','_',$val));

                        // Initialize default values
                        $value_date = '-';
                        $value_description = '-';
                        $value_status = '';
                        $is_vaccinated = 'no';

                        // Get existing vaccination data
                        if(!empty($admission_vaccination) && array_key_exists($vaccName, $admission_vaccination)){
                            $vac_data = $admission_vaccination[$vaccName];
                            $value_status = $vac_data['status'];
                            $is_vaccinated = ($value_status == '1') ? 'yes' : 'no';

                            // Format date
                            if(!empty($vac_data['vacc_date']) && $vac_data['vacc_date'] != '0000-00-00'){
                                $value_date = date('d-m-Y', strtotime($vac_data['vacc_date']));
                            }

                            // Get description
                            $value_description = !empty($vac_data['description']) ? $vac_data['description'] : '-';
                        }

                        $is_checked_yes = ($value_status == '1') ? 'checked' : '';
                        $is_checked_no = ($value_status != '1') ? 'checked' : '';
                        $display_date = ($value_date == '01-01-1970') ? '' : $value_date;
                        $form_date_value = ($value_date != '-' && $value_date != '01-01-1970') ? $value_date : '';
                ?>
                <tr>
                    <td style="width: 25px;"><?= $sn ?></td>
                    <td style="width: 110px;"><?= $val ?> <span style="color: red;">*</span></td>
                    <td style="width: 140px;" data-num="<?= $sn ?>">
                        <div class="vaccination-radio-group" style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                            <div class="radio-option" style="display: flex; align-items: center; gap: 5px;">
                                <input class="radio_input" type="radio" id="no_<?= $sn ?>" name="yes_no_<?= $sn ?>"
                                       value="no_<?= $sn ?>" onclick="onclick_fn(this, '<?= $sn ?>', 'n', '1', '<?= $val ?>')"
                                       <?= $is_checked_no ?> required>
                                <label style="margin-top:8px; cursor: pointer; font-size: 14px;" for="no_<?= $sn ?>">No</label>
                            </div>

                            <div class="radio-option" style="display: flex; align-items: center; gap: 5px;">
                                <input class="radio_input" type="radio" id="yes_<?= $sn ?>" name="yes_no_<?= $sn ?>"
                                       value="yes_<?= $sn ?>" onclick="onclick_fn(this, '<?= $sn ?>', 'y', '2', '<?= $val ?>')"
                                       <?= $is_checked_yes ?> required>
                                <label style="margin-top: 8px; cursor: pointer; font-size: 14px;" for="yes_<?= $sn ?>">Yes</label>
                            </div>
                        </div>

                        <input type="hidden" name="is_vaccinated[<?= $vaccName ?>]" value="<?= $is_vaccinated ?>" id="is_vaccinated_<?= $sn ?>">

                        <button type="button" onclick="add_vacc_details('<?= $val ?>', '<?= $sn ?>')"
                                class="btn vaccination-details-btn" id="add_details_vaccination_<?= $sn ?>"
                                style="<?php if($is_checked_yes == 'checked') { echo 'display: block;'; } else { echo 'display: none;'; } ?> margin-top: 8px; background: #623CE7; color: white; border: none; border-radius: 8px; font-size: 12px; padding: 4px 8px;">
                            <?php if($is_checked_yes == 'checked') { echo 'Edit Details'; } else { echo 'Add Details'; } ?>
                        </button>

                        <input type="text" class="vac_date_<?= $sn ?>" name="vaccination_date[<?= $vaccName ?>]"
                               value="<?= $form_date_value ?>" id="vaccination_date_<?= $sn ?>" style="display: none;">

                        <textarea class="vac_rem_<?= $sn ?>" name="description[<?= $vaccName ?>]"
                                  id="description_<?= $sn ?>" rows="3" style="display: none;"
                                  placeholder="<?= $val ?> Remarks"><?= ($value_description != '-') ? $value_description : '' ?></textarea>
                    </td>
                    <td style="width: 120px;">
                        <div id="id_date_<?= $sn ?>"><?= $display_date ?></div>
                    </td>
                    <td style="width: 150px;">
                        <div id="id_remarks_<?= $sn ?>"><?= $value_description ?></div>
                    </td>
                </tr>
                <?php $sn++; } } ?>
            </tbody>
        </table>
    </div>
</div>
<?php } ?>
<!-- ans end -->


<script>
$(document).ready(function() {
    // $(".vacc_date").val('');

    var startDate = null;
    var endDate = null;

    $('#start_date_picker').datepicker({
            format: 'dd-MM-yyyy',
            //startDate: new Date(),
            "autoclose": true
        })
        .on('changeDate', function(selected) {
            startDate = new Date(selected.date.valueOf());
            var date2 = $('#start_date_picker').datepicker('getDate');
            date2.setDate(date2.getDate());
            $('#end_date_picker').datepicker('setDate', date2);
            //sets minDate to dt1 date + 1
            $('#end_date_picker').datepicker('setStartDate', date2);

        });
    $('#end_date_picker').datepicker({
            format: 'dd-MM-yyyy',
            "autoclose": true
        })
        .on('changeDate', function(selected) {
            endDate = new Date(selected.date.valueOf());

        });

    $('#vac_date_modal').datepicker({
        todayBtn: "linked",
        language: "it",
        autoclose: true,
        todayHighlight: true,
        format: 'dd-mm-yyyy',
        orientation: "top",
        endDate: "today"
    });
    get_hospital_details();
});

function open_hospitalization_modal() {
    Swal.fire({
        title: `<div class="swal2-header-custom" style="text-align: center;margin-bottom: 10px;">
                    <span>Add Hospitalization Details</span>
                </div>`,
        width: '60%',
        html: `
            <form id="hospital_form">
                <div class="col-md-12">
                <div class="row mb-4 mt-4">
                    <label class="form-label text-end"><b>Hospital Admitted To</b></label>
                        <input type="text" class="form-control" id="hospital_name" name="hospital_name" placeholder="Enter Hospital Name">
                </div>
                <div class="row mb-4">
                    <label class="form-label text-end"><b>Hospitalization Date</b></label>
                    <div style="position: relative;">
                        <input type="text" class="form-control" id="fromdateId" name="hospitalization_date" placeholder="Enter Hospitalization Date">
                        <div class="calendar-icon" style="position: absolute; right: 1px; top: 50%; transform: translateY(-50%); z-index: 10;padding-right:20px; cursor: pointer;">
                            <?php $this->load->view('svg_icons/calendar_icon.svg'); ?>
                        </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <label class="form-label text-end"><b>Hospital Reason</b></label>
                        <textarea class="form-control" name="hospital_reason" id="hospital_reason" rows="2"></textarea>
                </div>
                <div class="row mb-4">
                    <label class="form-label text-end"><b>Discharge Date</b></label>
                    <div style="position: relative;">
                        <input type="text" class="form-control" id="todateId" name="discharge_date" placeholder="Enter Discharge Date">
                        <div class="calendar-icon" style="position: absolute; right: 1px; top: 50%; transform: translateY(-50%); z-index: 10;padding-right:20px; cursor: pointer;">
                            <?php $this->load->view('svg_icons/calendar_icon.svg'); ?>
                        </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <label class="form-label text-end"><b>Treatment Provided</b></label>
                        <textarea class="form-control" name="treatement_provided" id="treatement_provided" rows="2" placeholder="Enter the treatment given"></textarea>
                </div>
                <div class="row">
                    <label class="form-label text-end"><b>Remarks</b></label>
                        <textarea class="form-control" name="exit_remarks" id="remarks" rows="2" placeholder="Enter the Remarks"></textarea>
                </div>
                </div>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: 'Submit',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        customClass: {
            popup: 'terms-confirm-popup',
            confirmButton: 'swal2-submit-btn',
            cancelButton: 'swal2-cancel-btn'
        },
        buttonsStyling: false,
        didOpen: () => {
            const title = Swal.getTitle();
            if (title) {
                title.style.paddingTop = '20px';
                title.style.paddingBottom = '10px';
            }

            // Initialize datepicker for hospitalization dates (using simpler datepicker for better modal compatibility)
           $('#fromdateId').datetimepicker({
                viewMode: 'years',
                format: 'DD-MM-YYYY',
                maxDate: moment(), // Allow past and present only
            });

            $('#todateId').datetimepicker({
                viewMode: 'years',
                format: 'DD-MM-YYYY',
                minDate: moment().startOf('day'), // Allow present and future only
            });

        },
        preConfirm: () => {
            return add_hospital_details(); // Your existing JS function
        }
    });
   
}

function get_hospital_details() {
    var af_id = '<?php echo $insert_id ?>';
    $.ajax({
        url: '<?php echo site_url('admission_controller/get_hospital_details'); ?>',
        type: 'post',
        data: {
            'af_id': af_id
        },
        success: function(data) {
            var res_data = $.parseJSON(data);
            if (res_data != '') {
                $('#hospital_details').html(construct_hospitalization_details(res_data));
            } else {
                $('#hospital_details').html(
                    '<h3 class="no-data-display" style="text-align:center">No Hospitalization Data</h3>'
                );
            }
        }
    });
}

function construct_hospitalization_details(res_data) {
    var html = '';
    html += `<table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Hospital Admitted To</th>
                            <th>Hospitalization Date</th>
                            <th>Hospital Reason</th>
                            <th>Discharge Date</th>
                            <th>Treatment Provided</th>
                            <th>Remarks</th>
                        </tr>
                    </thead>
                    <tbody id="">`;
    for (var i = 0; i < res_data.length; i++) {
        html += `<tr>
                            <td>${i+1}</td>
                            <td>${res_data[i].hospital_admitted}</td>
                            <td>${res_data[i].hospitilization_date}</td>
                            <td>${res_data[i].hospitilization_reason}</td>
                            <td>${res_data[i].discharge_date}</td>
                            <td>${res_data[i].treatment_provided}</td>
                            <td>${res_data[i].remarks}</td>
                            </tr>`;
    }
    html += '</tbody>';
    html += '</table>';

    return html;
}

function add_hospital_details() {
    if (check_validation()) {
        var hospital_name = $('#hospital_name').val();
        var hospitalization_date = $('#fromdateId').val();
        var hospital_reason = $('#hospital_reason').val();
        var todateId = $('#todateId').val();
        var treatement_provided = $('#treatement_provided').val();
        var remarks = $('#remarks').val();
        var af_id = '<?php echo $insert_id ?>';
        $('#hosp_btn').html('Please wait').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('admission_controller/submit_hospital_details'); ?>',
            type: 'post',
            data: {
                'af_id': af_id,
                'hospital_name': hospital_name,
                'hospitalization_date': hospitalization_date,
                'hospital_reason': hospital_reason,
                'discharge_date': todateId,
                'treatement_provided': treatement_provided,
                'remarks': remarks
            },
            success: function(data) {
                var res_data = $.parseJSON(data);
                if (res_data) {
                    $('#hospital_details_modal').modal('hide');
                    get_hospital_details();
                    $('#hosp_btn').html('Submit').removeAttr('disabled');
                }
            }
        });
    } else {
        return false;
    }
}

function check_validation() {
    var hospital_name = $('#hospital_name').val();
    var hospitalization_date = $('#fromdateId').val();
    var hospital_reason = $('#hospital_reason').val();
    var todateId = $('#todateId').val();
    var treatement_provided = $('#treatement_provided').val();
    if (hospital_name != '' && hospitalization_date != '' && hospital_reason != '' && todateId != '' &&
        treatement_provided != '') {
        return true;
    } else {
        return false;
    }
}

function onclick_fn(current, number, yes_or_no, message_no, vac_name) {
    $(`#add_details_vaccination_${number}`).show();
    $(`.vac_rem_${number}`).val('');
    $(`.vac_date_${number}`).val('');
    $("#vac_date_modal").val(`${$(`#id_date_${number}`).html()}`);
    $("#vac_rem_modal").val(`${$(`#id_remarks_${number}`).html()}`);



    $(`.span_class_${number}`).remove();
    if ($(current).is(':checked') && yes_or_no == 'y') {
        $(`#add_details_vaccination_${number}`).show();
        $(`#is_vaccinated_${number}`).val('yes');
        $("#vac_date_modal").prop('readonly', false).css('pointer-events', 'auto');

    } else {
        $(`#add_details_vaccination_${number}`).hide();
        $(`#is_vaccinated_${number}`).val('no');
        $("#vac_date_modal").val('').prop('readonly', true).css('pointer-events', 'none');
    }

    if (message_no == '1') {
        if ($(`#vaccination_date_${number}`).val()) {
            bootbox.alert(
                `You are choosed 'Not Vaccinated' for vaccine: ${vac_name}. The details you filled for this vaccine will be dismantled.`
            );
            $(`#id_date_${number}`).html('');
            $(`#id_remarks_${number}`).html('');
            $(`.vac_date_${number}`).val('');
            $(`.vac_rem_${number}`).val('');
        }
    } else if (message_no == '2') {
        if ($(`#vaccination_date_${number}`).val()) {
            $(`#id_date_${number}`).html('');
            $(`#id_remarks_${number}`).html('');
            $(`.vac_date_${number}`).val('');
            $(`.vac_rem_${number}`).val('');
            bootbox.alert(
                `You already added the details for vaccine: ${vac_name}. This details is dismantled, Please fill the details again.`
            );
        }
    }
}

function add_vacc_details(val, sn) {
    // Set dynamic content
    let vacTitle = `Vaccination Entry ${val}`;
    
    Swal.fire({
        title: `<div class="swal2-header-custom" style="text-align: center;margin-bottom: 10px;">
                    <span>${vacTitle}</span>
                </div>`,
        
        html: `
            <input type="hidden" id="number" value="${sn}">
            <div class="form-group text-start mb-3">
                <label for="vac_date_modal" class="form-label">Date of Vaccination</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="vac_date_modal" name="vac_date_modal" placeholder="Select Date">
                </div>
            </div>
            <div class="form-group text-start">
                <label for="vac_rem_modal" class="form-label">Remarks</label>
                <textarea class="form-control" id="vac_rem_modal" name="vac_rem_modal" rows="3" placeholder="Enter remarks here..."></textarea>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Add Details',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        customClass: {
            popup: 'terms-confirm-popup',
            confirmButton: 'swal2-submit-btn',
            cancelButton: 'swal2-cancel-btn'
        },
        buttonsStyling: false,
        didOpen: () => {
            // Initialize datetimepicker
            $('#vac_date_modal').datetimepicker({
                viewMode: 'years',
                format: 'DD-MM-YYYY',
                widgetPositioning: {
                    horizontal: 'auto',
                    vertical: 'bottom' // Ensure it shows below the field
                },
                icons: {
                    time: 'fa fa-clock',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-calendar-check-o',
                    clear: 'fa fa-trash',
                    close: 'fa fa-times'
                }
            });

            // Optional CSS fix if the calendar is cut off
            $('.bootstrap-datetimepicker-widget').css('z-index', 9999);

            // Pre-fill values if available
            const existingDate = $(`#id_date_${sn}`).html();
            const existingRemarks = $(`#id_remarks_${sn}`).html();

            if (existingDate && existingDate !== '-') {
                $('#vac_date_modal').val(existingDate);
            }
            if (existingRemarks && existingRemarks !== '-') {
                $('#vac_rem_modal').val(existingRemarks);
            }
        },
        preConfirm: () => {
            const date = document.getElementById('vac_date_modal').value;
            const remarks = document.getElementById('vac_rem_modal').value;

            if (!date) {
                Swal.showValidationMessage('Please enter the vaccination date');
                return false;
            }

            // Call your handler
            add_vac_details_modal(date, remarks, sn);
        }
    });
}


function add_vac_details_modal(date, remarks, sn) {
    console.log('=== SAVING VACCINATION DETAILS ===');
    console.log('Date received:', date);
    console.log('Remarks received:', remarks);
    console.log('Serial number:', sn);

    // Debug: Check if elements exist
    console.log('Hidden date field exists:', $(`.vac_date_${sn}`).length > 0);
    console.log('Hidden remarks field exists:', $(`.vac_rem_${sn}`).length > 0);
    console.log('Display date element exists:', $(`#id_date_${sn}`).length > 0);
    console.log('Display remarks element exists:', $(`#id_remarks_${sn}`).length > 0);

    // Update the hidden form fields
    $(`.vac_date_${sn}`).val(date);
    $(`.vac_rem_${sn}`).val(remarks);

    // Debug: Verify values were set
    console.log('Hidden date field value after setting:', $(`.vac_date_${sn}`).val());
    console.log('Hidden remarks field value after setting:', $(`.vac_rem_${sn}`).val());

    // Update the display elements
    $(`#id_date_${sn}`).html(date || '-');
    $(`#id_remarks_${sn}`).html(remarks || '-');

    // Debug: Verify display values
    console.log('Display date after setting:', $(`#id_date_${sn}`).html());
    console.log('Display remarks after setting:', $(`#id_remarks_${sn}`).html());

    // Change button text to indicate data has been added
    $(`#add_details_vaccination_${sn}`).html('Edit Details');

}

function showPhyDisabilityBox() {
    if ($("#phyDisability").val() == "Yes") {
        $("#phyDisabilityResaon").css("display", "block");
        $('#physical_disability_reason').attr('required', 'required');
    } else {
        $('#physical_disability_reason').val('');
        $('#physical_disability_reason').removeAttr('required');
        $("#phyDisabilityResaon").css("display", "none");
    }
}

function show_toilet_express(e) {
    if (e == 'Yes') {
        $('.toilet_express_text').show();
        $('#toilet_express_text').attr('required', 'required');
    } else {
        $('#btn-1').prop('checked', true);
        $('.toilet_express_text').hide();
        $('#toilet_express_text').removeAttr('required');
    }
}

function showpastfracturehistory() {
    if ($("#child_past_hsitory_fracture").val() == "Yes") {
        $("#fracture_types_div").css("display", "block");
    } else {
        $("#fracture_types_div").css("display", "none");
    }
}

function morethanmonthspecify() {
    if ($("#more_than_month_disease").val() == "Yes") {
        $("#medicine_name_for_month_div").css("display", "block");
    } else {
        $('#medicine_names_reason').val('');
        document.querySelector("#medicine_names_reason").removeAttribute('required');
        $("#medicine_name_for_month_div").css("display", "none");
    }
}

function showLeaDisabilityBox() {
    if ($("#leaDisability").val() == "Yes") {
        $("#leaDisabilityReason").css("display", "block");
        $('#learning_disability_reason').attr('required', 'required');
    } else {
        $('#learning_disability_reason').val('');
        $('#learning_disability_reason').removeAttr('required');
        $("#leaDisabilityReason").css("display", "none");
    }
}
$(document).ready(function() {
    var medication = '<?php if(!empty($admission_medical)) echo $admission_medical->daily_medication ?>';
    if (medication != '') {
        show_medication_box(1);
    }
    var does_express_touse_toilet =
        '<?php if(!empty($admission_medical)) echo $admission_medical->does_express_touse_toilet ?>';
        show_toilet_express(does_express_touse_toilet);
});;

function show_medication_box(e) {
    if (e == 1) {
        $('.medication').show();
        $('#medication_text').attr('required', 'required');
    } else {
        $('.medication').hide();
        $('#medication_text').removeAttr('required');
    }
}
</script>

<style>
#blood_group_heading {
    color: black;
    margin-left: 10px;
}

#general_details {
    color: black;
    margin-top: -14px;

}

#systematic_examination {
    color: black;
    margin-top: -14px;
}

#dental_examination {
    color: black;
}

#intra_oral {
    color: black;
}

#vitamins {
    color: black;
}

#general_question {
    color: black;
    margin-top: -8px;
}

.bootbox .modal-content {
    width: 50%;
    margin-left: 325px;
    margin: auto;
    height: 10%;
}

input.radio_input {
    height: 20px;
    width: 20px;
    /* margin: 13px 0 0 0; */
}

/* Datepicker z-index fix for SweetAlert modals */
.bootstrap-datetimepicker-widget {
    z-index: 99999 !important;
}

.bootstrap-datetimepicker-widget.dropdown-menu {
    z-index: 99999 !important;
}

/* Ensure datepicker table is clickable */
.bootstrap-datetimepicker-widget table td,
.bootstrap-datetimepicker-widget table th {
    cursor: pointer !important;
    pointer-events: auto !important;
}

.bootstrap-datetimepicker-widget table td.day:hover,
.bootstrap-datetimepicker-widget table td.hour:hover,
.bootstrap-datetimepicker-widget table td.minute:hover {
    background-color: #e6e6e6 !important;
}

/* Calendar icon styling */
.calendar-icon {
    transition: opacity 0.2s ease;
}

.calendar-icon:hover {
    opacity: 0.7;
}
.bootstrap-datetimepicker-widget {
    z-index: 9999 !important;
}

.swal2-container {
    overflow: visible !important;
}

</style>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

<!-- Flatpickr JS -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

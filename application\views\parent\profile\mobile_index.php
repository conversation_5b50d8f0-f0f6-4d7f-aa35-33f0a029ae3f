<?php 
  /**
   * If isNex<PERSON><PERSON><PERSON><PERSON>tudent is not set in cache, then, we assume that he is a old student.
   * For new students, we will display only FEES and PROFILE. Other features are hidden.
   */
  $isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent)?$this->parentcache->getParentCache()->isNextYearStudent:'0';
?>
<div class="panel-group" style="margin-bottom:20%;margin-top:4%">
    <div class="card" style="box-shadow: none;border:none;border-radius: 16px;background:#dae6fa;margin:0px 3%;">
    <?php if($this->settings->getSetting('enable_enquiry_student_referral_url') == 1) { ?>
      <div class="card-body pb-3 pr-0">
          <div class="col-xs-9" style="padding: 0;">
            <h5 class="m-0">Referral Link</h5>
          </div>
          <div class="col-xs-3">
            <a style="margin-top:0.3rem" class="btn btn-md btn-warning pull-right" onclick="generate_enquiry_referal_link('<?php echo $student_id ?>')" id="confirm_profile">Get URL</a>
          </div>
          <br>
      </div>
    <?php } ?>
      <div class="card-body pb-3 pr-0">
       <?php if ($studentData->profile_status == 'Unlock') { ?>
          <div class="col-xs-9" style="padding: 0;">
            <h5 class="m-0">Click the Confirm button if the below information is correct.</h5>
          </div>
          <div class="col-xs-3">
            <a style="margin-top:0.3rem" class="btn btn-md btn-warning pull-right" onclick="update_profile_confirmedbyuser('<?php echo $studentData->stdYearId ?>')" id="confirm_profile">Confirm</a>
          </div>
      <?php }else{ ?>
        <?php if ($studentData->profile_confirmed == 'Yes' ) { ?>
        <?php if ($studentData->profile_confirmed_date) { ?>
        <h6 style="line-height: 26px;">You have confirmed profile information on Date : <?php echo date('d-M-Y',strtotime($studentData->profile_confirmed_date)) ?> <i class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right">   </span></h6>
        <?php }else{ ?>
        <h6 style="line-height: 26px;">You have confirmed profile information <i class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right">   </span></h6>
          <?php } ?>
      <?php }else{?>
        <?php if ($studentData->profile_status_changed_date) { ?>
          <h6 style="line-height: 26px;">The profile has been locked to prevent further edits on this date : <?php echo date('d-M-Y',strtotime($studentData->profile_status_changed_date)) ?> <i class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right">   </span></h6>
          <?php }else{ ?>
          <h6 style="line-height: 26px;">The profile has been locked to prevent further edits <i class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right">   </span></h6>
          <?php } ?>
        <?php } ?>
      <?php } ?>
      </div>
    </div>
  
    <!-- <div class="card-header panel_heading_new_style" style="padding-top: 10px; padding-bottom: 0px;">
    <h3 class="card-title panel_title_new_style"><strong>Student Profile</strong></h3>
    </div> -->
  <?php $this->load->view('parent/profile/blocks/_student_mobile.php') ?>
  <?php if ($this->settings->isProfile_profile_enabled('school_name')) : ?>
   <?php $this->load->view('parent/profile/blocks/_previous_school_details_mobile.php') ?>
  <?php endif ?>
  <?php if ($this->settings->isProfile_profile_enabled('FATHER_NAME')) : ?>
    <?php $this->load->view('parent/profile/blocks/_father_mobile.php') ?>
  <?php endif ?>
  <?php if ($this->settings->isProfile_profile_enabled('MOTHER_NAME')) : ?>
    <?php $this->load->view('parent/profile/blocks/_mother_mobile.php') ?>
   <?php endif ?>
  <?php 
    if($show_guardian) {
      $this->load->view('parent/profile/blocks/_guardian_mobile.php');
    }
  ?>
    <?php if ($this->settings->isProfile_profile_enabled('ELECTIVES')) : ?>
   <?php if(!$isNewStudent && !empty($electives)){ ?>
      <?php $this->load->view('parent/profile/blocks/_electives_mobile.php') ?>
    <?php } ?>
  <?php endif ?>

<?php if ($this->settings->isProfile_profile_enabled('FAMILY_PHOTO')) : ?>
  <div class="card" style="box-shadow: none;border:none;">
  <?php 
    $family_pic = $this->config->item('s3_base_url').'/nextelement-common/Staff and Admin icons 64px/father.png';
    if($family_picture_url != '') {
      $family_pic = $this->filemanager->getFilePath($family_picture_url);
    }
  ?>                     
  <div class="card-body" id="father" style="padding-bottom: 0px;">
        <div class="col-md-12 jContainer">
          <div class="jHead">
            <h4>
              <strong>Family Photo</strong>
            </h4>
          </div>

            <table class="table">
              <tr>
                <th style="height: 64px;">
                  <img id="previewing" class="img-fluid" src="<?php echo $family_pic; ?>"/> 
                </th>
                <td style="word-break: break-word;">
                  <span style="font-size: 20px;font-weight: 500;"></span>
                </td>
              </tr>
            </table>
        </div>
    </div>                               
  </div>
  <?php endif ?>
<?php if(($this->settings->isProfile_profile_enabled('STUDENT_REMARKS'))){ ?>
  <div class="card-body" id="father" style="padding-bottom: 0px;">
        <div class="col-md-12 jContainer">
          <div class="jHead">
            <h4>
              <strong>Remarks</strong>
            </h4>
          </div>
          <div style="margin-top: 5px;margin-left:10px">
            <h5><?php if(!empty($studentData->student_remarks)){
              echo $studentData->student_remarks;
            }else{
              echo '-';
            }   ?></h5>
          </div>
        </div>
    </div>                               
  </div>
  <?php } ?>
  <?php //if(!$isNewStudent){ ?>
  <?php //$this->load->view('parent/profile/blocks/_electives_mobile.php') ?>
  <?php //$staff_handling = $this->settings->getSetting('show_staff_handling_in_parent_profile',1,1)  ?>
  <?php ?>

  <?php //  if ($staff_handling == TRUE && !$isNewStudent) { $this->load->view('parent/profile/blocks/_cls_teacher_sub_mobile.php');  } ?>

  <?php //if ($canteenModuleEnabled && !$isNewStudent) $this->load->view('parent/profile/blocks/_canteen_mobile.php') ?>

</div>



<script type="text/javascript">
  function update_profile_confirmedbyuser(stdYearId) {
    bootbox.confirm({
      title : "Confirm",  
      message: "Are you sure that the profile information is correct ?",
      className: "medium",
      buttons: {
        confirm: {
            label: 'Yes',
            className: 'btn-success'
        },
        cancel: {
            label: 'No',
            className: 'btn-danger'
        }
      },
      callback: function (result) {
        if(result) { 
          $.ajax({
            url: '<?php echo site_url('parent_controller/update_profile_confirmed'); ?>',
            type: 'post',
            data: {'stdYearId' : stdYearId},
            success:function(data){
              var response = JSON.parse(data);
              // console.log(response);
              if (response == 1) {
                location.reload();
              }else{
                mandatory_fields_display_in_popup(response);
              }
            }
          });    
        }
      }
    });
  }
  function mandatory_fields_display_in_popup(response) {
    var html = '';
    html +='<h6>The following fields needs to be entered to \'Confirm\' - </h6>';
    for (var i = 0; i < response.length; i++) {
      var field =  response[i].replace('_',' ');
      html +='<div class="col-md-12">';
      html +='<span class="label label-form" style="color:#000" >'+(i+1)+'. '+field+'</span>';
      html +='</div>';
    }
    html +='<br><h6>Kindly add the fields and \'Confirm\' the data.</h6>';
    bootbox.alert({
      title:'Confirmation message.',
      message: html,
      className:'medium',
      backdrop: true
    });
  }

  function generate_enquiry_referal_link(student_id) {
    $.ajax({
        url: '<?php echo site_url('parent_controller/get_referal_link'); ?>',
        type: 'post',
        data: { 'student_id': student_id },
        success: function(data) {
            var response = JSON.parse(data);
            if (response) {
                bootbox.alert({
                    title: 'Referral Link',
                    message: `
                        <span id="referralLink">${response}</span>
                        <button id="copyBtn" onclick="copyToClipboard('${response}', this)" style="margin-left: 10px; cursor: pointer;">Copy</button>
                    `,
                    className: 'link_medium',
                    backdrop: true
                });
            }
        }
    });
}

// Function to copy text to clipboard and change button text
function copyToClipboard(text, button) {
    navigator.clipboard.writeText(text).then(() => {
        button.textContent = 'Copied';
        button.style.backgroundColor = '#4caf50'; // Optional: Change button color to indicate success
        button.style.color = '#fff';
        setTimeout(() => {
            button.textContent = 'Copy';
            button.style.backgroundColor = ''; // Reset to default
            button.style.color = ''; // Reset to default
        }, 2000); // Reset button text after 2 seconds
    }).catch(err => {
        console.error('Could not copy text: ', err);
    });
}
</script>
<style>

@media (min-width: 992px) {  
  .medium {
    width: 450px;
    margin: auto;
  } 
}

@media (min-width: 1200px) {  
  .medium {
    width: 450px;
    margin: auto;
  } 
}


  .panel.panel-primary {
      border:none;
  }

  .panel-group .panel {
      margin-bottom : 0;
      border-radius : 8px;
  }
  .control-label
    {
      color : #4165a2;
      font-size : 16px;
    }
    .form-horizontal .control-label {
    padding-top: 7px;
    margin-bottom: 0;
     text-align: left; 
}

.bootbox.modal.link_medium {
    max-width: 600px;  /* Adjust max-width as needed */
    overflow: hidden;
}

/* Make sure the modal body handles overflow */
.bootbox-body {
    max-height: 400px;  /* Set a fixed height or use percentage */
    overflow-y: auto;   /* Enable vertical scrolling if content exceeds */
}
</style>
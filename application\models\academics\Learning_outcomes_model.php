<?php
class Learning_outcomes_model extends CI_Model {
    private $yearId;
    function __construct() {
        parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearId();
    }

    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    public function getStaffMapping(){
        $staff_mapping = $this->db->select('id, concat(first_name, " ", ifnull(last_name, "")) as staffName')
        ->from('staff_master')
        ->get()->result();
        if(!empty($staff_mapping)){
            $mapping = [0 => 'Admin'];

            foreach ($staff_mapping as $row) {
                $mapping[$row->id] = trim($row->staffName);
            }
            return $mapping;
        } else {
            return [];
        } 
    }

    public function getClassMasterData(){
        $classes = $this->db->select('id as class_master_id, class_name')
        ->from('class_master')
        ->get()->result();
        if(!empty($classes))
            return $classes;
        else 
            return [];
    }

    public function get_class_section_and_subject_ids_for_lms_access_control(){
        $class_sections_and_subject_ids=$this->db->select("lp_subjects_id, class_section_id, access_level")
        ->from("lp_subjects_section_staff")
        ->where("staff_id",$this->authorization->getAvatarStakeHolderId())
        ->get()->result();

        $subject_list=[];
        $class_section_list = [];
        foreach($class_sections_and_subject_ids as $key => $val){
        if(!array_key_exists($val->lp_subjects_id,$subject_list)){
            $subject_list[$val->lp_subjects_id]=$val->lp_subjects_id;
        }

        if (!array_key_exists($val->class_section_id, $class_section_list)) {
            $class_section_list[$val->class_section_id] = $val->class_section_id;
        }
        }

        return ["class_section_list"=>$class_section_list,"subject_list"=>$subject_list];
    }

    public function getSubjectMasterData(){
        // $is_staff_access_control_enabled = $this->settings->getSetting("enable_staff_class_subject_access_control_for_lms");
        // $is_lms_admin = $this->authorization->isAuthorized('LESSON_PLAN.ADMIN');
        // $class_section_and_subject_lists = $this->get_class_section_and_subject_ids_for_lms_access_control();
        // $subject_list = $class_section_and_subject_lists["subject_list"];

        // $class_master_id = $_POST['class_master_id'];

        // $this->db->select('ls.*')
        // ->from('lp_subjects ls')
        // ->where("ls.acad_year_id",$this->acad_year->getAcadYearId())
        // ->where('ls.class_master_id', $class_master_id);
        
        // if($is_staff_access_control_enabled==1 && !$is_lms_admin){
        //     $this->db->where_in("ls.id",$subject_list);
        // }

        $this->db->select('id as subject_master_id, subject_name')
        ->from('subject_master');

        $result = $this->db->get()->result();

        if($result)
            return $result;
        else 
            return [];
    }

    public function create($data) {
        if (
            !isset($data['class_master_id']) || empty($data['class_master_id']) ||
            !isset($data['subject_master_id']) || empty($data['subject_master_id']) ||
            !isset($data['learning_outcome_name']) || empty($data['learning_outcome_name'])
        ) {
            return ['status' => false, 'message' => 'Required fields missing'];
        }

        $exists = $this->db->get_where('lp_learning_outcome', [
            'class_master_id' => $data['class_master_id'],
            'subject_master_id' => $data['subject_master_id'],
            'learning_outcome_name' => trim($data['learning_outcome_name']),
        ])->row();

        if($exists){
            return ['status' => false, 'message' => 'Learning outcome already exists'];
        }

        $now = $this->Kolkata_datetime();
        $user_id = $this->authorization->getAvatarStakeHolderId();

        $data['created_on'] = $now;
        $data['created_by'] = $user_id;

        $data['learning_outcome_description'] = isset($data['learning_outcome_description']) && !empty(trim($data['learning_outcome_description'])) ? trim($data['learning_outcome_description']) : null;
        $data['is_active'] = isset($data['is_active']) ? (int)$data['is_active'] : 1;
        $data['is_locked'] = isset($data['is_locked']) ? (int)$data['is_locked'] : 0; // Assuming you meant 'is_locked'

        if ($this->db->insert('lp_learning_outcome', $data)) {
            return ['status' => true, 'message' => 'Learning outcome created'];
        } else {
            return ['status' => false, 'message' => 'Database insert failed'];
        }
    }

    public function get_all($class_id = null, $subject_id = null, $search = '', $status = 1) {
        $this->db->select('
            lo.id AS learning_outcome_id,
            lo.learning_outcome_name,
            ifnull(lo.learning_outcome_description, "-") as learning_outcome_description,
            lo.is_active,
            lo.is_locked,
            DATE_FORMAT(lo.created_on, "%d-%m-%Y %r") AS created_on,

            CASE
                WHEN lo.created_by = 0 THEN "Admin"
                ELSE CONCAT(IFNULL(createdBy.first_name, ""), " ", IFNULL(createdBy.last_name, ""))
            END AS created_by,

            CASE
                WHEN lo.updated_by IS NULL THEN ""
                WHEN lo.updated_by = 0 THEN "Admin"
                ELSE CONCAT(IFNULL(updatedBy.first_name, ""), " ", IFNULL(updatedBy.last_name, ""))
            END AS updated_by,
            
            CASE
                WHEN lo.approved_by IS NULL THEN ""
                WHEN lo.approved_by = 0 THEN "Admin"
                ELSE CONCAT(IFNULL(approvedBy.first_name, ""), " ", IFNULL(approvedBy.last_name, ""))
            END AS approved_by,

            CASE
                WHEN lo.updated_on IS NULL THEN ""
                ELSE DATE_FORMAT(lo.updated_on, "%d-%m-%Y %r")
            END AS updated_on,
            
            CASE
                WHEN lo.approved_on IS NULL THEN ""
                ELSE DATE_FORMAT(lo.approved_on, "%d-%m-%Y %r")
            END AS approved_on,

            classMaster.class_name,
            subjectMaster.subject_name,
            lo.history,

            (SELECT COUNT(*)
             FROM lp_topic_learning_outcomes tlo
             WHERE tlo.learning_outcome_id = lo.id AND tlo.status = 1) as mapped_topics_count
        ')
        ->from('lp_learning_outcome lo')
        ->join('staff_master createdBy', 'createdBy.id = lo.created_by', 'left')
        ->join('class_master classMaster', 'classMaster.id = lo.class_master_id')
        // ->join('lp_subjects subjectMaster', 'subjectMaster.id = lo.subject_master_id')
        ->join('subject_master subjectMaster', 'subjectMaster.id = lo.subject_master_id')
        ->join('staff_master updatedBy', 'updatedBy.id = lo.updated_by', 'left')
        ->join('staff_master approvedBy', 'approvedBy.id = lo.approved_by', 'left');
        // ->where('lo.is_active', 1);

        if (!empty($class_id)) {
            $this->db->where('lo.class_master_id', $class_id);
        }

        if (!empty($subject_id)) {
            $this->db->where('lo.subject_master_id', $subject_id);
        }

        if (!empty($search)) {
            $this->db->like('lo.learning_outcome_name', $search);
        }

        if ($status != -1) {
            $this->db->where('lo.is_active', $status);
        }

        $this->db->order_by('classMaster.id', 'ASC');
        $this->db->order_by('subjectMaster.id', 'ASC');
        $this->db->order_by('lo.created_on', 'DESC');

        $result = $this->db->get()->result();

        return $result ? $result : [];
    }

    public function get_by_id($id) {
        if (empty($id)) return null;
        $this->db->select('
            lo.id AS learning_outcome_id,
            lo.learning_outcome_name,
            lo.learning_outcome_description,
            lo.is_active,
            lo.is_locked,
            
            DATE_FORMAT(lo.created_on, "%d-%m-%Y %r") AS created_on,
            
            CASE
                WHEN lo.created_by = 0 THEN "Admin"
                ELSE CONCAT(IFNULL(createdBy.first_name, ""), " ", IFNULL(createdBy.last_name, ""))
            END AS created_by,
            
            CASE
                WHEN lo.updated_by IS NULL THEN "-"
                WHEN lo.updated_by = 0 THEN "Admin"
                ELSE CONCAT(IFNULL(updatedBy.first_name, ""), " ", IFNULL(updatedBy.last_name, ""))
            END AS updated_by,
            
            CASE
                WHEN lo.approved_by IS NULL THEN "-"
                WHEN lo.approved_by = 0 THEN "Admin"
                ELSE CONCAT(IFNULL(approvedBy.first_name, ""), " ", IFNULL(approvedBy.last_name, ""))
            END AS approved_by,
            
            CASE
                WHEN lo.updated_on IS NULL THEN "-"
                ELSE DATE_FORMAT(lo.updated_on, "%d-%m-%Y %r")
            END AS updated_on,
            
            CASE
                WHEN lo.approved_on IS NULL THEN "-"
                ELSE DATE_FORMAT(lo.approved_on, "%d-%m-%Y %r")
            END AS approved_on,
            
            lo.class_master_id,
            lo.subject_master_id,
            classMaster.class_name,
            subjectMaster.subject_name,
            lo.history,

            (SELECT COUNT(*)
             FROM lp_topic_learning_outcomes tlo
             WHERE tlo.learning_outcome_id = lo.id AND tlo.status = 1) as mapped_topics_count
        ')
        ->from('lp_learning_outcome lo')
        ->join('class_master classMaster', 'classMaster.id = lo.class_master_id')
        // ->join('lp_subjects subjectMaster', 'subjectMaster.id = lo.subject_master_id')
        ->join('subject_master subjectMaster', 'subjectMaster.id = lo.subject_master_id')
        ->join('staff_master createdBy', 'createdBy.id = lo.created_by', 'left')
        ->join('staff_master updatedBy', 'updatedBy.id = lo.updated_by', 'left')
        ->join('staff_master approvedBy', 'approvedBy.id = lo.approved_by', 'left')
        ->where('lo.id', $id);

        return $this->db->get()->row();
    }

    public function update_with_history($id, $new_data, $mark_approved = false) {
        if (empty($id) || empty($new_data) || !is_array($new_data)) {
            return ['status' => false, 'message' => 'Invalid input'];
        }

        $existing = $this->get_by_id($id);
        if (!$existing) {
            return ['status' => false, 'message' => 'Learning outcome not found'];
        }

        if (isset($existing->is_locked) && $existing->is_locked == 1) {
            return ['status' => false, 'message' => 'Learning outcome is locked and cannot be modified'];
        }

        $changes = [];
        foreach ($new_data as $key => $new_value) {
            if (in_array($key, ['updated_on', 'updated_by', 'approved_on', 'approved_by', 'history'])) {
                continue; 
            }

            if (property_exists($existing, $key) && $existing->$key != $new_value) {
                $changes[$key] = [
                    'from' => $existing->$key,
                    'to' => $new_value
                ];
            }
        }

        if (empty($changes) && !$mark_approved) {
            return ['status' => true, 'message' => 'No changes made'];
        }

        $history = json_decode($existing->history, true) ?: [];
        $history[] = [
            'changed_at' => $this->Kolkata_datetime(),
            'changed_by' => $this->authorization->getAvatarStakeHolderId(),
            'changes' => $changes
        ];

        $now = $this->Kolkata_datetime();
        $user_id = $this->authorization->getAvatarStakeHolderId();

        $new_data['history'] = json_encode($history);
        $new_data['updated_on'] = $now;
        $new_data['updated_by'] = $user_id;

        if ($mark_approved) {
            $new_data['approved_by'] = $user_id;
            $new_data['approved_on'] = $now;
        }

        $this->db->where('id', $id);
        if ($this->db->update('lp_learning_outcome', $new_data)) {
            return ['status' => true, 'message' => 'Learning outcome updated'];
        } else {
            return ['status' => false, 'message' => 'Database update failed'];
        }
    }

    public function toggle_active($id, $status) {
        return $this->update_with_history($id, ['is_active' => (int)$status]);
    }

    public function toggle_locked($id, $status) {
        return $this->update_with_history($id, ['is_locked' => (int)$status]);
    }

    public function get_mapped_topics($learning_outcome_id) {
        $this->db->select('
            tlo.id as mapping_id,
            tlo.created_on,
            tlo.created_by,
            st.id as topic_id,
            st.sub_topic_name,
            l.id as lesson_id,
            l.lesson_name,
            s.id as subject_id,
            s.subject_name,
            cm.id as class_master_id,
            cm.class_name
        ')
        ->from('lp_topic_learning_outcomes tlo')
        ->join('lp_sub_topics st', 'st.id = tlo.lp_sub_topic_id', 'inner')
        ->join('lp_lessons l', 'l.id = st.lp_lesson_id', 'inner')
        ->join('lp_subjects s', 's.id = l.lp_subject_id', 'inner')
        ->join('class_master cm', 'cm.id = s.class_master_id', 'inner')
        ->where('tlo.learning_outcome_id', $learning_outcome_id)
        ->where('s.acad_year_id', $this->yearId)
        ->where('tlo.status', 1)
        ->order_by('tlo.created_on', 'DESC');

        return $this->db->get()->result();
    }

    public function get_available_topics_for_mapping($learning_outcome_id) {
        // First get the learning outcome details
        $learning_outcome = $this->get_by_id($learning_outcome_id);
        if (!$learning_outcome) {
            return ['status' => false, 'message' => 'Learning outcome not found'];
        }

        // Get all topics for this class and subject that are NOT already mapped to THIS specific learning outcome
        $query = "
            SELECT
                st.id as topic_id,
                st.sub_topic_name,
                l.id as lesson_id,
                l.lesson_name,
                s.id as subject_id,
                s.subject_name
            FROM lp_sub_topics st
            JOIN lp_lessons l ON l.id = st.lp_lesson_id
            JOIN lp_subjects s ON s.id = l.lp_subject_id
            WHERE s.class_master_id = ?
            AND s.subject_master_id = ?
            AND s.acad_year_id = ?
            AND st.id NOT IN (
                SELECT lp_sub_topic_id
                FROM lp_topic_learning_outcomes
                WHERE learning_outcome_id = ? AND status = 1
            )
            ORDER BY l.lesson_name, st.sub_topic_name
        ";

        $available_topics = $this->db->query($query, [
            $learning_outcome->class_master_id,
            $learning_outcome->subject_master_id,
            $this->yearId,
            $learning_outcome_id
        ])->result();

        return [
            'status' => true,
            'topics' => $available_topics,
            'learning_outcome' => $learning_outcome
        ];
    }

    public function create_learning_outcome_topic_mapping($learning_outcome_id, $topic_id) {
        // Check if any mapping already exists (active or inactive)
        $existing = $this->db->get_where('lp_topic_learning_outcomes', [
            'lp_sub_topic_id' => $topic_id,
            'learning_outcome_id' => $learning_outcome_id
        ])->row();

        if ($existing) {
            if ($existing->status == 1) {
                return ['status' => false, 'message' => 'This learning outcome is already mapped to this topic'];
            } else {
                // Reactivate existing mapping
                $update_data = [
                    'status' => 1,
                    'last_modified_by' => $this->authorization->getAvatarStakeHolderId(),
                    'last_modified_on' => $this->Kolkata_datetime(),
                    'history' => json_encode(array_merge(
                        json_decode($existing->history, true) ?: [],
                        [[
                            'changed_at' => $this->Kolkata_datetime(),
                            'changed_by' => $this->authorization->getAvatarStakeHolderId(),
                            'action' => 'reactivated',
                            'changes' => ['status' => ['old' => 0, 'new' => 1]]
                        ]]
                    ))
                ];

                $this->db->where('id', $existing->id);
                if ($this->db->update('lp_topic_learning_outcomes', $update_data)) {
                    return ['status' => true, 'message' => 'Topic mapping reactivated successfully', 'mapping_id' => $existing->id];
                } else {
                    return ['status' => false, 'message' => 'Failed to reactivate mapping'];
                }
            }
        }

        // Create the mapping
        $mapping_data = [
            'lp_sub_topic_id' => $topic_id,
            'learning_outcome_id' => $learning_outcome_id,
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'created_on' => $this->Kolkata_datetime(),
            'status' => 1,
            'history' => json_encode([
                [
                    'changed_at' => $this->Kolkata_datetime(),
                    'changed_by' => $this->authorization->getAvatarStakeHolderId(),
                    'action' => 'created',
                    'changes' => []
                ]
            ])
        ];

        if ($this->db->insert('lp_topic_learning_outcomes', $mapping_data)) {
            return ['status' => true, 'message' => 'Topic mapped successfully', 'mapping_id' => $this->db->insert_id()];
        } else {
            return ['status' => false, 'message' => 'Failed to create mapping'];
        }
    }

    public function remove_learning_outcome_topic_mapping($mapping_id) {
        // Get existing mapping for history
        $existing = $this->db->get_where('lp_topic_learning_outcomes', ['id' => $mapping_id])->row();

        if (!$existing) {
            return ['status' => false, 'message' => 'Mapping not found'];
        }

        // Update history
        $history = json_decode($existing->history, true) ?: [];
        $history[] = [
            'changed_at' => $this->Kolkata_datetime(),
            'changed_by' => $this->authorization->getAvatarStakeHolderId(),
            'action' => 'deleted',
            'changes' => [
                'status' => ['old' => 1, 'new' => 0]
            ]
        ];

        $update_data = [
            'status' => 0,
            'last_modified_by' => $this->authorization->getAvatarStakeHolderId(),
            'last_modified_on' => $this->Kolkata_datetime(),
            'history' => json_encode($history)
        ];

        $this->db->where('id', $mapping_id);
        if ($this->db->update('lp_topic_learning_outcomes', $update_data)) {
            return ['status' => true, 'message' => 'Mapping removed successfully'];
        } else {
            return ['status' => false, 'message' => 'Failed to remove mapping'];
        }
    }
}

<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a  href="<?php echo site_url('enquiry/enquiry_staff'); ?>">Enquiry</a></li>
  <li>Enquiry</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('enquiry/enquiry_staff'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Enquiry
          </h3>
        </div>
      </div>
    </div>
    <div class="card-body">
      <div class="row" style="margin: 0px">
        <div class="col-lg-2">
          <p style="margin-top: 10px; font-weight: bold;">Created Date</p>
           <select name="daterange" id="daterange" class="form-control classId select" onchange="changeDateRange()">
            <option value="_">All</option>
            <option value="<?= date('d-m-Y',strtotime('today')).'_'.date('d-m-Y',strtotime('today')) ?>">Today </option>
            <option value="<?= date('d-m-Y',strtotime('today - 7 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 7 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 30 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 30 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 60 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 60 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 90 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 90 Days </option>
            <option value="7">Custom Range </option>
          </select>
        </div>

        <div id="custom_range" class="col-lg-4 form-group" style="display: none;">
          <div class="col-lg-6">
            <p style="margin-top: 10px;font-weight: bold;">From</p>
            <div class="input-group date" id="datePicker"> 
              <input class="form-control" autocomplete="off" type="text" id="created_from_date" name="created_from_date" placeholder="Select Date">
              <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
              </span>
            </div>
          </div>
          <div class="col-lg-6">
            <p style="margin-top: 10px;font-weight: bold;">To</p>
            <div class="input-group date" id="datePicker"> 
              <input class="form-control" autocomplete="off" type="text" id="created_to_date" name="created_to_date" placeholder="Select Date">
              <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
              </span>  
            </div>
          </div>
        </div>

        <div class="col-md-2">
          <p style="margin-top: 10px;font-weight: bold;">Counselor</p>
          
          <?php 
            $array = array();
            foreach ($counselor as $key => $val) {
              if($this->authorization->isAuthorized('ENQUIRY.SHOW_COUNSELOR_LIST')){
                $array['-1'] = 'Un-Assigned';
                $array[$val->staffId] = $val->name; 
              }else if($val->staffId == $this->authorization->getAvatarStakeHolderId()){
                $array[$val->staffId] = $val->name; 
              }
            }
            if($this->authorization->isAuthorized('ENQUIRY.SHOW_COUNSELOR_LIST')){
            echo form_dropdown("counselor[]", $array, set_value("counselor"), "id='counselorId' multiple title='All' class='form-control classId select '");
            }else{
            echo form_dropdown("counselor[]", $array, set_value("counselor"), "id='counselorId'  class='form-control classId '");
            }
          ?>
        </div>

        <div class="col-md-2">
          <p style="margin-top: 10px;font-weight: bold;">Follow-up Status</p>
          <?php if ($enquiry_pick_status_from_table) { ?>
            <select class="form-control select"  name="follow_up_status" multiple="" title="All" id="follow_up_status">
              <?php foreach ($follow_up_status as $key => $val) { ?>
                <?php
                  $selectedUser= '';
                  if(array_key_exists($val->user_status, $follow_selected_status)){
                    $selectedUser ='selected';
                  }
                 ?>
                  <option <?php echo $selectedUser ?> <?php if($follow_up_status_select == $val->user_status) echo 'selected' ?> value="<?php echo $val->user_status ?>" style="color:black;"><?php echo $val->user_status ?></option>
              <?php } ?>
            </select>
          <?php } else {?>
            <select class="form-control select"  name="follow_up_status" multiple="" title="All" id="follow_up_status">
              <?php foreach ($follow_up_status as $key => $val) { ?>
                  <option <?php if($follow_up_status_select == $val) echo 'selected' ?> value="<?php echo $val ?>"><?php echo $val ?></option>
              <?php } ?>
            </select>
          <?php } ?>
        </div>
        <div class="col-lg-2">
          <p style="margin-top: 10px;font-weight: bold;">Next Follow-up Date</p>
           <select name="daterange" id="daterange1" class="form-control classId select" onchange="changeFollowDateRange()">
            <option value="_">All</option>
            <option value="<?= date('d-m-Y',strtotime('today')).'_'.date('d-m-Y',strtotime('today')) ?>">Today </option>
            <option value="<?= date('d-m-Y',strtotime('today - 7 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 7 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 30 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 30 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 60 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 60 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 90 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 90 Days </option>
            <option value="7">Custom Range </option>
          </select>

        </div>

        <div id="custom_range1" class="col-lg-4 form-group" style="display: none;">
          <div class="col-lg-6">
            <p style="margin-top: 10px;font-weight: bold;">From</p>
            <div class="input-group date" id="datePicker"> 
              <input class="form-control" autocomplete="off" type="text" id="followup_from_date" name="followup_from_date" placeholder="Select Date">
              <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
              </span>
            </div>
          </div>
          <div class="col-lg-6">
            <p style="margin-top: 10px;font-weight: bold;">To</p>
            <div class="input-group date" id="datePicker"> 
              <input class="form-control" autocomplete="off" type="text" id="followup_to_date" name="followup_to_date" placeholder="Select Date">
              <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
              </span>  
            </div>
          </div>
        </div>
        <div class="col-lg-2">
          <p style="margin-top: 10px;font-weight: bold;"><?php if($this->settings->getSetting('your_word_for_class')) { echo $this->settings->getSetting('your_word_for_class'); } else{
            echo 'Grade';
          } ;?></p>

          <select class="form-control select" multiple="" title="All"  name="grade" id="grade">
            <?php foreach ($grades as $key => $val) { ?>
              <option value="<?php echo $val->id ?>"><?php echo $val->class_name ?></option>
            <?php } ?>
          </select>
        </div>

        <div class="col-md-2">
          <p style="margin-top: 10px;font-weight: bold;">Lead Status</p>
          <select id="lead_status-filter" name="lead_status" class="form-control classId select">
            <option value="">All Lead Status</option>
          </select>
        </div>
        <div class="col-md-2">
          <p style="margin-top: 10px;font-weight: bold;">Source</p>
          <select id="source-filter" name="source" class="form-control">
            <option value="">All</option>
            <?php if(!empty($sources)) {
              foreach($sources as $key => $val) { ?>
                  <option value="<?= $val->source ?>"><?= ucfirst($val->source) ?></option>
            <?php } } ?>
          </select>
        </div>
              <div class="col-sm-2 col-md-2 d-flex align-items-end pl-0" style="height: 4.7rem;margin-top: 10px;">
          <input type="button" name="search" id="generate" class="btn btn-primary" style="font-weight: bold;"value="Get Report">
        </div>

      </div>
    </div>

    <div class="card-body">
    <div id="loader" class="loaderclass" style="display:none;"></div>
      
     <!--   <div class="card-header panel_heading_new_style_staff_border">
          <div class="row" style="margin: 0px">
            <div class="col-md-10">
              <h3 class="card-title panel_title_new_style_staff">
                Enquiry data (<span id="countEnquiries"></span>)
              </h3>
            </div>
          </div>
        </div> -->
        <input type="hidden" id="date_check" value="<?php echo date('Y-m-d')?>">
        <input type="hidden" id="todayDate" value="<?php echo date('Y-m-d')?>">
        <div class="col-md-5" style="padding: 0;">
          <div  id="pipelinebar"  class="pendingdates" style="padding: 10px;">

          <!--  <span onclick="followupActionTabs(0)" id="followUp-today" data-id='1' class="checkActive label label-default label-form active"> Today (<span id="todayCount">0</span>)</span>
           <span onclick="followupActionTabs(1)" id="followUp-previous" data-id='2' class="checkActive label label-default label-form"> Previous (<span id="prevousCount">0</span>)</span>
           <span onclick="followupActionTabs(2)" id="followUp-all" data-id='3' class="checkActive label label-default label-form"> All (<span id="allCount">0</span>)</span>
           <span id="date_select" class="checkActive label label-default label-form"></span>
 -->
            <div class="form-group" style="width: 110px; float: right;">
              <!-- <label class="control-label">Select Date</label> -->
              <!-- <input type="text" class="form-control" placeholder="Date" id="enquiry_date" value=""> -->
            </div>
          </div>
          <div class="list-group-enquiry" style="height: 650px; overflow-y: scroll; ">
          </div>
        </div>

        <div class="col-md-7" style="height: 56rem; overflow-y: scroll; ">
          <div class="row" >
              <div class="card-body" style="display:none" id="details-followup">
                  <input type="hidden" id="enquiry_id_generate"  name="enquiry_id_generate">
                  <span onclick="actionTabs(0)" id="detailsForm" class="label label-default label-form active"><i id="faAction1" class="fa fa-angle-down"></i> Details</span>
                  <a onclick="actionTabs(1)"><span id="followForm" class="label label-default label-form"><i id="faAction2" class="fa fa-angle-up"></i> Follow-up</span></a> 


              </div>
          </div>

          <div class="row">
            <div class="card-body">
                
                <div id="studentEnquiryDetails">
                </div>
                <div style="display: none;" id="followEnquiryDetails">
                  <form method="post" class="form-horizontal" id="follow-up-action" data-parsley-validate="">
                  <input type="hidden" name="enquiry_id" id="enquiry_id">
                  <input type="hidden" name="follow_up_type" value="Enquiry">
                  <div class="row">
                  <div class="form-group col-md-12">
                    <div class="row">
                      <label class="control-label col-md-3">Follow-up Action <font color="red">*</font></label>
                      <div class="col-md-8">
                        <select class="form-control" id="followup_action" onchange="change_followup_action()" required="" name="followup_action">
                          <option value="">Select</option>
                          <option value="Email">Email</option>
                          <option value="SMS">SMS</option>
                          <option value="In-person">In-person</option>
                          <option value="Phone-call">Phone-call</option>
                          <option value="status-update">Status-update </option>
                        </select>
                      </div>                  
                    </div>
                  </div>

                  <div class="form-group col-md-12" id="emailpop" style="display: none;">
                    <div class="row">
                      <label class="col-md-3 control-label">Select Template </label>
                       <div class="col-md-8">
                         <select class="form-control"  style="margin-bottom: 10px;" onchange="get_templ_content_and_forms_email()" name="template_name" id="emailtemplateId">
                         </select>
                       </div>                  
                    </div>
                  </div>

                  <div class="form-group col-md-12" id="sms" style="display: none;">
                    <div class="row">
                      <label class="col-md-3 control-label">Select Template </label>
                      <div class="col-md-8">
                        <select class="form-control"  style="margin-bottom: 10px;" onchange="get_templ_content_and_forms_sms()" name="template_name" id="smstemplateId">
                        </select>
                      </div>                  
                    </div>
                   </div>

                  <div class="col-md-12" id="email_content">
                  </div>

                  <div class="col-md-12" id="sms_content">
                  </div>
                  <input type="hidden" id="sms_template_enquiry">
                  <div class="form-group col-md-12">
                    <div class="row">
                      <label class="control-label col-md-3">Remarks </label>
                      <div class="col-md-8">
                        <input type="hidden" value="<?php echo $enquiry_remarks ?>" id="enquiry_remarks_message">
                        <textarea class="form-control" id="remarksFollowup" rows="3" name="message" placeholder="Enter Remarks">
                      </textarea>
                      </div>                  
                    </div>
                  </div>
                  <input type="hidden" id="reporting_status" name="reporting_status">
                  <div class="form-group col-md-12">
                    <div class="row">
                      <label class="control-label col-md-3">Status <font color="red">*</font></label>
                        <div class="col-md-8">
                          <select class="form-control" required="" name="status" id="follow_status" onchange="isReporting_status_convert()">
                          </select>
                        </div>
                      </div>
                    </div>

                  <div class="form-group col-md-12" id="closure-reason" style="display: none;">
                    <div class="row">
                      <label class="control-label col-md-3"> Closure Reason <font color="red">*</font></label>  
                      <div class="col-md-6">
                        <select id="closure_reason" name="closure_reason" class="form-control">
                      
                        </select>
                      </div>
                     <div class="col-md-2">
                        <input type="button" class="btn btn-info " data-toggle="modal" data-target="#newSource" value="Add New" />
                      </div>
                    </div>
                  </div>

                 

                  <div class="form-group col-md-12">
                    <div class="row">
                      <label class="control-label col-md-3"> Lead Status </label>  
                      <div class="col-md-6">
                        <select id="lead_status" name="lead_status" class="form-control">
                        <option value="">Select Lead Status</option>
                        </select>
                      </div>
                     <div class="col-md-2">
                        <input type="button" class="btn btn-info " data-toggle="modal" data-target="#newSource1" value="Add New" />
                      </div>
                    </div>
                  </div>


                  <div class="form-group col-md-12">
                    <div class="row">
                    <label class="control-label col-md-3">Next follow up date </label>
                    <div class="col-md-8">
                      <div class="input-group">

                      <!--   <input type="date" name="StartDate" id="userdate" onchange="TDate()" required /> -->
                      

                        <input type="text" autocomplete="off" class="form-control date_pick" id="dob_dtpicker" name="next_follow_date"  placeholder="Next follow up date"> 

                        <span class="input-group-addon">
                          <span class="glyphicon glyphicon-calendar"></span>
                        </span>
                      </div>
                    </div>
                    </div>
                  </div>

                  
                </div>
                <div style="margin-top: 30px; ">
                <center>
                  <button type="button" id="submitbutton" style="margin-left: 270px;" onclick="submit_followUp()" class="col-md-3 btn btn-primary" >Submit</button><br><br>
                    
                </center>
                </form>

                <div id="success" style="color: green; font-weight: bold; font-size: 14px; display:none;">Enquiry Follow-up successfully Inserted </div>
                  <div id="error" style="color: red; font-weight: bold; font-size: 14px; display:none;">Something went wrong </div>
              
                </div>
            

                <div>
                 
                  <input type="button" class="btn btn-info " data-toggle="modal" data-target="#moveNextYr" id="MvNextyr" value="Move Enquiry to Next Year" />

                  <div class="modal fade" id="moveNextYr" role="dialog" style="vertical-align: middle;">
                        <div class="modal-dialog">
                            <!-- Modal content-->
                            <div class="modal-content" style="width: 60%; margin-left: 25%;" >
                                <div class="modal-header">
                                    <h4 class="modal-title">Add Remarks<font color="red">*</font></h4>
                                    <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
                                </div>
                                <div class="modal-body">
                                    <input type="text" class="form-control" data-parsley-required-message="Enter Remarks" required placeholder="Enter Remarks" id="mvremarks" value="Move To Next  Year">
                                    <div id="error-message" style="color: red; display: none;">Please Enter Remarks.</div>
                                    
                                  </div>
                                  <div class="modal-footer">
                                    
                                  <button type="submit"  class="col-md-2 btn btn-primary " name="nextyrsubmit" onclick="move_next_yr()" id="nextyrsubmit">Submit</button>

                                  <button type="button" class="col-md-2 btn btn-danger" data-dismiss="modal"  name="close" >Close</button>
            
                              </div>
                                  
                                </div>
                            </div>
                        </div>

                        
            </div>
                

                    
                <br> <hr> <br>

                <div id="follow-history" style="height: 200px; overflow-y: scroll;"></div>
                </div>

              </div>
            </div>
          </div>
          
      <!--   <div class="row">
          <div class="card-body">
            <div id="follow-history" style="height: 200px; overflow-y: scroll;"></div>
            </div>
          </div> -->
        </div>

    </div>
  </div>
</div><br><br>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">
 function TDate() {
    var UserDate = document.getElementById("userdate").value;
    var ToDate = new Date();

    if (new Date(UserDate).getTime() <= ToDate.getTime()) {
          alert("The Date must be Bigger or Equal to today date");
          return false;
     }
    return true;
}

$('#mvremarks').on('keyup', function() {
    if ($(this).val() !== '') {
      $('#error-message').hide(); 
    }else{
      $('#error-message').show(); 

    }
  });

function move_next_yr(){
  var enquiry_id = $('#enquiry_id').val();
  var remarks = $('#mvremarks').val();
 // alert(remarks);
  if (remarks ==''){
    $('#mvremarks').css('required', true);
    $('#error-message').css('display', 'inline-block');
    
    return false;
  }
    $.ajax({
        url: '<?php echo site_url('enquiry/enquiry_staff/insert_enqiry_data_next_yr'); ?>',
        type: 'post',
        data:{'enquiry_id': enquiry_id,  'remarks' : remarks},
        success: function(data) {
          var result = $.parseJSON(data);
          if(result == 2){
            Swal.fire({
              // position: "top-end",
              icon: "error",
              title: "Class Not Added for Next Year",
              showConfirmButton: false,
              timer: 1500
            });
            return false;
          }else{
            get_enquiry_details(enquiry_id);
            //console.log(get_enquiry_details(enquiry_id));
            $('#moveNextYr').modal('hide');
            Swal.fire({
                // position: "top-end",
                icon: "success",
                title: "Your Enquiry Moved to Next Year",
                showConfirmButton: false,
                timer: 1500
              });

          }
          
        }
      });
      

}
  

</script>

<style type="text/css">
  .modal {
    overflow-y:auto;
    
  }

  .modal-dialog{
    margin:8%   18% ;
    width: 60%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }
.loaderclass {
  border: 8px solid #eee;
  border-top: 8px solid #7193be;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  position: fixed;
  z-index: 1;
  animation: spin 2s linear infinite;
  margin-top: 40%;
  margin-left: 50%;
  position: absolute;
  z-index: 99999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.active{
  background: #6893ca;
}
</style>

<script>

function isReporting_status_convert(){
  var follow_status = $('#follow_status').val();
  $.ajax({
    url: '<?php echo site_url('enquiry/enquiry_staff/isReporting_status_convert'); ?>',
    data: {'follow_status':follow_status},
    type: "post",
    success: function (data) {
      var data = $.parseJSON(data);
      if(data){
        $('#reporting_status').val(1);
      }else{
        $('#reporting_status').val(0);
      }
    }
  });
}
function BootboxContent() {
  // console.log(admissionExpireDate);
   var frm_str = '<form id="some-form">'
        + '<div class="form-group">'
        + '<label for="date">Admission Link Expire Date</label>'
        + '<input id="admission_expire_date" class="date span2 form-control input-sm" size="16" value="'+admissionExpireDate+'" name="admisison_link_expire_date" placeholder="dd-mm-yyyy" type="text">'
        + '</div>'
        + '</form>';

    var object = $('<div/>').html(frm_str).contents();

    object.find('#admission_expire_date').datepicker({
        format: 'dd-mm-yyyy',
        autoclose: true,
        startDate: new Date()
    }).on('changeDate', function (ev) {
        $(this).blur();
        $(this).datepicker('hide');
    });

    return object
}

function preview_admissionLink_sendingEmail_content(admission_expire_date,followup_action,follow_status){
  var enquiry_id = $('#enquiry_id').val();
  $('#admission_expire_date_new').val(admission_expire_date)
  $('#follow_up_action_new').val(followup_action)
  $('#follow_status_new').val(follow_status)
    $('#admission_link_preview').modal('show');
    $.ajax({
      url: '<?php echo site_url('enquiry/enquiry_staff/get_admission_link_email_data'); ?>',
      type: 'post',
      data: {'enquiry_id':enquiry_id,'admission_expire_date':admission_expire_date},
      success: function(data) {
        console.log(data)
        $('#send_link_email_content').html(data);
      }
    
  });

}

function send_admission_link_email(){
  var enquiry_id = $('#enquiry_id').val();
  var followup_action = $('#follow_up_action_new').val()
  var follow_status = $('#follow_status').val();
  var admission_expire_date = $('#admission_expire_date_new').val()
  generate_admission_link(enquiry_id,admission_expire_date,followup_action,follow_status)
}

function generate_admission_form(followup_action = '',follow_status=''){
  var enquiry_id = $('#enquiry_id').val();
  var create_admission_record = '<?php echo $this->settings->getSetting('enquiry_create_admission_record') ?>';
  var follow_status = $('#follow_status').val();
  bootbox.confirm({
      title: "Select Admission Link Expiry Date",
      message: BootboxContent,
      className: "medium",
      buttons: {
          confirm: {
              label: 'Next',
              className: 'btn-success'
          },
          cancel: {
              label: 'No',
              className: 'btn-danger'
          }
        },
        callback: function (result) {
            if(result) {
              var admission_expire_date = $('#admission_expire_date').val();
              if(admission_expire_date == ''){
                bootbox.confirm({
                  message: '<h3>Do you want continue without selecting expire date  ?</h3>',
                  className: 'confirmBootboxClass',
                  size: 'small',
                  callback: function (result) {
                    if(result){
                      // generate_admission_link(enquiry_id, admission_expire_date,followup_action,follow_status);
                      preview_admissionLink_sendingEmail_content(admission_expire_date,followup_action,follow_status)
                    }
                  }
                });
              }
              else{
                preview_admissionLink_sendingEmail_content(admission_expire_date,followup_action,follow_status)

                // generate_admission_link(enquiry_id, admission_expire_date,followup_action,follow_status);
                // submit_followup_data();
              }
            }
        }
    });
}

function generate_admission_link(enquiry_id, admission_expire_date,followup_action,follow_status){
  $.ajax({
      url: '<?php echo site_url('enquiry/enquiry_staff/send_admission_link_to_parent'); ?>',
      type: 'post',
      data: {'enquiry_id':enquiry_id,'admission_expire_date':admission_expire_date,'followup_action':followup_action,'follow_status':follow_status},
      success: function(data) {
        var data = JSON.parse(data);
        if(data){
              $(function(){
                  new PNotify({
                      title: 'Success',
                      text:  'Successfully Sent',
                      type: 'success',
                  });
              });
              get_enquiry_details(enquiry_id);
          }else{
              $(function(){
                  new PNotify({
                      title: 'Error',
                      text:  'Something went wrong',
                      type: 'error',
                  });
              });
          }
          $('#admission_link_preview').modal('hide');
      }
    
  });
}

function changeDateRange(){
  var range = $('#daterange').val();
  if(range == 7)
    $("#custom_range").show();
  else
    $("#custom_range").hide();
}

function changeFollowDateRange(){
  var range = $('#daterange1').val();
  if(range == 7)
    $("#custom_range1").show();
  else
    $("#custom_range1").hide();
}
$(document).ready(function() {
  $('.date').datetimepicker({
    viewMode: 'days',
    format: 'DD-MM-YYYY',
    allowInputToggle: true
  });
  $('#created_from_date, #created_from_date').on('focus click', function() {
    $('#created_from_date').datetimepicker('show');
  });
  $('#created_to_date, #created_to_date').on('focus click', function() {
    $('#created_to_date').datetimepicker('show');
  });
});
function actionTabs(e) {
   $('#followup_action').val('');
   $("#smstemplateId").val('');
   var remarksFollowup = $('#enquiry_remarks_message').val();
   $('#remarksFollowup').val(remarksFollowup);
  if (e == 0) {
      $('#studentEnquiryDetails').show();
      $('#followEnquiryDetails').hide();
      $('#detailsForm').addClass('active');
      $('#followForm').removeClass('active');

      $('#faAction1').addClass('fa-angle-down');
      $('#faAction2').removeClass('fa-angle-down');
      $('#faAction2').addClass('fa-angle-up');
      
  }else{
      $('#studentEnquiryDetails').hide();
      $('#followEnquiryDetails').show();
      $('#followForm').addClass('active');
      $('#detailsForm').removeClass('active');

      $('#faAction2').addClass('fa-angle-down');
      $('#faAction1').removeClass('fa-angle-down');
      $('#faAction1').addClass('fa-angle-up');
      change_followup_action();
  }       
}   

$(document).ready(function(){
  get_enquiry_data();
  $('#loader').show();
  // $('#enquiry_date').datepicker({
  //   format: 'd-m-yyyy',
  //   "autoclose": true
  //   })
  //   .on('changeDate', function (value) {
  //     value = new Date(value.date.valueOf()),
  //     year = new Date(value).getFullYear();
  //     month = new Date(value).getMonth()+1;
  //     if (month < 10) {
  //       month = '0'+month
  //     }
  //     date = new Date(value).getDate();
  //    if (date < 10) {
  //       date = '0'+date
  //     }
  //     enqdate = year+'-'+month+'-'+date;

  //     $('#followUp-previous').removeClass('active');
  //     $('#followUp-today').removeClass('active');
  //     $('#followUp-all').removeClass('active');
  //     $('#date_select').addClass('active');
  //     $('#date_select').html(enqdate);
  //     $('#date_check').val(enqdate);
  //     get_enquiry_data();
     
  // });
});


$('#generate').on('click',function(){
  var range = $('#daterange').val();
  if(range == 7){
    var createdfrom_date = $("#created_from_date").val();
    var createdto_date = $('#created_to_date').val();
    if(!createdfrom_date || !createdto_date) {
      alert('Please fill both From and To date fields for custom range.');
      return false;
    }
  }
  var range1 = $('#daterange1').val();
  if(range1 == 7){
    var followupfrom_date = $("#followup_from_date").val();
    var followupto_date = $('#followup_to_date').val();
    if(!followupfrom_date || !followupto_date) {
      alert('Please fill both From and To date fields for Next Follow-up custom range.');
      return false;
    }
  }
  // Change button text to "Please wait" and disable it
  var $button = $(this);
  $button.prop('disabled', true).val('Please wait...');
  
  // Call the enquiry data function
  get_enquiry_data();
});

$('#follow_ups').on('change',function(){
  if($(this).val() ==''){
    $('#pipelinebar').css('display','none');
    $('#statusShow').show();

    $('#followUp-all').addClass('active');
    $('#followUp-previous').removeClass('active');
    $('#followUp-today').removeClass('active');
    $('#date_select').removeClass('active');

  }else{
    $('#pipelinebar').css('display','block');
    $('#follow_up_status').val('');
    $('#statusShow').hide();
  }

});


// function followupActionTabs(e) {
//   $('#date_select').html('');
//   $('#enquiry_date').val($('#todayDate').val());
//   $('#date_check').val($('#todayDate').val());
//   if (e == 0) {
//     $('#followUp-today').addClass('active');
//     $('#followUp-previous').removeClass('active');
//     $('#followUp-all').removeClass('active');
//   }else if(e == 1){
//     $('#followUp-previous').addClass('active');
//     $('#followUp-today').removeClass('active');
//     $('#followUp-all').removeClass('active');
//   }else if(e == 2){
//     $('#followUp-all').addClass('active');
//     $('#followUp-previous').removeClass('active');
//     $('#followUp-today').removeClass('active');
//   }
//   get_enquiry_data()
// }
  
function get_enquiry_data() {
  var range = $('#daterange').val();
  if(range == 7){
    var createdfrom_date = $("#created_from_date").val();
    var createdto_date = $('#created_to_date').val();
  }else{
    var range = $('#daterange').val().split('_');
    var createdfrom_date = range[0];
    var createdto_date = range[1];
  }
  var range1 = $('#daterange1').val();
  if(range1 == 7){
    var followupfrom_date = $("#followup_from_date").val();
    var followupto_date = $('#followup_to_date').val();
  }else{
    var range = $('#daterange1').val().split('_');
    var followupfrom_date = range[0];
    var followupto_date = range[1];
  }
  var counselor = $('#counselorId').val();
  if (counselor == null) {
    counselor = '';
  }
  var follow_up_status = $('#follow_up_status').val();
  if (follow_up_status == null) {
    follow_up_status = '';
  }
  var leadStatus = $('#lead_status-filter').val();
  if (leadStatus == null) {
    leadStatus = '';
  }

  var source = $('#source-filter').val();
  if (source == null) {
    source = '';
  }
  // var follow_ups = $('#follow_ups').val();
  // var LogedInStaffId = $('#counselor').val();
  // var followupStatus = $('#follow_up_status').val();

  var grade = $('#grade').val();
  if (grade == null) {
    grade = '';
  }
  $.ajax({
    url: '<?php echo site_url('enquiry/enquiry_staff/get_enquiry_data'); ?>',
    data: {'createdfrom_date': createdfrom_date,'createdto_date':createdto_date,'followupfrom_date':followupfrom_date,'followupto_date':followupto_date,'counselor':counselor,'follow_up_status':follow_up_status,'grade':grade,'leadStatus':leadStatus,'source':source},
    type: "post",
    success: function (data) {
      var data = JSON.parse(data);
      // console.log(data);
      if (data =='') {
        $('.list-group-enquiry').html('<h5 style="padding:15px; font-size:18px; display:block;" class="no-data-display">No Enquiries to follow-up today</h5>');
        $('#countEnquiries').html('');
        get_enquiry_details('');
        $('#details-followup').hide();
        $('#followForm').hide();
        // Restore button state
        $('#generate').prop('disabled', false).val('Get Report');
        return false;
      }
      $('#details-followup').show();
        $('#followForm').show();
      $('.list-group-enquiry').html(construct_enquiry_data(data));
      // $('.list-group-item').on('click',function() {
      //   $(this).addClass('active').siblings().removeClass('active');
      // });
      $(".datatable").DataTable({
          order: [],
          //"dom": '<"wrapper"flipt>'
          // "dom": '<"top"i>rt<"bottom"flp><"clear">'
          dom: 'lfrtip',
          
        paging :true,
				"language": {
				"search": "",
				"searchPlaceholder": "Enter Search..."
				},
        "pageLength": 10,
        
      });
      onclickgetEnquiryId();
      // Restore button state
      $('#generate').prop('disabled', false).val('Get Report');
    },
    error: function (err) {
      console.log(err);
      // Restore button state on error
      $('#generate').prop('disabled', false).val('Get Report');
    }
  });
}

function construct_enquiry_data(enquiries){
  count_enquiries(enquiries);
  $('#countEnquiries').html(enquiries.length);
  var dates = $('#date_check').val();  
  var enquiryArr = [];
  for(var e = 0; e < enquiries.length; e++){
    enquiryArr.push(enquiries[e]);  
    // if (($('#followUp-today').attr('class')) === 'checkActive label label-default label-form active') {
    //   if (enquiries[e].next_follow_date == dates) {
    //     enquiryArr.push(enquiries[e])
    //   }
    // }else if(($('#followUp-previous').attr('class')) === 'checkActive label label-default label-form active'){
    //   if (enquiries[e].next_follow_date < dates) {
    //     enquiryArr.push(enquiries[e])
    //   }
    // }else if(($('#followUp-all').attr('class')) === 'checkActive label label-default label-form active'){
    //   enquiryArr.push(enquiries[e])
    // }else if(($('#date_select').attr('class')) === 'checkActive label label-default label-form active'){
    //   if (enquiries[e].next_follow_date == dates) {
    //     enquiryArr.push(enquiries[e])
    //   }
    // }
  }
  return construct_enquiry_data1(enquiryArr);
}

function construct_enquiry_data1(enquirydata) {

  var output='';
  var m=1;
  output += '<table class="table table-bordered datatable" id="dataTable" >';
  output +='<thead >';
  output += '<tr>';
  output += '<th   >Enquiry Details</th>';
  output += '</tr>';
  output +='</thead>';
  output +='<tbody>';
  for(var i = 0; i < enquirydata.length; i++){
    var bg = '';
    var className = '';
    var mr ='';
    switch(enquirydata[i].lead_status){
      case 'Hot Lead':
        bg = 'red';
        className = 'new_circleShape_res';
        mr ='5px';
        break;
         case 'Warm Lead':
        bg = 'orange';
        className = 'new_circleShape_res';
        mr ='5px';
        break;
        case 'Cold Lead':
        bg = 'grey';
        className = 'new_circleShape_res';
        mr ='5px';
        break;
       default:
        bg = '';
        className = '';
        mr ='';
      }
    
    if (m == 1) {
        var active = 'active';
      }else{
        var active = '';
      }

      var createdDate = enquirydata[i].createddate;
      if (enquirydata[i].createddate == null) {
        createdDate = 'No Follow-up date'
      }
      var nextFollowupdate = enquirydata[i].next_follow_date;
      if (enquirydata[i].next_follow_date == null || enquirydata[i].next_follow_date == '01-01-1970') {
        nextFollowupdate = 'No Follow-up date'
      }
      var enquiryNumber = '';
      if(enquirydata[i].enquiry_number != '0' && enquirydata[i].enquiry_number != null){
        enquiryNumber = enquirydata[i].enquiry_number+' - ';
      }
      var en_number = enquirydata[i].en_number;
      // if(enquirydata[i].mobile_number =='' || enquirydata[i].mobile_number == null || enquirydata[i].mobile_number == undefined){
      //   en_number = enquirydata[i].father_phone_number;
      // }else if(enquirydata[i].father_phone_number =='' || enquirydata[i].father_phone_number == null || enquirydata[i].father_phone_number == undefined){
      //   en_number = enquirydata[i].mother_phone_number;
      // }else{
      //   en_number = '';
      // }
      //console.log(en_number);
      output += '<tr>';
      output += '<td><a style="color:'+enquirydata[i].color_code+'" id="'+enquirydata[i].id+'" href="javascript:void(0)" onclick="onlclickgetId('+enquirydata[i].id+')" class="list-group-item '+active+'"><span  style="background-color:'+bg+'; margin-right:'+mr+'" class="'+className+'"></span><span style="color: 000; font-weight: bold;">'+enquiryNumber+' '+enquirydata[i].student_name+' </span><span style="float: right; font-weight:bold" >'+createdDate+'</span><p>'+enquirydata[i].parent_name+' <b>Mobile No: </b> '+en_number+'<br><b>Counselor: </b>'+enquirydata[i].counselor+'<br><b>No of Followups: </b>'+enquirydata[i].followup_count+'<span style="float: right; font-weight:bold; text-align: right;" >'+enquirydata[i].status+'<br>'+nextFollowupdate+'</span></p></a></td>';
      output += '</tr>';
   
     m++;
      
    }
  output +='</tbody>';
  output += '</table>';

  return output;
}

function count_enquiries(enquirydata) {
  
  var dates = $('#date_check').val();
  var todayCount= 0;
  var previousCount= 0;
  var allCount= 0;
  var reminingCount = 0;
  for(var j = 0; j < enquirydata.length; j++){
    if (enquirydata[j].next_follow_date == dates) {
      todayCount++;
    }else if(enquirydata[j].next_follow_date < dates){
      previousCount ++;
    }else{
     reminingCount++;
    }
  }
  allCount = parseInt(todayCount) + parseInt(previousCount) + parseInt(reminingCount);
  $('#todayCount').html(todayCount)
  $('#prevousCount').html(previousCount)
  $('#allCount').html(allCount)
}

function onlclickgetId(enquiryId) {
  $('.list-group-item').removeClass('active');
  $('#'+enquiryId).addClass('active');
  $('#success').hide();
  $('#enquiry_id').val(enquiryId);

  $('#followup_action').val('');
  $("#smstemplateId").val('');
  var remarksFollowup = $('#enquiry_remarks_message').val();
  $('#remarksFollowup').html(remarksFollowup);
  
  $('#studentEnquiryDetails').show();
  $('#followEnquiryDetails').hide();
  $('#detailsForm').addClass('active');
  $('#followForm').removeClass('active');

  $('#faAction1').addClass('fa-angle-down');
  $('#faAction2').removeClass('fa-angle-down');
  $('#faAction2').addClass('fa-angle-up');
  get_enquiry_details(enquiryId);
}

function onclickgetEnquiryId() {
  var enquiryId = $('a.list-group-item.active').attr('id');
  
  if (typeof enquiryId === 'undefined') {
    $('.list-group-enquiry').html('<h5 style="padding:15px; font-size:18px;" class="no-data-display">No Enquiries to follow-up today</h5>');
    return false;
  }
  
  $('#enquiry_id').val(enquiryId);
  get_enquiry_details(enquiryId);
}

var classArray = [];
var admissionExpireDate = '';
function get_enquiry_details(enquiryId) {
  $('.parsley-success').css('background-color','#fff');
  $('.parsley-success').css('color','#555');
  
  if (enquiryId == '') {
   
    $('#studentEnquiryDetails').html('');
    $('#follow-history').html('');
    $('#emailtemplateId').html('');
    $('#smstemplateId').html('');
    $('#follow_status').html('');
    $('#lead_status').html('');    
    $('#closure_reason').html('');
    $('#remarksFollowup').html('');
    $('#remarksFollowup').html('');
    return false;
  }
  
  // $('#success').html('');
  $('#enquiry_id_generate').val(enquiryId);
  $('#followup_action').val('');
  $('#emailtemplateId').val('');
  $('#smstemplateId').val('');
  // $('#remarksFollowup').val('');
  $('#dob_dtpicker').val('');
  // $('#success').html('');
  $('#remarksFollowup').val('');
  
  $('#opacity').css('opacity','0.5');
  $.ajax({
    url: '<?php echo site_url('enquiry/enquiry_staff/enquiry_detailsbyId'); ?>',
    data: {'enquiryId': enquiryId},
    type: "post",
    success: function (data) {
      $('#opacity').css('opacity','5');
      $('#loader').hide();
      $('#details-followup').show();

      var data = JSON.parse(data);
      classArray = data.classes;
      boards = data.boards;
      s_gender = data.s_gender;
      is_transfer_certificate_available = data.is_transfer_certificate_available; 
      boarding_type =data.boarding_type;
      got_to_know_by = data.got_to_know_by;
      previous_academic_report = data.previous_academic_report;
      enquiry_combination = data.enquiry_combination;
      enquiry_additional_coaching = data.enquiry_additional_coaching;
      current_country = data.current_country;
      acad_year_settings = data.acad_year_all;
      enquiry_print = data.enquiry_print;
      admissionExpireDate = data.admissionExpireDate;
      
      if (enquiry_print == 1) {
      $('#download_btn').show();
      }

      $('#studentEnquiryDetails').html(construct_enquiry_template(data.follow_enuiry, data.academic_year));
      $('#follow-history').html(construct_enquiry_history(data.follow_enuiry.followup_history));
      $('#emailtemplateId').html(construct_emails_template(data.eTemplate));
      $('#smstemplateId').html(construct_sms_template(data.sTemplate));
      $('#follow_status').html(construct_follow_up_status(data.follow_up_status, data.enquiry_pick_status_from_table));
      $('#lead_status').html(construct_lead_status(data.lead_status));
      $('#lead_status-filter').html(construct_lead_status_filter(data.lead_status));
      $('#lead_status-filter').selectpicker('refresh');
      $('#closure_reason').html(construct_closure_reasons_status(data.closure_reasons));
      after_call(classArray, data.ask_closure_reason, boards, s_gender, is_transfer_certificate_available,boarding_type,got_to_know_by, previous_academic_report, enquiry_combination, enquiry_additional_coaching,current_country, acad_year_settings);
    },
    error: function (err) {
      console.log(err);
    }
  });
}

function construct_closure_reasons_status(closure_reasons) {
    var output = '';
      output += '<option value="">Select Reason</option>';
    for(var i in closure_reasons){
      output+='<option value="'+ closure_reasons[i] +'">'+closure_reasons[i]+'</option>';
    }
  
  
  return output;
}

function construct_emails_template(eTemplate) {
  var output = '';
  output+='<option value="">Select Template</option>';      
  for(var i =0; i < eTemplate.length; i++){
    output +='<option value="'+eTemplate[i]+'">'+eTemplate[i]+'</option>'
  }
  return output;
}

function construct_sms_template(sTemplate) {
  var output = '';
  output+='<option value="">Select Template</option>';      
  for(var i =0; i < sTemplate.length; i++){
    output +='<option value="'+sTemplate[i].name+'">'+sTemplate[i].name+'</option>'
  }
  return output;
}

function construct_follow_up_status(follow_up_status, enquiry_pick_status_from_table) {
  var output = '';
  output+='<option value="">Select status</option>';

  if (enquiry_pick_status_from_table) {
    for(var i =0; i < follow_up_status.length; i++){
      output +=`<option value="${follow_up_status[i].user_status}">${follow_up_status[i].user_status}</option>`;
    }
  } else {
    for(var i =0; i < follow_up_status.length; i++){
      output +='<option value="'+follow_up_status[i]+'">'+follow_up_status[i]+'</option>'
    }
  }
  return output;
}

function construct_lead_status(lead_status) {
   var dbLeadStatus = [];
   lead_status.forEach(function(item) {
    if (item.lead_status!='') {
      dbLeadStatus.push(item.lead_status);
    }
  });
  var leadStatus =  ['Hot Lead','Warm Lead','Cold Lead'];

  var mergeleadStatus = concatUniqueWithSort(leadStatus, dbLeadStatus);
  var output = '';
  output +='<option value="">All Lead status</option>'; 
  for(var i =0; i < mergeleadStatus.length; i++){
    if(mergeleadStatus[i] !=''){
       output +='<option value="'+mergeleadStatus[i]+'">'+mergeleadStatus[i]+'</option>';
    }
   
  }
  return output;
}

function construct_lead_status_filter(lead_status) {
  var lead_status_filter = $('#lead_status-filter').val();
   var dbLeadStatus = [];
   lead_status.forEach(function(item) {
    if (item.lead_status!='') {
      dbLeadStatus.push(item.lead_status);
    }
  });
  var leadStatus =  ['Hot Lead','Warm Lead','Cold Lead'];

  var mergeleadStatus = concatUniqueWithSort(leadStatus, dbLeadStatus);
  var output = '';
  output +='<option value="">All Lead status</option>'; 
  for(var i =0; i < mergeleadStatus.length; i++){
    var selected = '';
    if(mergeleadStatus[i] == lead_status_filter){
      selected = 'selected';
    }
    if(mergeleadStatus[i] !=''){
       output +='<option value="'+mergeleadStatus[i]+'" '+selected+'>'+mergeleadStatus[i]+'</option>';
    }
   
  }
  return output;
}
var concatUniqueWithSort = function (thisArray, otherArray) {
    var newArray = thisArray.concat(otherArray).sort(function (a, b) {
        return a > b ? 1 : a < b ? -1 : 0;
    });

    return newArray.filter(function (item, index) {
        return newArray.indexOf(item) === index;
    });
};
function construct_enquiry_history(fh) {
    var his ='';
    if (fh =='') {
     his +='<h4>Enquiry History Not Found</h4>';
    }else{
        his +='<h4>Follow up history</h4>';
        his +='<table class="table table-bordered">';
        his +='<thead>';
        his +='<tr>';
        his +='<th>#</th>';
        his +='<th>Date</th>';
        his +='<th>Action taken</th>';
        his +='<th>Follow up by</th>';
        his +='<th>Remarks</th>';
        his +='<th>Status</th>';
        his +='<th>Lead Status</th>';
        his +='<th>Next follow up date</th>';
        his +='</tr>';
        his +='</thead>';
        his +='<tbody>'; 
        k=1;
        for(key in fh){
          var lead_status = fh[key].lead_status;
          if (fh[key].lead_status == null) {
            lead_status = '';
          }
          var closure_reason = '';
          if(fh[key].closure_reason){
            closure_reason = ` ( Reason : ${fh[key].closure_reason})`;
          }
            his +='<tr>';
            his +='<td>'+(k)+'</td>';
            his +='<td>'+fh[key].created_on+'</td>';
            his +='<td>';
            his +='<a href="#" onclick="email_sms_popup_details(\'Enquiry\',\''+fh[key].follow_up_action+'\',\''+fh[key].id+'\')" data-toggle="modal" data-target="#summary">'+fh[key].follow_up_action+'</a>';
            his +='</td>';            
            his +='<td>'+fh[key].first_name+'</td>';
            his +='<td >'+fh[key].remarks+'</td>';
            his +='<td>'+fh[key].status+closure_reason+'</td>';
            his +='<td>'+lead_status+'</td>';
            his +='<td>'+fh[key].next_follow_date+'</td>';
            his +='</tr>';
            k++;
        }
        his +='</tbody>';
        his +='</table>';
    }
    
   
    return his;
}

 function construct_enquiry_template(follow_enuiry, academic_year) {
    var enquiry_acad_year = academic_year;
    if (academic_year == null) {
      enquiry_acad_year = '';
    }
    var grade_or_course = '<?php echo $this->settings->getSetting('your_word_for_class') ?>';
    if(grade_or_course == ''){
      grade_or_course = 'Grade Applied For';
    }
    var create_admission_record = '<?php echo $this->settings->getSetting('enquiry_create_admission_record') ?>';
    var permission = '<?php echo $this->authorization->isAuthorized('ENQUIRY.ACAD_YEAR_CHANGE') ?>';
    var generate_pdf = '<?php echo $this->settings->getSetting('enquiry_generate_pdf') ?>';
    var std = '';
    if(create_admission_record == 1 && follow_enuiry.reporting_status == 'convert' && follow_enuiry.sent_admission_link == 1){
      std += `<a class="btn btn-info send_admission_link_btn" style="float: right;margin-bottom:10px;" onclick="send_admission_link('Re-Send');">Resend Admission Link</a>`;
    }else if(create_admission_record == 1 && follow_enuiry.reporting_status == 'convert' && follow_enuiry.sent_admission_link == 0){
      std += `<a class="btn btn-info send_admission_link_btn" style="float: right;margin-bottom:10px;" onclick="send_admission_link('Send');">Send Admission Link</a>`;
    }
    if(generate_pdf){
      std += `<a onclick="generate_pdf()" id="generate_pdf_btn" class="btn btn-secondary" style="float:right;margin-bottom:10px">Generate PDF</a> `;
      if(follow_enuiry.pdf_status == 1){
        std += `<a onclick="download_pdf()" id="download_btn_pdf" class="btn btn-primary" style="float:right;margin-right: 5px;margin-bottom:10px ">Download PDF</a>`;
      }
    }
    std += '<table class="table table-bordered" style="margin-bottom:10px">';
    if(permission == 1){
      std += '<tr>';
      std += '<td>';
      std += '<span><strong>Academic Year</strong></span>';
      std += '</td>';
      std += '<td class="select-editable-enquiry_acad_year" id="academic_year">'+enquiry_acad_year+'</td>';
      std += '</tr>';
    }

    std += '<tr>';
    std += '<td style="width: 40%;">';
    std += '<span><strong>Student Name </strong></span>';
    std += '</td>';
    std += '<td class="editable text-left" id="student_name">'+follow_enuiry.student_name+'</td>';
    std += '</tr>';
  <?php if(!in_array('parent_name', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td>';
    std += '<span><strong>Parent Name</strong></span>';
    std += '</td>';
    var pName = follow_enuiry.parent_name;
    if (follow_enuiry.parent_name == null) {
      pName = '';
    }
    std += '<td class="editable" id="parent_name">'+pName+'</td>';
    std += '</tr>';
  <?php endif ?>

    <?php if(!in_array('father_name', $disabled_fields)) { ?>
      std += '<tr>';
      std += '<td>';
      std += '<span><strong>Father Name</strong></span>';
      std += '</td>';
      var fathername = follow_enuiry.father_name;
    if (follow_enuiry.father_name == null) {
      fathername = '';
    }
      std += '<td class="editable" id="father_name">'+fathername+'</td>';
      std += '</tr>';
      <?php }  ?>
  <?php if(!in_array('mother_name', $disabled_fields)) :  ?>
      std += '<tr>';
      std += '<td>';
    std += '<span><strong>Mother Name</strong></span>';
    std += '</td>';
     var mothername = follow_enuiry.mother_name;
    if (follow_enuiry.mother_name == null) {
      mothername = '';
    }
    std += '<td class="editable" id="mother_name">'+mothername+'</td>';
    std += '</tr>';
  <?php endif ?>
  <?php if(!in_array('mobile_number', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td>';
    std += '<strong>Mobile Number </strong>';
    std += '</td>';
    var number = follow_enuiry.mobile_number;
    if (follow_enuiry.mobile_number == null) {
      number = '';
    }
    std += '<td class="editable" id="mobile_number">'+number+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('student_phone_number', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td>';
    std += '<strong>Studet Mobile Number </strong>';
    std += '</td>';
     var studentphonenumber = follow_enuiry.student_phone_number;
    if (follow_enuiry.student_phone_number == null) {
      studentphonenumber = '';
    }
    std += '<td class="editable" id="student_phone_number">'+studentphonenumber+'</td>';
    std += '</tr>';
  <?php endif ?>
  <?php if(!in_array('father_phone_number', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td>';
    std += '<strong>Father Mobile Number </strong>';
    std += '</td>';
    var fatherphonenumber = follow_enuiry.father_phone_number;
    if (follow_enuiry.father_phone_number == null) {
      fatherphonenumber = '';
    }
    std += '<td class="editable" id="father_phone_number">'+fatherphonenumber+'</td>';
    std += '</tr>';
  <?php endif ?>
  <?php if(!in_array('mother_phone_number', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td>';
    std += '<strong>Mother Mobile Number </strong>';
    std += '</td>';
    var motherphonenumber = follow_enuiry.mother_phone_number;
    if (follow_enuiry.mother_phone_number == null) {
      motherphonenumber = '';
    }
    std += '<td class="editable" id="mother_phone_number">'+motherphonenumber+'</td>';
    std += '</tr>';
  <?php endif ?>
  <?php if(!in_array('wtsapp_number', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td>';
    std += '<strong>Whatsapp Mobile Number </strong>';
    std += '</td>';
    var whatsappphonenumber = follow_enuiry.wtsapp_number;
    if (follow_enuiry.wtsapp_number == null) {
      whatsappphonenumber = '';
    }
    std += '<td class="editable" id="wtsapp_number">'+whatsappphonenumber+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('alternate_mobile_number', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td>';
    std += '<span><strong>Alternate Mobile Number </strong></span>';
    std += '</td>';
    alternate_mobile_number
    var alternate_mobile_number = follow_enuiry.alternate_mobile_number;
    if (follow_enuiry.alternate_mobile_number == null) {
      alternate_mobile_number = '';
    }
    std += '<td class="editable" id="alternate_mobile_number">'+alternate_mobile_number+'</td>';
    std += '</tr>';
    <?php endif ?>
    std += '<tr>';
    std += '<td>';
    std += '<span><strong>'+grade_or_course+'</strong></span>';
    std += '</td>';
    var className = follow_enuiry.class_name;
    if (follow_enuiry.class_name == null) {
      className = '';
    }

    var board_opted = follow_enuiry.board_opted;
    if (follow_enuiry.board_opted == null) {
      board_opted = '';
    }

    var current_curriculum = follow_enuiry.board;
    if (follow_enuiry.board == null) {
      current_curriculum = '';
    }

    var source = follow_enuiry.source;
    if (follow_enuiry.source == null) {
      source = '';
    }
    var message = follow_enuiry.message;
    if (follow_enuiry.message == null) {
      message = '';
    }

    var stdDob = follow_enuiry.std_dob;
    if (follow_enuiry.std_dob == null) {
      stdDob = '';
    }

    var stdCurrentSchool = follow_enuiry.student_current_school;
    if (follow_enuiry.student_current_school == null) {
      stdCurrentSchool = '';
    }

     var interested_in = follow_enuiry.interested_in;
    if (follow_enuiry.interested_in == null) {
      interested_in = '';
    }

    var current_country = follow_enuiry.current_country;
    if (follow_enuiry.current_country == null) {
      current_country = '';
    }

    var current_city = follow_enuiry.current_city;
    if (follow_enuiry.current_city == null) {
      current_city = '';
    }
    var father_name = follow_enuiry.father_name;
    if (follow_enuiry.father_name == null) {
      father_name = '';
    }
    var mother_name = follow_enuiry.mother_name;
    if (follow_enuiry.mother_name == null) {
      mother_name = '';
    }
    var student_phone_number = follow_enuiry.student_phone_number;
    if (follow_enuiry.student_phone_number == null) {
      student_phone_number = '';
    }
    var father_phone_number = follow_enuiry.father_phone_number;
    if (follow_enuiry.father_phone_number == null) {
      father_phone_number = '';
    }
    var mother_phone_number = follow_enuiry.mother_phone_number;
    if (follow_enuiry.mother_phone_number == null) {
      mother_phone_number = '';
    }
    var student_email_id = follow_enuiry.student_email_id;
    if (follow_enuiry.student_email_id == null) {
      student_email_id = '';
    }
    var mother_email_id = follow_enuiry.mother_email_id;
    if (follow_enuiry.mother_email_id == null) {
      mother_email_id = '';
    }
    
    var wtsapp_number = follow_enuiry.wtsapp_number;
    if (follow_enuiry.wtsapp_number == null) {
      wtsapp_number = '';
    }
    var residential_address = follow_enuiry.residential_address;
    if (follow_enuiry.residential_address == null) {
      residential_address = '';
    }
    var enquiry_combination = follow_enuiry.enquiry_combination;
    if (follow_enuiry.enquiry_combination == null) {
      enquiry_combination = '';
    }
    var enquiry_additional_coaching = follow_enuiry.enquiry_additional_coaching;
    if (follow_enuiry.enquiry_additional_coaching == null) {
      enquiry_additional_coaching = '';
    }
    var boarding_type = follow_enuiry.boardingType;
    if (follow_enuiry.boardingType == null) {
      boarding_type = '';
    }
    var is_transfer_certificate_available = follow_enuiry.is_transfer_certificate_available;
    if (follow_enuiry.is_transfer_certificate_available == null) {
      is_transfer_certificate_available = '';
    }
    var previous_academic_report = follow_enuiry.previous_academic_report;
    if (follow_enuiry.previous_academic_report == null) {
      previous_academic_report = '';
    }
    var parent_occupation = follow_enuiry.parent_occupation;
    if (follow_enuiry.parent_occupation == null) {
      parent_occupation = '';
    }
    var additional_education_needs = follow_enuiry.additional_education_needs;
    if (follow_enuiry.additional_education_needs == null) {
      additional_education_needs = '';
    }

    var sibling_class = follow_enuiry.sibling_class;
    if (follow_enuiry.sibling_class == null) {
      sibling_class = '';
    }
    var sibling_name = follow_enuiry.sibling_name;
    if (follow_enuiry.sibling_name == null) {
      sibling_name = '';
    }
    var transportation_facility = follow_enuiry.transportation_facility;
    if (follow_enuiry.transportation_facility == null) {
      transportation_facility = '';
    }


    var got_to_know_by = follow_enuiry.got_to_know_by;
    if (follow_enuiry.got_to_know_by == null) {
      got_to_know_by = '';
    }
    var displaylead_status = follow_enuiry.lead_status;
    if (follow_enuiry.lead_status == null) {
      displaylead_status = '';
    }
    var enabledAddtionalAmt = '<?php echo $this->settings->getSetting('show_enquiry_advance_amount_paid') ?>';
    if (enabledAddtionalAmt == 1) {
      var advance_amount = follow_enuiry.advance_amount_paid;
      if (follow_enuiry.advance_amount_paid == null) {
        advance_amount = '0';
      }
    }
    

    std += '<td class="select-editable" id="class_name">'+className+'</td>';
    std += '</tr>';

    <?php if(!in_array('board', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td>';
    std += '<span><strong>Current Curriculum</strong></span>';
    std += '</td>';
    std += '<td class="select-editable-board" id="board">'+current_curriculum+'</td>';
    std += '</tr>';
    <?php endif ?>

    <?php if(!in_array('board_opted', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td>';
    std += '<span><strong>Curriculum Opted</strong></span>';
    std += '</td>';
    std += '<td class="select-editable-board" id="board_opted">'+board_opted+'</td>';
    std += '</tr>';
    <?php endif ?>
    
    std += '<tr>';
    std += '<td><strong>Date of Birth</strong></td>';
    std += '<td class="select-editable_date" id="student_dob">'+stdDob+'</td>';


    std += '</tr>';
   
    std += '<tr>';
    std += '<td><strong>Gender</strong></td>';
    std += '<td class="select-editable-gender" id="gender">'+follow_enuiry.s_gender+'</td>';
    std += '</tr>';

    // std += '<tr>';
    // std += '<td><strong>Academic Year </strong></td>';
    // std += '<td>'+academic_year+'</td>';
    // std += '</tr>';

  <?php if(!in_array('email', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td><strong>Email</strong></td>';
    var emailid = follow_enuiry.email;
    if (follow_enuiry.email == null) {
      emailid = '';
    }
    std += '<td class="editable" id="email">'+emailid+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('category', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Category</strong></td>';
    var category = follow_enuiry.category;
    if (follow_enuiry.category == null) {
      category = '';
    }
    std += '<td class="" id="category">'+category+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('caste', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Caste</strong></td>';
    var caste = follow_enuiry.caste;
    if (follow_enuiry.caste == null) {
      caste = '';
    }
    std += '<td  id="caste">'+caste+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('student_email_id', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td><strong>Student Email Id</strong></td>';
    var studentemail = follow_enuiry.student_email_id;
    if (follow_enuiry.student_email_id == null) {
      studentemail = '';
    }
    std += '<td class="editable" id="student_email_id">'+studentemail+'</td>';
    std += '</tr>';
    std += '<tr>';
  <?php endif ?>
  <?php if(!in_array('mother_email_id', $disabled_fields)) :  ?>

    std += '<td><strong>Mother Email Id</strong></td>';
    var motheremail = follow_enuiry.mother_email_id;
    if (follow_enuiry.mother_email_id == null) {
      motheremail = '';
    }
    std += '<td class="editable" id="mother_email_id">'+motheremail+'</td>';
    std += '</tr>';
  <?php endif ?>
  <?php if(!in_array('father_email_id', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td><strong>Father Email Id</strong></td>';
    var fatheremail = follow_enuiry.father_email_id;
    if (follow_enuiry.father_email_id == null) {
      fatheremail = '';
    }
    std += '<td class="editable" id="father_email_id">'+fatheremail+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('examination_passed', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Examination Passed</strong></td>';
    var examination_passed = follow_enuiry.examination_passed;
    if (follow_enuiry.examination_passed == null) {
      examination_passed = '';
    }
    std += '<td class="editable" id="examination_passed">'+examination_passed+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('year_of_passing', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Year of Passing</strong></td>';
    var year_of_passing = follow_enuiry.year_of_passing;
    if (follow_enuiry.year_of_passing == null) {
      year_of_passing = '';
    }
    std += '<td class="editable" id="year_of_passing">'+year_of_passing+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('total_marks', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Total Marks</strong></td>';
    var total_marks = follow_enuiry.total_marks;
    if (follow_enuiry.total_marks == null) {
      total_marks = '';
    }
    std += '<td class="editable" id="total_marks">'+total_marks+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('marks_in_percentage', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Total Marks in Percentage</strong></td>';
    var marks_in_percentage = follow_enuiry.marks_in_percentage;
    if (follow_enuiry.marks_in_percentage == null) {
      marks_in_percentage = '';
    }
    std += '<td class="editable" id="marks_in_percentage">'+marks_in_percentage+'</td>';
    std += '</tr>';
  <?php endif ?>
    
    if (follow_enuiry.reporting_status) {
      std += '<tr>';
      std += '<td><strong>Reporting Status</strong></td>';
      std += `<td><strong>${follow_enuiry.reporting_status}</strong></td>`;
    }
    std += '<tr>';
    std += '<td><strong>Status</strong></td>';
    std += `<td><strong>${follow_enuiry.status}</strong></td>`;
    std += '</tr>';
    std += '<tr>';
    std += '<td><strong>Lead Status</strong></td>';
    std += '<td><strong>'+displaylead_status+'</strong></td>';
    std += '</tr>';
    std += '<tr>';
    std += '<td><strong>Created Date </strong></td>';
    std += '<td>'+follow_enuiry.createdEnquiry+'</td>';
    std += '</tr>';

    std += '<tr>';
    std += '<td ><strong>Previous/Current School Name</strong></td>';
    std += '<td class="editable" id="student_current_school">'+stdCurrentSchool+'</td>';
    std += '</tr>';
  
    <?php if(!in_array('currently_studying', $disabled_fields)) :  ?>
    var currently_studying = follow_enuiry.currently_studying;
    if (follow_enuiry.currently_studying == null) {
      currently_studying = '';
    }
    std += '<tr>';
    std += '<td ><strong>Current Grade</strong></td>';
    std += '<td class="editable" id="currently_studying">'+currently_studying+'</td>';
    std += '</tr>';
    
    <?php endif ?>
    
    
    std += '<tr>';
    std += '<td><strong>Source </strong></td>';
    std += '<td>'+source+'</td>';
    std += '</tr>';

    <?php if(!in_array('referred_by', $disabled_fields)) :  ?>
      var reffered_by = follow_enuiry.referred_by_student+' - '+follow_enuiry.referred_by_class_setion+ ' ('+follow_enuiry.referred_by+')';
      if(follow_enuiry.referred_by == null || follow_enuiry.referred_by== ''){
        reffered_by = '-';
      }
    std += '<tr>';
    std += '<td><strong>Referred By</strong></td>';
    std += '<td class="" id="">'+reffered_by+'</td>';
    std += '</tr>';
  <?php endif ?>

    std += '<tr>';
    std += '<td><strong>Curriculam Interested In </strong></td>';
    std += '<td class="editable" id="interested_in">'+interested_in+'</td>';
    std += '</tr>';

    <?php if(!in_array('current_country', $disabled_fields)) :  ?>
     std += '<tr>';
    std += '<td><strong>Student\'s Current Country</strong></td>';
    std += '<td class="select-editable-currentcountry" id="current_country"> '+current_country+'</td>';
    std += '</tr>';
    <?php endif ?>

    <?php if(!in_array('current_city', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Student\'s Current City</strong></td>';
    std += '<td class="editable" id="current_city">'+current_city+'</td>';
    std += '</tr>';
    <?php endif ?>

    std += '<tr>';
    std += '<td><strong>Preferred Boarding Type</strong></td>';
    std += '<td class="select-editable-bordingtype" id="boarding_type">'+boarding_type+'</td>';
    std += '</tr>';

    <?php if(!in_array('got_to_know_by', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Known by </strong></td>';
    std += '<td class="select-editable-gottoknow" id="got_to_know_by">'+got_to_know_by+'</td>';
    std += '</tr>';
    <?php endif ?>

    <?php if(!in_array('message', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Message</td>';
    std += '<td>'+message+'</td>';
    std += '</tr>';
    <?php endif ?>

    <?php if(!in_array('is_transfer_certificate_available', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Is Transfer Certificate Available? </strong></td>';
    std += '<td class="select-editable-tcertificate" id="is_transfer_certificate_available">'+is_transfer_certificate_available+'</td>';
    std += '</tr>';
    <?php endif ?>

    <?php if(!in_array('previous_academic_report', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Previous Academic Reports</strong></td>';
    std += '<td class="select-editable-previousacademic" id="previous_academic_report">'+previous_academic_report+'</td>';
    std += '</tr>';
    <?php endif ?>

    <?php if(!in_array('parent_occupation', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Parent Occupation</strong></td>';
    std += '<td class="editable" id="parent_occupation">'+parent_occupation+'</td>';
    std += '</tr>';
    <?php endif ?>

     <?php if(!in_array('father_occupation', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td><strong>Father Occupation </strong></td>';
    var fatheroccupation = follow_enuiry.father_occupation;
    if (follow_enuiry.father_occupation == null) {
      fatheroccupation = '';
    }
    std += '<td class="editable" id="father_occupation">'+fatheroccupation+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('mother_occupation', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td><strong>Mother Occupation </strong></td>';
    var motheroccupation = follow_enuiry.mother_occupation;
    if (follow_enuiry.father_occupation == null) {
      motheroccupation = '';
    }
    std += '<td class="editable" id="mother_occupation">'+motheroccupation+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('has_sibling', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Student Have Sibling</strong></td>';
    var has_sibling = follow_enuiry.has_sibling;
    var hasSibling = '';
    if (has_sibling == 1) {
      hasSibling = 'Yes';
    }else{
      hasSibling = 'No';
    }
    std += '<td class="" id="">'+hasSibling+'</td>';
    std += '</tr>';

    if(hasSibling == 'Yes'){
    std += '<tr>';
    std += '<td><strong>Sibling Studying In</strong></td>';
    std += '<td class="" id="">'+follow_enuiry.where_is_sibling+'</td>';
    std += '</tr>';

    std += '<tr>';
    std += '<td><strong>Sibling Class</strong></td>';
    std += '<td class="" id="">'+follow_enuiry.sibling_class+'</td>';
    std += '</tr>';
    }

  var siblingNameEnabled = <?php echo (!in_array('sibling_name', $disabled_fields)) ? 'true' : 'false'; ?>;
   if(hasSibling == 'Yes' || siblingNameEnabled) { 
    std += '<tr>';
    std += '<td><strong>Sibling Name</strong></td>';
    std += '<td class="" id="sibling_name">'+follow_enuiry.sibling_name+'</td>';
    std += '</tr>';
  } 
  <?php endif ?>

    <?php if(!in_array('university', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td><strong>University </strong></td>';
    var universityname = follow_enuiry.university;
    if (follow_enuiry.university == null) {
      universityname = '';
    }
    std += '<td class="editable" id="university">'+universityname+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('additional_education_needs', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Additional Education Needs</strong></td>';
    std += '<td class="editable" id="additional_education_needs">'+additional_education_needs+'</td>';
    std += '</tr>';
    <?php endif ?>

    var combinations = '<?php echo $this->settings->getSetting('enquiry_combination') ?>';
    if (combinations) {
      std += '<tr>';
      std += '<td><strong>Combination</strong></td>';
      std += '<td class="select-editable-combination" id="enquiry_combination">'+enquiry_combination+'</td>';
      std += '</tr>';

    }
    var addCoaching = '<?php echo $this->settings->getSetting('enquiry_additional_coaching') ?>';
    if (addCoaching) {
      std += '<tr>';
      std += '<td><strong>Additional Coaching</strong></td>';
      std += '<td class="select-editable-additionalcoaching" id="enquiry_additional_coaching">'+enquiry_additional_coaching+'</td>';
      std += '</tr>';
    }
    
    <?php if(!in_array('residential_address', $disabled_fields)) :  ?>
    std += '<tr>';
    std += '<td><strong>Residential Address</strong></td>';
    std += '<td class="editable" id="residential_address">'+residential_address+'</td>';
    std += '</tr>';
    <?php endif ?>

    if (enabledAddtionalAmt == 1) {
      std += '<tr>';
      std += '<td><strong>Advance Amount</td>';
      std += '<td class="editable text-left" id="advance_amount_paid">'+advance_amount+'</td>';
      std += '</tr>';
    }

    <?php if(!in_array('reason_for_leaving_school', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td><strong>Reasons For Leaving School</strong></td>';
    var reasonforleavingschool = follow_enuiry.reason_for_leaving_school;
    if (follow_enuiry.reason_for_leaving_school == null) {
      reasonforleavingschool = '';
    }
    std += '<td class="editable" id="reason_for_leaving_school">'+reasonforleavingschool+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('transportation_facility', $disabled_fields)) :  ?>

    std += '<tr>';
    std += '<td><strong>Transportation Facility</strong></td>';
    std += '<td class="select-editable-transport" id="transportation_facility">'+follow_enuiry.transportation_facility+'</td>';
    std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('utm_campaign', $disabled_fields)) :  ?>
  std += '<tr>';
  std += '<td><strong>UTM Campaign</strong></td>';
  std += '<td class="select-editable-utm_campaign" id="utm_campaign">'+follow_enuiry.utm_campaign+'</td>';
  std += '</tr>';
  <?php endif ?>
  std += '</table>';

  std += '<h5>UTM Parameters</h5>'
  std += '<table class="table table-bordered" style="margin-bottom:10px">';
  <?php if (!in_array('page_url', $disabled_fields)): ?>

std += '<tr>';
std += '<td><strong>Page URL</strong></td>';
var page_url = follow_enuiry.page_url;
if (follow_enuiry.page_url == null) {
  page_url = '';
}
std += '<td class="" id="page_url">' + page_url + '</td>';
std += '</tr>';
<?php endif ?>

  <?php if(!in_array('utm_medium', $disabled_fields)) :  ?>
  std += '<tr>';
  std += '<td><strong>UTM Medium</strong></td>';
  std += '<td class="select-editable-utm_medium" id="utm_medium">'+follow_enuiry.utm_medium+'</td>';
  std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('utm_source', $disabled_fields)) :  ?>
  std += '<tr>';
  std += '<td><strong>UTM Source</strong></td>';
  std += '<td class="select-editable-utm_source" id="utm_source">'+follow_enuiry.utm_source+'</td>';
  std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('utm_content', $disabled_fields)) :  ?>
  std += '<tr>';
  std += '<td><strong>UTM Content</strong></td>';
  std += '<td class="select-editable-utm_content" id="utm_content">'+follow_enuiry.utm_content+'</td>';
  std += '</tr>';
  <?php endif ?>

  <?php if(!in_array('utm_term', $disabled_fields)) :  ?>
  std += '<tr>';
  std += '<td><strong>UTM Team</strong></td>';
  std += '<td class="select-editable-utm_term" id="utm_term">'+follow_enuiry.utm_term+'</td>';
  std += '</tr>';
  <?php endif ?>

  
  <?php if(!in_array('utm_adgroup', $disabled_fields)) :  ?>
  std += '<tr>';
  std += '<td><strong>UTM AD Group</strong></td>';
  std += '<td class="select-editable-utm_adgroup" id="utm_adgroup">'+(follow_enuiry.utm_adgroup || '')+'</td>';
  std += '</tr>';
  <?php endif ?>
   

    std += '</table>';
    return std;
 }
</script>

<style type="text/css">
   
    .list-group-item{
        margin-bottom: 1px;
    }
    .confirmBootboxClass{
      width:30%;
      margin:auto;
    }
</style>

<div class="modal fade" id="newSource" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width: 50%; margin-left:25%" >
            <div class="modal-header">
                <h4 class="modal-title">Add Closure Reason</h4>
               <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
                <input type="text" name="newClosureItem" id="newClosureItem" onkeydown="return /[a-z ]/i.test(event.key)" class="form-control" placeholder="Enter new closure reason" value=""><br>
                </div>
                <div class="modal-footer">
                <button type="submit" class="col-md-2 btn btn-primary" data-dismiss="modal" id="newClosureReason" name="newClosureReason">Add</button>
              </div>
            
        </div>
    </div>
</div>

<div class="modal fade" id="newSource1" role="dialog" style="vertical-align: middle;">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width: 50%; margin-left: 25%;" >
            <div class="modal-header">
                <h4 class="modal-title">Add Lead status</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
                <input type="text" name="newClosureItem1" id="newClosureItem1" class="form-control" placeholder="Enter new lead-status" value=""><br>
              </div>
              <div class="modal-footer">
                <button type="submit" class="col-md-2 btn btn-primary" data-dismiss="modal" id="newClosureReason1" name="newClosureReason1">Add</button>
              </div>  
            </div>
        </div>
    </div>
</div>

<style type="text/css">
  .label-default,.label-success,.label-danger {
    cursor: pointer;
  }
  .editable, .select-editable {
    cursor: pointer;
    position: relative;
    margin-left: 5px;
  }
  .editable:hover, .select-editable:hover {
    font-weight: 700;
  }
  .editable::before, .select-editable::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }

  .select-editable-board, .select-editable-transport,.select-editable-gender, .select-editable-tcertificate, .select-editable-bordingtype, .select-editable-gottoknow, .select-editable-previousacademic, .select-editable-combination, .select-editable-additionalcoaching, .select-editable-currentcountry {
    cursor: pointer;
    position: relative;
    margin-left: 5px;
  }
  .select-editable-currentcountry:hover, .select-editable-currentcountry:hover {
    font-weight: 700;
  }
  .select-editable-additionalcoaching:hover, .select-editable-additionalcoaching:hover {
    font-weight: 700;
  }
  .select-editable-combination:hover, .select-editable-combination:hover {
    font-weight: 700;
  }
  .select-editable-previousacademic:hover, .select-editable-previousacademic:hover {
    font-weight: 700;
  }
  .select-editable-gottoknow:hover, .select-editable-gottoknow:hover {
    font-weight: 700;
  }
  .select-editable-board:hover, .select-editable-board:hover ,.select-editable-transport:hover{
    font-weight: 700;
  }

  .select-editable-gender:hover, .select-editable-gender:hover {
    font-weight: 700;
  }
  .select-editable-tcertificate:hover, .select-editable-tcertificate:hover {
    font-weight: 700;
  }
  .select-editable-bordingtype:hover, .select-editable-bordingtype:hover {
    font-weight: 700;
  }


  .select-editable-currentcountry::before, .select-editable-currentcountry::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-additionalcoaching::before, .select-editable-additionalcoaching::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-combination::before, .select-editable-combination::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-previousacademic::before, .select-editable-previousacademic::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-gottoknow::before, .select-editable-gottoknow::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-bordingtype::before, .select-editable-bordingtype::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-board::before, .select-editable-board::before,.select-editable-transport::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-gender::before, .select-editable-gender::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .select-editable-gender::before, .select-editable-tcertificate::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
  .list-group-item.active{
    background-color: #ebf3f9;
    border-color: #ebf3f9;
    color: #737373;
  }
  .list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
    background: #ebf3f9;
    color: #737373;
  }
  .list-group-item{
    border:none;
  }

   .new_circleShape_res {
/*    background-color:red;*/
  display:inline-block;
  height:12px;
  width:12px;
  margin:auto;
  text-align:left;
   }

</style>

<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jeditor/editable.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jeditor/editable_date.js"></script>
<script type="text/javascript">
var ask_closure_reason = 0;
var classes = {};
classes[0] = 'Select Class';
var enquiry_id = 0;
$(document).ready(function(){
  $('.date_pick').datepicker({
    format: 'd-m-yyyy',
    "autoclose": true,
    startDate: new Date()
  });
});
function after_call(classArray, ask_closure_reason, boards,gender,is_transfer_certificate_available, boarding_type, got_to_know_by, previous_academic_report, enquiry_combination, enquiry_additional_coaching,current_country, acad_year_settings) {

    ask_closure_reason = ask_closure_reason;
    enquiry_id = $("#enquiry_id").val();
    var cs = classArray;
    for (var i = 0; i < cs.length; i++) {
        classes[cs[i].id] = cs[i].class_name;
    }
    var boardsArray = {};
    for (var b in boards){
       boardsArray[boards[b]] = boards[b];
    }
    var transportArray = {};
    for (var b in boards){
      transportArray['Yes'] = 'Yes';
      transportArray['No'] = 'No';
    }
    var genderArray = {};
    for (var a in gender){
      genderArray = gender;
    }



    var currentcountryArray = {};
    for (var y in current_country){
      currentcountryArray[''] = 'Select Option';
      currentcountryArray[current_country[y]] = current_country[y];
    }

    var acad_year_settingsArray = {};
    for (var y in acad_year_settings){
      acad_year_settingsArray[acad_year_settings[y].id] = acad_year_settings[y].acad_year;
    }

    var s_tcertificateArray = {};
    for (var c in is_transfer_certificate_available){
      s_tcertificateArray[is_transfer_certificate_available[c]] = is_transfer_certificate_available[c];
    }

    var boarding_typeArray = {};
    for (var d in boarding_type){
      boarding_typeArray[''] = 'Select Option';
      boarding_typeArray[boarding_type[d]] = boarding_type[d];
    }

    var got_toknow_by = $.parseJSON(got_to_know_by);
    var gottoknowArray = {};
    for (var e in got_toknow_by){
      gottoknowArray[''] = 'Select Option';
      gottoknowArray[got_toknow_by[e]] = got_toknow_by[e];
    }

    var previousacademicArray = {};
    for (var f in previous_academic_report){
      previousacademicArray = previous_academic_report;
    }
    if (enquiry_combination !='') {
      var enquirycombination = $.parseJSON(enquiry_combination);
      var combinationArray = {};
      for (var g in enquirycombination){
        combinationArray[''] = 'Select Option';
        combinationArray[enquirycombination[g]] = enquirycombination[g];
      }
    }
    if (enquiry_additional_coaching !='') {
      var enquiryadditionalcoaching = $.parseJSON(enquiry_additional_coaching);
      var addintionalcoachingArray = {};
      for (var h in enquiryadditionalcoaching){
        addintionalcoachingArray[''] = 'Select Option';
        addintionalcoachingArray[enquiryadditionalcoaching[h]] = enquiryadditionalcoaching[h];
      }
    }
   
    
     $(".select-editable_date").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        type   : "datepicker",
        submit : 'OK',
        datepicker : {
            format: "dd-mm-yyyy"
        },
        tooltip : "Click to edit...",
        inputcssclass: 'form-control',
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

    $(".editable").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });
  

    $(".select-editable").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : classes,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return classes[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

    $(".select-editable-board").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : boardsArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return boardsArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

    $(".select-editable-transport").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : transportArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return transportArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

    $(".select-editable-gender").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : genderArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return genderArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

     $(".select-editable-tcertificate").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : s_tcertificateArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return s_tcertificateArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

    $(".select-editable-enquiry_acad_year").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : acad_year_settingsArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return acad_year_settingsArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

     $(".select-editable-currentcountry").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : currentcountryArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return currentcountryArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

     $(".select-editable-bordingtype").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : boarding_typeArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return boarding_typeArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

     $(".select-editable-gottoknow").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : gottoknowArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return gottoknowArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

     $(".select-editable-previousacademic").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : previousacademicArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return previousacademicArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

     $(".select-editable-combination").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : combinationArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return combinationArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });

     $(".select-editable-additionalcoaching").editable("<?php echo site_url('enquiry/enquiry_staff/updateEnquiryData'); ?>", {
        tooltip : "Click to edit...",
        type   : "select",
        data : addintionalcoachingArray,
        indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        intercept: function(result, status) {
          return addintionalcoachingArray[result.trim()];
        },
        submitdata : {'enquiry_id': enquiry_id},
        width:'100%'
    });




  $("#follow_status").change(function(){
    var status = $(this).val();
    if(ask_closure_reason == 1) {
      if(status == 'Closed-not interested') {
        $("#closure-reason").show();
        $("#closure_reason").prop('required', true);
        
      } else {
        $("#closure-reason").hide();
        $('#closure_reason').val('');
        $("#closure_reason").prop('required', false);
      }
      
    }
  });

}
function editData(id) {
  $("#"+id).show();
  $("#"+id+"_display").hide();
  $("#"+id+"_edit").hide();
  $("#"+id+"_save").show();
  $("#"+id+"_cancel").show();
}

function saveData(id, type='text') {
  var val = $("#"+id).val();
  if(type == 'select') {
    $("#"+id+"_display").html($("#"+id+" option:selected").text());
  } else {
    $("#"+id+"_display").html(val);
  }
  $("#"+id+"_display").show();
  $("#"+id).hide();
  $("#"+id+"_edit").show();
  $("#"+id+"_save").hide();
  $("#"+id+"_cancel").hide();
  saveValue(id, val);
}

function cancelData(id) {
  $("#"+id+"_display").show();
  $("#"+id).hide();
  $("#"+id+"_edit").show();
  $("#"+id+"_save").hide();
  $("#"+id+"_cancel").hide();
}

function saveValue(id, value) {
  var enquiry_id = $("#enquiry_id").val();
  $.ajax({
    url: '<?php echo site_url('enquiry/enquiry_staff/saveFieldValue'); ?>',
    data: {'field_name': id,'value':value, 'enquiry_id': enquiry_id},
    type: "post",
    success: function (data) {
      var data = JSON.parse(data);
    },
    error: function (err) {
      console.log(err);
    }
  });
}

$("#newClosureReason").click(function(){
    var newClosureItem = $("#newClosureItem").val();
    if (newClosureItem != "") {
        $('#closure_reason').append('<option value="'+ newClosureItem.charAt(0).toUpperCase() + newClosureItem.slice(1) +'" selected="selected">'+ newClosureItem +'</option>');
        $("#newClosureItem").val("");
    } else {
        alert('Enter a valid value');
    }
});

$("#newClosureReason1").click(function(){
    var newClosureItem1 = $("#newClosureItem1").val();
    if (newClosureItem1 != "") {
        $('#lead_status').append('<option value="'+ newClosureItem1.charAt(0).toUpperCase() + newClosureItem1.slice(1) +'" selected="selected">'+ newClosureItem1 +'</option>');
        $("#newClosureItem1").val("");
    } else {
        alert('Enter a valid value');
    }
});





function delete_enquiry(id) {
    bootbox.confirm({
    title : "Enquiry form ",  
    message: "Are you sure you want to delete this enquery follow up ? ",
    buttons: {
        confirm: {
            label: 'Yes',
            className: 'btn-success'
        },
        cancel: {
            label: 'No',
            className: 'btn-danger'
        }
    },
    callback: function (result) {
      if(result) { 
        url = '<?php echo site_url('enquiry/enquiry_staff/delete_follow_up/') ?>'+id;
        location.reload();         
      }
    }
  });
}

function change_followup_action() {
  var followup_action = $('#followup_action').val();
  if(followup_action ==='Email') {
    $('#emailpop').show();
    $('#sms').hide();
    $('#email_content').html('');
    $('#sms_content').html('');
  }else if(followup_action ==='SMS'){
    $('#emailpop').hide();
    $('#sms').show();
    $('#email_content').html('');
    $('#sms_content').html('');
  }else{
    $('#emailpop').hide();
    $('#sms').hide();
    $('#email_content').html('');
    $('#sms_content').html('');
  }
}
function get_templ_content_and_forms_email() {
  var emailtemplateId = $('#emailtemplateId').val();
  if (emailtemplateId=='') {
    $('#email_content').html('');
  }
  var enquiry_id =  $("#enquiry_id").val();
  $.ajax({
      url: '<?php echo site_url('enquiry/enquiry_staff/get_enquiry_email_content'); ?>',
      type: 'POST',
      data: {'enquiry_id':enquiry_id,'emailtemplateId':emailtemplateId},
  })
  .done(function(data){
    if (data==0) {
      $('#email_content').html('');
      return false;
    }
    var reData = $.parseJSON(data);
    construct_emails(reData, enquiry_id);
  });
}

function construct_emails(reData, enquiry_id) {

  var emailSubject = reData.email_subject;
  if (reData.email_subject == undefined)
    emailSubject = 'Email subject not added';

  var registered_email = reData.registered_email;
  if (reData.registered_email ==undefined) 
    registered_email = 'From email not assigned';
  var content = reData.content;
  content = content.replace('%%student_name%%',reData.to_email.student_name);
  content = content.replace('%%class_name%%',reData.to_email.class_name);
  if(reData.to_email.enquiry_number != null){
    content = content.replace('%%enquiry_number%%',reData.to_email.enquiry_number);
  }else{
    content = content.replace('%%enquiry_number%%','');
  }
  if(reData.to_email.parent_name != null){
    content = content.replace('%%parent_name%%',reData.to_email.parent_name);
  }else{
    content = content.replace('%%parent_name%%','');
  }
  
  content = content.replace('%%created_date%%',reData.to_email.created_date);
  content = content.replace('%%created_time%%',reData.to_email.created_time);
  // console.log(content);
  var display = 'block';
  if (reData.content == undefined) 
    display = 'none';

  var html = '';
  html +='<div class="form-group">';
  html +='<label class="col-md-3 control-label">From: </label>';
  html +='<div class="col-md-8">';
  html +='<input readonly type="text" value="'+registered_email+'" name="registered_email" id="registered_email" class="form-control">';
  html +='</div>';
  html +='</div>';

  html +='<div class="form-group">';
  html +='<label class="col-md-3 control-label">To: </label>';
  html +='<div class="col-md-8">';
  html +='<input type="text" name="to_mails" id="to_emails" class="form-control" value="'+reData.to_email.parent_email+','+reData.to_email.father_email_id+','+reData.to_email.mother_email_id+'">';
  html +='<span class="help-block">To send email to multiple recipients, enter their email ids as a comma-separated list. Eg: <EMAIL>, <EMAIL></span>';
  html +='</div>';
  html +='</div>';

  html +='<div class="form-group">';
  html +='<label class="col-md-3 control-label">Subject: </label>';
  html +='<div class="col-md-8" style="margin-bottom:12px;">';
  html +='<input type="text" value="'+emailSubject+'" name="email_subject" id="email_subject" class="form-control">';
  html +='</div>';
  html +='</div>';
  html +='<div class="form-group" style="display:'+display+'">';
   html +='<label class="col-md-3 control-label">Message: </label>';
  html +='<div class="col-md-8">';
  html +='<textarea name="template_content" id="template_content" class="summernote template">'+content+'</textarea>';
  html +='</div>';
  html +='</div>';
  $('#email_content').html('');
  $('#email_content').html(html);
  $('.template').summernote({
    tabsize: 2,
    height: 200
  });
}   

function get_templ_content_and_forms_sms() {
  var smstemplateId = $('#smstemplateId').val();
  var enquiry_id =  $("#enquiry_id").val();
  $('#remarksFollowup').html('');
  $.ajax({
      url: '<?php echo site_url('enquiry/enquiry_staff/get_enquiry_sms_content'); ?>',
      type: 'POST',
      data: {'enquiry_id':enquiry_id,'smstemplateId':smstemplateId},
  })
  .done(function(data){
    // console.log(data);
    if (data==0) {
      $('#sms_content').html('');
      return false;
    }
    var reData = $.parseJSON(data);
    construct_sms(reData, enquiry_id);
  });
}

function construct_sms(reData, enquiry_id) {
  var sms_content = reData.content;
  var sms_content_remarks = reData.content;
  //console.log(reData);
  sms_content = sms_content.replace('%%student_name%%',reData.to_email.student_name);
  sms_content = sms_content.replace('%%grade_applied_for%%',reData.to_email.className);
  sms_content_remarks = sms_content_remarks.replace('%%grade_applied_for%%',reData.to_email.className);
  sms_content_remarks = sms_content_remarks.replace('%%grade_applied_for%%',reData.to_email.className);

  sms_content = sms_content.replace(/\{\#var\#\}/g, '<input type="text" name="template_mappings[]" class="template-mappings" value="" maxlength="50" style="border: none; border-bottom: 1px solid #000;">');

  // sms_content_remarks = sms_content_remarks.replace(/\{\#var\#\}/g, '<span id="mapping_string_enter"></span>');

  var sms = '';
  sms +='<input  type="hidden" value="'+enquiry_id+'" name="enquiry_id" id="enquiry_id" class="form-control">';
  sms +='<div class="form-group">';
  sms +='<label class="col-md-3 control-label">Message : </label>';
  sms +='<div class="col-md-8" id="enquiry_sms_content" style="margin-bottom:28px;">';
  // sms +='<textarea name="template_content" rows="4" id="template_content" class="form-control">'+sms_content+'</textarea>';
  sms += sms_content;
  sms +='</div>';
  sms +='<a onclick="copytoremarkssms()"  class="btn btn-info btn-sm" style="position: absolute;bottom: 0;right: 10%;" href="javascript:void(0)">Copy to remarks</a>';
  $('#sms_content').html('');
  $('#sms_content').html(sms);
  // $('#remarksFollowup').val(sms_content_remarks);
  $('#sms_template_enquiry').val(sms_content_remarks);
}     
</script>

<script type="text/javascript">

function copytoremarkssms() {
  let copy_remarks_content = $('#sms_template_enquiry').val();
  document.querySelectorAll('.template-mappings').forEach(function(input) {
    copy_remarks_content = copy_remarks_content.replace("{#var#}", input.value);
  });
  
  $('#enquiry_remarks_message').val(copy_remarks_content);
  $('#remarksFollowup').val(copy_remarks_content);
}

  function email_sms_popup_details(follow_up_type,follow_up_action,source_id) {
    $.ajax({
      url: '<?php echo site_url('admission_process/get_email_sms_pop_details'); ?>',
      data: {'follow_up_type': follow_up_type,'follow_up_action':follow_up_action,'source_id':source_id},
      type: "post",
      success: function (data) {
        var data = JSON.parse(data);
        $('#dynamic-content').html(construct_table(data));
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function construct_table(data) {
    var html ='';
    html+='<tr>';
    html+='<td><strong>Follow up action: </strong>'+data.follow_up_action+'</td>';
    html+='</tr>';
    if (data.follow_up_action==='Email') {
      html+='<tr>';
      html+='<td><strong>Email Ids: </strong>'+data.email_ids+'</td>';
      html+='</tr>';
      html+='<tr>';
      html+='<td><strong>Email Subject: </strong>'+data.email_subject+'</td>';
      html+='</tr>';
    }
    if (data.follow_up_action==='Email' || data.follow_up_action==='SMS') {
      html+='<tr>';
      html+='<td><strong>Message: </strong>'+data.template_content+'</td>';
      html+='</tr>';
    }
    html+='<tr>';
    html+='<td><strong>Remarks: </strong>'+data.remarks+'</td>';
    html+='</tr>';
    return html;
  }

  function submit_followUp() {
    var reporting_status = $('#reporting_status').val();
    var create_admission_record = '<?php echo $this->settings->getSetting('enquiry_create_admission_record') ?>';
    var followup_action = $('#followup_action').val();
    var follow_status = $('#follow_status').val();
    var template_name = $('#emailtemplateId').val();
    var $form = $('#follow-up-action');
    if ($form.parsley().validate()){
      if(create_admission_record == 1 && reporting_status == 1 ){
        generate_admission_form(followup_action,follow_status);
      }else{
        submit_followup_data()
      }
    }
   }
   
   function submit_followup_data(){
    var enquiry_id = $('#enquiry_id').val();
    var mobile_number = $('#mobile_number').html();

    let content = $('#sms_template_enquiry').val();
    document.querySelectorAll('.template-mappings').forEach(function(input) {
      content = content.replace("{#var#}", input.value);
    });

    var form = $('#follow-up-action')[0];
        var formData = new FormData(form);
        formData.append('mobile_number',mobile_number);
        formData.append('sms_enquiry',content);
        formData.append('template_content',$('#template_content').code());

        $('#submitbutton').prop('disabled',true).html('Please wait..');
        $.ajax({
          url: '<?php echo site_url('enquiry/enquiry_staff/follow_up_details_insert'); ?>',
          type: 'post',
          data: formData,
          // async: false,
          processData: false,
          contentType: false,
          // cache : false,
          success: function(data) {
            //console.log(data);
            $('#submitbutton').prop('disabled',false).html('Submit');
            if (data == 1) {
              $('#success').show('slow');
              setTimeout(function() {
                $("#success").hide();
              },1000);
              get_enquiry_details(enquiry_id);
              //console.log($('#pendingdates').attr('id'));
            }else{
              $('#error').hide();
              get_enquiry_details(enquiry_id);
            }
          }
      });
   }

   function send_admission_link(title_name = ''){
    var enquiry_id = $('#enquiry_id').val();
    var follow_status = $('#follow_status').val();
      bootbox.confirm({
      title: "Do you want to "+title_name+" admission Link to parent..?",
      message: BootboxContent,
      className: "medium",
      buttons: {
          confirm: {
              label: 'Yes',
              className: 'btn-success'
          },
          cancel: {
              label: 'No',
              className: 'btn-danger'
          }
        },
        callback: function (result) {
            if(result) {
              $('.send_admission_link_btn').attr('disabled','disabled');
              var admission_expire_date = $('#admission_expire_date').val();
                $.ajax({
                    url: '<?php echo site_url('enquiry/enquiry_staff/send_admission_link_to_parent'); ?>',
                    type: 'post',
                    data: {'enquiry_id':enquiry_id,'admission_expire_date':admission_expire_date,'followup_action':'','follow_status':''},
                    success: function(data) {
                      var data = $.parseJSON(data);
                        if(data){
                            $(function(){
                                new PNotify({
                                    title: 'Success',
                                    text:  'Successfully Sent',
                                    type: 'success',
                                });
                            });
                            
                        }else{
                            $(function(){
                                new PNotify({
                                    title: 'Error',
                                    text:  'Something went wrong',
                                    type: 'error',
                                });
                            });
                        }
                        $('.send_admission_link_btn').removeAttr('disabled');
                    }
                });
                get_enquiry_details(enquiry_id);
            }
        }
    });
   }


function generate_pdf() {
  var enquiry_id_generate = $('#enquiry_id_generate').val();
  $('#generate_pdf_btn').attr('disabled','disabled').html('Please wait..');
        $.ajax({
            url: '<?php echo site_url('enquiry/enquiry_staff/generate_pdf_enquiry'); ?>',
            type: 'POST',
            data:{'enquiry_id_generate': enquiry_id_generate},
            success: function(data) {
                setInterval(function() { 
                    $('#generate_pdf_btn').removeAttr('disabled').html('Generate PDF');
                    $('#download_btn_pdf').show();
                }, 5000);
              
            },
            complete:function(){
              get_enquiry_details(enquiry_id_generate);
            }
        })
    }

function download_pdf() {
  var enquiry_id_generate = $('#enquiry_id_generate').val();

    var downloadUrl = '<?php echo site_url('enquiry/enquiry_staff/download_enquiry_form/'); ?>'+enquiry_id_generate;
    window.location.href = downloadUrl;
  
}


</script>

 <div id="summary" class="modal fade" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width: 60%;margin: auto;">
      <div class="modal-header">
        <h4 class="modal-title">Follow-up details</h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:auto;">
        <table class="table" id="dynamic-content" width="100%">
        
        </table>
      </div>
      <div class="modal-footer">
        <button type="button" id="cancelModal" class="col-md-2 btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<div id="admission_link_preview" class="modal fade" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width: 70%;margin: auto;">
      <div class="modal-header">
        <h4 class="modal-title">Do you want to send admission Link to parent..?</h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:auto;">
        <input type="hidden" id="admission_expire_date_new">
        <input type="hidden" id="follow_up_action_new">
        <input type="hidden" id="follow_status_new">
        <div id="send_link_email_content"></div>
      </div>
      <div class="modal-footer">
        <button type="button" id="cancelModal" class="col-md-2 btn btn-danger" data-dismiss="modal">No</button>
        <button type="button" id="" class="col-md-2 btn btn-success" onclick="send_admission_link_email()">Yes</button>
      </div>
    </div>
  </div>
</div>

<style>
  .medium{
    width: 45%;
    margin: auto;
  }
</style>
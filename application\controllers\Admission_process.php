<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Admission_process extends CI_Controller {

    public $studentData = [
        [
          'display_name'=>'Student Name',
          'data_input'=>'text',
          'column_name'=>'std_name',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Gender',
          'data_input'=>'text',
          'column_name'=>'gender',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Date of Birth',
          'data_input'=>'date',
          'column_name'=>'dob',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Place of birth',
          'data_input'=>'address',
          'column_name'=>'birth_place',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Present Address',
          'data_input'=>'text',
          'column_name'=>'s_present_addr',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Permanet Address',
          'data_input'=>'text',
          'column_name'=>'s_permanent_addr',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Blood Group',
          'data_input'=>'text',
          'column_name'=>'student_blood_group',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Nationality',
          'data_input'=>'text',
          'column_name'=>'nationality',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Caste',
          'data_input'=>'text',
          'column_name'=>'student_caste',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Category',
          'data_input'=>'text',
          'column_name'=>'category',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Religion',
          'data_input'=>'text',
          'column_name'=>'religion',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Student Aadhar card No',
          'data_input'=>'text',
          'column_name'=>'student_aadhar',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Mother Tongue',
          'data_input'=>'text',
          'column_name'=>'std_mother_tongue',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'SATS Number',
          'data_input'=>'text',
          'column_name'=>'sats_number',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Does your child have any disability',
          'data_input'=>'text',
          'column_name'=>'physical_disability',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Any evidence of learning disability',
          'data_input'=>'text',
          'column_name'=>'learning_disability',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Sibling (School & class studying in )',
          'data_input'=>'text',
          'column_name'=>'',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Sibling (Student name)',
          'data_input'=>'text',
          'column_name'=>'sibling_student_name',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Sibling Class',
          'data_input'=>'text',
          'column_name'=>'sibling_student_class',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Sibling School name',
          'data_input'=>'text',
          'column_name'=>'sibling_school_name',
          'heading'=>'Personal Details'
        ],
        [
          'display_name'=>'Name',
          'data_input'=>'text',
          'column_name'=>'m_name',
          'heading'=>"Mother's Details"
        ],
        [
          'display_name'=>'Residential Address',
          'data_input'=>'address',
          'column_name'=>'m_addr',
          'heading'=>"Mother's Details"
        ],
        [
          'display_name'=>'Aadhar card No',
          'data_input'=>'text',
          'column_name'=>'mother_aadhar',
          'heading'=>"Mother's Details"
        ],
        [
          'display_name'=>'Qualification',
          'data_input'=>'text',
          'column_name'=>'m_qualification',
          'heading'=>"Mother's Details"
        ],
        [
          'display_name'=>'Profession',
          'data_input'=>'text',
          'column_name'=>'m_profession',
          'heading'=>"Mother's Details"
        ],
        [
          'display_name'=>'Designation & Official Address',
          'data_input'=>'address',
          'column_name'=>'m_office_add',
          'heading'=>"Mother's Details"
        ],
        [
          'display_name'=>'Mobile No',
          'data_input'=>'text',
          'column_name'=>'m_mobile_no',
          'heading'=>"Mother's Details"
        ],
        [
          'display_name'=>'Anual Income',
          'data_input'=>'text',
          'column_name'=>'m_annual_gross_income',
          'heading'=>"Mother's Details"
        ],
        [
          'display_name'=>'Email ID',
          'data_input'=>'text',
          'column_name'=>'m_email_id',
          'heading'=>"Mother's Details"
        ],
        [
          'display_name'=>'Name',
          'data_input'=>'text',
          'column_name'=>'f_name',
          'heading'=>"Father's Details"
        ],
        [
          'display_name'=>'Residential Address',
          'data_input'=>'address',
          'column_name'=>'f_addr',
          'heading'=>"Father's Details"
        ],
        [
          'display_name'=>'Aadhar card No',
          'data_input'=>'text',
          'column_name'=>'father_aadhar',
          'heading'=>"Father's Details"
        ],
        [
          'display_name'=>'Qualification',
          'data_input'=>'text',
          'column_name'=>'f_qualification',
          'heading'=>"Father's Details"
        ],
        [
          'display_name'=>'Profession',
          'data_input'=>'text',
          'column_name'=>'f_profession',
          'heading'=>"Father's Details"
        ],
        [
          'display_name'=>'Designation & Official Address',
          'data_input'=>'address',
          'column_name'=>'f_office_add',
          'heading'=>"Father's Details"
        ],
        [
          'display_name'=>'Mobile No',
          'data_input'=>'text',
          'column_name'=>'f_mobile_no',
          'heading'=>"Father's Details"
        ],
        [
          'display_name'=>'Anual Income',
          'data_input'=>'text',
          'column_name'=>'f_annual_gross_income',
          'heading'=>"Father's Details"
        ],
        [
          'display_name'=>'Email ID',
          'data_input'=>'text',
          'column_name'=>'f_email_id',
          'heading'=>"Father's Details"
        ],
        [
          'display_name'=>'Name',
          'data_input'=>'text',
          'column_name'=>'g_name',
          'heading'=>'Guardian Details'
        ],
        [
          'display_name'=>'Mobile No',
          'data_input'=>'text',
          'column_name'=>'g_mobile_no',
          'heading'=>'Guardian Details'
        ],
        [
          'display_name'=>'Email Id',
          'data_input'=>'text',
          'column_name'=>'g_email_id',
          'heading'=>'Guardian Details'
        ],
        [
          'display_name'=>'Residential Address',
          'data_input'=>'text',
          'column_name'=>'g_addr',
          'heading'=>'Guardian Details'
        ],
        [
          'display_name'=>'Designation & Ocoupation',
          'data_input'=>'text',
          'column_name'=>'g_profession',
          'heading'=>'Guardian Details'
        ],
        [
          'display_name'=>'Aadhar card No',
          'data_input'=>'text',
          'column_name'=>'guardian_aadhar',
          'heading'=>'Guardian Details'
        ],
        [
          'display_name'=>'Qualification',
          'data_input'=>'text',
          'column_name'=>'g_qualification',
          'heading'=>'Guardian Details'
        ]
        
  ];

    public $columnList = [
        [
          'displayName'=>'Academic Year',
          'columnNameWithTable'=>'af.academic_year_applied_for',
          'columnName'=>'academic_year_applied_for',
          'varName'=>'ac_year',
          'table'=>'admission_forms',
          'index'=>'1',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Joining Period',
          'columnNameWithTable'=>'af.joining_period',
          'columnName'=>'joining_period',
          'varName'=>'joining_period',
          'table'=>'admission_forms',
          'index'=>'2',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student First Name',
          'columnNameWithTable'=>'af.std_name',
          'columnName'=>'std_name',
          'varName'=>'student_first_name',
          'table'=>'admission_forms',
          'index'=>'3',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Middle Name',
          'columnNameWithTable'=>'af.student_middle_name',
          'columnName'=>'student_middle_name',
          'varName'=>'student_middle_name',
          'table'=>'admission_forms',
          'index'=>'4',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Last Name',
          'columnNameWithTable'=>'af.student_last_name',
          'columnName'=>'student_last_name',
          'varName'=>'student_last_name',
          'table'=>'admission_forms',
          'index'=>'5',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Date of Birth',
          'columnNameWithTable'=>'date_format(af.dob,"%d-%m-%Y")',
          'columnName'=>'dob',
          'varName'=>'dob',
          'table'=>'admission_forms',
          'index'=>'6',
          'displayType'=>'date',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Gender',
          'columnNameWithTable'=>'af.gender',
          'columnName'=>'gender',
          'varName'=>'gender',
          'table'=>'admission_forms',
          'index'=>'7',
          'displayType'=>'char',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Birth Taluk',
          'columnNameWithTable'=>'af.birth_taluk',
          'columnName'=>'birth_taluk',
          'varName'=>'birthTaluk',
          'table'=>'admission_forms',
          'index'=>'8',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Birth District',
          'columnNameWithTable'=>'af.birth_district',
          'columnName'=>'birth_district',
          'varName'=>'birthDistrict',
          'table'=>'admission_forms',
          'index'=>'9',
          'displayType'=>'text',
          'dataType'=>'string'
        ],

        [
          'displayName'=>'Town/City',
          'columnNameWithTable'=>'af.s_present_district',
          'columnName'=>'s_present_district',
          'varName'=>'Student_district',
          'table'=>'admission_forms',
          'index'=>'10',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'State',
          'columnNameWithTable'=>'af.s_present_state',
          'columnName'=>'s_present_state',
          'varName'=>'Student_state',
          'table'=>'admission_forms',
          'index'=>'11',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Pin Code',
          'columnNameWithTable'=>'af.s_present_pincode',
          'columnName'=>'s_present_pincode',
          'varName'=>'Student_pincode',
          'table'=>'admission_forms',
          'index'=>'12',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Country of Birth',
          'columnNameWithTable'=>'af.s_present_country',
          'columnName'=>'s_present_country',
          'varName'=>'Student_country',
          'table'=>'admission_forms',
          'index'=>'13',
          'displayType'=>'text',
          'dataType'=>'string'
        ],

        [
          'displayName'=>'Nationality',
          'columnNameWithTable'=>'af.nationality',
          'columnName'=>'nationality',
          'varName'=>'nationality',
          'table'=>'admission_forms',
          'index'=>'14',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Religion',
          'columnNameWithTable'=>'af.religion',
          'columnName'=>'religion',
          'varName'=>'religion',
          'table'=>'admission_forms',
          'index'=>'15',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Mother Tongue',
          'columnNameWithTable'=>'af.std_mother_tongue',
          'columnName'=>'std_mother_tongue',
          'varName'=>'std_mother_tongue',
          'table'=>'admission_forms',
          'index'=>'16',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Physical Disability',
          'columnNameWithTable'=>'af.physical_disability',
          'columnName'=>'physical_disability',
          'varName'=>'physical_disability',
          'table'=>'admission_forms',
          'index'=>'17',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Learning Disability',
          'columnNameWithTable'=>'af.learning_disability',
          'columnName'=>'learning_disability',
          'varName'=>'learning_disability',
          'table'=>'admission_forms',
          'index'=>'18',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Aadhar No.',
          'columnNameWithTable'=>"af.student_aadhar",
          'columnName'=>'student_aadhar',
          'varName'=>'student_aadhar',
          'table'=>'admission_forms',
          'index'=>'19',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Email ID',
          'columnNameWithTable'=>"af.student_email_id",
          'columnName'=>'student_email_id',
          'varName'=>'student_email_id',
          'table'=>'admission_forms',
          'index'=>'20',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Mobile No.',
          'columnNameWithTable'=>"af.student_mobile_no",
          'columnName'=>'student_mobile_no',
          'varName'=>'student_mobile_no',
          'table'=>'admission_forms',
          'index'=>'21',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Blood group',
          'columnNameWithTable'=>"af.student_blood_group",
          'columnName'=>'student_blood_group',
          'varName'=>'student_blood_group',
          'table'=>'admission_forms',
          'index'=>'22',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Caste',
          'columnNameWithTable'=>"af.student_caste",
          'columnName'=>'student_caste',
          'varName'=>'student_caste',
          'table'=>'admission_forms',
          'index'=>'24',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Category',
          'columnNameWithTable'=>"af.category",
          'columnName'=>'category',
          'varName'=>'category',
          'table'=>'admission_forms',
          'index'=>'25',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Category',
          'columnNameWithTable'=>"af.boarding",
          'columnName'=>'boarding',
          'varName'=>'boarding',
          'table'=>'admission_forms',
          'index'=>'26',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Present Address',
          'columnNameWithTable'=>"concat(ifnull(af.s_present_addr,''),' ', ifnull(af.s_present_area,''),' ',ifnull(af.s_present_district,''),' ', ifnull(af.s_present_state,' '),ifnull(af.s_present_country,' '),' -', ifnull(af.s_present_pincode,' '))",
          'columnName'=>'s_present_addr',
          'varName'=>'s_present_addr',
          'table'=>'admission_forms',
          'index'=>'27',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Permanent Address',
          'columnNameWithTable'=>"concat(ifnull(af.s_permanent_addr,''),' ', ifnull(af.s_permanent_area,''),' ',ifnull(af.s_permanent_district,''),' ', ifnull(af.s_permanent_state,' '),ifnull(af.s_permanent_country,' '),' -', ifnull(af.s_permanent_pincode,' '))",
          'columnName'=>'s_permanent_addr',
          'varName'=>'s_permanent_addr',
          'table'=>'admission_forms',
          'index'=>'28',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Last Curriculum',
          'columnNameWithTable'=>'af.curriculum_currently_studying',
          'columnName'=>'curriculum_currently_studying',
          'varName'=>'Last_curriculum',
          'table'=>'admission_forms',
          'index'=>'29',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Ration Card No',
          'columnNameWithTable'=>"af.ration_card_number",
          'columnName'=>'ration_card_number',
          'varName'=>'ration_card_number',
          'table'=>'admission_forms',
          'index'=>'30',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Ration Card Type',
          'columnNameWithTable'=>"af.ration_card_type",
          'columnName'=>'ration_card_type',
          'varName'=>'ration_card_type',
          'table'=>'admission_forms',
          'index'=>'31',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Caste Income ertificate No',
          'columnNameWithTable'=>"af.caste_income_certificate_number",
          'columnName'=>'caste_income_certificate_number',
          'varName'=>'caste_income_certificate_number',
          'table'=>'admission_forms',
          'index'=>'32',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Extracurricular Activities',
          'columnNameWithTable'=>"af.extracurricular_activities",
          'columnName'=>'extracurricular_activities',
          'varName'=>'extracurricular_activities',
          'table'=>'admission_forms',
          'index'=>'33',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'language 1st choise',
          'columnNameWithTable'=>"af.lang_1_choice",
          'columnName'=>'lang_1_choice',
          'varName'=>'lang_1_choice',
          'table'=>'admission_forms',
          'index'=>'34',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'language 2nd choise',
          'columnNameWithTable'=>"af.lang_2_choice",
          'columnName'=>'lang_2_choice',
          'varName'=>'lang_2_choice',
          'table'=>'admission_forms',
          'index'=>'35',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'language 3rd choise',
          'columnNameWithTable'=>"af.lang_3_choice",
          'columnName'=>'lang_3_choice',
          'varName'=>'lang_3_choice',
          'table'=>'admission_forms',
          'index'=>'36',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Family Annual Income',
          'columnNameWithTable'=>"af.family_annual_income",
          'columnName'=>'family_annual_income',
          'varName'=>'family_annual_income',
          'table'=>'admission_forms',
          'index'=>'37',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Quoata',
          'columnNameWithTable'=>"af.student_quota",
          'columnName'=>'student_quota',
          'varName'=>'student_quota',
          'table'=>'admission_forms',
          'index'=>'38',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Seat Allotment No',
          'columnNameWithTable'=>"af.seat_allotment_no",
          'columnName'=>'seat_allotment_no',
          'varName'=>'seat_allotment_no',
          'table'=>'admission_forms',
          'index'=>'39',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Seat Allotment Date',
          'columnNameWithTable'=>"af.seat_allotment_date",
          'columnName'=>'seat_allotment_date',
          'varName'=>'seat_allotment_date',
          'table'=>'admission_forms',
          'index'=>'40',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Passport No',
          'columnNameWithTable'=>"af.passport_number",
          'columnName'=>'passport_number',
          'varName'=>'passport_number',
          'table'=>'admission_forms',
          'index'=>'41',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Passport issued place',
          'columnNameWithTable'=>"af.passport_issued_place",
          'columnName'=>'passport_issued_place',
          'varName'=>'passport_issued_place',
          'table'=>'admission_forms',
          'index'=>'42',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Second language currently studying',
          'columnNameWithTable'=>"af.second_language_currently_studying",
          'columnName'=>'second_language_currently_studying',
          'varName'=>'second_language_currently_studying',
          'table'=>'admission_forms',
          'index'=>'43',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Nationality Other',
          'columnNameWithTable'=>"af.nationality_other",
          'columnName'=>'nationality_other',
          'varName'=>'nationality_other',
          'table'=>'admission_forms',
          'index'=>'44',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Religion Other',
          'columnNameWithTable'=>"af.religion_other",
          'columnName'=>'religion_other',
          'varName'=>'religion_other',
          'table'=>'admission_forms',
          'index'=>'45',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Primary language spoken ',
          'columnNameWithTable'=>"af.primary_language_spoken",
          'columnName'=>'primary_language_spoken',
          'varName'=>'primary_language_spoken',
          'table'=>'admission_forms',
          'index'=>'46',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'transport',
          'columnNameWithTable'=>"af.transport",
          'columnName'=>'transport',
          'varName'=>'transport',
          'table'=>'admission_forms',
          'index'=>'47',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Fathers Name',
          'columnNameWithTable'=>'af.f_name',
          'columnName'=>'f_name',
          'varName'=>'Father_name',
          'table'=>'admission_forms',
          'index'=>'48',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Fathers Last Name',
          'columnNameWithTable'=>'af.f_last_name',
          'columnName'=>'f_last_name',
          'varName'=>'Father_last_name',
          'table'=>'admission_forms',
          'index'=>'49',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mothers Name',
          'columnNameWithTable'=>'af.m_name',
          'columnName'=>'m_name',
          'varName'=>'Mother_name',
          'table'=>'admission_forms',
          'index'=>'50',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mothers Last Name',
          'columnNameWithTable'=>'af.m_last_name',
          'columnName'=>'m_last_name',
          'varName'=>'Mother_last_name',
          'table'=>'admission_forms',
          'index'=>'51',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Fathers Email Id',
          'columnNameWithTable'=>'af.f_email_id',
          'columnName'=>'f_email_id',
          'varName'=>'Father_email_id',
          'table'=>'admission_forms',
          'index'=>'52',
          'displayType'=>'text',
          'dataType'=>'string'
        ],

         [
          'displayName'=>'Mothers Email Id',
          'columnNameWithTable'=>'af.m_email_id',
          'columnName'=>'m_email_id',
          'varName'=>'Mother_email_id',
          'table'=>'admission_forms',
          'index'=>'53',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Father Address',
          'columnNameWithTable'=>"concat(ifnull(af.f_addr,''),' ', ifnull(af.f_district,''),' ',ifnull(af.f_state,''),' ', ifnull(af.f_county,' '),' -', ifnull(af.f_pincode,' '))",
          'columnName'=>'f_addr',
          'varName'=>'fAddr',
          'table'=>'admission_forms',
          'index'=>'54',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Address',
          'columnNameWithTable'=>"concat(ifnull(af.m_addr,''),' ', ifnull(af.m_district,''),' ',ifnull(af.m_state,''),' ', ifnull(af.m_county,' '),' -', ifnull(af.m_pincode,' '))",
          'columnName'=>'m_addr',
          'varName'=>'m_addr',
          'table'=>'admission_forms',
          'index'=>'54',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Father company name',
          'columnNameWithTable'=>"af.f_company_name",
          'columnName'=>'f_company_name',
          'varName'=>'f_company_name',
          'table'=>'admission_forms',
          'index'=>'55',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother company name',
          'columnNameWithTable'=>"af.m_company_name",
          'columnName'=>'m_company_name',
          'varName'=>'m_company_name',
          'table'=>'admission_forms',
          'index'=>'56',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Father Company Address',
          'columnNameWithTable'=>"concat(ifnull(af.f_company_addr,''),' ',ifnull(af.f_company_district,''),' ', ifnull(af.f_company_state,' '),ifnull(af.f_company_county,' '),' -', ifnull(af.f_company_pincode,' '))",
          'columnName'=>'f_company_addr',
          'varName'=>'f_company_addr',
          'table'=>'admission_forms',
          'index'=>'57',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Company Address',
          'columnNameWithTable'=>"concat(ifnull(af.m_company_addr,''),' ',ifnull(af.m_company_district,''),' ', ifnull(af.m_company_state,' '),ifnull(af.m_company_county,' '),' -', ifnull(af.m_company_pincode,' '))",
          'columnName'=>'m_company_addr',
          'varName'=>'m_company_addr',
          'table'=>'admission_forms',
          'index'=>'58',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Father Mobile No',
          'columnNameWithTable'=>"af.f_mobile_no",
          'columnName'=>'f_mobile_no',
          'varName'=>'father_mobile_no',
          'table'=>'admission_forms',
          'index'=>'59',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Mobile No',
          'columnNameWithTable'=>"af.m_mobile_no",
          'columnName'=>'m_mobile_no',
          'varName'=>'mother_mobile_no',
          'table'=>'admission_forms',
          'index'=>'60',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
       
        [
          'displayName'=>'Father Mother Tongue',
          'columnNameWithTable'=>"af.father_mother_tongue",
          'columnName'=>'father_mother_tongue',
          'varName'=>'father_mother_tongue',
          'table'=>'admission_forms',
          'index'=>'60',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Mother Tongue',
          'columnNameWithTable'=>"af.mother_mother_tongue",
          'columnName'=>'mother_mother_tongue',
          'varName'=>'mother_mother_tongue',
          'table'=>'admission_forms',
          'index'=>'61',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        
        [
          'displayName'=>'Father Aadhar No.',
          'columnNameWithTable'=>"af.father_aadhar",
          'columnName'=>'father_aadhar',
          'varName'=>'father_aadhar',
          'table'=>'admission_forms',
          'index'=>'62',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Aadhar No.',
          'columnNameWithTable'=>"af.mother_aadhar",
          'columnName'=>'mother_aadhar',
          'varName'=>'mother_aadhar',
          'table'=>'admission_forms',
          'index'=>'62',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        
        [
          'displayName'=>'Fathers Occupation',
          'columnNameWithTable'=>'af.f_profession',
          'columnName'=>'f_profession',
          'varName'=>'father_profession',
          'table'=>'admission_forms',
          'index'=>'63',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Occupation',
          'columnNameWithTable'=>'af.m_profession',
          'columnName'=>'m_profession',
          'varName'=>'mother_profession',
          'table'=>'admission_forms',
          'index'=>'64',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        
        [
          'displayName'=>'Father PAN No.',
          'columnNameWithTable'=>"af.f_pan_number",
          'columnName'=>'f_pan_number',
          'varName'=>'f_pan_number',
          'table'=>'admission_forms',
          'index'=>'65',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother PAN No.',
          'columnNameWithTable'=>"af.m_pan_number",
          'columnName'=>'m_pan_number',
          'varName'=>'m_pan_number',
          'table'=>'admission_forms',
          'index'=>'66',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        
        [
          'displayName'=>'Father Qualification',
          'columnNameWithTable'=>"af.f_qualification",
          'columnName'=>'f_qualification',
          'varName'=>'f_qualification',
          'table'=>'admission_forms',
          'index'=>'67',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Qualification',
          'columnNameWithTable'=>"af.m_qualification",
          'columnName'=>'m_qualification',
          'varName'=>'m_qualification',
          'table'=>'admission_forms',
          'index'=>'68',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Father Annual Gross Income',
          'columnNameWithTable'=>"af.f_annual_gross_income",
          'columnName'=>'f_annual_gross_income',
          'varName'=>'f_annual_gross_income',
          'table'=>'admission_forms',
          'index'=>'69',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Annual Gross Income',
          'columnNameWithTable'=>"af.m_annual_gross_income",
          'columnName'=>'m_annual_gross_income',
          'varName'=>'m_annual_gross_income',
          'table'=>'admission_forms',
          'index'=>'70',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        
        [
          'displayName'=>'Sibling Student Name',
          'columnNameWithTable'=>"af.sibling_student_name",
          'columnName'=>'sibling_student_name',
          'varName'=>'sibling_student_name',
          'table'=>'admission_forms',
          'index'=>'71',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Guardian Name',
          'columnNameWithTable'=>"af.g_name",
          'columnName'=>'g_name',
          'varName'=>'g_name',
          'table'=>'admission_forms',
          'index'=>'72',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Guardian Mobile No',
          'columnNameWithTable'=>"af.g_mobile_no",
          'columnName'=>'g_mobile_no',
          'varName'=>'g_mobile_no',
          'table'=>'admission_forms',
          'index'=>'73',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Guardian Email Id',
          'columnNameWithTable'=>"af.g_email_id",
          'columnName'=>'g_email_id',
          'varName'=>'g_email_id',
          'table'=>'admission_forms',
          'index'=>'74',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Gurdian Aadhar No',
          'columnNameWithTable'=>"af.guardian_aadhar",
          'columnName'=>'guardian_aadhar',
          'varName'=>'guardian_aadhar',
          'table'=>'admission_forms',
          'index'=>'75',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Guardian Address',
          'columnNameWithTable'=>"concat(ifnull(af.g_addr,''),' ', ifnull(af.g_district,''),' ',ifnull(af.g_state,''),' ', ifnull(af.g_county,' '),' -', ifnull(af.g_pincode,' '))",
          'columnName'=>'g_addr',
          'varName'=>'g_addr',
          'table'=>'admission_forms',
          'index'=>'76',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Guardian Annual Gross Income',
          'columnNameWithTable'=>"af.g_annual_gross_income",
          'columnName'=>'g_annual_gross_income',
          'varName'=>'g_annual_gross_income',
          'table'=>'admission_forms',
          'index'=>'77',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Guardian Mother Tongue',
          'columnNameWithTable'=>"af.guardian_mother_tongue",
          'columnName'=>'guardian_mother_tongue',
          'varName'=>'guardian_mother_tongue',
          'table'=>'admission_forms',
          'index'=>'78',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Guardian Qualification',
          'columnNameWithTable'=>"af.g_qualification",
          'columnName'=>'g_qualification',
          'varName'=>'g_qualification',
          'table'=>'admission_forms',
          'index'=>'79',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Link to Enquiry',
          'columnNameWithTable'=>"af.enquiry_id",
          'columnName'=>'enquiry_id',
          'varName'=>'enquiry_id',
          'table'=>'admission_forms',
          'index'=>'80',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Country Code',
          'columnNameWithTable'=>"af.s_country_code",
          'columnName'=>'s_country_code',
          'varName'=>'s_country_code',
          'table'=>'admission_forms',
          'index'=>'81',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Father Country Code',
          'columnNameWithTable'=>"af.f_country_code",
          'columnName'=>'f_country_code',
          'varName'=>'f_country_code',
          'table'=>'admission_forms',
          'index'=>'82',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Country Code',
          'columnNameWithTable'=>"af.m_country_code",
          'columnName'=>'m_country_code',
          'varName'=>'m_country_code',
          'table'=>'admission_forms',
          'index'=>'83',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Guardian Country Code',
          'columnNameWithTable'=>"af.g_country_code",
          'columnName'=>'g_country_code',
          'varName'=>'g_country_code',
          'table'=>'admission_forms',
          'index'=>'84',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Father Nationality',
          'columnNameWithTable'=>"af.f_nationality",
          'columnName'=>'f_nationality',
          'varName'=>'f_nationality',
          'table'=>'admission_forms',
          'index'=>'85',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Nationality',
          'columnNameWithTable'=>"af.m_nationality",
          'columnName'=>'m_nationality',
          'varName'=>'m_nationality',
          'table'=>'admission_forms',
          'index'=>'86',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Is ready to take proficiency test',
          'columnNameWithTable'=>"af.is_ready_to_take_proficiency_test",
          'columnName'=>'is_ready_to_take_proficiency_test',
          'varName'=>'is_ready_to_take_proficiency_test',
          'table'=>'admission_forms',
          'index'=>'87',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Status',
          'columnNameWithTable'=>"sa.admission_status",
          'columnName'=>'admission_status',
          'varName'=>'student_admission_status',
          'table'=>'student_admission',
          'index'=>'88',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Applied Date',
          'columnNameWithTable'=>'date_format(af.created_on,"%d-%m-%Y")',
          'columnName'=>'created_on',
          'varName'=>'created_on',
          'table'=>'admission_forms',
          'index'=>'89',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Curriculum Interested In',
          'columnNameWithTable'=>"af.curriculum_interested_in",
          'columnName'=>'curriculum_interested_in',
          'varName'=>'curriculum_interested_in',
          'table'=>'admission_forms',
          'index'=>'90',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Total Marks',
          'columnNameWithTable'=>"aps.total_marks",
          'columnName'=>'total_marks',
          'varName'=>'total_marks',
          'table'=>'admission_prev_school',
          'index'=>'91',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Total Marks Obtained',
          'columnNameWithTable'=>"aps.total_marks_scored",
          'columnName'=>'total_marks_scored',
          'varName'=>'total_marks_scored',
          'table'=>'admission_prev_school',
          'index'=>'92',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Caste',
          'columnNameWithTable'=>"af.student_caste",
          'columnName'=>'student_caste',
          'varName'=>'student_caste',
          'table'=>'admission_forms',
          'index'=>'93',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Registration Number',
          'columnNameWithTable'=>"aps.registration_no",
          'columnName'=>'registration_no',
          'varName'=>'registration_no',
          'table'=>'admission_forms',
          'index'=>'95',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'SATS Number',
          'columnNameWithTable'=>"af.sats_number",
          'columnName'=>'sats_number',
          'varName'=>'sats_number',
          'table'=>'admission_forms',
          'index'=>'96',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'reason for join this institute',
          'columnNameWithTable'=>"af.reason_for_joining_this_institute",
          'columnName'=>'reason_for_joining_this_institute',
          'varName'=>'reason_for_joining_this_institute',
          'table'=>'admission_forms',
          'index'=>'97',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Area of Strength',
          'columnNameWithTable'=>"af.student_area_of_strength",
          'columnName'=>'student_area_of_strength',
          'varName'=>'student_area_of_strength',
          'table'=>'admission_forms',
          'index'=>'98',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Student Hobbies',
          'columnNameWithTable'=>"af.student_hobbies",
          'columnName'=>'student_hobbies',
          'varName'=>'student_hobbies',
          'table'=>'admission_forms',
          'index'=>'99',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Did the Enrolled in different institute Earlier',
          'columnNameWithTable'=>"af.did_they_enrolled_in_different_institute_earlier",
          'columnName'=>'did_they_enrolled_in_different_institute_earlier',
          'varName'=>'did_they_enrolled_in_different_institute_earlier',
          'table'=>'admission_forms',
          'index'=>'100',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Father type of Organization',
          'columnNameWithTable'=>"af.f_type_of_organization",
          'columnName'=>'f_type_of_organization',
          'varName'=>'f_type_of_organization',
          'table'=>'admission_forms',
          'index'=>'101',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Mother Type of organization',
          'columnNameWithTable'=>"af.m_type_of_organization",
          'columnName'=>'m_type_of_organization',
          'varName'=>'m_type_of_organization',
          'table'=>'admission_forms',
          'index'=>'102',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'APAAR Id',
          'columnNameWithTable'=>"af.apaar_id",
          'columnName'=>'apaar_id',
          'varName'=>'apaar_id',
          'table'=>'admission_forms',
          'index'=>'103',
          // 'displayType'=>'text',
          'dataType'=>'string'
        ]
    ];  
    

	public function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    } 
    $this->load->model('Admission_model');
    $this->load->model('Admission_staff_model');
    $this->load->library('session');
    $this->load->helper('captcha');
    $this->load->helper('texting_helper');
    $this->load->model('enquiry_model');
	  $this->config->load('form_elements');
    $this->load->library('filemanager');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/Fees_blueprint_model','fbm');
    $this->load->library('fee_library');
    $this->load->library('payment_application');
    $this->load->helper('email_helper');
    $this->load->model('communication/emails_model');
	}

  public function index(){
    $data['permViewEditApplication'] = $this->authorization->isAuthorized('ADMISSION.VIEW_EDIT_APPLICATION');
    $applicationv2 = strtolower($this->settings->getSetting('admission_filter_version'));
    $data['permMovetoERP'] = $this->authorization->isAuthorized('ADMISSION.MOVE_TO_ERP');
    $data['permCreateOfflineApp'] = $this->authorization->isAuthorized('ADMISSION.CREATE_OFFLINE_APPLICATION');
    $data['permSettlementReport'] = $this->authorization->isAuthorized('ADMISSION.ONLINE_SETTLEMENT_REPORT');
    $data['permit_view_online_tx_report'] = $this->authorization->isAuthorized('ADMISSION.ONLINE_TRANSACTION_REPORT');
    $data['permSettings'] = $this->authorization->isAuthorized('ADMISSION.SETTINGS');
    $data['permFieldSelection'] = $this->authorization->isAuthorized('ADMISSION.FIELD_SELECTION');
    $data['permReports'] = $this->authorization->isAuthorized('ADMISSION.REPORTS');
    $data['permCreateEmptyApplication'] = $this->authorization->isAuthorized('ADMISSION.PRINT_EMPTY_APPLICATION');
    $data['permJoiningForms'] = $this->authorization->isAuthorized('ADMISSION.JOINING_FORMS');
    $data['permIndusReport'] = $this->authorization->isAuthorized('ADMISSION.INDUS_APPLICATION_REPORT');
    $data['count_app'] = $this->Admission_model->count_all_no_of_application_recevied();
    // echo "<pre>"; print_r($data['count_app']); die();
    $data['fee_paid_count'] = $this->Admission_model->count_all_no_of_application_fee_paid();
    $data['total_application_count'] = $this->Admission_model->admission_application_count();
    //echo "<pre>"; print_r($data['total_application_count']); die();

    $site_url = site_url();
    if ($applicationv2 == 'v2') {
      $application_filter =  array(
        'title' => 'Manage Applications',
        'sub_title' => 'Total applications received:'.$data['count_app'],
        'icon' => 'svg_icons/applications.svg',
        'url' => $site_url.'admission_process/application_view',
        'permission' => $data['permViewEditApplication']
      );
    }else{
      $application_filter = array(
        'title' => 'Applications',
        'sub_title' => 'Total applications received:'.$data['count_app'],
        'icon' => 'svg_icons/applications.svg',
        'url' => $site_url.'admission_process/admission_process',
        'permission' => $data['permViewEditApplication']
      );
    }
    $data['tiles'] = array(
        $application_filter,
        [
          'title' => 'Create Application',
          'sub_title' => 'Create a new application from scratch or from enquiry',
          'icon' => 'svg_icons/add.svg',
          'url' => $site_url.'admission_staff_controller/mobile_registration',
          'permission' => $data['permCreateOfflineApp']
        ],
        [
          'title' => 'Move to ERP (Mass)',
          'sub_title' => 'Move approved students to ERP',
          'icon' => 'svg_icons/swtichprof.svg',
          'url' => $site_url.'admission_process/student_data_add_to_erp',
          'permission' => $data['permMovetoERP']
        ],
        [
          'title' => 'Move To ERP',
          'sub_title' => 'Move approved students to ERP',
          'icon' => 'svg_icons/swtichprof.svg',
          'url' => $site_url.'admission_flow',
          'permission' =>  $this->authorization->isAuthorized('ADMISSION.MOVE_TO_ERP_INDIVIDUAL')
        ],
        [
          'title' => 'Empty Application Form',
          'sub_title' => 'Print an empty application form',
          'icon' => 'svg_icons/emptyapplicationform.svg',
          'url' => $site_url.'admission_process/print_empty_form_gradewise',
          'permission' => $data['permCreateEmptyApplication']
        ],
        [
          'title' => ' Individual Application',
          'sub_title' => ' Individual Application',
          'icon' => 'svg_icons/emptyapplicationform.svg',
          'url' => $site_url.'admission_process/individual_application_form',
          'permission' => $this->authorization->isSuperAdmin()
        ],
        [
          'title' => 'Admission Offers',
          'sub_title' => 'Add offers',
          'icon' => 'svg_icons/emptyapplicationform.svg',
          'url' => $site_url . 'admission_process/admission_offers',
          'permission' => $this->authorization->isAuthorized('ADMISSION.CREATE_OFFERS')
        ],
        [
          'title' => 'Admission Budget',
          'sub_title' => 'Add Budget',
          'icon' => 'svg_icons/emptyapplicationform.svg',
          'url' => $site_url . 'admission_process/admission_budget',
          'permission' => $this->authorization->isSuperAdmin()
        ],
        [
          'title' => 'Assign Counselor ',
          'sub_title' => 'Assign Counselor ',
          'icon' => 'svg_icons/applications.svg',
          'url' => $site_url . 'admission_process/assign_counselor',
          'permission' => $this->authorization->isAuthorized('ADMISSION.ASSIGN_COUNSELOR')
        ],
        [
          'title' => 'Mass E-Mail/Status Follow up',
          'sub_title' => 'Mass E-Mail/Status Follow up',
          'icon' => 'svg_icons/applications.svg',
          'url' => $site_url . 'admission_process/mass_email',
          'permission' => $this->authorization->isAuthorized('ADMISSION.MASS_EMAIL')
        ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
        [
          'title' => 'Daily transaction report',
          'sub_title' => 'Daily transaction report',
          'icon' => 'svg_icons/dailytransaction.svg',
          'url' => $site_url.'admission_process/daily_tx_admissions',
          'permission' => $data['permReports']
        ],
        [
          'title' => 'Application Report',
          'sub_title' => 'View/Export application data',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'admission_process/report_admissions',
          'permission' => $data['permReports']
        ],
        [
          'title' => 'Indus Application Report',
          'sub_title' => 'View/Export application data',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'admission_process/indus_report_admissions',
          'permission' => $data['permIndusReport']
        ],

        // [
        //   'title' => 'Summary report',
        //   'sub_title' => 'View application summary',
        //   'icon' => 'svg_icons/summary.svg',
        //   'url' => $site_url.'admission_process/summar_report',
        //   'permission' => $data['permMovetoERP']
        // ],
        [
          'title' => 'Admissions Summary report',
          'sub_title' => 'Summary Report',
          'icon' => 'svg_icons/summary.svg',
          'url' => $site_url.'admission_process/admissions_summary_report',
          'permission' => $data['permReports']
        ],
        [
          'title' => 'Online Transcation Report',
          'sub_title' => '',
          'icon' => 'svg_icons/transactionreport.svg',
          'url' => $site_url.'admission_process/online_transaction_report',
          'permission' => $data['permit_view_online_tx_report']
        ],
        [
          'title' => 'Online Settlement Report',
          'sub_title' => 'View/confirm online settlement data',
          'icon' => 'svg_icons/fastfeecollection.svg',
          'url' => $site_url.'admission_process/admissions_online_settlement',
          'permission' => $data['permSettlementReport']
        ],
        [
          'title' => 'Joining Form Report',
          'sub_title' => 'View/status of joining form report',
          'icon' => 'svg_icons/fastfeecollection.svg',
          'url' => $site_url.'admission_process/joining_form_report',
          'permission' => $data['permJoiningForms']
        ],
        [
          'title' => 'Admission Offers Report',
          'sub_title' => 'Admission Offers Report',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'admission_process/admission_offers_report',
          'permission' => $this->authorization->isSuperAdmin()
        ],
        [
          'title' => 'Admission Analysis',
          'sub_title' => 'Admission Analysis',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'admission_process/admission_analysis',
          'permission' => $this->authorization->isAuthorized('ADMISSION.ADMISSION_ANALYSIS')
        ],
        [
          'title' => 'Receipt Cancellation Report',
          'sub_title' => 'Receipt Cancellation Report',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'admission_process/receipt_cancellation_report',
          'permission' => $this->authorization->isAuthorized('ADMISSION.RECEIPT_CANCLED_REPORT')
        ],
        [
          'title' => 'Admission Activity',
          'sub_title' => 'Admission Activity',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'admission_process/admission_activity_report',
          'permission' => $this->authorization->isAuthorized('ADMISSION.ACTIVITY_REPORT')
        ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['admin_tiles'] = array(
        [
          'title' => 'Configure admission fields',
          'sub_title' => 'Configure admission fields',
          'icon' => 'svg_icons/configmanagement.svg',
          'url' => $site_url.'admission_process/admission_fields',
          'permission' => $data['permFieldSelection']
        ],
        [
          'title' => 'Admissions Settings',
          'sub_title' => 'Admissions Settings',
          'icon' => 'svg_icons/settings.svg',
          'url' => $site_url.'admission_process/admission_settings',
          'permission' => $data['permSettings']
        ],
      [
        'title' => 'Admissions Seat Allotment',
        'sub_title' => 'Admissions Seat Allotment',
        'icon' => 'svg_icons/settings.svg',
        'url' => $site_url . 'admission_process/admission_process_seat_allotment',
        'permission' => $data['permSettings']
      ],
      [
        'title' => 'Manage Admission Status',
        'sub_title' => 'Manage Admission Status',
        'icon' => 'svg_icons/settings.svg',
        'url' => $site_url . 'admission_process/manage_admission_status',
        'permission' => $data['permSettings']
      ],
      [
        'title' => 'Manage Consent form Templates',
        'sub_title' => 'Manage Consent form Templates',
        'icon' => 'svg_icons/settings.svg',
        'url' => $site_url . 'admission_process/manage_consentform_templates',
        'permission' => $data['permSettings']
      ]

    );
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'admission/staff/admission_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'admission/staff/admission_mobile';
    }else{
      $data['main_content'] = 'admission/staff/admission';    	
    }

    
    $this->load->view('inc/template', $data);
  }
  public function admission_process($filter = ''){
    $from_date =  date('d-m-Y', strtotime('today - 29 days'));
    $to_date = date('d-m-Y');
    
    // if ($filter == 1) {
    //   $from_date = $this->input->post('from_date');
    //   $to_date = $this->input->post('to_date');
    //   $data['SelectedFrom_date'] = $from_date;
    //   $data['SelectedTo_date'] = $to_date;
    //   $data['application'] = $this->Admission_model->get_all_applications($from_date,$to_date);
    // }else{
    //   $data['application'] = $this->Admission_model->get_all_applications($from_date, $to_date);
    // }

    $data['application'] = $this->Admission_model->get_all_applications($from_date, $to_date);

    // $admissions = $this->settings->getSetting('admissions')['forms'];
    $admissions = $this->Admission_model->admission_settings_get();
    $currentAcadYearId = $this->acad_year->getAcadYearId();
    $data['classList'] =  $this->Student_Model->getClassByAcadYear($currentAcadYearId);

    $grades = array();
    foreach ($admissions as $key => $val) {
        $class_applied_for = json_decode($val['class_applied_for'], true);
        if (is_array($class_applied_for) && !empty($class_applied_for)) {
            foreach ($class_applied_for as $key => $value) {        
                $grades[] = $value;
            }
        }
    }
    $data['grades'] = $grades;
    $data['mother_tongue'] = $this->Admission_model->get_mother_tongue_all();
    $data['follow_up_status'] = json_decode($this->settings->getSetting('application_form_status'));
    // echo "<pre>"; print_r($data['application']); die();
    // $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($lastId);
    $data['main_content'] = 'admission/staff/report';
    $this->load->view('inc/template', $data);
  }


  public function application_details($id){
      $data['adm_view'] = $this->Admission_model->get_admission_form_detailsby_id($id);
      // echo "<pre>"; print_r($data['adm_view']); die();
      $data['main_content'] = 'admission/staff/details';
      $this->load->view('inc/template', $data); 
  }

  public function application_details_view($id){
    $data['adm_view'] = $this->Admission_model->get_admission_form_detailsby_id($id);
    //echo "<pre>"; print_r($data['adm_view']); die();
    $data['collect_fee'] =  $this->authorization->isAuthorized('ADMISSION.COLLECT_OFFLINE_FEE');
    $data['seat_allotment'] =  $this->authorization->isAuthorized('ADMISSION.SEAT_ALLOTMENT');
    $data['edit_application_form'] =  $this->authorization->isAuthorized('ADMISSION.EDIT_APPLICATION_FORM');
    $data['application_fee_status'] =  $this->Admission_model->application_fee_status($id);
    $data['settings'] = $this->Admission_model->get_admission_form_setting_detailsby_id($id);
    $data['main_content'] = 'admission/staff/details_view';
    $this->load->view('inc/template', $data); 
  }

  public function check_application_form_staff(){
    $admissionId = $_POST['admissionId'];
    $admissions = $this->Admission_model->admission_receipt_by_id($admissionId);
    if($admissions->pdf_status == 1){
        echo 1;
    }else{
        echo 0;
    }    
  }

  public function download_application_form_staff($admissionId){
    $link = $this->Admission_model->get_application_form_pdf_path($admissionId);
    $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download('Application Form.pdf', $data, TRUE); 
  }

  public function check_seat_allotment_application_form(){
    $seat_allotment_no = $_POST['seat_allotment_no'];
    if($this->Admission_model->checkSeatAllotmentNo_exits($seat_allotment_no))
      echo 'exists';
    else
      echo 'not found';
  }
  public function admission_seat_allotment($id){
    $data['adm_view'] = $this->Admission_model->get_admission_form_detailsby_id($id);
    // echo "<pre>"; print_r($data['adm_view']); die();
    $data['main_content'] = 'admission/staff/admission_seat_allotment';
    $this->load->view('inc/template', $data);
  }

  public function seat_allotment_generate_pdf(){
    $admission_form_id = $_POST['admission_form_id'];
    $seat_allotment_no = $_POST['seat_allotment_no'];
    $admission_setting_id = $_POST['admission_setting_id'];
    $seat_allotment_date = $_POST['seat_allotment_date'];
    $this->Admission_model->update_seat_allotment_no($seat_allotment_no, $admission_form_id, $seat_allotment_date);
   
    $adm_details = $this->Admission_model->get_admission_form_detailsby_id($admission_form_id);
    echo $this->_generate_seat_allotment_form_pdf($admission_form_id,$adm_details, $seat_allotment_no, $admission_setting_id);
  }

  public function seat_allotment_delete(){
    $admission_id = $_POST['admission_id'];
    echo $this->Admission_model->seat_allotment_deletebyid($admission_id);
  }

  public function delete_receipt(){
    $admission_id = $this->input->post('admission_id');
    $remarks = $this->input->post('remarks');
    echo $this->Admission_model->delete_receipt_byid($admission_id,$remarks);
  }

  private function _generate_seat_allotment_form_pdf($admission_form_id,$adm_details, $seat_allotment_no, $admission_setting_id){

    $config_val = $this->_get_admissions_settings_byId($admission_setting_id);
    if (!empty($config_val['seat_allotment_form_html'])) {
      $result = $this->_construct_seat_allotment_form_template($adm_details, $config_val['seat_allotment_form_html'], $seat_allotment_no);
      if ($result) {
        return $this->_generate_seat_allotment_pdf_receipt($result, $admission_form_id);
      }
    }
  }
  public function _construct_seat_allotment_form_template($adm_details, $template, $seat_allotment_no){
    $template = str_replace('%%seat_no%%',$seat_allotment_no, $template);
    $template = str_replace('%%date%%', $adm_details->seat_allotment_date, $template);
    $template = str_replace('%%student_name%%',$adm_details->std_name, $template);
    $template = str_replace('%%course%%',$adm_details->grade_applied_for, $template);
    $template = str_replace('%%acadmic_year%%', $this->acad_year->getAcadYearById($adm_details->academic_year_applied_for), $template);    
    return $template;
  }

   private function _generate_seat_allotment_pdf_receipt($html, $afId) {
      $school = CONFIG_ENV['main_folder'];
      $path = $school.'/admissions_reciepts/'.uniqid().'-'.time().".pdf";
      $bucket = $this->config->item('s3_bucket');
      $status = $this->Admission_model->update_seat_allotment_form_path($afId, $path);
      $page_size = 'a4';
      $page = 'portrait';
      $curl = curl_init();
      $postData = urlencode($html);
      $username = CONFIG_ENV['job_server_username'];
      $password = CONFIG_ENV['job_server_password'];
      $return_url = site_url().'Callback_Controller/updateFeePdfLink';

      curl_setopt_array($curl, array(
          CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_USERPWD => $username . ":" . $password,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
          CURLOPT_HTTPHEADER => array(
              "Accept: application/json",
              "Cache-Control: no-cache",
              "Content-Type: application/x-www-form-urlencoded",
              "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
          ),
      ));

      $response = curl_exec($curl);
      $err = curl_error($curl);
      curl_close($curl);
    }

  private function _get_selection_template_values($file){
    $admissions = $this->Admission_model->admission_settings_get();
    $form_value= array();
    foreach ($admissions as $key => $val) {
        if ($val['instructin_file'] == $file) {
            $form_value = $val;
        }
    }
    return $form_value;
  }

  
  private function _get_admissions_settings_byId($id){
    return  $this->Admission_model->admission_settings_getbyId($id);
  }

  public function view_application_from_byId($id,$au_id,$admission_setting_id,$v1=1){
    $data['app_id'] = $id;
    $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
    
    $data['admission_setting_id'] = $admission_setting_id;
    $data['receipts_view'] = '1';
    $data['form_name'] = $data['config_val']['form_name'];
    $data['form_year'] = $data['config_val']['form_year'];
    $data['school_name'] = $data['config_val']['school_name'];
    $data['school_short'] = $data['config_val']['school_short'];
    $data['instructions'] = $data['config_val']['instruction_file'];
    $data['v1'] = $v1;
    $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_id,$id);
    $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($id);
    $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($id);
    $data['subject_master'] = $this->Admission_model->get_subject_master_list();
    $data['lang_selection'] = $this->Admission_model->get_language_selection_by_id($admission_setting_id, $data['final_preview']->grade_applied_for);
    $data['combinations'] = $this->Admission_model->get_combinations_for_select_list($au_id,$id);

    $streams = json_decode($data['config_val']['streams'], true);
    $prev_eduction_info = json_decode($data['config_val']['prev_eduction_info'], true);

    if (!empty($streams)) {
        $combArray = $streams[$data['combinations']->combination];
        $resultArray = array();
        foreach ($combArray as $key => $val) {
            if ($val['id'] == $data['combinations']->combination_id) {
                $data['comb'] = $val['name'];
            }
        }
    }
    if (!empty($prev_eduction_info)) {
        $data['subjectArray'] = $prev_eduction_info['class'][$data['final_preview']->grade_applied_for]['subject'];
    }
    $data['main_content'] = 'admission/receipts/header_staff';
    $this->load->view('inc/template', $data); 
  }

  public function get_stu_basic_data()
  {
    $stu_id = $_POST['stu_id'] ;
    $result = $this->Admission_model->get_stu_detils($stu_id);
    $data['config_val'] = $this->_get_admissions_settings_byId($result->admission_setting_id);
    $combination = '';
    if(!empty($result->combination_id)){
      $streams = json_decode($data['config_val']['streams']);
      $combination = $result->stream;
          foreach ($streams->$combination as $key => $val) {
            if ($val->id == $result->combination_id) {
                $combination = $val->name;
            }
        }
    }
    $result->combination = $combination;
    echo json_encode($result);
  }

    public function application_document_view($id,$au_id,$admission_setting_id, $v1=1){
       $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
        // echo "<pre>"; print_r($data['config_val']); die();
        $data['admission_setting_id'] = $admission_setting_id;
        $data['receipts_view'] = '1';
        $data['form_name'] = $data['config_val']['form_name'];
        $data['form_year'] = $data['config_val']['form_year'];
        $data['school_name'] = $data['config_val']['school_name'];
        $data['school_short'] = $data['config_val']['school_short'];
        $data['instructions'] = $data['config_val']['instruction_file'];
        $data['v1'] = $v1;
        $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_id,$id);
        $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($id);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($id);
        $data['main_content'] = 'admission/staff/document_view';
        $this->load->view('inc/template', $data); 
    }

    public function download_application_form($id,$au_id,$admission_setting_id){
      $linkpath = $this->Admission_model->get_application_form_pdf_path($id);
      if ($linkpath !='0') {
        $url = $this->filemanager->getFilePath($linkpath);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download('Application Form.pdf', $data, TRUE);
      }else{
        $this->_generate_admission_form_pdf($id,$au_id,$admission_setting_id);
      }
    }

    public function download_application_form_ajax(){
      $id= $_POST['admissionId'];
      $au_id= $_POST['au_id'];
      $admission_setting_id= $_POST['admission_setting_id'];
      $linkpath = $this->Admission_model->get_application_form_pdf_path($id);
      if ($linkpath !='0') {
        $url = $this->filemanager->getFilePath($linkpath);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download('Application Form.pdf', $data, TRUE);
      }else{
        $this->_generate_admission_form_pdf($id,$au_id,$admission_setting_id);
      }
    }

    public function re_generate_individual_application(){
      $id= $_POST['admissionId'];
      $au_id= $_POST['au_id'];
      $enquiry_number = $this->Admission_model->get_enquiry_number($_POST['admissionId']);
      $admission_setting_id= $_POST['admission_setting_id'];
      $config_val = $this->_get_admissions_settings_byId($admission_setting_id);
      $data = array();
      if (!empty($config_val['application_form_html'])) {
        $final_preview = $this->Admission_model->get_admission_form_detailsby_all_auId($au_id,$id);
        $admission_doc = $this->Admission_model->get_admission_form_detailsby_auId($id);
        $admission_prev_schools = $this->Admission_model->get_admission_form_previous_school_details($id);
        $subject_master = $this->Admission_model->get_subject_master_list();
        $admission_stream = $this->Admission_model->get_admission_form_combinations($id);
        $result = $this->_construct_admisison_form_template($final_preview, $admission_doc, $admission_prev_schools, $config_val['application_form_html'], $subject_master,$admission_stream, json_decode($config_val['streams'], true),$enquiry_number);

        if ($result) {
          // $update =  $this->Admission_model->update_admission_html_receipt($result, $id);
          $pdf_path = $this->_generate_admisison_pdf_receipt($result, $id);

          $status = ($pdf_path == '0') ? 0 : 1;
          $data['pdf_relative_path'] = $pdf_path;
          $data['pdf_path'] = $this->filemanager->getFilePath($pdf_path);
        }
      }
      echo json_encode($data);
    }

    public function check_pdf_individual_application(){
      $id= $_POST['admissionId'];
      echo $this->Admission_model->get_application_form_pdf_path_status($id);
    }
    

    public function download_seat_allotment_form($id){
      $linkpath = $this->Admission_model->get_application_seat_allotment_form_pdf_path($id);
      $url = $this->filemanager->getFilePath($linkpath);
      $data = file_get_contents($url);
      $this->load->helper('download');
      force_download('admission_seat_allotment_form.pdf', $data, TRUE);
    }

    private function _generate_admission_form_pdf($id,$au_id,$admission_setting_id){
      $config_val = $this->_get_admissions_settings_byId($admission_setting_id);
      $enquiry_number = $this->Admission_model->get_enquiry_number($id);
      if (!empty($config_val['application_form_html'])) {
        $final_preview = $this->Admission_model->get_admission_form_detailsby_all_auId($au_id,$id);
        $admission_doc = $this->Admission_model->get_admission_form_detailsby_auId($id);
        $admission_prev_schools = $this->Admission_model->get_admission_form_previous_school_details($id);
        $subject_master = $this->Admission_model->get_subject_master_list();
        $admission_stream = $this->Admission_model->get_admission_form_combinations($id);
        $result = $this->_construct_admisison_form_template($final_preview, $admission_doc, $admission_prev_schools, $config_val['application_form_html'], $subject_master,$admission_stream, json_decode($config_val['streams'], true),$enquiry_number);
        if ($result) {
          // $update =  $this->Admission_model->update_admission_html_receipt($result, $id);
          $this->_generate_admisison_pdf_receipt($result, $id);
        }
      }
      redirect('admission_process/application_details_view/'.$id);
    }

    public function serachby_application_area(){
        $area= $this->input->post('area');
        $results = $this->Admission_model->serachby_areawise_applications_wise($area);
        echo json_encode($results);
        // $template = $this->construct_table_dispaly_applications($results);
        // print($template);
    }

    public function serachby_application_number(){
        $app_no= $this->input->post('app_no');
        $result = $this->Admission_model->serachby_application_number_wise($app_no);
        echo json_encode($result);
        // $template =  $this->construct_table_dispaly_applications($results);
        // print($template);
    }

    public function serachby_stream_data(){
        $stream= $this->input->post('stream');
        $combination = $this->input->post('combination');
        $result = $this->Admission_model->serachby_stream_wise_applications($stream, $combination);
        echo json_encode($result);
        // $template =  $this->construct_table_dispaly_applications($results);
        // print($template);
    }

    public function serachby_combination_data(){
      $stream = $_POST['stream'];
      $result =  $this->Admission_model->get_admission_setting_streams();
      $streams = json_decode($result->streams, true);
      $combination = $streams[$stream];
      echo json_encode($combination);

    }

    public function serachby_application_filterv1(){
      $class_list = $_POST['class_list'];
      $apStatus = $_POST['apStatus'];
      $from_date = $_POST['from_date'];
      $to_date = $_POST['to_date'];
      $result = $this->Admission_model->serachby_application_filterwisev1($class_list,$apStatus,$from_date,$to_date);
      echo json_encode($result);
    }

    public function serachby_application_filter(){
      $from_date = $_POST['from_date'];
      $to_date = $_POST['to_date'];
      $grade = $_POST['grade'];
      $filter_by = $_POST['filter_by'];
      $results = $this->Admission_model->serachby_application_filterwise($from_date,$to_date,$grade,$filter_by);
      echo json_encode($results);
    }

    public function serachby_application_status_wise_filter(){
      // $status_from_date = $_POST['status_from_date'];
      // $status_to_date = $_POST['status_to_date'];
      $app_status = $_POST['app_status'];
      $results = $this->Admission_model->serachby_application_status_wise($app_status);
      echo json_encode($results);
    }

    public function serachby_application_grade_wise(){
        $class_list= $this->input->post('class_list');
        $results = $this->Admission_model->serachby_application_gradewise($class_list);
        echo json_encode($results);
        // $template =  $this->construct_table_dispaly_applications($results);
        // print($template);
    }

    public function serachby_application_status(){
        $apStatus= $this->input->post('apStatus');
        $results = $this->Admission_model->serachby_application_status($apStatus);
        echo json_encode($results);
        // $template =  $this->construct_table_dispaly_applications($results);
        // print($template);
    }

    public function serachby_name(){
        $name = $this->input->post('name');
        $results = $this->Admission_model->serachby_student_name($name);
        echo json_encode($results);
    }

    public function get_genderwise_applications(){
        $gender = $this->input->post('gender');
        $results = $this->Admission_model->serachby_gender($gender);
        echo json_encode($results);
    }

    public function get_mother_tongue_applications(){
        $mother_tongue = $this->input->post('mother_tongue');
        $results = $this->Admission_model->serachby_mother_tongue($mother_tongue);
        echo json_encode($results);
    }
    public function construct_table_dispaly_applications($results){
        $template = "";
        if (empty($results)) {
            $template = 0;
        }else{
          $i=1;
            $template .='<table class="table table-bordered">';
            $template .='<thead>';
            $template .='<tr>';
            $template .='<th>#</th>';
            $template .='<th>Submitted on</th>';
            $template .='<th>Application #</th>';
            $template .='<th>Student name</th>';
            $template .='<th>Father Name</th>';
            $template .='<th>Date of birth</th>';
            $template .='<th>Father Mobile</th>';
            $template .='<th>Status</th>';
            $template .='<th>Action</th>';
            $template .='</tr>';
            $template .='</thead>';
            $template .='<tbody>';
        foreach ($results as $key => $result) {
            if($result->curr_status == 'Fee Paid'){
              $bg = '#ccc';
            }else{
              $bg = '';
            } 
            $template .='<tr style="color:'.$bg.'">';
            $template .='<td>'.$i++.'</td>';
            $template .='<td>'.$result->submitted_on.'</td>';
            $template .='<td>'.$result->application_no.'</td>';
            $template .='<td>'.$result->std_name.'</td>';
            $template .='<td>'.$result->f_name.'</td>';
            $template .='<td>'.date('d-M-Y',strtotime($result->dob)).'</td>';
            $template .='<td>'.$result->f_mobile_no.'</td>';
            $template .='<td>'.$result->curr_status.'</td>';
            $template .='<td><a  href='.site_url('admission_process/application_details/'.$result->id).' class="btn btn-primary">Details</a>';
            $template .='<a class="btn btn-warning" data-toggle="modal" data-target="#status_'.$result->id.'" data-placement="top" data-toggle="tooltip" data-id="'.$result->id.'">Update
              </a></td>';
            $template .="</tr>";
        }
            $template .="</tbody>";
            $template .="</table>";
        }
        return $template;
    }

    public function download(){
        $input = $this->input->get();
        $ext = explode('.',$input['path']);
        $extn = $ext[count($ext)-1];
        $data = file_get_contents($input['path']);
        //load download helper
        $this->load->helper('download');
        //download file from directory
        force_download('document.'.$extn, $data, TRUE);
    }

   
    public function summar_report(){
      //Manjukiran 28-Jan-2022: Removed this additional feature from the report as it was rarely used
        // $data['admission_erp'] = $this->Admission_model->summary_report_move_to_erp();
        $follow_up_status_additional = ['Submitted','Application Amount Paid', 'Admit','Student added to ERP','Rejected', 'Duplicate'];

        $follow_up_status = json_decode($this->settings->getSetting('application_form_status'));
        if ($follow_up_status != null)
          $data['admissionStatus'] = array_merge($follow_up_status, $follow_up_status_additional);
        else
          $data['admissionStatus'] = $follow_up_status_additional;

        $data['admission_summary'] = $this->Admission_model->summary_report_admission($data['admissionStatus']);
        // echo '<pre>';print_r($data['admission_summary']);die();

        $adminStatus = [];
        foreach ($data['admission_summary'] as $grade => $value) {
          foreach ($value as $status => $val) {
            if(!array_key_exists($status, $adminStatus)) {
              $adminStatus[$status] = 0;
            }
            $adminStatus[$status] += $val;
          }
        }
        $data['vertical_total'] =  $adminStatus;
        $data['main_content'] = 'admission/staff/summary_report';
        $this->load->view('inc/template', $data); 
    }

    public function admissions_summary_report(){
      $acad_years = $this->acad_year->getAllYearData();
      $acaYearId = $this->settings->getSetting('academic_year_id');
      $promAcadYearId = $this->settings->getSetting('promotion_academic_year_id');
      $acadYear = $this->input->post('acadyear');
      if (!empty($acadYear)) {
        $acadYear = $acadYear;
      }else{
        $acadYear = $this->settings->getSetting('promotion_academic_year_id');
      }
      $acdYears = [];
      foreach ($acad_years as $key => $year) {
        if ($year->id == $acaYearId || $year->id == $promAcadYearId) {
            array_push($acdYears, $year);
          }  
      }
      $data['select_acadYearId'] = $acadYear;
      $data['acadYears'] = $acdYears;
      $data['summary'] = $this->Admission_model->get_admissions_summary_report($acadYear);
      $data['main_content'] = 'admission/staff/admissions_summary_report';
      $this->load->view('inc/template', $data);
    }

   
    public function submit_admission_fee_status($afId){
        $result = $this->Admission_model->submit_admission_fee_statusbyId($afId);
        if($result){
            $this->session->set_flashdata('flashSuccess', 'Successfully Submitted');
            redirect('admission_process/application_details/'.$afId);
        }else{
            $this->session->set_flashdata('flashSuccess', 'Successfully Submitted');
            redirect('admission_process/application_details/'.$afId);
        }
    }

    public function submit_admission_fee_status_ajax(){
        $afId = $this->input->post('afId'); 
        $adm_status = $this->input->post('adm_status'); 
        echo $this->Admission_model->submit_admission_fee_statusbyId_ajax($afId,$adm_status);
    }

    public function student_data_add_to_erp(){
      $admissions = $this->Admission_model->admission_settings_get();
      $grades = array();
      foreach ($admissions as $key => $val) {
        $class_applied_for = json_decode($val['class_applied_for'], true);
          foreach ($class_applied_for as $key => $value) {
            array_push($grades, $value);
          }
      }
      // echo "<pre>"; print_r($data['application']); die();
      $data['grades'] = $grades;

      $applied_class = $this->input->post('applied_class');
      if (!empty($applied_class)) {
        $applied_class = $applied_class;
      }else{
        $applied_class = 'All';
      }
      // echo "<pre>"; print_r($applied_class); die();
      $data['currentAcadYearId'] = $this->acad_year->getAcadYearId();
      $promotionAcadYearId = $this->acad_year->getPromotionAcadYearId();
      $data['getclassinfo'] = $this->Student_Model->getClassByAcadYear($data['currentAcadYearId']);
      $data['form_data'] = $this->Admission_model->get_std_data_to_admission_form($applied_class, $data['currentAcadYearId']);
      $data['checkPlaceholder'] = $this->Admission_model->check_placeholderbyAcadearYear($data['currentAcadYearId']);
      // echo "<pre>"; print_r($data['form_data']);die();
      $data['main_content'] = 'admission/student_form/index';
      $this->load->view('inc/template', $data);
    }

    public function get_classsSection(){
      $classid = $_POST['classid'];
      $getclassectioninfo = $this->Admission_model->getclassection_for_admission($classid);
      echo json_encode($getclassectioninfo);
    }
    private function _constrcut_student_array($formData, $classId, $sectionid, $promotionAcadYearId, $doj){
      $stdData = array();
      $stdData['admission_acad_year'] = $formData->academic_year_applied_for;
      $stdData['student_firstname'] = ucwords(strtolower($formData->std_name));
      $stdData['student_lastname'] = null;
      $stdData['student_dob'] = ($formData->dob == '')?null:date('Y-m-d', strtotime($formData->dob));
      $stdData['gender'] = $formData->gender;
      $stdData['nationality'] = ($formData->nationality == '')?'Indian':$formData->nationality;
      $stdData['religion'] =  ($formData->religion == '')?null:$formData->religion;
      $stdData['category'] = ($formData->category == '')?null:$formData->category;
      $stdData['mother_tongue'] =  ($formData->std_mother_tongue == '')?null:$formData->std_mother_tongue;
      $stdData['rteid'] = 2; // if applicatiom form enabled
      $stdData['student_doj'] = $doj;
      $stdData['birth_taluk'] = ($formData->birth_taluk == '')?null:$formData->birth_taluk;
      $stdData['birth_district'] = ($formData->birth_district == '')?null:$formData->birth_district;
      $stdData['caste'] = ($formData->student_caste == '')?null:$formData->student_caste;
      $stdData['std_aadhar'] = ($formData->student_aadhar == '')?null:$formData->student_aadhar;
      $stdData['class_admitted_to'] = null;
      $stdData['admission_year'] = null;
      $stdData['admission_acad_year_id'] = $formData->academic_year_applied_for;
      $stdData['roll_no'] = null;
      $stdData['classid'] = $classId;
      $stdData['classsection'] = $sectionid;
      $stdData['boardingid'] = '1'; // get config
      $stdData['file_name'] = $formData->std_photo_uri;
      $stdData['acad_year'] = $formData->academic_year_applied_for;
      // $stdData['acad_year'] = '19';
      $stdData['board'] = '2'; // get config
      $stdData['medid'] = '1'; // get config
      $stdData['acad_year_id'] = $formData->academic_year_applied_for;
      $stdData['roll_num'] = 0;
      $stdData['contact_no'] = null;
      $stdData['add_status'] = 2;
      $stdData['s_email'] = '';
      $stdData['donor_name'] = '';
      return $stdData;
    }

    private function _constrcut_father_array($formData){
      $fatherData = array();
      $fatherData['first_name'] = $formData->f_name;
      $fatherData['last_name'] = '';
      $fatherData['qualification'] = null;
      $fatherData['occupation'] = $formData->f_position;
      $fatherData['mobile_no'] = $formData->f_mobile_no;
      $fatherData['aadhar'] = $formData->father_aadhar;
      $fatherData['annual_income'] = $formData->f_annual_gross_income;
      $fatherData['company'] = $formData->f_company_name;
      $fatherData['mother_tongue'] = $formData->father_mother_tongue;
      $fatherData['email'] = $formData->f_email_id;
      $fatherData['userid'] = '';
      return $fatherData;
    }

    private function _constrcut_mother_array($formData){
      $motherData = array();
      $motherData['first_name'] = $formData->m_name;
      $motherData['last_name'] ='';
      $motherData['qualification'] = null;
      $motherData['occupation'] = $formData->m_position;
      $motherData['mobile_no'] = $formData->m_mobile_no;
      $motherData['aadhar'] = $formData->mother_aadhar;
      $motherData['annual_income'] = $formData->m_annual_gross_income;
      $motherData['company'] = $formData->m_company_name;
      $motherData['mother_tongue'] = $formData->mother_mother_tongue;
      $motherData['email'] = $formData->m_email_id;
      $motherData['userid'] = '';
      return $motherData;
    }

    public function insert_student_data_to_erp(){
      $input =  $this->input->post();
    
      $medium = $this->settings->getSetting('medium');
      $board = $this->settings->getSetting('board');
      $boarding = $this->settings->getSetting('boarding');
      $promotionAcadYearId = $this->acad_year->getPromotionAcadYearId();

      foreach ($input['af_id'] as $afId => $val) {
        if (!empty($input['classid'][$afId])) {

          $result = $this->Admission_model->checkExit_in_student_admission($afId);
          $formData = $this->Admission_model->get_std_data_from_admission_formbyId($afId);
            if ($result) {
            $schoolDetails = $this->Admission_model->get_schoolDetailsbyfromid($afId);
            $schoolDocuments = $this->Admission_model->get_schoolDocumentsbyfromid($afId);
            $stdData = $this->_constrcut_student_array($formData, $input['classid'][$afId],  $input['classsection'][$afId], $promotionAcadYearId,  $input['student_doj'][$afId]);
            // echo "<pre>"; print_r($stdData);die();
            $fatherData = $this->_constrcut_father_array($formData);
            $motherData = $this->_constrcut_mother_array($formData);

            $config_admission_number = $this->settings->getSetting('admission_number');
            $lastRecord = $this->Student_Model->getLastStudentid(); 
            if (!$lastRecord) {
              $lastRecordId = 1;
            } else {
              $lastRecordId = ($lastRecord->id + 1);
            }
            $params['number'] = sprintf("%0".$config_admission_number->digit_count . "s", $lastRecordId);
            $params['year_of_joining'] = $stdData['admission_acad_year'];
            $params['classid'] = $this->Student_Model->getClassByID($stdData['classid']);   
            $stdData['admission_no'] = $this->_generateAdmissionNo($config_admission_number,$params);

            $this->db->trans_begin();

            $student_uid = $this->Student_Model->addStudentInfo($stdData, $patth=array('file_name'=>$formData->std_photo_uri));
            if($student_uid == 0) {
              $this->db->trans_rollback();
              $this->session->set_flashdata('flashError', 'Failed to insert Student Details.');
              redirect('admission_process/student_data_add_to_erp');
            }else{
              $father_uid = $this->Student_Model->addParentInfo($fatherData,$student_uid['stdAdmId'],'Father',$patth=array('file_name'=>''), $stdData['student_firstname']);
              if (!$father_uid) {
                $this->db->trans_rollback();
                $this->session->set_flashdata('flashError', 'Failed to insert Father Details.');
                redirect('admission_process/student_data_add_to_erp');
              }

              $mother_uid = $this->Student_Model->addParentInfo($motherData,$student_uid['stdAdmId'],'Mother',$patth=array('file_name'=>''), $stdData['student_firstname']);

              if (!$mother_uid) {
                $this->db->trans_rollback();
                $this->session->set_flashdata('flashError', 'Failed to insert Father Details.');
                redirect('admission_process/student_data_add_to_erp');
              }

              // $fatherAdd = array(
              //   'address_type'=>'1',
              //   'avatar_type'=>'2',
              //   'stakeholder_id'=>$father_uid,
              //   'Address_line1'=>$formData->f_addr,
              //   'district'=>$formData->f_district,
              //   'state'=>$formData->f_state,
              //   'country'=>$formData->f_county,
              //   'pin_code'=>$formData->f_pincode,
              //   'unformatted_address'=>$formData->f_addr.', '.$formData->f_district.', '.$formData->f_state.', '.$formData->f_county.', '.$formData->f_pincode
              // ); 

              // $motherAdd = array(
              //   'address_type'=>'1',
              //   'avatar_type'=>'2',
              //   'stakeholder_id'=>$mother_uid,
              //   'Address_line1'=>$formData->m_addr,
              //   'district'=>$formData->m_district,
              //   'state'=>$formData->m_state,
              //   'country'=>$formData->m_county,
              //   'pin_code'=>$formData->m_pincode,
              //   'unformatted_address'=>$formData->m_addr.', '.$formData->m_district.', '.$formData->m_state.', '.$formData->m_county.', '.$formData->m_pincode
              // ); 

             
              // $this->Admission_model->insert_father_address_details($fatherAdd);
              // $this->Admission_model->insert_mother_address_details($motherAdd);
              if (!empty($schoolDetails)) {
               $this->Admission_model->insert_schooling_details($schoolDetails, $student_uid['stdAdmId']);
              }
              if (!empty($schoolDocuments)) {
                $this->Admission_model->insert_documents_details($schoolDocuments, $student_uid['stdAdmId']);
              }
              
              if ($this->db->trans_status()) {
                $this->db->trans_commit();
                $this->Admission_model->status_change_admission_idInsert('Student added to ERP',$afId, $student_uid['stdAdmId']);
                $this->session->set_flashdata('Student Successfully moved');
              } else {
                $this->db->trans_rollback();
                $this->session->set_flashdata('flashError', 'Something went wrong');
              }
            }
          }else{   
            $this->session->set_flashdata('flashError','"'. $formData->std_name.' - " Already in ERP');
          }
        }
      }
      redirect('admission_process/student_data_add_to_erp');
    }

    private function _generateAdmissionNo($config_admission, $params = []) {
      
      $admission_number = '';

      switch ($config_admission->admission_generation_algo) {
        case 'NPSAGA':
           $admission_number = $params['year_of_joining'].$config_admission->infix.$params['number'];
          break;
        case 'NH': {
            // Greater than 2 takes all the class from 1st to so on.
            if ($params['classid']->type >= 2) 
              $admission_number = $config_admission->infix.$params['number'];
            else 
              $admission_number = 'P'.$params['number'];            
          break;
        }
        case 'NEXTELEMENT': {
          $admission_number = $params['year_of_joining'].$config_admission->infix.$params['number'];
          break;
        }
        case 'YASHASVI': {
          $admission_number = $config_admission->infix.$params['number'];
          break;
        }
        case 'WPL':
        //Infix 'N' if nursery, 'P' if primary, 'H' if high school
          switch ($params['classid']->type) {
            case 1:
              $classType = 'N';
              break;
            case 2:
              $classType = 'P';
              break;
            case 3:
              $classType = 'H';
              break;
          }
          $admission_number =$config_admission->infix.$classType.$params['number'];
          break;
      }

      return $admission_number;

    }

    public function submit_admission_fee_status_ajax_popup(){
        $afId = $this->input->post('afId'); 
        $adm_status_ajax = $this->input->post('adm_status_ajax'); 
        echo $this->Admission_model->submit_admission_fee_statusbyId_ajax($afId,$adm_status_ajax);
    }

    public function print_empty_form_gradewise($id=''){
      // $data['admissions'] = $this->Admission_model->admission_settings_get();
      $school_short_name = $this->settings->getSetting('school_short_name');
      if ($school_short_name == 'vetjpnagar') {
        $linkpath = $this->Admission_model->get_application_form_pdf_path($id);
        $url = $this->filemanager->getFilePath($linkpath);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download('admission_form.pdf', $data, TRUE);        
      }
      $data['main_content'] = 'admission/staff/forms/'.$school_short_name;
      $this->load->view('inc/template', $data);
    }


    public function individual_application_form($admId, $adm_au_id,$adm_setting_id){
   
       $fields = $this->db->list_fields('admission_forms');
    
       $fData = [];
       foreach ($fields as $field){
         if ($field !='id' && $field!='au_id' && $field!='created_on' && $field!='admission_setting_id' && $field!='filled_by' && 
            $field!='enquiry_id' && $field!='receipt_html_path' && $field!='receipt_number' && $field!='template_pdf_path' && 
            $field!='seat_allotment_pdf_path' && $field!='seat_allotment_no' && $field!='std_photo_uri' && $field!='last_modified_by' && 
            $field!='pdf_status' && $field!='modified_on' && $field!='custom_field'  && $field!='instruction_file' && 
            $field!='lang_1_choice' && $field!='lang_2_choice' && $field!='lang_3_choice' && $field!='seat_allotment_date' && 
            $field!='nationality_other' && $field!='religion_other' && $field!='mother_tongue_other' && 
            $field!='father_mother_tongue_other' && $field!='academic_year_applied_for' && $field!='application_no' && $field!='std_photo_uri_resize') {
           array_push($fData, $field);
         } 
       }
       $disabled_fields = $this->Admission_model->get_admission_enabled_fields();
       
       //Remove disabled fields
       foreach ($disabled_fields as $dis_field) {
        foreach ($fData as $key => $field) {
          if ($dis_field == $field) {
            unset($fData[$key]);
            break;
          }
        } 
       }
      $data['fields'] = $this->_prepareAdmissionSeperateInput($fData);
      $data['academic_years'] = $this->Admission_model->get_academic_years();
      $data['studentData'] = $this->studentData;      
      if(!$this->settings->getSetting('admission_pick_status_from_table')){
        $follow_up_status = json_decode($this->settings->getSetting('application_form_status'));
        $follow_up_status_const = ['Application Amount Paid', 'Admit','Rejected', 'Duplicate'];
        $data['follow_up_status'] = array_merge($follow_up_status, $follow_up_status_const);
      }else{
        $data['follow_up_status'] =  $this->Admission_model->get_user_status();
      }
      $data['status_permission'] = $this->Admission_model->get_admission_status_permissions();
      $data['enquiry_pick_status_from_table'] = $this->settings->getSetting('enquiry_pick_status_from_table');
        if ($data['enquiry_pick_status_from_table']) {
          $status = $this->enquiry_model->get_status_list_from_table();
          $data['enquiry_follow_up_status'] = [];
          foreach( $status as $key => $val){
            $data['enquiry_follow_up_status'][$key] = $val->user_status;
          }
        } else {
          $data['enquiry_follow_up_status'] = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
        }
      $data['school_details'] = $this->Admission_model->get_SchoolDetailsbyStdId($admId);
      $data['school_short_name'] = $this->settings->getSetting('school_short_name');
      // echo '<pre>';print_r($data['school_short_name']);die();
      $required_fields = $this->enquiry_model->get_enquiry_required_fields();
      $data['acad_years'] = $this->__formAcadYears();
      $data['required_fields'] = $this->__construct_name_wise_required($required_fields);
      $data['disabled_fields'] = $this->enquiry_model->get_enquiry_disabled_fields();
      $data['follow_admission'] = $this->Admission_model->get_admission_byId($admId);
      $data['eTemplate'] = $this->Admission_model->get_admission_email_template();
      if(empty($data['eTemplate'])){
        $data['eTemplate'] = $this->Admission_model->get_email_template();
      }
      $data['revert_admission_email_template'] = $this->Admission_model->get_admission_rever_email_template();
      $data['dob_instruction'] = $this->settings->getSetting('enquiry_dob_instruction',0);
      $data['boards'] = $this->settings->getSetting('board');
      $data['know_aboutUs'] = json_decode($this->settings->getSetting('enquiry_how_did_you_get_to_know_about_us'));
      $data['enquiry_additional_coaching'] = json_decode($this->settings->getSetting('enquiry_additional_coaching'));
      $data['enquiry_combination'] = json_decode($this->settings->getSetting('enquiry_combination'));
      $category = 'Admission';
      $data['sTemplate'] = $this->Admission_model->get_sms_template($category);
      $data['collect_fee'] =  $this->authorization->isAuthorized('ADMISSION.COLLECT_OFFLINE_FEE');
      $data['admissions_payment_mode'] = $this->settings->getSetting('admissions_payment_mode');
      $data['application_fee_status'] =  $this->Admission_model->application_fee_status($admId);
      $data['admissions'] = $this->Admission_model->admission_receipt_by_id($admId);
      $data['admission_setting_id'] = $adm_setting_id;
      $data['afId'] = $admId;
      $data['au_Id'] = $adm_au_id;
      $data['v1'] = 0;
      $data['config_val'] = $this->_get_admissions_settings_byId($adm_setting_id);
      $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($adm_au_id, $admId);
      $combination = '';
      $data['streams'] = json_decode($data['config_val']['streams']);
      if (!empty($data['final_preview']->combination_id)) {
        $combination = $data['final_preview']->combination;
    
        if (isset($data['streams']->$combination) && is_array($data['streams']->$combination)) {
            foreach ($data['streams']->$combination as $key => $val) {
                if ($val->id == $data['final_preview']->combination_id) {
                    $combination = $val->name;
                }
            }
        }
    
        $data['fields']['student']['stream'] = $data['final_preview']->combination;
        $data['fields']['student']['combination'] = $combination;
      }
      $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($admId);
      $data['grade_list'] = $this->Admission_model->get_all_grade();
      $data['student_admission_form_id'] = $this->Admission_model->get_student_admission_form_id($admId); 
      $data['rte'] = $this->settings->getSetting('rte');
      $data['document_verification_in_admissions'] = $this->settings->getSetting('document_verification_in_admissions');
      $data['enable_payment_link_tab'] = $this->settings->getSetting('show_payment_link_tab_in_admissions');
      $data['name_to_caps'] = $this->settings->getSetting('display_names_caps');
      $data['document_list'] = $this->Admission_model->get_documents_list_by_adm_setting_id($adm_setting_id);
      $data['main_content'] = 'admission/student_form/individual_application/individual_application_form';
      $this->load->view('inc/template', $data);
    }

     private function __construct_name_wise_required($requiredData){
      $fields = $this->db->list_fields('enquiry');
      $rData = [];
      foreach ($fields as $key => $val) {
          if (in_array($val, $requiredData)) {
              $rData[$val] = array('font' =>'TRUE', 'required' =>'required');
          }else{
              $rData[$val] = array('font' =>'', 'required' =>'');
          }
      }
      return $rData;
  }

  private function __formAcadYears() {
    $acad_years = [];

    //Get the current year
    $acad_year = new stdClass();
    $acad_year->acad_year_id = $this->settings->getSetting('academic_year_id');
    $acad_year->acad_year_name = $this->acad_year->getAcadYearById($acad_year->acad_year_id);
    $acad_year->selected = '';
    $acad_years[] = $acad_year;

    //Get the promotion year
    $prom_acad_year = new stdClass();
    $prom_acad_year->acad_year_id = $this->settings->getSetting('promotion_academic_year_id');
    $prom_acad_year->acad_year_name = $this->acad_year->getAcadYearById($prom_acad_year->acad_year_id);
    $prom_acad_year->selected = 'selected';
    $acad_years[] = $prom_acad_year;

    //Get the double promotion year
    $d_prom_acad_year = new stdClass();
    $d_prom_acad_year->acad_year_id = $this->settings->getSetting('double_promotion_academic_year_id');
    $d_prom_acad_year->acad_year_name = $this->acad_year->getAcadYearById($d_prom_acad_year->acad_year_id);
    $d_prom_acad_year->selected = '';
    $acad_years[] = $d_prom_acad_year;

    $default_acad_year_id = $this->settings->getSetting('default_enquiry_year_id');
    if ($default_acad_year_id == '' || $default_acad_year_id == null) {
      //Do Nothing
    } else {
      $acad_year->selected = ($default_acad_year_id == $acad_year->acad_year_id) ? 'selected' : '';
      $prom_acad_year->selected = ($default_acad_year_id == $prom_acad_year->acad_year_id) ? 'selected' : '';
      $d_prom_acad_year->selected = ($default_acad_year_id == $d_prom_acad_year->acad_year_id) ? 'selected' : '';
    }

    return $acad_years;
  }

    public function get_mapped_status() {
      $result = $this->Admission_model->get_mapped_status($_POST['curr_status']);
      echo json_encode($result);
    }

    private function _prepareAdmissionSeperateInput(&$input){
      $return_data = [];
      foreach ($input as $k => $v) {
        $start_key = substr($v, 0, 2);
        if ($start_key == 'f_' || $start_key == 'fa') {
          $key = str_replace("f_", "", $v);
          $return_data['father'][$key] = $v;
        } elseif ($start_key == 'm_' || $start_key == 'mo') {
          $key = str_replace("m_", "", $v);
          $return_data['mother'][$key] = $v;
        }elseif ($start_key == 'g_' || $start_key == 'gu') {
          $key = str_replace("g_", "", $v);
          $return_data['gurdian'][$key] = $v;
        }
        else {
          $return_data['student'][$v] = $v;
        }
      }
      //echo '<pre>';print_r($return_data);

      return $return_data;
  }
    public function get_admission_followup_details($admId) {
      $result = $this->Admission_model->get_admission_followup_details($admId);
      echo json_encode($result);
      
    }

    // public function save_student_profile_by_user($admId){
    //   // echo '<pre>';print_r($admId);die();
  	// 	echo $this->Admission_model->save_student_profile_by_user_by_id($admId);
  	// }

    public function save_student_profile_by_user($admId){
      $edit_tracking =  $this->Admission_model->save_edited_data($admId,$_POST);
  		$result = $this->Admission_model->save_student_profile_by_user_by_id($admId);
      echo $result;
  	}

    public function get_student_address_by_user(){
  		// $input_name = $_POST['input_name'];
      $af_id = $_POST['af_id'];
  		$result = $this->Admission_model->get_student_address_by_user_id($af_id);
  		echo json_encode($result);
  	}

  public function admission_offers()
  {
    $data['main_content'] = 'admissionoffers/offers';
    $this->load->view('inc/template', $data);
  }
  public function admission_budget()
  {
    $seat_book = $this->Admission_model->get_seat_series_book();
    $data['seat_format'] = $this->Admission_model->receipt_format_creation($seat_book);
    $data['admission_combination'] = json_decode($this->settings->getSetting('admission_combination'));
    $data['admission_budget_filter'] = json_decode($this->settings->getSetting('admission_budget_filter'));
    $data['combinationList'] = $this->Student_Model->getCombinations();
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['main_content'] = 'admissionbudget/budget';
    $this->load->view('inc/template',$data);
  }

  public function assign_counselor(){
    $data['counselor_load'] = $this->Admission_model->get_counselor_load();
    $data['staff_list'] = $this->Admission_model->get_staff_list_search();
    $data['unassinged'] = '-1';
    $data['counselor_staff'] = $this->Admission_model->get_couselor_list();
    if(!$this->settings->getSetting('admission_pick_status_from_table')){
      $data['admission_status'] = json_decode($this->settings->getSetting('application_form_status'));
    }else{
      $data['admission_status'] =  $this->Admission_model->get_user_status();
    }
    $data['counselor'] = $this->Admission_model->get_couselor_list();
    // echo '<pre>';print_r($data['counselor_load']);die();
    $data['main_content'] = 'admission/assign_counselor_page';
    $this->load->view('inc/template',$data);
  }
  public function add_budget(){
    
    $classes = explode('_', $_POST['classId']);
    $classId = $classes[0];
    $class_name = $classes[1];
    $admission_combination = $_POST['admission_combination'];
    $casteid = $_POST['casteid'];
    $number_of_seats = $_POST['number_of_seats'];
    $friendly_name = $_POST['friendly_name'];
    $remarks = $_POST['remarks'];
    $seat_allotment_id = $_POST['seat_allotment_id'];
    echo $this->Admission_model->add_new_budget($classId, $admission_combination, $casteid, $number_of_seats,$friendly_name,$remarks, $class_name, $seat_allotment_id);
   }

  public function add_new_seats() {

    $budget_id = $_POST['budget_id'];
    $number_of_seats = $_POST['number_of_seats'];
    $seat_allotment_id = $_POST['seat_allotment_id'];
    echo $this->Admission_model->add_new_seats($budget_id,$number_of_seats, $seat_allotment_id);
  }
 
   public function get_budget() {
    $result = $this->Admission_model->get_budget();
    echo json_encode($result);

   }
  public function change_seat_allotment_status()
  {
    $id = $_POST['id'];
    echo $this->Admission_model->change_seat_allotment_status($id);
  }
  public function remove_seat()
  {
    $id=$_POST['id'];
    // $budget_id = $_POST['budget_id'];
    // echo "<pre>";
    // print_r($budget_id);
    // die();
    echo $this->Admission_model->remove_seat($id);
  }
  
   public function get_view_details_budget_table($id){
    $data['budget_id'] = $id;
    $data['budget'] = $this->Admission_model->get_view_details_budget_table($id);
    $data['seat_allotment_series_id'] = $this->Admission_model->get_admission_budgetidallotment_series_id($id);
    $data['main_content'] = 'admissionbudget/view_budget_details';
    $this->load->view('inc/template', $data);
  }

    
    public function add_new_offer() {
      $offer_name = $_POST['offer_name'];
      $offer_amount = $_POST['offer_amount'];
      $offer_disc = $_POST['offer_disc'];
      echo $this->Admission_model->add_new_offer($offer_name, $offer_amount, $offer_disc);
    }
    public function get_offer() {
      $result=$this->Admission_model->get_offer();
      echo json_encode($result);
    }

    public function collect_offline_fee_for_application(){
      $input = $this->input->post();
      $this->db->trans_begin();
      $transaction = $this->Admission_staff_model->insert_transcations_details($input);

      if (empty($transaction)) 
      {
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Something went wrong');    
          redirect('admission_process/individual_application_form');
      }else{
          $this->Admission_model->update_transcation_details($input['lastId'], 'SUCCESS','Application Amount Paid');
          $isApplicationGenerated = $this->Admission_model->is_application_no_generated($input['lastId']);
          if ($isApplicationGenerated == 0){
              $this->Admission_model->update_application_receipt($input['lastId']);
          }
          $isReceiptNoGenerated = $this->Admission_model->is_application_receipt_no_generated($input['lastId']);
          if ($isReceiptNoGenerated == 0){
              $this->Admission_model->update_admission_application_receipt($input['lastId']);
          }
          $this->db->trans_commit();
          // redirect('admission_process/individual_application_form');
      }    
      echo $transaction;
  }

    public function view_empty_form($file){
        $rValue = $this->_get_selection_template_values($file);
        $data['file']=$file;
        $data['sc_name']=$rValue['school_short'];
        $data['guidelines']=$rValue['guidelines'];
        $data['school_name']= $rValue['school_name'];
        $data['config_val']= $rValue;
        $data['main_content'] = 'admission/staff/forms/'.$rValue['school_short'].'/index';
        $this->load->view('inc/template', $data);
    }


    public function report_admissions() {
        $colname= $this->columnList;
        $disabled_fields = $this->Admission_model->get_admission_enabled_fields();
        foreach ($disabled_fields as $dis_field) {
          foreach ($colname as $key => $col) {
            if ($dis_field == $col['columnName']) {
              unset($colname[$key]);
              break;
            }
          }
        }
       
        $data['columnList'] = $colname; 
        // $admissions = $this->Admission_model->admission_settings_get();
        // $grades = array();
        // foreach ($admissions as $key => $val) {
        //   $class_applied_for = json_decode($val['class_applied_for'], true);
        //   foreach ($class_applied_for as $key => $value) {
        //     array_push($grades, $value);
        //   }
        // }
        
        if(!$this->settings->getSetting('admission_pick_status_from_table')){
          $data['follow_up_status'] = json_decode($this->settings->getSetting('application_form_status'));
        }else{
          $data['follow_up_status'] =  $this->Admission_model->get_user_status();
        }
        // $data['grades'] =array_unique($grades, SORT_REGULAR);
        $data['grades'] =  $this->Admission_model->get_grade_names();
        $data['combinations'] =  $this->Admission_model->get_combinations();
        $combination = [];
        if(!empty($data['combinations'])){
          foreach($data['combinations'] as $key=>$val){
            $val->streams = json_decode($val->streams);
          }
          foreach($data['combinations'] as $key => $val){
            if(!empty($val->streams)){
              foreach($val->streams as $k =>$v){
                foreach($v as $m =>$n){
                    array_push($combination,$n);
                }
              }
            }
          }
        }
        $data['combination'] =$combination;
        $data['main_content'] = 'admission/staff/export';
        $this->load->view('inc/template', $data);
    }

    public function export_to_excel(){
        $admissions = $this->Admission_model->admission_settings_get();
        $grades = array();
        foreach ($admissions as $key => $val) {
          $class_applied_for = json_decode($val['class_applied_for'], true);
          foreach ($class_applied_for as $key => $value) {
            array_push($grades, $value);
          }
        }
        $data['grades'] =$grades;
        $data['columnList'] = $this->columnList;
        $selectedIndex = $this->input->post('fields');
        $class_list = $this->input->post('class_list');
        // $schooling = $this->input->post('schooling');
        $ad_status = $this->input->post('ad_status');
        // $exp = explode('-', $schooling);
  
        $selectedColumns = array();
        $displayColumns = array();
        foreach($selectedIndex as $fIndex) { 
          foreach ($data['columnList'] as $col) {
            if ($col['index'] == $fIndex) {
                $selectedColumns[] = (array)$col;
                $displayColumns[] = $col;
            }
          }
        }
 
        $colString = 'application_no';
        foreach ($selectedColumns as $col) {
            if ($colString != '') {
              $colString .= ',';
            }
            $colString .= $col['columnNameWithTable'] . ' as ' . $col['varName'];
        }

        $data['selectedColumns'] = $displayColumns;
        $data['admission_prev_schools'] = $this->Admission_model->get_schooling_details_clsWise($colString, $ad_status, $class_list);
        // echo '<pre>';print_r($data['admission_prev_schools']);die();

        $header = array();
        $values = array();
        $years = array();
        $table_info = array();
        if (!empty($data['admission_prev_schools'])) {
          foreach ($data['admission_prev_schools'] as $key => $database) {
            $student_info[$database->application_no]['std_name'] = $database->std_name;
            $student_info[$database->application_no]['applied_for'] = $database->grade_applied_for;
            $table_info[$database->application_no] = $database;
            foreach ($admissions as $key => $config_val) {
              $prev_eduction_info = json_decode($val['prev_eduction_info'], true);
              if (!empty($config_val['prev_eduction_info'])) {
                if(array_key_exists($database->grade_applied_for, $prev_eduction_info['class'])) {
                  foreach ($prev_eduction_info['year'] as $key => $year) {
                    if($year == $database->year) {
                      $years[] = $year;
                      foreach ($prev_eduction_info['class'][$database->grade_applied_for]['subject'] as $key => $subject) {
                        if (isset($database->marks)) {
                           foreach ($database->marks as $key => $val) {                        
                            $jsan =  json_decode($val->sub_name);
                            if ($subject['id'] == $jsan->sub_id) {
                              $lang_name = $jsan->name;
                              switch ($subject['type']) {
                                case 'label':
                                  $header[$year][] = $subject['name'];
                                  $values[$database->application_no][$year][$subject['name'].'_grade'] = strtoupper($val->grade);
                                  $values[$database->application_no][$year][$subject['name'].'_percentage'] = $val->percentage;
                                  break;
                                case 'text':
                                if (!empty($val->grade)) 
                                  $gr = '('.strtoupper($val->grade) .')';
                                else
                                  $gr = '';

                                if (!empty($val->percentage)) 
                                  $per = '('.strtoupper($val->percentage) .')';
                                else
                                  $per = '';
                                  $header[$year][]  = '_'.'lName';
                                  $header[$year][]  = $subject['name'];
                                  // if (!empty($lang_name)) {
                                  $values[$database->application_no][$year][$subject['name'].'-'.'Name'] = $lang_name.'<br>';
                                  $values[$database->application_no][$year][$subject['name'].'-'.'grade'] = $gr;
                                  $values[$database->application_no][$year][$subject['name'].'-'.'percentage'] = $per;
                                  // }
                                break;
                              }
                              break;
                            }
                          }
                        }                      
                      }
                    }
                  }
                }
              }
            }
          }


          foreach ($header as $year => $value) {
            $data['headers'][$year] = array_unique($value);
          }
          $data['values'] = $values;
          // echo '<pre>';print_r($data['headers']);
          // echo '<pre>';print_r($data['values']);die();
          $data['adm_info'] = $student_info;
          $data['tableInfo'] = $table_info;
          $data['years'] = array_unique($years);
        }
       
     
        // echo "<pre>"; print_r($data['headers']); 
        // echo "<pre>"; print_r($data['values']); die();
        $data['main_content'] = 'admission/staff/export';
        $this->load->view('inc/template', $data); 
    }

    public function generateReport(){
      // $admissions = $this->Admission_model->admission_settings_get();
      // $grades = array();
      // foreach ($admissions as $key => $val) {
      //   $class_applied_for = json_decode($val['class_applied_for'], true);
      //   foreach ($class_applied_for as $key => $value) {
      //     array_push($grades, $value);
      //   }
      // }
      // $data['grades'] =$grades;
      $data['columnList'] = $this->columnList;
      
      $selectedIndex = $this->input->post('fields');
      $class_list = $this->input->post('class_list');

      $combination = $this->input->post('combination');
      $ad_status = $this->input->post('ad_status');
      // $exp = explode('-', $schooling);

      $selectedColumns = array();
      $displayColumns = array();
      foreach($selectedIndex as $fIndex) { 
        foreach ($data['columnList'] as $col) {
          if ($col['index'] == $fIndex) {
              $selectedColumns[] = (array)$col;
              $displayColumns[] = $col;
          }
        }
      }

      $colString = 'application_no';
      foreach ($selectedColumns as $col) {
          if ($colString != '') {
            $colString .= ',';
          }
          $colString .= $col['columnNameWithTable'] . ' as ' . $col['varName'];
      }
      $data['selectedColumns'] = $displayColumns;
      $adm_info = $this->Admission_model->get_schooling_details_clsWise($colString, $ad_status, $class_list,$combination, $selectedIndex);
      if(empty($adm_info)){
        echo 0;
        exit();
      }

      // $data['feeCheck'] = array_intersect($selectedIndex, $adm_info['header']);

      foreach ($adm_info['result'] as $key => &$val) {
        if (isset($val->category)!= ''&& isset($val->category) !='0') {
          foreach ($this->settings->getSetting('category') as $value => $name) {
            if ($val->category == $value) {
              $val->category = $name;
            }
          }
         }else{
          $val->category = 'NA';
         }

        if (isset($val->boarding)!= ''&& isset($val->boarding) !='0') {
          foreach ($this->settings->getSetting('boarding') as $value => $name) {
            if ($val->boarding == $value) {
              $val->boarding = $name;
            }
          }
         }else{
          $val->boarding = 'NA';
         }

         if(isset($val->student_admission_status)){
            if($val->student_admission_status == 1){
              $val->student_admission_status = 'Pending';
            }else if($val->student_admission_status == 2){
              $val->student_admission_status = 'Approved';
            }else if($val->student_admission_status == 3){
              $val->student_admission_status = 'Rejected';
            }else if($val->student_admission_status == 4){
              $val->student_admission_status = 'Alumini';
            }
         }

      }
      $data['adm_info']  = $adm_info;
      // echo '<pre>';print_r( $data['adm_info']);
      echo json_encode($data['adm_info']);
      // $data['follow_up_status'] = json_decode($this->settings->getSetting('application_form_status'));
      // $data['follow_up_status'] =  $this->Admission_model->get_user_status();
      // $data['main_content'] = 'admission/staff/export';
      // $this->load->view('inc/template', $data); 
    }

    public function send_sms_getdata(){
      $afId = $this->input->post('afId');
      $result = $this->Admission_model->get_dataById_for_sms($afId);
      $admissions = $this->Admission_model->admission_settings_get();
      foreach ($admissions as $key => $adm) {
          if ($adm['instructin_file'] == $result->instruction_file) {
              $sms = $adm['sms'];
          }
      }
      $sms_sontent = $sms;
      $sms_sontent = str_replace('%std_name%', $result->std_name, $sms_sontent);
      $sms_sontent = str_replace('%applied_for%', $result->grade_applied_for, $sms_sontent);
      $template = '';
      $template.='<div class="panel panel-default">';
      $template.='<div class="panel-body">';
      $template.='<input type="hidden" id="afId" name="afId" value="'.$afId.'">';
      $template.='<input type="hidden" id="fMobile" name="f_mobile" value="'.$result->f_mobile_no.'">';
      $template.='<input type="hidden" id="mMobile" name="m_mobile" value="'.$result->m_mobile_no.'">';
      $template.='<table class="table table-bordered">';
      $template.='<tr>';
      $template.='<th>Student Name</th>';
      $template.='<th>'.$result->std_name.'</th>';
      $template.='<th>Applied for</th>';
      $template.='<th>'.$result->grade_applied_for.'</th>';
      $template.='</tr>';
      $template.='<tr>';
      $template.='<th>Father Name</th>';
      $template.='<th>'.$result->f_name.'</th>';
      $template.='<th>Mobie Number</th>';
      $template.='<th>'.$result->f_mobile_no.'</th>';
      $template.='</tr>';
      $template.='<tr>';
      $template.='<th>Mother Name</th>';
      $template.='<th>'.$result->m_name.'</th>';
      $template.='<th>Mobie Number</th>';
      $template.='<th>'.$result->m_mobile_no.'</th>';
      $template.='</tr>';
      $template.='</table>';
      $template.='<h3 style="color: red"><strong>' .$result->std_name. ' will be marked as SELECTED <strong></h3>';
      $template.='</div>';
      $template.='<div class="panel-heading">';
      $template.='<h3 class="panel-title">SMS</h3>';
      $template.='</div>';
      $template.='<div class="panel-body">';
      $template.='<input type="hidden" id="sms_value" value="'.$sms_sontent.'">';
      $template.='<p style="text-align:justify">'.$sms_sontent.'</p>';
      $template.='</div>';
      $template.='</div>';
      print($template);
    }

    public function send_email_getdata(){
      $afId = $this->input->post('afId');
      $emailtemplateId = $this->input->post('emailtemplateId');
      $result = $this->Admission_model->get_email_templatebyId($afId, $emailtemplateId);
      echo json_encode($result);
    }

    public function submit_selected_student(){
      $afId = $this->input->post('afId');
      $fMobile = $this->input->post('fMobile');
      $mMobile = $this->input->post('mMobile');
      $sms = $this->input->post('sms');
      $number = $fMobile.','.$mMobile;

      $smsint = $this->settings->getSetting('smsintergration');
      $content =  urlencode(''.$sms.'');
      $get_url = 'http://'.$smsint->url.'?method=sms&api_key='.$smsint->api_key.'&to='.$number.'&sender='.$smsint->sender.'&message='.$content;
      $check_returned = $this->curl->simple_get($get_url);
      if(!empty($check_returned)) {
          $check_returned = json_decode($check_returned);
          if($check_returned->status == 'OK') {
            echo $this->Admission_model->update_sms_status($afId);
          } else {
            echo  $this->session->set_flashdata('flashError', 'Unable to send SMS');
          }
        } else {
            echo $this->session->set_flashdata('flashError', 'Unable to send SMS');
        }
    }

    public function send_email_selected_student(){
      $fMail = $this->input->post('fMail');
      $mMail = $this->input->post('mMail');
      $email_subject = $this->input->post('email_subject');
      $template_content = $this->input->post('template_content');
      $registered_email = $this->input->post('registered_email');
      $af_id = $this->input->post('af_id');
      $emails = [];
      array_push($emails, $this->input->post('fMail'),$this->input->post('mMail'));
      
      $memberEmail = [];
      foreach ($emails as $key => $val) {
        $memberEmail[]['email'] = $val;
        // array_push($memberEmail,$val);
      }

      // $sent_by = $this->authorization->getAvatarStakeHolderId();
      // $email_master_data = array(
      //   'subject' => $email_subject,
      //   'body' => $template_content,
      //   'source' => 'Admissions',
      //   'sent_by' => $sent_by,
      //   'recievers' => "Parents",
      //   'from_email' => $registered_email,
      //   'acad_year_id' => $this->acad_year->getAcadYearID(), 
      //   'visible' => 1,
      //   'sending_status' => 'Completed',
      //   'sender_list'=>implode(',',$memberEmail)
      // );

      // $email_master_id = $this->emails_model->saveEmail($email_master_data);
      // foreach ($memberEmail as $email) {
      //   if (!empty($email)) {
      //       $this->enquiry_model->save_sending_email_data([
      //           'stakeholder_id'   => 0,
      //           'avatar_type'      => 0,
      //           'email'            => $email,
      //           'email_master_id'  => $email_master_id,
      //           'status'           => 'Awaited'
      //       ]);
      //   }
      // }

      $result = $this->Admission_model->insert_email_details_admission_data($af_id,$fMail,$mMail,$email_subject,$template_content);
      if ($result) {
        return $this->__send_email($template_content, $email_subject, $memberEmail, $registered_email);
        // sendEmail($template_content, $email_subject, $email_master_id, $memberEmail, $registered_email, '');
      }else{
        return 0;
      }

    }


    private function __send_email($message, $subject, $members, $from_email) {
      if($from_email == '') {
        return -1;
      }
      $from_name = $this->settings->getSetting('school_name');
      // $smtp_user = $set['settings']['smtp_user'];
      // $smtp_pass = urlencode($set['settings']['smtp_pass']);
      // $smtp_host = $set['settings']['smtp_host'];
      // $smtp_port = $set['settings']['smtp_port'];
      $smtp_user = CONFIG_ENV['smtp_user'];
      $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
      $smtp_host = CONFIG_ENV['smtp_host'];
      $smtp_port = CONFIG_ENV['smtp_port'];

      $data = array(
          'message' => $message,
          'attachment' => '',
          'subject' => $subject,
          'members' => $members,
          'from_email' => $from_email,
          'from_name' => $from_name,
          'smtp_user' => $smtp_user,
          'smtp_pass' => $smtp_pass,
          'smtp_host' => $smtp_host,
          'smtp_port' => $smtp_port
      );


      $data = http_build_query($data);

      // echo "data: ".$data;die();
      
      $curl = curl_init();


      $username = CONFIG_ENV['job_server_username'];
      $password = CONFIG_ENV['job_server_password'];

      curl_setopt_array($curl, array(
          CURLOPT_URL => CONFIG_ENV['job_server_email_uri'],
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_USERPWD => $username . ":" . $password,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POST => 1,
          CURLOPT_POSTFIELDS => $data,
          CURLOPT_HTTPHEADER => array(
              "Accept: application/json",
              "Cache-Control: no-cache",
              "Content-Type: application/x-www-form-urlencoded",
              "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
          ),
      ));

      $response = curl_exec($curl);
      $err = curl_error($curl);

      curl_close($curl);

      if ($err) {
        // echo "cURL Error #:" . $err;
        return 0;
      } else {
        return 1;
      }
    }

    public function combinations(){
      $selectedClass = $_POST['selectedClass'];
      $admissions = $this->Admission_model->admission_settings_get();

      $combination =array();
      foreach ($admissions as $key => $val) {
        $class_applied_for = json_decode($val['class_applied_for'], true);
        $streams = json_decode($val['streams'], true);
        foreach ($class_applied_for as $key => $value) {
          if ($value == $selectedClass) {
            if (!empty($streams)) {
             array_push($combination, $streams);
            }
          }
        }
      }
      $output = '';
      foreach ($combination as $comb) {
        $output .='<option value="">Select Streams</option>';
        foreach ($comb as $stream => $val) {
          $output .='<option value="'.$stream.'">'.$stream.'</option>';
        }
      }
      print($output);
    }

    public function download_report($previewId){
      $report = $this->Admission_model->get_admission_report_download($previewId);
      $link = $report->report_card;
      $file = explode("/", $link);
      $file_name = 'Report card';
      $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
      $url = $this->filemanager->getFilePath($link);
      $data = file_get_contents($url);
      $this->load->helper('download');
      force_download($fname, $data, TRUE);
    }

    public function admission_settings(){
      $receipt_book = $this->fbm->get_receipt_books();
      $data['email_templates'] = $this->Admission_model->get_email_templates_all();
      // echo "<pre>"; print_r($data['email_templates']); 
      $data['recept_format'] = $this->fee_library->receipt_format_creation($receipt_book);
      $data['admission_settings'] = $this->Admission_model->get_admission_settings_all();
      $data['staffDetails']  = $this->Admission_model->getStaffDetails_for_admission();
      $data['main_content'] = 'admission/settings/index';
      $this->load->view('inc/template', $data); 
    }
    public function admission_process_seat_allotment(){
    $seat_book = $this->Admission_model->get_seat_series_book();
    $data['seat_format'] = $this->Admission_model->receipt_format_creation($seat_book);
    $data['main_content'] = 'admission/admission_seat_allotment/index';
    $this->load->view('inc/template', $data);
    
    }
  public function insert_seat_series()
  {
    $result = $this->Admission_model->insert_seat_series();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Seat Booking algo created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('admission_process/admission_process_seat_allotment');
  }
  public function delete_seat_series($id)
  
  {
    $result = $this->Admission_model->delete_seat_series($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Seat Series Deleted Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('admission_process/admission_process_seat_allotment');
  }
  public function get_seat_seriesbyid()
  {                       
    $rbId = $_POST['rbId'];
    $result = $this->Admission_model->get_seat_seriesbyid($rbId);
    echo json_encode($result);
  }
  
  public function update_seat_series_numberbyid()
  {
    $rbId = $_POST['rbId'];
    $running_number = $_POST['running_number'];
    echo $this->Admission_model->update_seat_series_numberbyid($rbId, $running_number);
  }

    public function edit_admission_setting($id){
      $classId = $this->input->post('className');
      if ($classId == null)
      $classId = array ();
      $data['selectedClasses'] = $classId;
      $currentAcadYearId = $this->acad_year->getAcadYearId();
      $data['classList'] =  $this->Student_Model->getClassByAcadYear($currentAcadYearId);
      $data['edit_setting'] = $this->Admission_model->edit_admission_settingbyId($id);
      // echo "<pre>"; print_r($data['edit_setting']); die();
      $data['documents_list'] = $this->Admission_model->get_documents_list();
      $data['main_content'] = 'admission/settings/edit_setting';
      $this->load->view('inc/template', $data); 
    }

    public function add_settings(){
      $classId = $this->input->post('className');
      if ($classId == null)
      $classId = array ();
      $data['selectedClasses'] = $classId;
      $currentAcadYearId = $this->acad_year->getAcadYearId();
      $data['classList'] =  $this->Student_Model->getClassByAcadYear($currentAcadYearId);
      $data['acad_year'] =  $this->acad_year->getAllYearData();
      $data['documents_list'] = $this->Admission_model->get_documents_list();
      $data['main_content'] = 'admission/settings/add_setting';
      $this->load->view('inc/template', $data); 
    }

  
    public function add_prev_school_details($id){
      $data['id'] = $id;
      $classId = $this->input->post('className');
      if ($classId == null)
      $classId = array ();
      $data['selectedClasses'] = $classId;
      $currentAcadYearId = $this->acad_year->getAcadYearId();
      $data['classList'] =  $this->Student_Model->getClassByAcadYear($currentAcadYearId);
      $data['get_prev_settings'] = $this->Admission_model->get_prev_school_details_setting($id);
      $data['main_content'] = 'admission/settings/prev_school_details';
      $this->load->view('inc/template', $data); 
    }

    public function s3FileUpload_logo($file) {
      if($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }        
      return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'admissions');
    }

    public function s3FileUpload_bg_logo($file) {
      if($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }        
      return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'admissions');
    }

    public function submit_admission_settings(){

      $result = $this->Admission_model->insert_admission_settings($this->s3FileUpload_logo($_FILES['admission_logo']),$this->s3FileUpload_bg_logo($_FILES['admission_bg_logo']));
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Insert Successfully');
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('admission_process/admission_settings');
    } 

    public function update_admission_settings($id){
      $result = $this->Admission_model->update_admission_settings($id,$this->s3FileUpload($_FILES['aadhar_declaration_template'],'declaration'),$this->s3FileUpload($_FILES['pan_declaration_template'],'declaration'));
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Update Successfully');
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('admission_process/admission_settings');
    }

    public function update_previous_school_details($id){
      $result = $this->Admission_model->insert_previou_school_details($id);
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Insert Successfully');
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('admission_process/admission_settings');
    }

    public function add_stream_details($id){
      $data['id'] = $id;
      $data['get_stream_settings'] = $this->Admission_model->get_streams_school_details_setting($id);
      $data['main_content'] = 'admission/settings/streams_settings';
      $this->load->view('inc/template', $data);
    }

    public function update_streams_details($id){
      $result = $this->Admission_model->insert_streams_details($id);
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Insert Successfully');
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('admission_process/admission_settings');
    }

    public function receipt_book_assign_admission_Setting(){
      $admSettingId = $_POST['admSettingId'];
      $receiptbook = $_POST['receiptbook'];
      echo $this->Admission_model->receipt_book_id_update($admSettingId,$receiptbook); 
    }

    public function admission_receipt_book_assign_admission_Setting(){
      $admSettingId = $_POST['admSettingId'];
      $admissionReceiptbook = $_POST['admissionReceiptbook'];
      echo $this->Admission_model->admission_receipt_book_id_update($admSettingId,$admissionReceiptbook); 
    }


    public function admission_open_enable_disabled(){
      $stngId = $_POST['stngId'];
      $value = $_POST['value'];
      echo $this->Admission_model->update_admssion_status_in_setting($stngId,$value); 
    }

    public function admission_online_enabled_disabled(){
       $stngId = $_POST['stngId'];
      $value = $_POST['value'];
      echo $this->Admission_model->update_admssion_online_status($stngId,$value); 
    }

    public function admission_qualification_disable(){
      $stngId = $_POST['stngId'];
     $value = $_POST['value'];
     echo $this->Admission_model->update_qualification_status($stngId,$value); 
   }

   public function admission_proficiencyTest_enable(){
     $stngId = $_POST['stngId'];
     $value = $_POST['value'];
     echo $this->Admission_model->admission_proficiencyTest_enable($stngId,$value); 
   }

   public function enable_guardian_details_based_on_boarder(){
    $stngId = $_POST['stngId'];
    $value = $_POST['value'];
    echo $this->Admission_model->enable_guardian_details_based_on_boarder($stngId,$value); 
  }
    public function admission_guardian_enabled_disabled(){
      $stngId = $_POST['stngId'];
     $value = $_POST['value'];
     echo $this->Admission_model->update_admssion_guardian_status($stngId,$value); 
   }

    public function email_template_admission_Setting(){
      $admSettingId = $_POST['admSettingId'];
      $email_template = $_POST['email_template'];
      $online_email_temp = $_POST['online_email_temp'];
      echo $this->Admission_model->email_template_id_update($admSettingId,$email_template,$online_email_temp); 
    }

    public function pdf_template_admission_Setting(){
      $admSettingId = $_POST['admSettingId'];
      $pdf_template = $_POST['pdf_template'];
      echo $this->Admission_model->pdf_template_id_update($admSettingId,$pdf_template); 
    }

    public function admission_pdf_template_admission_Setting(){
      $admSettingId = $_POST['admSettingId'];
      $admission_pdf_template = $_POST['admission_pdf_template'];
      echo $this->Admission_model->admission_pdf_template_id_update($admSettingId,$admission_pdf_template); 
    }

    public function seat_allotment_pdf_template_admission_Setting(){
      $admSettingId = $_POST['admSettingId'];
      $seat_allotment_pdf_template = $_POST['seat_allotment_pdf_template'];
      echo $this->Admission_model->seat_allotment_pdf_template_id_update($admSettingId,$seat_allotment_pdf_template); 
    }
    public function email_template_for_staff_admission_Setting(){
      $admSettingId = $_POST['admSettingId'];
      $email_template_staff = $_POST['email_template_staff'];
      $staff_id = $_POST['staff_id'];
      echo $this->Admission_model->email_template_staff_id_update($admSettingId,$email_template_staff,$staff_id); 
    }

    public function admissions_online_settlement(){
      $data['transaction_dates'] = $this->Admission_model->get_admission_trans_dates();
      $data['main_content'] = 'admission/settings/online_settlement';
      $this->load->view('inc/template_fee', $data);
    }

	  public function online_transaction_report(){
			if (!$this->authorization->isAuthorized('ADMISSION.ONLINE_TRANSACTION_REPORT')) {
				redirect('dashboard', 'redirect');
			}

			$data['transaction_list'] = $this->Admission_model->get_online_transaction_data();	
			$data['main_content'] = 'admission/online_payments/index';
			$this->load->view('inc/template', $data);
		}

    public function getAdmissionTransactionDetails(){
      $mode = $_POST['mode'];
      switch ($mode) {
        case 'std_name':
          $name = $_POST['name'];
          $stdData = $this->Admission_model->getAdmissionTransactionDetailsByStudent($name);
          // $data->stdData = $stdData;
          // $data->isAdmin = $this->authorization->isSuperAdmin();
          echo json_encode($stdData);
          break;
        case 'order_id':
          $order = $_POST['order'];
          $transactionData = $this->Admission_model->getAdmissionTransactionDetailsByOrderId($order);
          echo json_encode($transactionData);
          break;
        case 'transaction_id':
          $transaction = $_POST['transaction'];
          $transactionData = $this->Admission_model->getAdmissionTransactionDetailsByTransactionId($transaction);
          echo json_encode($transactionData);
          break;
        case 'init_date':
          $date = $_POST['date'];
          $date  = date('Y-m-d', strtotime($date));
          $transactionData = $this->Admission_model->getAdmissionTransactionDetailsByDate($date);
          echo json_encode($transactionData);
          break;
      }
    }

		public function online_transaction_detail() {
			$opm_id = $_POST['opm_id'];
			$data['tx'] = $this->Admission_model->get_transaction_detail($opm_id);
			$data['main_content'] = 'admission/online_payments/detail';
			$this->load->view('inc/template', $data);
		}

    public function get_transaction_status() {
			$opm_id = $this->input->post('opm_id');
			echo json_encode($this->payment_application->get_transaction_status($opm_id));
		}

		public function redrive_transaction() {
      $response = json_decode($this->input->post('response'));
      $_SESSION['loginstatus'] = 1; //Not needed.
			$data = $this->payment_application->redrive_transaction($response->data[0]);
			$data['main_content'] = 'online_payment/payment_done';
			$this->load->view('inc/template', $data);	  
		}

    public function get_daily_admission_transactions(){
      $date = date("Y-m-d", strtotime($_POST['date']));
      $transaction_list = $this->Admission_model->get_daily_admission_transactions_model($date);

      $data['trans_amount'] = 0;
      $data['isConfirmed'] = 0;
      $data['transaction_list'] = array();
      $data['component_total'] = array();
      foreach ($transaction_list as $key => $val) {
        if(!array_key_exists($val->id, $data['transaction_list'])) {
          $data['trans_amount'] += $val->amount;
          $data['transaction_list'][$val->id] = array();
          $data['transaction_list'][$val->id]['stdName'] = $val->stdName;
          $data['transaction_list'][$val->id]['source'] = $val->source;
          $data['transaction_list'][$val->id]['application_no'] = $val->application_no;
          $data['transaction_list'][$val->id]['amount'] = $val->amount;
        }
        if($val->settlement_status == 'SETTLED')
          $data['isConfirmed'] = 1;

      }
      $settlements = $this->payment_application->get_admission_settlementdata($date);
      $data['settlement'] = array();
      // $date = '2019-03-11';
      $data['settlement_amount'] = 0;
      $data['settled_count'] = 0;
      $data['not_settled_count'] = 0;

      $settlements_grouped = [];

      foreach ($settlements->data as $key => $val) {
        if (!empty($val->transaction_date)) {
            $startDate = date('Y-m-d', strtotime($val->transaction_date));
            if ($startDate == $date) {
                $val->settlement_datetime = date('d-m-Y', strtotime($val->settlement_datetime));
                $val->status = '<span style="color:#ff4335;font-size:16px;font-weight:700;">Settlement Pending</span>';
                if ($val->completed == 'y')
                    $val->status = '<span style="color:#00701a;font-size:16px;font-weight:700;">Settlement Done</span>';
                
                if ($val->bank_reference != NULL) {
                    $data['settled_count']++;
                } else {
                    $data['not_settled_count']++;
                }
                if (!isset($settlements_grouped[$val->settlement_id])) {
                    $settlements_grouped[$val->settlement_id] = $val;
                    $settlements_grouped[$val->settlement_id]->total_reimbursed = $val->amount_reimbursed;
                } else {
                    $settlements_grouped[$val->settlement_id]->total_reimbursed += $val->amount_reimbursed;
                }
            }
        }
      }
      $data['settlement'] = array_values($settlements_grouped);
      $data['settlement_amount'] = array_sum(array_column($data['settlement'], 'total_reimbursed'));
      echo json_encode($data);
    }

    public function confirmAdmissionSettlement() {
      $status = $this->Admission_model->confirmAdmissionSettlement();
      if ($status) {
              $this->session->set_flashdata('flashSuccess', 'Successfully Confirmed.');
          }else{
              $this->session->set_flashdata('flashError', 'Failed to confirm .');
          }
      redirect('admission_process/admissions_online_settlement');
    }

    public function daily_tx_admissions(){
      if ($this->input->post('daterange') == 'custom') {
          $from_date = $this->input->post('created_from_date');
          $to_date = $this->input->post('created_to_date');
      } elseif (strpos($this->input->post('daterange'), '_') !== false) {
          $date_range = explode('_', $this->input->post('daterange'));
  
          if (count($date_range) == 2) {
              $from_date = $date_range[0];
              $to_date = $date_range[1];
          } else {
              $from_date = $to_date = null; 
          }
      } else {
          $from_date = $to_date = null;
      }
      $grades_filter = [];
      if(!empty($this->input->post('grade_filter'))){
        $grades_filter = $this->input->post('grade_filter');
      }
      $payment_mode = (isset($_POST['payment_mode'])) ? $this->input->post('payment_mode') : -1;
      $data['payment_mode'] = $payment_mode;
      $data['date_range'] = $this->input->post('daterange');
      $data['from_date'] = $from_date;
      $data['to_date'] = $to_date;
      $data['grades_filter'] = $grades_filter;
      $data['grades'] = $this->Admission_model->get_grade_names();
      $data['daily_tx'] = $this->Admission_model->get_daily_trans($payment_mode, $from_date, $to_date, $grades_filter);
      if (!empty($data['daily_tx'])) {
        $paymentModes = $this->settings->getSetting('admissions_payment_mode'); // Fetch payment modes once
    
        foreach ($data['daily_tx'] as $key => $val) {
            $val->payment_mode = 'Online Payment'; // Default to Online Payment
    
            foreach ($paymentModes as $v) {
                if (!empty($val->payment_type) && $val->payment_type == $v->value) {
                    $val->payment_mode = $v->name; // Assign matched payment mode
                    break; // Exit loop once a match is found
                }
            }
        }
    }

      $data['main_content'] = 'admission/staff/daily_tx';
      $this->load->view('inc/template', $data);
  }

    public function admission_followup($id){
      $data['eTemplate'] = $this->Admission_model->get_email_template();
      $data['applicationv2'] = strtolower($this->settings->getSetting('admission_filter_version'));
      $category = 'Admission';
      $data['sTemplate'] = $this->Admission_model->get_sms_template($category);
      $data['follow_admission'] = $this->Admission_model->get_admission_byId($id);
      if(!$this->settings->getSetting('admission_pick_status_from_table')){
        $follow_up_status = json_decode($this->settings->getSetting('application_form_status'));
        $follow_up_status_const = ['Application Amount Paid', 'Admit','Rejected', 'Duplicate'];
        $data['follow_up_status'] = array_merge($follow_up_status, $follow_up_status_const);
      }else{
        $data['follow_up_status'] =  $this->Admission_model->get_user_status();
      }
      // echo "<pre>"; print_r($data['follow_up_status']); die();
      $data['main_content'] = 'admission/staff/follow_up';
      $this->load->view('inc/template', $data);
    }

    
  public function submit_follow_up($id){
    $input = $this->input->post();
    $result = $this->Admission_model->update_admission_follow_up_data($id, $input);
    if ($result) {
      switch ($input['followup_action']) {
        case 'Email':
          $result = $this->_email_to_parent_admission($input);
          if (!$result) {
            $this->session->set_flashdata('flashError', "Email couldn't be sent");
          }
          break;
        case 'SMS':
          $result ='';
          if (isset($input['template_content'])) {
            $result = $this->_sms_to_parent($input,$id);
          }
          if (!$result) {            
            $this->session->set_flashdata('flashError', "SMS couldn't be sent");
          }
          break;
      }
    }
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Follow-up Successful.');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    redirect('admission_process/admission_followup/'.$id);
  }

  public function submit_follow_up_details($id){
    $input = $this->input->post();
    $is_student_moved_to_erp = $this->Admission_model->checkExit_in_Admission_studentTable($id);
    if($is_student_moved_to_erp == 0){
      echo -1;
      return;
    }
    $curr_status = $this->Admission_model->get_curr_status($id);
      if($curr_status->curr_status != $input['status']){
        $staff_emailids_to_send_email = $this->Admission_model->get_staff_emailid_to_send_email($input['status']);
        $admission_followup_email_content = $this->Admission_model->get_admission_follow_up_email($id);
        if(!empty($staff_emailids_to_send_email) && !empty($admission_followup_email_content)){
          $send_email = $this->_admission_followup_email_to_staff($admission_followup_email_content,$staff_emailids_to_send_email);
        }
      }

    $file = $this->s3FileUpload($_FILES['follow_up_document'],'Follow-up DOcument');
    // echo '<pre>';print_r($file);die();
    $result = $this->Admission_model->update_admission_follow_up_data($id, $input,$file['file_name']);
    if ($result) {
      switch ($input['followup_action']) {
        case 'Email':
          $result = $this->_email_to_parent_admission($input);
          if (!$result) {
            $this->session->set_flashdata('flashError', "Email couldn't be sent");
          }
          break;
        case 'SMS':
          $result = $this->_sms_to_parent($input,$id);
          if (!$result) {
            $this->session->set_flashdata('flashError', "SMS couldn't be sent");
          }
          break;
      }
    }
    if($result){
      $curr_status = $this->Admission_model->get_curr_status($id);
      if($curr_status->curr_status != $input['status']){
        $staff_emailids_to_send_email = $this->Admission_model->get_staff_emailid_to_send_email($input['status']);
        $admission_followup_email_content = $this->Admission_model->get_admission_follow_up_email($id);
        if(!empty($staff_emailids_to_send_email) && !empty($admission_followup_email_content)){
          $send_email = $this->_admission_followup_email_to_staff($admission_followup_email_content,$staff_emailids_to_send_email);
        }
      }
    }
    echo $input['status'];
  }

  private function _admission_followup_email_to_staff($email_content,$emailIds){
    $member_email = [];
    foreach($emailIds as $key => $val){
      array_push($member_email, $val->email);
    }
    // echo '<pre>';print_r($member_email);
    $email_master_data = array(
      'subject' => $email_content['email_subject'],
      'body' => $email_content['content'],
      'source' => 'Admission Followup Status Update',
      'sent_by' => $this->authorization->getAvatarStakeHolderId(),
      'recievers' => 'Staff',
      'from_email' => $email_content['registered_email'],
      'files' => '',
      'visible' => 1,
      'sender_list' => implode(',',$member_email),
      'sending_status' => 'Completed'
    );
    $this->load->model('communication/emails_model');
    $email_master_id = $this->emails_model->saveEmail($email_master_data);
    
    foreach ($member_email as $key => $val) {
       if(empty($val)){
         continue;
       }
       $email_data = [];
       $email_obj = new stdClass();
       $email_obj->stakeholder_id = 0;
       $email_obj->avatar_type = 0;
       $email_obj->email = $val;
       $email_data[] = $email_obj;

       $this->emails_model->save_sending_email_data($email_data,$email_master_id);
     }
    return sendEmail($email_content['content'], $email_content['email_subject'], $email_master_id, $member_email, $email_content['registered_email'],'');}

  private function _email_to_parent_admission($input){
    $emailIds = explode(',', $input['to_mails']);
    // $memberEmail = [];
    // foreach ($emailIds as $key => $val) {
    //   $memberEmail[]['email'] = $val;
    //   if(!empty($val)){
    //     $this->_save_to_email_master($input['template_content'], $input['email_subject'], $input['registered_email'],'', $val,'Admission Followup');
    //   }
    // }

    $email_master_data = array(
      'subject' => $input['email_subject'],
      'body' => $input['template_content'],
      'source' => 'Admission Followup',
      'sent_by' => $this->authorization->getAvatarStakeHolderId(),
      'recievers' => 'Parents',
      'from_email' => $input['registered_email'],
      'files' => '',
      'visible' => 1,
      'sender_list' => $input['to_mails'],
      'sending_status' => 'Completed'
    );

    $email_master_id = $this->emails_model->saveEmail($email_master_data);
    
    foreach ($emailIds as $key => $val) {
       if(empty($val)){
         continue;
       }
       $email_data = [];
       $email_obj = new stdClass();
       $email_obj->stakeholder_id = 0;
       $email_obj->avatar_type = 0;
       $email_obj->email = $val;
       $email_data[] = $email_obj;

       $this->emails_model->save_sending_email_data($email_data,$email_master_id);
     }

    sendEmail($input['template_content'], $input['email_subject'], $email_master_id, $emailIds, $input['registered_email'], '');
    // return $this->__send_email($input['template_content'], $input['email_subject'], $memberEmail, $input['registered_email']);
  }

  private function _sms_to_parent($input, $id){
    $custom_numbers = [];
    array_push($custom_numbers, $input['f_mobile_number'],$input['m_mobile_number']);
    return sendToCustomNumbersForFollowups($custom_numbers,$id, $input['template_content'],'Admission','Admission Parent');
  }

  

  public function get_admission_email_content(){
    $afId = $this->input->post('afId');
    $emailtemplateId = $this->input->post('emailtemplateId');
    $result = $this->Admission_model->get_admission_email_templatebyId($afId, $emailtemplateId);
    echo json_encode($result);
  }

  public function get_admission_email_content_revert(){
    $afId = $this->input->post('afId');
    $emailtemplateId = $this->input->post('emailtemplateId');
    $result = $this->Admission_model->get_admission_email_templatebyId_revert($afId, $emailtemplateId);
    echo json_encode($result);
  }

  public function get_admission_sms_content(){
    $afId = $this->input->post('afId');
    $smstemplateId = $this->input->post('smstemplateId');
    $result = $this->Admission_model->get_admission_sms_templatebyId($afId, $smstemplateId);
    echo json_encode($result);
  }

  public function get_email_sms_pop_details(){
    $follow_up_type = $this->input->post('follow_up_type');
    $follow_up_action = $this->input->post('follow_up_action');
    $source_id = $this->input->post('source_id');
    $result = $this->Admission_model->get_email_sms_pop_details_byType($follow_up_type, $follow_up_action, $source_id);
    echo json_encode($result);
  }

  public function admission_fields(){

    $fields = $this->db->list_fields('admission_forms');
    $prevSchoolFields = $this->db->list_fields('admission_prev_school');

    $merge = array_merge($fields, $prevSchoolFields);
    $fData = [];
    foreach ($merge as $field){
      if ($field !='id' && $field!='au_id') {
        array_push($fData, $field);
      } 
    }
    $data['fields'] = $fData;
    // echo "<pre>"; print_r($fData); die();
    $data['selected_required_fields'] = $this->Admission_model->get_admission_required_fields();
    $data['selected_enabled_fields'] = $this->Admission_model->get_admission_enabled_fields();
    $data['main_content'] = 'admission/staff/admission_fields';
    $this->load->view('inc/template', $data);
  }

  public function admission_configure_fields(){
    $result = $this->Admission_model->insert_admission_configure_fields();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', "Successfully inserted");
    }else{
      $this->session->set_flashdata('flashError', "Something went wrong");
    }
    redirect('admission_process/admission_fields');
  }

  public function admission_receipt_view($admissionId, $v1=1){
    $data['admissionId'] = $admissionId;
    $data['v1'] = $v1;
    $data['admissions'] = $this->Admission_model->admission_receipt_by_id($admissionId);
    // echo "<pre>"; print_r($data['admissions']); die();
    $data['admissions_payment_mode'] = $this->settings->getSetting('admissions_payment_mode');
    if ($this->settings->getSetting('application_receipt_is_custom')) {
      $shortName = $this->settings->getSetting('school_short_name');
      $data['main_content'] = 'admission/receipts/'.$shortName.'_receipt';
    }else{
      $data['main_content'] = 'admission/receipts/admission_receipt';
    }
    $this->load->view('inc/template_fee', $data);
  }

  public function admission_receipt_view_page($admissionId){
    $data['admissionId'] = $admissionId;
    $data['admissions'] = $this->Admission_model->admission_receipt_by_id($admissionId);
    $data['main_content'] = 'admission/receipts/admission_receipt_view_page';
    $this->load->view('inc/template_fee', $data);
  }

  // public function admission_receipt_re_generate(){
  //   $admissionId = $_POST['admissionId'];
  //   $admissions = $this->Admission_model->admission_receipt_by_id($admissionId);
  //   $result = $this->_create_template_admission_receipt($admissions, $admissions->receipt_html);    
  //   if($result) {
  //     $pdfResult = $this->__generatefee_admission_pdf_receipt($result, $admissionId);
  //     if ($pdfResult) {
  //       $link = $this->Admission_model->download_admission_receipt($admissionId);
  //       $url = $this->filemanager->getFilePath($link);
  //       $data = file_get_contents($url);
  //       $this->load->helper('download');
  //       force_download('admission_receipt.pdf', $data, TRUE);
  //     }else{
  //       echo 0;
  //     }
      
  //   }else{      
  //     echo 0;
  //   }

  // }

  // public function download_admission_pdf_receipt($admissionId){
  //   $link = $this->Admission_model->download_admission_receipt($admissionId);
  //   $url = $this->filemanager->getFilePath($link);
  //   $data = file_get_contents($url);
  //   $this->load->helper('download');
  //   force_download('admission_receipt.pdf', $data, TRUE);
  // }

  public function download_admission_pdf_receipt($admissionId){
    $admissions = $this->Admission_model->admission_receipt_by_id($admissionId);
    if ($admissions->receipt_html_path == '') {
      $result = $this->_create_template_admission_receipt($admissions, $admissions->receipt_html);
      $pdfResult = $this->__generatefee_admission_pdf_receipt($result, $admissionId);
    }
    $link = $this->Admission_model->download_admission_receipt($admissionId);
    $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download('admission_receipt.pdf', $data, TRUE);

  }
  public function _create_template_admission_receipt($admData, $template){  
    $template = str_replace('%%application_no%%',$admData->receipt_number, $template);
    $template = str_replace('%%student_name%%',$admData->student_name, $template);
    $template = str_replace('%%admission_amount%%',$admData->admission_fee_amount, $template);
    $template = str_replace('%%admission_date%%',$admData->receipt_date, $template);

    switch ($admData->payment_type) {
      case '10':
        $type = 'Online Payment';
        break;
      case '4':
        $type = 'Cheque';
        break;
      case '8':
        $type = 'Net Banking';
        break;  
      default:
        $type = 'Cash';
        break;
    }
    $template = str_replace('%%payment_type_mode%%',$type, $template);
    $payment_mode ='<tr>';
    $payment_mode.='<td colspan="2" style="border: none !important;"><b>Payment Type : </b>'.$type.'</td>';
    $payment_mode.='</tr>';
    if ($admData->payment_type == '4') {
      $payment_mode.='<tr>';
      $payment_mode.='<td style="border: none !important;"><b>Bank : </b>'.$admData->bank_name.'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <b>Branch : </b>  '.$admData->bank_branch.'</td>';
      $payment_mode.='<td style="border: none !important;"><b>Cheque No  </b>'.$admData->cheque_dd_nb_cc_dd_number.'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <b>Date : </b>  '.$admData->cheque_or_dd_date.'</td>';              
      $payment_mode.='</tr>';
    }
    $template = str_replace('%%payment_type%%',$payment_mode, $template);

    $amountInWords = $this->getIndianCurrency($admData->admission_fee_amount);
    $template = str_replace('%%amoun_in_words%%',ucfirst($amountInWords), $template);

    return $template;
  }

  public function getIndianCurrency(float $number){
    $schoolName = $this->settings->getSetting('school_short_name');
    $decimal = round($number - ($no = floor($number)), 2) * 100;
    $hundred = null;
    $digits_length = strlen($no);
    $i = 0;
    $str = array();
    $words = array(0 => '', 1 => 'one', 2 => 'two',
        3 => 'three', 4 => 'four', 5 => 'five', 6 => 'six',
        7 => 'seven', 8 => 'eight', 9 => 'nine',
        10 => 'ten', 11 => 'eleven', 12 => 'twelve',
        13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
        16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen',
        19 => 'nineteen', 20 => 'twenty', 30 => 'thirty',
        40 => 'forty', 50 => 'fifty', 60 => 'sixty',
        70 => 'seventy', 80 => 'eighty', 90 => 'ninety');
    $digits = array('', 'hundred','thousand','lakh', 'crore');
    while( $i < $digits_length ) {
        $divider = ($i == 2) ? 10 : 100;
        $number = floor($no % $divider);
        $no = floor($no / $divider);
        $i += $divider == 10 ? 1 : 2;
        if ($number) {
            $plural = (($counter = count($str)) && $number > 9) ? '' : null;
            $hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
            $str [] = ($number < 21) ? $words[$number].' '. $digits[$counter]. $plural.' '.$hundred:$words[floor($number / 10) * 10].' '.$words[$number % 10]. ' '.$digits[$counter].$plural.' '.$hundred;
        } else $str[] = null;
    }
    $Rupees = implode('', array_reverse($str));
    $paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
    if ($schoolName === 'prarthana') {
      return 'Rupees ' . ( $Rupees ? $Rupees . 'Only ' : ' ') . $paise ;
    }
    return ($Rupees ? $Rupees . 'Rupees ' : '') . $paise ;
  }

   private function __generatefee_admission_pdf_receipt($html, $admissionId) {
    $school = CONFIG_ENV['main_folder'];
    $path = $school.'/admission_fee_reciepts/'.uniqid().'-'.time().".pdf";

    $bucket = $this->config->item('s3_bucket');

    $status = $this->Admission_model->update_admission_receipt_Path($admissionId, $path);
    $page = 'portrait';
    $page_size = 'a4';
    $curl = curl_init();
    $postData = urlencode($html);
    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url().'Callback_Controller/updateFeePdfLink';

    curl_setopt_array($curl, array(
        CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
        CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
  }

    public function application_redrive_fee_trans_done(){
        $this->__handle_redrive_op_response($_POST);
    }

    private function __handle_redrive_op_response($response){
      if ($response['transaction_status'] === 'SUCCESS') {

        // if receipt is already generated, we have already updated the admission forms. No need to update again. Just show the already generated receipt.
        $isApplicationGenerated = $this->Admission_model->is_application_no_generated($response['source_id']);
        if ($isApplicationGenerated){
          $this->session->set_flashdata('flashError', 'Update unsuccessful. Receipt was already generated');
          redirect('admission_process/online_transaction_report');
        }

        $this->Admission_model->update_transcation_details($response['source_id'], 'SUCCESS','Application Amount Paid');
        $this->Admission_model->update_redrive_application_receipt($response['source_id']);
        if($this->settings->getSetting('show_admission_receipt_button')){
          $this->Admission_model->update_admission_application_receipt($response['source_id']);
        }
                   
        $admUsers = $this->Admission_model->get_auId_mobileNumber($response['source_id']);
        $admin_no = $this->Admission_model->get_admission_number_byauid_lastIdwise($admUsers->au_id,$response['source_id']);

        $this->_sms_to_parent_admission($admin_no, $admUsers->mobile_no);
        $this->session->set_flashdata('flashSuccess', 'Transcation successful');
      } else {
        $this->Admission_model->update_transcation_details($response['source_id'],'FAILED','Draft');
        $this->Admission_model->submit_final_data($response['source_id'],'Payment Pending');
        $this->session->set_flashdata('flashError', 'Transcation unsuccessful');
      }
      redirect('admission_process/online_transaction_report');
    }

    private function _sms_to_parent_admission($admin_no, $mobileNumber){
        $smsint = (array) $this->settings->getSetting('smsintergration');
        $admissionSMS = $this->settings->getSetting('admissions_sms');
        $school_name = $this->settings->getSetting('school_name');
        if (!empty($admissionSMS)) {
            $msg =  $admissionSMS;
            $msg = str_replace('%%admission_no%%',$admin_no, $msg);
            $msg = str_replace('%%school_name%%',$school_name, $msg);
        }else{
            $msg = "Dear Parent, your online application (Reg. No: ".$admin_no.") has been submitted successfully. Please print, sign, and submit the form at the school front office within 5 working days to complete the registration process. - ".$school_name."";
        }
        $content =  urlencode(''.$msg.'');
        $get_url = 'https://'.$smsint['url'].'?apikey='.$smsint['api_key'].'&senderid='.$smsint['sender'].'&number='.$mobileNumber.'&message='.$content;
        return $this->curl->simple_get($get_url);
    }

    public function collect_application_offline_fee($afId, $au_Id, $admission_setting_id){
      $data['au_id'] = $au_Id;
      $data['insert_id'] = $afId;
      $data['admission_setting_id'] = $admission_setting_id;
      $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
      $data['form_name'] = $data['config_val']['form_name'];
      $data['form_year'] = $data['config_val']['form_year'];
      $data['school_name'] = $data['config_val']['school_name'];
      $data['school_short'] = $data['config_val']['school_short'];
      $data['instructions'] = $data['config_val']['instruction_file'];
      $data['streams'] = json_decode($data['config_val']['streams'], true);
      $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);      
      $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_Id,$afId);
      $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($afId);
      $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($afId);
      if (!empty($data['prev_eduction_info'])) {
          $data['subjectArray'] = $data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for]['subject'];
      }
      
      $data['combinations'] = $this->Admission_model->get_combinations_for_select_list($au_Id,$afId);

      if (!empty($data['streams'])) {
          $combArray = $data['streams'][$data['combinations']->combination];

          $resultArray = array();
          foreach ($combArray as $key => $val) {
              if ($val['id'] == $data['combinations']->combination_id) {
                  $data['comb'] = $val['name'];
              }
          }
      }
      $data['admissions_payment_mode'] = $this->settings->getSetting('admissions_payment_mode');
      $data['disabled_fields'] = $this->Admission_model->get_admission_enabled_fields();
      $data['main_content'] = 'admission/staff/application/collect_fee_offline';
      $this->load->view('inc/template', $data);
    }

    public function _construct_admisison_form_template($final_preview, $admission_doc, $admission_prev_schools, $template, $subjectMaster, $admission_stream, $streams,$enquiry_number=''){
      // echo "<pre>"; print_r($admission_prev_schools); die();
      $combination = '';
        if (!empty($admission_stream)) {
            foreach ($streams[$admission_stream->combination] as $key => $val) {
                if ($val['id'] == $admission_stream->combination_id) {
                    $combination = $val['name'];
                }
            }
        }
        $student_quota = '';
        if (!empty($final_preview->student_quota)) {
            foreach ($this->settings->getSetting('quota') as $key => $value) {
                if ($final_preview->student_quota == $key) {
                    $student_quota = $value;
                }
            }   
        }
        $language = '';
            if (!empty($final_preview->lang_1_choice)) {
              foreach ($subjectMaster as $key => $val) { 
                if($val->id == $final_preview->lang_1_choice){
                  $language =  $val->subject_name;
                } 
            } 
        }

        if (!empty($final_preview->lang_2_choice)) {
            foreach ($subjectMaster as $key => $val) { 
                if($val->id == $final_preview->lang_2_choice){
                    $language =  $val->subject_name;
                } 
            } 
        }

        if (!empty($final_preview->lang_3_choice)) {
            foreach ($subjectMaster as $key => $val) { 
                if($val->id == $final_preview->lang_3_choice){
                    $language =  $val->subject_name;
                } 
            } 
        }
      $formData = (array)$final_preview;
      $fields = $this->db->list_fields('admission_forms');
      $prevSchoolFields = $this->db->list_fields('admission_prev_school');

      $merge = array_merge($fields, $prevSchoolFields);
      $fData = [];
      foreach ($merge as $field){
        if ($field !='id' && $field!='au_id') {
          array_push($fData, $field);
        } 
      }
      $photo = '';
      if ($final_preview->std_photo_uri !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->std_photo_uri);
        $photo = '<img style="margin-top: -8rem !important; float: right; margin-right: 0rem;width:132px !important; height:150px!important" src="'.$getURL.'" />';
      }
      $father_photo = '';
      if ($final_preview->father_photo !='') {
        $f_getURL = $this->filemanager->getFilePath($final_preview->father_photo);
        $father_photo = '<img style="margin-top: -8rem !important; float: right; margin-right: 0rem;width:132px !important; height:150px!important" src="'.$f_getURL.'" />';
      }
      $mother_photo = '';
      if ($final_preview->mother_photo !='') {
        $m_getURL = $this->filemanager->getFilePath($final_preview->mother_photo);
        $mother_photo = '<img style="margin-top: -8rem !important; float: right; margin-right: 0rem;width:132px !important; height:150px!important" src="'.$m_getURL.'" />';
      }
      $guardian_photo = '';
      if ($final_preview->g_photo_uri !='') {
        $g_getURL = $this->filemanager->getFilePath($final_preview->g_photo_uri);
        $guardian_photo = '<img style="margin-top: -8rem !important; float: right; margin-right: 0rem;width:132px !important; height:150px!important" src="'.$g_getURL.'" />';
      }
      $fatherSignature = '';
      if ($final_preview->f_signature !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->f_signature);
        $fatherSignature = '<img style="width:100px !important; height:80px!important" src="'.$getURL.'" />';
      }
      $motherSignature = '';
      if ($final_preview->m_signature !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->m_signature);
        $motherSignature = '<img style="width:100px !important; height:80px!important" src="'.$getURL.'" />';
      }

      $family_photo = '';
      if ($final_preview->family_photo !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->family_photo);
        $family_photo = '<img style="width:100px !important; height:80px!important" src="'.$getURL.'" />';
      }

      $category = '';
      if ($final_preview->category !='0' && !empty($final_preview->category)) {
        $category = $this->settings->getSetting('category')[$final_preview->category];
      }

      $student_quota = '';
      if ($final_preview->student_quota !='0' && !empty($final_preview->student_quota)) {
        $student_quota = $this->settings->getSetting('quota')[$final_preview->student_quota];
      }
    
      $boarding = '';
      if ($final_preview->boarding !='0' && !empty($final_preview->boarding)) {
        $boarding = $this->settings->getSetting('boarding')[$final_preview->boarding];
      }
      $blood_group = '';
      if($final_preview->student_blood_group == 'A +ve'){
        $blood_group = 'A positive';
      }elseif($final_preview->student_blood_group == 'B +ve'){
        $blood_group = 'B positive';
      }elseif($final_preview->student_blood_group == 'O +ve'){
        $blood_group = 'O positive';
      }elseif($final_preview->student_blood_group == 'A -ve'){
        $blood_group = 'A negative';
      }elseif($final_preview->student_blood_group == 'B -ve'){
        $blood_group = 'B negative';
      }elseif($final_preview->student_blood_group == 'O -ve'){
        $blood_group = 'O negative';
      }elseif($final_preview->student_blood_group == 'AB +ve'){
        $blood_group = 'AB positive';
      }elseif($final_preview->student_blood_group == 'AB -ve'){
        $blood_group = 'AB negative';
      }elseif($final_preview->student_blood_group == 'A1B+'){
        $blood_group = 'A1B positive';
      }elseif($final_preview->student_blood_group == 'Unknown'){
        $blood_group = 'Unknown';
      }
        
      $student_passport_expiry_date = $final_preview->passport_expiry_date;
      if ($final_preview->passport_expiry_date == '01-01-1970' || empty($final_preview->passport_expiry_date)) {
        $student_passport_expiry_date = '-';
      }
      $prevSchool = '';
      $f_position = $final_preview->f_position;
      if(strtolower($f_position) == 'not working' || empty($f_position) || $f_position == '-'){
        $f_position = 'NA';
      }
      $m_position = $final_preview->m_position;
      if(strtolower($m_position == 'not working') || empty($m_position) || $m_position == '-') {
        $m_position = 'NA';
      }
      $has_sibling = 'NA';
      if($final_preview->has_sibling == 1){
        $has_sibling = 'Yes';
      }else if($final_preview->has_sibling == 0){
        $has_sibling = 'No';
      }
      $student_present_address = 'NA';
      $student_permanent_address = 'NA';
      $father_address = 'NA';
      $mother_address = 'NA';
      $father_office_address = 'NA';
      $mother_office_address = 'NA';
      $guardian_address = 'NA';
      $student_present_address_parts = array_filter([
        $final_preview->s_present_addr,
        $final_preview->s_present_area,
        $final_preview->s_present_district,
        $final_preview->s_present_state,
        $final_preview->s_present_country,
        $final_preview->s_present_pincode,
    ]);
    
    $student_present_address = implode(', ', $student_present_address_parts);

    $student_permanent_address_parts = array_filter([
        $final_preview->s_permanent_addr,
        $final_preview->s_permanent_area,
        $final_preview->s_permanent_district,
        $final_preview->s_permanent_state,
        $final_preview->s_permanent_country,
        $final_preview->s_permanent_pincode,
    ]);
    
    $student_permanent_address = implode(', ', $student_permanent_address_parts);

    $father_address_parts = array_filter([
        $final_preview->f_addr,
        $final_preview->f_area,
        $final_preview->f_district,
        $final_preview->f_state,
        $final_preview->f_county,
        $final_preview->f_pincode,
    ]);
    
    $father_address = implode(', ', $father_address_parts);

    $mother_address_parts = array_filter([
        $final_preview->m_addr,
        $final_preview->m_area,
        $final_preview->m_district,
        $final_preview->m_state,
        $final_preview->m_county,
        $final_preview->m_pincode,
    ]);
    
    $mother_address = implode(', ', $mother_address_parts);
    
    $father_office_address_parts = array_filter([
        $final_preview->f_company_addr,
        $final_preview->f_company_area,
        $final_preview->f_company_district,
        $final_preview->f_company_state,
        $final_preview->f_company_county,
        $final_preview->f_company_pincode,
    ]);
    
    $father_office_address = implode(', ', $father_office_address_parts);

    $mother_office_address_parts = array_filter([
        $final_preview->m_company_addr,
        $final_preview->m_company_area,
        $final_preview->m_company_district,
        $final_preview->m_company_state,
        $final_preview->m_company_county,
        $final_preview->m_company_pincode,
    ]);
    
    $mother_office_address = implode(', ', $mother_office_address_parts);

    $guardian_address_parts = array_filter([
        $final_preview->g_addr,
        $final_preview->g_area,
        $final_preview->g_district,
        $final_preview->g_state,
        $final_preview->g_county,
        $final_preview->g_pincode,
    ]);
    
    $guardian_address = implode(', ', $guardian_address_parts);

      $template = str_replace('%%enquiry_number%%', $enquiry_number, $template);
      $template = str_replace('%%academic_year_applied_for%%', $this->acad_year->getAcadYearById($final_preview->academic_year_applied_for), $template);
      $template = str_replace('%%boarding%%', $boarding, $template);
      $template = str_replace('%%has_sibling%%', $has_sibling, $template);
      $template = str_replace('%%student_blood_group%%', $blood_group, $template);
      $template = str_replace('%%language%%', $language, $template);
      $template = str_replace('%%student_photo%%', $photo, $template);
      $template = str_replace('%%father_photo%%', $father_photo, $template);
      $template = str_replace('%%mother_photo%%', $mother_photo, $template);
      $template = str_replace('%%family_photo%%', $family_photo, $template);
      $template = str_replace('%%guardian_photo%%', $guardian_photo, $template);
      $template = str_replace('%%student_passport_expiry_date%%', $student_passport_expiry_date, $template);
      $template = str_replace('%%father_signtuare%%', $fatherSignature, $template);
      $template = str_replace('%%mother_signtuare%%', $motherSignature, $template);
      $template = str_replace('%%application_date%%', $final_preview->created_date, $template);
      $template = str_replace('%%s_first_name%%', $final_preview->std_name, $template);
      $template = str_replace('%%s_middle_name%%', $final_preview->student_middle_name, $template);
      $template = str_replace('%%s_last_name%%', $final_preview->student_last_name, $template);
      $template = str_replace('%%category%%', $category, $template);
      $template = str_replace('%%student_quota_name%%', $student_quota, $template);
      $template = str_replace('%%counselor_name%%', $final_preview->counselor_name, $template);
      $template = str_replace('%%f_position%%', $f_position, $template);
      $template = str_replace('%%m_position%%', $m_position, $template);
      $template = str_replace('%%student_present_address%%', $student_present_address, $template);
      $template = str_replace('%%student_permanent_address%%', $student_permanent_address, $template);
      $template = str_replace('%%father_address%%', $father_address, $template);
      $template = str_replace('%%mother_address%%', $mother_address, $template);
      $template = str_replace('%%father_office_address%%', $father_office_address, $template);
      $template = str_replace('%%mother_office_address%%', $mother_office_address, $template);
      $template = str_replace('%%guardian_address%%', $guardian_address, $template);
      // $template = str_replace('%%extracurricular_activities%%', $final_preview->extracurricular_activities, $template);
      // $template = str_replace('%%joining_period%%', $final_preview->joining_period, $template);
      // $template = str_replace('%%previous_school_name%%', '', $template);
      // $template = str_replace('%%previous_class_name%%', '', $template);
      // $template = str_replace('%%previous_board%%', '', $template);
      // $template = str_replace('%%previous_total_marks%%', '', $template);
      // $template = str_replace('%%previous_total_marks_scored%%', '', $template);
      // $template = str_replace('%%previous_total_percentage%%', '', $template);
      // $template = str_replace('%%previous_year%%', '', $template);
      // $template = str_replace('%%previous_school_address%%', '', $template);
      // $template = str_replace('%%medium_of_instruction%%', '', $template);
      if (!empty($admission_prev_schools)) {
        $template = str_replace('%%previous_school_name%%', $admission_prev_schools[0]->school_name, $template);
        $template = str_replace('%%previous_class_name%%', $admission_prev_schools[0]->class, $template);
        $template = str_replace('%%previous_board%%', $admission_prev_schools[0]->board, $template);
        $template = str_replace('%%previous_total_marks%%', $admission_prev_schools[0]->total_marks, $template);
        $template = str_replace('%%previous_total_marks_scored%%', $admission_prev_schools[0]->total_marks_scored, $template);
        $template = str_replace('%%previous_total_percentage%%', $admission_prev_schools[0]->total_percentage, $template);
        $template = str_replace('%%previous_year%%', $admission_prev_schools[0]->year, $template);
        $template = str_replace('%%previous_school_address%%', $admission_prev_schools[0]->school_address, $template);
        $template = str_replace('%%medium_of_instruction%%', $admission_prev_schools[0]->medium_of_instruction, $template);
        $template = str_replace('%%registration_no%%', $admission_prev_schools[0]->registration_no, $template);
        $template = str_replace('%%expelled_or_suspended%%', $admission_prev_schools[0]->expelled_or_suspended, $template);
        $template = str_replace('%%transfer_reason%%', $admission_prev_schools[0]->transfer_reason, $template);
        $template = str_replace('%%expelled_or_suspended_description%%', $admission_prev_schools[0]->expelled_or_suspended_description, $template);
        $template = str_replace('%%previous_school_ratings%%', $admission_prev_schools[0]->previous_school_ratings, $template);
      }else{
        $template = str_replace('%%previous_school_name%%','', $template);
        $template = str_replace('%%previous_class_name%%Std.', '', $template);
      }
      $your_word_for_institute = $this->settings->getSetting('your_word_for_institute');
      $prevSchoolnotinmarks = '';
      if (!empty($admission_prev_schools)) {
         $prevSchoolnotinmarks .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500;margin-bottom: 2rem;margin-top: 2rem;">Previous School Information</h5>';        
        $prevSchoolnotinmarks .= '<table style="border:none;margin-bottom: 8px; width: 98%; margin-top: -1rem; padding-bottom: -12px; margin-left: auto; margin-right: auto; ">
                <tr>
                    <td style="font-size: 11px;">Year</td>
                    <td style="font-size: 11px;">Board</td>
                    <td style="font-size: 11px;">'.(!empty($your_word_for_institute) ? $your_word_for_institute : 'School').' name</td>
                    <td style="font-size: 11px;">'.(!empty($your_word_for_institute) ? $your_word_for_institute : 'School').' Address</td>
                    <td style="font-size: 11px;">Transfer reason</td>
                    <td style="font-size: 11px;">Expelled/Suspended</td>
                    <td style="font-size: 11px;">Expelled/Suspended description</td>
                </tr>';
            foreach ($admission_prev_schools as $key => $value) {
                $prevSchoolnotinmarks .= '<tr>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->year.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->board.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->school_name.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->school_address.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->transfer_reason.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->expelled_or_suspended.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->expelled_or_suspended_description.'</td>';
                $prevSchoolnotinmarks .= '</tr>';
            }
            $prevSchoolnotinmarks .= '</table>';
      }
      if (!empty($admission_prev_schools)) {
        $prevSchool .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500; margin-top: 1rem !important;">EDUCATIONAL QUALIFICATION</h5>';
        $prevSchool .= '<table style="width: 98%; margin: auto; ">
                <tr>
                    <td style="font-size: 11px;">Year</td>
                    <td style="font-size: 11px;">Board</td>
                    <td style="font-size: 11px;">'.(!empty($your_word_for_institute) ? $your_word_for_institute : 'School').' Name</td>
                    <td style="font-size: 11px;">'.(!empty($your_word_for_institute) ? $your_word_for_institute : 'School').' Address</td>
                    <td style="font-size: 11px;">Subjects</td>
                    <td style="font-size: 11px;">Max Marks</td>
                    <td style="font-size: 11px;">Marks Obtained</td>
                </tr>';
          $rowspan = 0;
          $totalMarks = 0;
          $totalMarksScored = 0;
          $totalPercentage = 0;
          $break = 1;
          foreach ($admission_prev_schools as $key => $value) {
            if($break == 1){
                $rowspan =0;
                if (!empty($value->marks)) {
                  $rowspan = sizeof($value->marks);
                }
                $totalPercentage = $value->total_percentage;    
                $prevSchool .= '<tr>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->year.'</td>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->board.'</td>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->school_name.'</td>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->school_address.'</td>';
                // $prevSchool .= '</tr>';
                $close = 1;
                if (!empty($value->marks)) {
                  foreach ($value->marks as $key => $val) {
                    $jsan =  json_decode($val->sub_name);
                    $totalMarks += $val->marks;
                    $totalMarksScored += $val->marks_scored;
                    $subName = $jsan->name;
                    if($jsan->name == ''){
                      $subName = '-';
                    }
                    if($close != 1) {
                      $prevSchool .= '<tr>';
                    }
                    $prevSchool .= '<td style="font-size: 11px;">'.$subName.'</td>';
                    $prevSchool .= '<td style="font-size: 11px;">'.$val->marks.'</td>';
                    $prevSchool .= '<td style="font-size: 11px;">'.$val->marks_scored.'</td>';
                    $prevSchool .= '</tr>';
                    $close++;
                  }
                }
                
              }
              break;
            }
            $prevSchool .= '<tr style="font-weight:bold">';
            $prevSchool .= '<td style="font-size: 11px;border-right:none" colspan="4"><span>Percentage : '.$totalPercentage.'</span></td>';
            $prevSchool .= '<td style="font-size: 11px;border:none;text-align:right" ><span>Total Marks </span></td>';
            $prevSchool .= '<td style="text-align:left !important;font-size: 11px;">'.number_format((float)$totalMarks, 2, '.', '').'</td> ';
            $prevSchool .= '<td style="text-align:left !important;font-size: 11px;">'.number_format((float)$totalMarksScored, 2, '.', '').'</td> ';
            $prevSchool .= '</tr>';
            $prevSchool .= '</table>';
      }
      $documents = '';
      if(!empty($admission_doc)){
        $documents = '<table class="table table-bordered">';
        $documents .='<th colspan="4">Documents Submitted</th>';
        $documents .='<tr>';
        $documents .='<td colspan="4">';
        $i=1; 
        foreach ($admission_doc as $key => $doc) {
          $documents .=''.$i++.'. ';
            if ($doc->document_type == 'Others') {
          $documents .=''.strtoupper($doc->document_other).'<br>';
            }else{
          $documents .=''.strtoupper($doc->document_type).'<br>';
            }
          }
          $documents .='</td>';
          $documents .='</tr>';
          $documents .='</table>';
      }
      

      foreach ($fData as $key => $val) {
        if (array_key_exists($val, $formData)) {
            if (!empty($formData[$val])) {
              $template = str_replace('%%'.$val.'%%',$formData[$val], $template);
            }else{
              $template = str_replace('%%'.$val.'%%','NA', $template);
            }
          
        }
      }

      if(!empty($final_preview->custom_field)){
        $final_preview->custom_field = (array)json_decode($final_preview->custom_field);
        foreach($final_preview->custom_field as $key => $value){
          $template = str_replace('%%'.$key.'%%',$value, $template);
        }
      }
      
      $income_yes_no = 'No';
      if (!empty($final_preview->caste_income_certificate_number)) {
        $income_yes_no = 'Yes';
      }
      $physical_disability_yes_no = 'No';
      if (!empty($final_preview->physical_disability == 'Y')) {
        $physical_disability_yes_no = 'Yes';
      }
      $learning_disability_yes_no = 'No';
      if (!empty($final_preview->learning_disability == 'Y')) {
        $learning_disability_yes_no = 'Yes';
      }
      $student_gender = '';
      if ($final_preview->gender =='M') {
          $student_gender = 'Male';
      }else if($final_preview->gender == 'F'){
        $student_gender = 'Female';
      }else if($final_preview->gender == 'O'){
        $student_gender = 'Other';
      }
      $dobAge = $this->getAge($final_preview->dob);
      $template = str_replace('%%prevoius_school_not_in_marks%%', $prevSchoolnotinmarks, $template);
      $template = str_replace('%%previous_education_details%%', $prevSchool, $template);
      $template = str_replace('%%income_yes_no%%', $income_yes_no, $template);
      $template = str_replace('%%physical_disability_yes_no%%', $physical_disability_yes_no, $template);
      $template = str_replace('%%learning_disability_yes_no%%', $learning_disability_yes_no, $template);
      $template = str_replace('%%s_permanent_pincode%%', $final_preview->s_permanent_pincode, $template);
      $template = str_replace('%%combinations%%', $combination, $template);
      $template = str_replace('%%student_quota%%', $student_quota, $template);
      $template = str_replace('%%dob_age%%', $dobAge, $template);
      $template = str_replace('%%student_gender%%', $student_gender, $template);
      $template = str_replace('%%father_name%%', $final_preview->father_name, $template);
      $template = str_replace('%%mother_name%%',$final_preview->mother_name, $template);
      $template = str_replace('%%student_name%%',$final_preview->student_name, $template);
      $dob_in_words = $this->dateToWords($final_preview->dob);
      $template = str_replace('%%dob_in_words%%',$dob_in_words, $template);
      $template = str_replace('%%documents%%',$documents, $template);
      return $template;
    }

    function getAge($dateVal) {
      $cal_date = $this->settings->getSetting('enquiry_date_to_calculate_age');
  
      $onSelectDate = new DateTime($dateVal);
  
      if ($cal_date) {
          $today = new DateTime($cal_date);
      } else {
          $today = new DateTime(); // Current date
      }
  
      $monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
  
      $day = $today->format('d');
      $month = $monthNames[$today->format('n') - 1]; // 'n' gives the month number (1-12)
      $year = $today->format('Y');
  
      $formattedDate = $day . ' ' . $month . ' ' . $year;
  
      $interval = $today->diff($onSelectDate);
  
      $years = $interval->y;
      $months = $interval->m;
      $days = $interval->d;
  
      return $years . ' years ' . $months . ' months ' . $days . ' days (as on ' . $formattedDate.')';
    }
    private function numberToWords($num) {
      $ones = array(
          0 => "Zero", 1 => "One", 2 => "Two", 3 => "Three", 4 => "Four", 5 => "Five",
          6 => "Six", 7 => "Seven", 8 => "Eight", 9 => "Nine", 10 => "Ten",
          11 => "Eleven", 12 => "Twelve", 13 => "Thirteen", 14 => "Fourteen", 15 => "Fifteen",
          16 => "Sixteen", 17 => "Seventeen", 18 => "Eighteen", 19 => "Nineteen"
      );
  
      $tens = array(
          0 => "Zero", 1 => "Ten", 2 => "Twenty", 3 => "Thirty", 4 => "Forty", 5 => "Fifty",
          6 => "Sixty", 7 => "Seventy", 8 => "Eighty", 9 => "Ninety"
      );
  
      if ($num < 20) {
          return $ones[$num];
      } elseif ($num < 100) {
          return $tens[intval($num / 10)] . (($num % 10 > 0) ? " " . $ones[$num % 10] : "");
      } elseif ($num < 1000) {
          return $ones[intval($num / 100)] . " Hundred" . (($num % 100 > 0) ? " " . $this->numberToWords($num % 100) : "");
      } else {
          return $this->numberToWords(intval($num / 1000)) . " Thousand" . (($num % 1000 > 0) ? " " . $this->numberToWords($num % 1000) : "");
      }
  }
  
  private function dateToWords($dateStr) {
      $date = DateTime::createFromFormat('d-m-Y', $dateStr);
      $day = intval($date->format('d'));
      $month = $date->format('F');
      $year = intval($date->format('Y'));
  
      $dayInWords = $this->numberToWords($day);
      $yearInWords = $this->numberToWords($year);
  
      return "$dayInWords - $month - $yearInWords";
  }

    private function _generate_admisison_pdf_receipt($html, $afId) {
      $school = CONFIG_ENV['main_folder'];
      $path = $school.'/admissions_reciepts/'.uniqid().'-'.time().".pdf";
      $bucket = $this->config->item('s3_bucket');
      $status = $this->Admission_model->update_admission_form_path($afId, $path);
      $page_size = 'a4';
      $page = 'portrait';
      $curl = curl_init();
      $postData = urlencode($html);
      $username = CONFIG_ENV['job_server_username'];
      $password = CONFIG_ENV['job_server_password'];
      $return_url = site_url().'Callback_Controller/updateApplicationPdfLink';

      $curl_req_params = array(
        CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
        CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
          )
        );

      curl_setopt_array($curl, $curl_req_params);
      $response = curl_exec($curl);
      $err = curl_error($curl);
      curl_close($curl);
      if ($err) {
        return 0;
      } else {
        return $path;
      }

    }

    public function language_mapping($admissionSettingId){
      $data['admission_setting_id'] = $admissionSettingId;
      $data['elective_master_group'] = $this->Admission_model->get_elective_master_group_details();
      $data['class_master'] = $this->Admission_model->get_class_master_details();
      $data['mapping_data'] = $this->Admission_model->get_admission_lang_mapping_data($admissionSettingId);
      $data['main_content'] = 'admission/settings/language_mapping';
      $this->load->view('inc/template', $data); 
    }

    public function submit_lang_class_wise_mapping(){
      $status = $this->Admission_model->insert_lang_class_wise_mapping();
      if ($status == 1) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Inserted.');
      }else{
        $this->session->set_flashdata('flashError', 'Failed to confirm .');
      }
      redirect('admission_process/language_mapping/'.$this->input->post('admission_setting_id'));
    }

    public function collect_offline_fee($afId, $au_Id, $admission_setting_id){
      $data['afId'] = $afId;
      $data['au_Id'] = $au_Id;
      $data['admission_setting_id'] = $admission_setting_id;
      $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
      $data['form_name'] = $data['config_val']['form_name'];
      $data['form_year'] = $data['config_val']['form_year'];
      $data['school_name'] = $data['config_val']['school_name'];
      $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_Id, $afId);
      $data['admissions_payment_mode'] = $this->settings->getSetting('admissions_payment_mode');
      $data['main_content'] = 'admission/staff/application/application_collect_fees';
      $this->load->view('inc/template', $data);
    }


    public function application_view(){
      $admissions = $this->Admission_model->admission_settings_get();
      $grades = array();
      $streams = [];
      foreach ($admissions as $key => $val) {
        // $class_applied_for = json_decode($val['class_applied_for'], true);
        // foreach ($class_applied_for as $key => $value) {
        //   array_push($grades, $value);
        // }
        $streams = json_decode($val['streams'], true);
        $grades = json_decode($val['class_applied_for'], true);
      }

      $data['grades'] = $grades;
      $data['streams'] = $streams;
      if(!$this->settings->getSetting('admission_pick_status_from_table')){
        $data['follow_up_status'] = json_decode($this->settings->getSetting('application_form_status'));
      }else{
        $data['follow_up_status'] =  $this->Admission_model->get_user_status();
      }
      // // $data['follow_up_status'] = json_decode($this->settings->getSetting('application_form_status'));  
      // $data['follow_up_status'] =  $this->Admission_model->get_user_status();    
      $data['main_content'] = 'admission/staff/view_application_list';
      $this->load->view('inc/template', $data);
    }

    public function get_admission_movetoerp_status(){
      $afId = $_POST['afId'];
      $result = $this->Admission_model->check_all_status_for_admission($afId);
      echo json_encode($result);
    }

    public function update_pay_to_date(){
      $cohort_student_id = $_POST['cohort_student_id'];
      $pay_date = $_POST['pay_date'];
      echo $this->Admission_model->update_pay_to_dateby_cohort_student_id($cohort_student_id, $pay_date);
    }

    // public function get_joining_forms_status() {
    //   $afId = $_POST['afId'];

    //   $data['documents'] = $this->Admission_model->get_consent_form_templates($afId);
    //   foreach ($data['documents'] as &$val) {
    //     if ($val->status == 'Submitted') {
    //       $val->consent_form_path = $this->filemanager->getFilePath($val->consent_form_path);
    //     }
    //     //Modify time format too
    //     if (empty($val->submitted_on)) {
    //       $val->submitted_on = 'NA';
    //     } else {
    //       $val->submitted_on = date('d-M-Y', strtotime($val->submitted_on));
    //     }
    //   }
    //   echo json_encode($data);
    // }

    public function upload_new_documents(){
        $path = $_POST['path'];
        $doc_name = $_POST['doc_name'];
        $doc_type = $_POST['doc_type'];
        $af_id = $_POST['af_id'];
        echo $this->Admission_model->upload_new_document($path, $af_id, $doc_type, $doc_name);
    }

    public function get_joining_forms_status() {
      $af_id= $_POST['afId'];
      $result = $this->Admission_model->get_joining_forms_status($af_id);
      // foreach ($result as &$val) {
      //   if ($val->status == 'Submitted' && !empty($val->consent_form_path)) {
      //     $val->consent_form_path = $this->filemanager->getFilePath($val->consent_form_path);
      //   }
      // }

      echo json_encode($result);
    }

    public function upload_joining_form_by_staff() {
      $this->load->library('filemanager');
      // echo '<pre>'; print_r($_POST); die();
      $path = $_POST['path'];
      $doc_id = $_POST['document_for'];
      $af_id = $_POST['af_id'];
      // $staff_id = $_POST['staff_id'];
      $url= $this->Admission_model->upload_joining_form_by_staff($path, $doc_id, $af_id);
      if($url){
        $template_data = $this->Admission_model->get_template_data($doc_id);
        if(!empty($template_data->email_template_id)){
          $email_data = $this->Admission_model->get_data_toSend_email($template_data->email_template_id,$af_id,$template_data->notification_staff_ids);
          $sent_mail = $this->_email_to_parent_staff($email_data);
          if($sent_mail){
            $this->session->set_flashdata('flashSuccess', "Email Sent Successfully");
          }
        }
      }
        $link= $this->filemanager->getFilePath($url);
        echo ($link);
    }

    private function _email_to_parent_staff($input){
      $emailIds = $input['to_emails'];
      // $memberEmail = [];
      // foreach ($emailIds as $key => $val) {
        // $memberEmail[]['email'] = $val;
      // }

      $sent_by = $this->authorization->getAvatarStakeHolderId();
      $email_master_data = array(
        'subject' => $input['email_subject'],
        'body' => $input['template_content'],
        'source' => 'Admission Mass Email',
        'sent_by' => $sent_by,
        'recievers' => "Parents",
        'from_email' => $input['registered_email'],
        'acad_year_id' => $this->acad_year->getAcadYearID(), 
        'visible' => 1,
        'sending_status' => 'Completed',
        'sender_list'=>implode(',',$emailIds)
      );

      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      foreach ($emailIds as $email) {
        if (!empty($email)) {
            $this->enquiry_model->save_sending_email_data([
                'stakeholder_id'   => 0,
                'avatar_type'      => 0,
                'email'            => $email,
                'email_master_id'  => $email_master_id,
                'status'           => 'Awaited'
            ]);
        }
      }
      return sendEmail($input['template_content'], $input['email_subject'], $email_master_id, $emailIds, $input['registered_email'], '');
      // return $this->__send_consent_email($input['template_content'], $input['email_subject'], $memberEmail, $input['registered_email']);
    }
    
    private function __send_consent_email($message, $subject, $members, $from_email) {
      if($from_email == '') {
        return -1;
      }
        $from_name = $this->settings->getSetting('school_name');
        // $smtp_user = $set['settings']['smtp_user'];
        // $smtp_pass = urlencode($set['settings']['smtp_pass']);
        // $smtp_host = $set['settings']['smtp_host'];
        // $smtp_port = $set['settings']['smtp_port'];
        $smtp_user = CONFIG_ENV['smtp_user'];
        $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
        $smtp_host = CONFIG_ENV['smtp_host'];
        $smtp_port = CONFIG_ENV['smtp_port'];
    
        $data = array(
        'message' => $message,
        'attachment' => '',
        'subject' => $subject,
        'members' => $members,
        'from_email' => $from_email,
        'from_name' => $from_name,
        'smtp_user' => $smtp_user,
        'smtp_pass' => $smtp_pass,
        'smtp_host' => $smtp_host,
        'smtp_port' => $smtp_port
        );
        // echo "<pre>"; print_r($data); die();
        $data = http_build_query($data);
    
        // echo "data: ".$data;die();
      
        $curl = curl_init();
    
    
        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
    
        curl_setopt_array($curl, array(
        CURLOPT_URL => CONFIG_ENV['job_server_email_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POST => 1,
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => array(
        "Accept: application/json",
        "Cache-Control: no-cache",
        "Content-Type: application/x-www-form-urlencoded",
        "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
      ));
    
      $response = curl_exec($curl);
      $err = curl_error($curl);
    
      curl_close($curl);
    
      if ($err) {
        // echo "cURL Error #:" . $err;
        return 0;
      } else {
        return 1;
      }
      }
  

    public function delete_documentby_Id() {
      $id = $_POST['id'];
      echo $this->Admission_model->delete_documentby_Id($id);
    }

    public function deletedocument_row_parent() {
      $submisssion_id = $_POST['submisssion_id'];
      $reason_of_rejection = $_POST['reason_of_rejection'];
      echo $this->Admission_model->deletedocument_row_parent($submisssion_id, $reason_of_rejection);
    }

    public function joining_form_report() {
      $data['main_content'] = 'admission/joining_form_status_report';
      $data['consent_form_templates'] = $this->Admission_model->get_enquiry_templates();
      // echo '<pre>';print_r($data['consent_form_templates']);die();
      $this->load->view('inc/template_fee', $data);
    }

    public function show_students_joinig_reports_according_to_admissionType() {
      $data['joining_status']= $this->Admission_model->joining_form_report($_POST['admission_type_id']);
      $template = array();
      $i = 0;
      foreach($data['joining_status'] as $key=>$val){
        if(!empty($val->tmp_sts))
          foreach($val->tmp_sts as $k => $v){
            if(!in_array($v['template_name'],$template)){
              $template[$i++] = $v['template_name'];
            }
          }
      }
      echo json_encode(array('joining_status'=>$data['joining_status'],'template'=>$template));
    }

    public function admission_offers_report(){
      $data['offers_summary']= $this->Admission_model->get_offers_summary();
      $data['offers_name']= $this->Admission_model->get_admission_offers();
      // echo '<pre>'; print_r($data['offers_name']); die();
      $data['main_content'] = 'admission/admission_offers_report';
      $this->load->view('inc/template_fee', $data);
    }

    public function admission_analysis(){
      $data['conselor_wise_data'] = $this->Admission_model->getAdmission_conselor_data();
      $data['status_arr'] = $this->Admission_model->get_reporting_status();
      $data['reporting_status_admissions'] = $this->Admission_model->get_admissions_basedOn_reporting_status();
      $data['user_status_admissions'] = $this->Admission_model->get_admissions_basedOn_user_status();
      $data['gender_wise_data'] = $this->Admission_model->gender_wise_Admission_data();
      $data['grade_wise_data'] = $this->Admission_model->get_gradeWise_status();
      $data['status_arra'] = $this->Admission_model->get_reporting_status_arr();
      $data['closure_reasons'] = $this->Admission_model->getClosureReasons();
      // echo '<pre>'; print_r($data['closure_reasons']); die();
      $data['main_content'] = 'admission/counselor_efficiency_report';
      $this->load->view('inc/template_fee', $data);
    }

    public function receipt_cancellation_report(){
      $data['cancled_receipts'] = $this->Admission_model->get_cancled_receipts();
      // echo '<pre>';print_r($data['cancled_receipts']);die();
      $data['main_content'] = 'admission/receipt_cancellation_report';
      $this->load->view('inc/template', $data);
    }

    public function admission_activity_report(){
      $data['counselor'] = $this->Admission_model->get_couselor_list();
      $data['main_content'] = 'admission/admission_activity_report_page';
      $this->load->view('inc/template', $data);
    }

    public function manage_admission_status(){
      $data['staff_names'] = $this->Admission_model->get_staff_names();
      $data['admission_status_list'] = $this->Admission_model->get_user_status();
      $data['main_content'] = 'admission/manage_admission_status';
      $this->load->view('inc/template', $data);
      
    }

    public function manage_consentform_templates(){
      $data['email_templates'] = $this->Admission_model->get_email_templates();
      $data['acad_year'] = $this->Admission_model->get_acad_years();
      $data['staff_names'] = $this->Admission_model->get_staff_names();
      $data['main_content'] = 'admission/manage_consentform_templates';
      $this->load->view('inc/template', $data);
    }

    public function get_admission_status_data() {
      $result = $this->Admission_model->get_admission_status_data();
      echo json_encode($result);
    }

    public function submit_admission_status()
    {
      echo $this->Admission_model->create_admission_status($_POST);
    }

    public function update_admission_status()
    {
      echo $this->Admission_model->update_admission_status($_POST);
    }

    public function save_student_profile_photo(){
      $input = $_POST;
      $file = $_FILES['file'];
      $edit_tracking =  $this->Admission_model->edited_photos_history($input);
      $sRealphoto = $this->Admission_model->update_student_profile_photo($input['af_id'], $input['high_quality'], $input['type']);
      if($sRealphoto){
        echo 1;
      }else{
        echo 0;
      }
    
    }

    public function save_admission_fields(){
    
      $fied_names = $_POST['fied_names'];
      $class_list = $_POST['class_list'];
      $ad_status = $_POST['ad_status'];
      $title = $_POST['title'];
      // echo '<pre>';print_r($selectedIndex);die();
      $result =  $this->Admission_model->save_admission_fields_names($fied_names, $class_list,$ad_status,$title);
      echo $result;
    }

    public function update_admission_fields(){
      $id = $_POST['id'];
      $fied_names = $_POST['fied_names'];
      $class_list = $_POST['class_list'];
      $ad_status = $_POST['ad_status'];
      $title = $_POST['title'];

      // echo '<pre>';print_r($selectedIndex);die();
      $result =  $this->Admission_model->update_admission_fields_names($id,$fied_names, $class_list,$ad_status,$title);
      echo $result;
    }

    public function get_predefined_names(){
      $data['predefined_data'] =  $this->Admission_model->get_title_names();
      echo json_encode($data);
    }

    public function get_predefined_data(){
      $id = $_POST['id'];
      $result =  $this->Admission_model->get_predefined_data($id );
      echo json_encode($result);
    }

    public function get_previous_school_details_data(){ 
      $af_id = $_POST['af_id'];
      $admission_setting_id = $_POST['admission_setting_id'];
      $school_details = $this->Admission_model->get_SchoolDetailsbyStdId($af_id);
// converting to download
    if(!empty($school_details)){
      foreach ($school_details as $key => $val) {
        if(!empty($val->report_card)){
          $school_details[$key]->report_card = $this->filemanager->getFilePath($val->report_card);
        }
      }
    }

      $yearWise = [];
      if(!empty($school_details)){
      foreach ($school_details as $key => $val) {
        $yearWise[$val->year] = $val;
      }}
      
      $config_val = $this->_get_admissions_settings_byId($admission_setting_id);

      $prev_eduction_info = json_decode($config_val['prev_eduction_info'], true);
      $preSchoolingmarks = $config_val['show_previous_schooling_overall_total_marks'];
      $show_previous_schooling_subject_total_marks = $config_val['show_previous_schooling_subject_total_marks'];
      $show_previous_schooling_subject_percentage = $config_val['show_previous_schooling_subject_percentage'];
      $admissionPrevSchool =  $this->_get_previous_year_marks_data($af_id, $prev_eduction_info, $preSchoolingmarks, $show_previous_schooling_subject_total_marks, $show_previous_schooling_subject_percentage);
      echo json_encode(array('edit_data'=>$yearWise,'config_val'=>$config_val,'submarks'=>$admissionPrevSchool));
    }
    private function _get_previous_year_marks_data($af_id, $prev_eduction_info, $preSchoolingmarks, $show_previous_schooling_subject_total_marks, $show_previous_schooling_subject_percentage){
      $gradeAppliedFor = $this->Admission_model->getAdmissiongradeAppliedfor($af_id);
      $subjectArray = [];
      if (!empty($prev_eduction_info['class'])) {
        if (!empty($prev_eduction_info['class'][$gradeAppliedFor])) {
          $subjectArray = $prev_eduction_info['class'][$gradeAppliedFor]['subject'];
        }
      }

      $admission_prev_schools = $this->Admission_model->get_admission_form_previous_school_details($af_id);
      // echo "<pre>"; print_r($admission_prev_schools);
      // die();
      // echo "<pre>"; print_r($subjectArray);
      $prevHtml = '';
      if(!empty($subjectArray)){
        $prevHtml .= '<table class="table table-bordered" style="margin: 0;">';
        $prevHtml .= '<tr>';
        $prevHtml .= '<th style="background: #ccc;font-size: 14px;text-align:center">Subjects Details</th>';
        $prevHtml .= '</tr>';
        $prevHtml .= '</table>';
        $prevHtml .= '<table class="table table-bordered">';
        $countPreSchool = count($admission_prev_schools);
        foreach ($admission_prev_schools as $key => $sPrev){
          $prevHtml .= '<tr>';
          $prevHtml .= '<th style="vertical-align: middle;" rowspan="'.($countPreSchool).'">'.$sPrev->year.'</th>';
          if (!empty($subjectArray)) {
            foreach ($subjectArray as $key => $sub) { 
              if (!empty($sPrev->marks)) {
                foreach ($sPrev->marks as $key => $val) {
                   $jsan =  json_decode($val->sub_name);
                       if ($sub['id'] == $jsan->sub_id) {
                           $lang_name = $jsan->name;
                       }
                   }
                   $prevHtml .= '<th>';
                   switch ($sub['type']) {
                       case 'label':
                        $prevHtml .= $sub['name'];
                           break;
                           case 'text':
                        $prevHtml .= $sub['name'].'- '.$lang_name;
                           break;
                   }
                   $prevHtml .= '</th>';
               }
            }
            if ($preSchoolingmarks == 1) {
            $prevHtml .= '<th>Total Marks</th>';
            $prevHtml .= '<th>Total Marks Scored</th>';
            $prevHtml .= '<th>Total Percentage</th>';
            }
          }
          $prevHtml .= '</tr>';
          if (!empty($sPrev->marks)) {
            $prevHtml .= '<tr>';
            foreach ($sPrev->marks as $key => $val) {
              $jsan =  json_decode($val->sub_name);
              foreach ($subjectArray as $key => $sub) {
                if ($sub['id'] == $jsan->sub_id) { 
                  if ($show_previous_schooling_subject_total_marks) {
                    $prevHtml .= '<td>'.$val->marks_scored.'</td>';
                  }                 
               }
              }
            }
            $prevHtml .= '<th>'.$sPrev->total_marks.'</th>';
            $prevHtml .= '<th>'.$sPrev->total_marks_scored.'</th>';
            $prevHtml .= '<th>'.$sPrev->total_percentage.'</th>';
            $prevHtml .= '</tr>';
          }
          if ($preSchoolingmarks != 1) {
            $prevHtml .= '<tr>';
            $prevHtml .= '<th>Grade</th>';
            if (!empty($sPrev->marks)) {
              foreach ($sPrev->marks as $key => $val) {
                $jsan =  json_decode($val->sub_name);
                foreach ($subjectArray as $key => $sub) {
                  if ($sub['id'] == $jsan->sub_id) {
                    if ($show_previous_schooling_subject_percentage) {
                      $prevHtml .= '<td>'.$val->grade.'</td>';
                    }
                  }
                }
              }
            }
            $prevHtml .= '</tr>'; 
          }

        }
        $prevHtml .= '</table>';
      }
      return $prevHtml;
    }
     
     public function update_previous_school_details_year_wise(){
        $input = $this->input->post();
        $report_card = '';
        if (isset($_FILES['report_card'])) {
            $report_card = $this->s3FileUpload($_FILES['report_card']);
        }
        echo $this->Admission_model->update_previous_school_details_year_wise_in_admission($input, $report_card);
    }

    public function s3FileUpload($file,$folder_name='Admission_form_document') {
        if($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
        }        
        return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],$folder_name);
    }

    
    public function indus_report_admissions() {
      $data['indus_report'] = $this->Admission_model->get_application_indus_report();
      $data['main_content'] = 'admission/staff/indus_report';
      $this->load->view('inc/template', $data);
    }

    public function get_edit_history_tracking_data(){
      $data['edit_history'] = $this->Admission_model->get_edit_history_tracking_data($_POST);
      $data['followup_history'] = $this->Admission_model->get_followup_history($_POST);
      // echo '<pre>';print_r($data['followup_history']);die();
      echo json_encode($data);
    }

    public function search_enquiry(){
      $result = $this->Admission_model->search_enquiry($_POST);
      // echo '<pre>';print_r($result);die();
      echo json_encode($result);
    }

    public function link_to_enquiry(){
      $result = $this->Admission_model->link_to_enquiry($_POST);
      echo $result;
    }

    public function get_class_sections(){
      $acadYearId = $_POST['acadYearId'];
      $result =  $this->enquiry_model->getClassByAcadYear($acadYearId);
      echo json_encode($result);
    }

    public function submit_enquiry(){
      $input = $this->input->post();
      $result = $this->Admission_model->insert_enquiry_data($input);
      // echo '<pre>';print_r($result);die();
      echo $result;
    }

    public function show_enquiry_details_by_id(){
      $result = $this->Admission_model->show_enquiry_details($_POST);
      echo json_encode($result);
    }

    public function move_to_draft_application(){
      $af_id = $_POST['af_id'];
      $message = $this->settings->getSetting('revert_application_sms',0);
      $mobileNumber = array();
      array_push($mobileNumber,$this->input->post('f_mobile_no'));
      array_push($mobileNumber,$this->input->post('m_mobile_no'));
      if($message){
        $sent_sms = $this->_admissions_sms($mobileNumber,$message);
        if($sent_sms){
          $this->session->set_flashdata('flashSuccess', "SMS Sent");
        }
      }
      echo $this->Admission_model->move_to_draft_application_by_id($af_id);
    }

    private function _admissions_sms($number,$message){
      $input_arr = array();
      $input_arr['source'] = 'Admissions';
      $input_arr['message'] = $message;
      $input_arr['custom_numbers'] = [$number];
      $input_arr['mode'] = 'sms';
      $response = sendText($input_arr);
      if($response['success'] != '') {
        $status = 1;
      } else {
        $status = 0;
      }
      return $status;
    }
    
    public function admission_unassigned_data(){
      $createdfrom_date = $_POST['createdfrom_date'];
      $createdto_date = $_POST['createdto_date'];
      $counselor = $_POST['counselor'];
      $app_status = $_POST['app_status'];
  
      $result =  $this->Admission_model->admission_unassigned_data($createdfrom_date,$createdto_date,$counselor,$app_status);
      echo json_encode($result);
    }

    public function update_counselor_by_selected_admissions(){
      $counselor_id = $_POST['counselor_id'];
      $adm_ids = $_POST['adm_ids'];
      echo $this->Admission_model->update_counselor_by_selected_admissions($counselor_id, $adm_ids);
    }

    public function get_table_format_counselor_data(){
      $data['status_arr'] = $this->Admission_model->get_reporting_status();
      $data['conselor_wise_data'] = $this->Admission_model->getAdmission_conselor_data();
      echo json_encode($data);
    }

    public function save_admission_form_id(){
      $admission_form_id = $_POST['admission_form_id'];
      $status_id = $_POST['status_id'];
      echo $this->Admission_model->save_admission_form_id_by_status($admission_form_id, $status_id);
    }

    public function change_acadYear(){
      echo $this->Admission_model->change_acadYear($_POST);
    }

    public function submit_consent_form_template(){
      echo $this->Admission_model->submit_consent_form_template($_POST);
    }

    public function update_consent_form_template(){
      echo $this->Admission_model->update_consent_form_template();
    }

    public function get_consent_forms(){
      $result = $this->Admission_model->get_consent_forms();
      echo json_encode($result);
    }

    public function mass_email(){
      $admissions = $this->Admission_model->grades_from_admission_settings_get();
      $grades = [];
      if(!empty($admissions)){
        foreach($admissions as $key =>$val){
          $grades = array_merge($grades,json_decode($val->class_applied_for));
        }
      }
      $data['grades'] = $grades;
      $category = 'Admission';
      $data['sTemplate'] = $this->Admission_model->get_sms_template($category);
      if(!$this->settings->getSetting('admission_pick_status_from_table')){
        $data['follow_up_status'] = json_decode($this->settings->getSetting('application_form_status'));
      }else{
        $data['follow_up_status'] =  $this->Admission_model->get_user_status();
      }
      $data['main_content'] = 'admission/staff/mass_emailsms';
      $this->load->view('inc/template', $data);
    }

    public function mass_email_sms_search(){
      // $status_from_date = $_POST['status_from_date'];
      // $status_to_date = $_POST['status_to_date'];
      $app_status = $_POST['app_status'];
      $grade = $_POST['grade'];
      $results = $this->Admission_model->serach_email_sms($app_status,$grade);
      echo json_encode($results);
    }

    public function getPreviewData(){
      $input = $this->input->post();
      if(!empty($input['student_ids'])) {
          $students = $this->Admission_model->getStudents($input['student_ids']);
          echo json_encode($students);
      }
    }
    public function document_vefication(){
      $result = $this->Admission_model->document_vefication($_POST);
      if($_POST['verification_status'] == 'Rejected'){
        $email_data = $this->Admission_model->get_reject_doc_email_template($this->input->post('af_id'));
        if(!empty($email_data)){
          $email_data['template_content'] = str_replace('%%rejected_remarks%%',$this->input->post('verification_remarks'), $email_data['template_content']);
          $sent_email = $this->_email_to_parent($email_data);
        }
      }
      echo $result;
    }
    public function get_enquiry_sms_content(){
      $smstemplateId = $this->input->post('smstemplateId');
      $result = $this->Admission_model->get_enquiry_sms_templatebyId($smstemplateId);
      echo json_encode($result);
    }

  //   private function _rejected_doc_sms_to_parent($application_no, $mobileNumber){
  //     $smsint = (array) $this->settings->getSetting('smsintergration');
  //     $document_rejected_sms = $this->settings->getSetting('document_rejected_sms',0);
  //     if (!empty($document_rejected_sms)) {
  //         $msg =  $document_rejected_sms;
  //         $msg = str_replace('%%admission_no%%',$application_no, $msg);
  //     }else{
  //         $msg = "Your Document is rejected application number ".$application_no." . Re upload the document.";
  //     }
  //     $content =  urlencode(''.$msg.'');

  //     $get_url = 'http://'.$smsint['url'].'?method=sms&api_key='.$smsint['api_key'].'&to='.$mobileNumber.'&sender='.$smsint['sender'].'&message='.$content;
  //     return $this->curl->simple_get($get_url);
  // }

    private function _email_to_parent($input){
      $this->load->model('communication/emails_model');
      $emailIds = explode(',', $input['to_mails']);
      $input['template_content'] = str_replace('%%application_number%%',$input['application_number'], $input['template_content']);

      $sent_by = $this->authorization->getAvatarStakeHolderId();
      $email_master_data = array(
        'subject' => $input['email_subject'],
        'body' => $input['template_content'],
        'source' => 'Document Rejected in Admission',
        'sent_by' => $sent_by,
        'recievers' => "Parents",
        'from_email' => $input['registered_email'],
        'files' => empty($files_array) ? '' : json_encode($files_array) ,
        'acad_year_id' => $this->acad_year->getAcadYearID(),
        'visible' => 1,
        'sender_list'=>$input['to_mails'],
        'sending_status' => 'Completed'
      );

      $email_master_id = $this->emails_model->saveEmail($email_master_data);

      // $memberEmail = [];
      foreach ($emailIds as $key => $val) {
      //  $memberEmail[]['email'] = $val;

        if(empty($val)){
          continue;
        }
        $email_data = [];
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = 0;
        $email_obj->avatar_type = 0;
        $email_obj->email = $val;
        $email_data[] = $email_obj;

        $this->emails_model->save_sending_email_data($email_data,$email_master_id);
      }
      sendEmail($input['template_content'], $input['email_subject'], $email_master_id, $emailIds, $input['registered_email'], '');
      // return $this->__send_email($input['template_content'], $input['email_subject'], $memberEmail, $input['registered_email']);
    }
    public function get_student_documents(){
      $result = $this->Admission_model->get_admission_form_detailsby_auId($_POST['afId']);
      echo json_encode($result);
    }

    public function revert_submission_email_to_parent(){
      // If required File attachement
      // $files_array = array();
      // if($set->attached_file != '') {
      //   array_push($files_array, array('name' => 'Attached', 'path' => $set->attached_file));
      // }
      $sendEmails = explode(',',$this->input->post('to_mails'));
      $email_ids = [];
      foreach ($sendEmails as $pid => $email) {
        $sender_list['sender_email'] = [
          'send_to' => $email
        ];
        array_push($email_ids, $email);
      }

        $email_master_data = array(
          'subject' => $this->input->post('email_subject'),
          'body' => $this->input->post('email_revert_content'),
          'source' => 'Admission Revert Submission',
          'sent_by' => $this->authorization->getAvatarId(),
          'recievers' => 'Admission users',
          'from_email' => $this->input->post('registered_email'),
          'files' => '',
          'visible' => 1,
          'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
          'sending_status' => 'Completed',
          'admission_form_id' => $this->input->post('email_admission_form_id'),
        );
        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail_admission($email_master_data);
        
        $this->load->helper('email_helper');
        $email = $this->emails_model->getEmailInfo_admission($email_master_id);
        return sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
    }

    public function reupdate_documents_new(){
      echo  $this->Admission_model->reupdate_documents_new($_POST);
    }

    public function delete_document_path(){
      echo  $this->Admission_model->delete_document_path($_POST);
    }

    public function get_document_verify_data(){
      $result = $this->Admission_model->get_document_verify_data($_POST['document_id']);
      echo json_encode($result);
    }

    public function get_admission_document_by_id(){
      $result = $this->Admission_model->get_admission_document_by_id($_POST['af_id'],$_POST['adm_setting_id']);
      echo json_encode($result);
    }

    public function submit_admission_documents(){
      $document_path = '';
      if(isset($_FILES['document_file'])){
        $document_path = $this->s3FileUpload($_FILES['document_file']);
      }
      $ackn_path = '';
      if(isset($_FILES['acknowledgement_file'])){
        $ackn_path = $this->s3FileUpload($_FILES['acknowledgement_file']);
      }
      $declaration_path = '';
      if(isset($_FILES['declaration_file'])){
        $declaration_path = $this->s3FileUpload($_FILES['declaration_file']);
      }

      echo $this->Admission_model->update_admission_documents($_POST,$document_path,$ackn_path,$declaration_path);
  }

  public function get_conselor_load_data(){
    $counselor_load = $this->Admission_model->get_counselor_load();
    echo json_encode($counselor_load);
  }

  public function close_application(){
    echo $this->Admission_model->close_application($this->input->post());
  }

  public function mass_email_send(){
    $input = $this->input->post();
    
    $student_ids = array_unique(explode(',',$input['student_ids']));
    $files_array = array();
		if(isset($_FILES['attachment'])) {
			foreach ($_FILES['attachment']['name'] as $key => $file_name) {
				$file = array(
					'name' => $file_name,
					'type' => $_FILES['attachment']['type'][$key],
					'tmp_name' => $_FILES['attachment']['tmp_name'][$key],
					'error' => $_FILES['attachment']['error'][$key],
					'size' => $_FILES['attachment']['size'][$key]
				);
				$path = $this->__s3FileUpload($file);
				if($path['file_name'] != '') {
					array_push($files_array, array('name' => $file_name, 'path' => $path['file_name']));
				}
			}
		}
    $memberEmail = array_unique(explode(',', $input['send_to']));
    $memberEmail = array_filter($memberEmail, function ($email) {
      return filter_var(trim($email), FILTER_VALIDATE_EMAIL);
    });
    $admission_details = $this->db->select('id,student_email_id,f_email_id,m_email_id ')->from('admission_forms')->where_in('id',$student_ids)->get()->result();

    foreach ($admission_details as $admission) {
      $adm_arr_by_id[$admission->id] = [
          $admission->student_email_id,
          $admission->f_email_id,
          $admission->m_email_id
      ];
    }

    foreach ($adm_arr_by_id as $key => $val) {
      $email_sent_to_ids_arr = []; 
  
      foreach ($val as $email) {
        $email_sent_to_id = 0;
        if(!empty($email)){
          $email_sent_to_id = $this->_save_to_email_master($input['body'], $input['remarks'], $input['fromemail'], $files_array, $email,'Admissions Mass Email');
        }
          $email_sent_to_ids_arr[] = $email_sent_to_id; // Append to the array
      }
      $parents_emails = implode(',', $val);
      $email_sent_to_ids = implode(',', $email_sent_to_ids_arr);
      
      $this->Admission_model->submit_follow_up($key, $input['remarks'], $input['fromemail'], $input['body'], $input['title'], $email_sent_to_ids,$parents_emails);
  }
      
    // $this->Admission_model->submit_follow_up($student_ids,$input['remarks'],$input['fromemail'],$input['body'],$input['title']);
    echo $this->_send_mass_mail($input['body'], $input['title'], $memberEmail, $input['fromemail'],$files_array); 
    
  }

  function _save_to_email_master($body,$title,$fromemail,$files_array,$email,$source){
    $this->load->model('communication/emails_model');
    $sent_by = $this->authorization->getAvatarStakeHolderId();
    $email_master_data = array(
      'subject' => $title,
      'body' => $body,
      'source' => $source,
      'sent_by' => $sent_by,
      'recievers' => "Parents",
      'from_email' => $fromemail,
      'files' => empty($files_array) ? '' : json_encode($files_array) ,
      'acad_year_id' => $this->acad_year->getAcadYearID(),
      'visible' => 1,
      'sender_list'=>$email,
      'sending_status' => 'Completed'
    );

    $email_master_id = $this->emails_model->saveEmail($email_master_data);

    $email_data = [];
    $email_data['stakeholder_id'] = 0;
    $email_data['avatar_type'] = 0;
    $email_data['email'] = $email;
    $email_data['email_master_id'] = $email_master_id;
    $email_data['status'] = 'Awaited';

    return $this->Admission_model->save_sending_email_data($email_data);
  }

  private function __s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'admission_mass_email');
  }

  private function _send_mass_mail($body,$title,$memberEmail,$fromemail,$files_array){
    $this->load->helper('email_helper');

      $files_string = '';
      if(!empty($files_array)) {
        $files_string = json_encode($files_array);
      }
    return sendEmail($body, $title, 0, $memberEmail, $fromemail, json_decode($files_string));
    }

    public function submit_follow_up_status(){
      $student_ids = array_unique($_POST['student_ids']);
      echo  $this->Admission_model->update_mass_followup_status($student_ids,$_POST['FollowupStatus'],$_POST['followup_action'],$_POST['remarks']);
    }

    public function send_sms_follow_up_status(){
      $this->load->helper('sms_helper');
      $student_ids = array_unique($_POST['student_ids']);
      $sms_content = $this->Admission_model->get_sms_content_by_id($_POST['sms_template']);
      $student_data = $this->Admission_model->get_student_mobile_numbers($student_ids);
      foreach($student_data as $key=>$val){
        $sms_content->content = str_replace("%%student_name%%",$val->student_name,$sms_content->content);
        $sms_content->content = str_replace("%%grade_applied_for%%",$val->grade_applied_for,$sms_content->content);
        sendToCustomNumbersForFollowups($val->mobile_numbers,$val->id, $sms_content->content,'Admission','Admission Parent');
      }
      echo  $this->Admission_model->update_mass_followup_status($student_ids,$_POST['FollowupStatus'],'SMS',$_POST['remarks']);
    }

    public function delete_consent_form(){
      echo $this->Admission_model->delete_consent_form($this->input->post('id'));
    }

    public function fees_autoAssign_default_credentials(){
      echo $this->Admission_model->fees_autoAssign_default_credentials($_POST['form_id'],$_POST['default_credentials']);
    }

    public function enable_partial_payment(){
      $stngId = $_POST['stngId'];
      $value = $_POST['value'];
      echo $this->Admission_model->enable_partial_payment($stngId,$value); 
    }

    public function get_admission_activities(){
      $result = $this->Admission_model->get_admission_activities($_POST['from_date'],$_POST['to_date'],$_POST['counselor']);
      echo json_encode($result);
    }

    public function get_admission_next_status(){
      $result = $this->Admission_model->get_admission_next_status($_POST['curr_status']);
      echo json_encode($result);
    }

    public function save_admission_documents(){
     
      $document_path = '';
      if(isset($_FILES['document_file'])){
        $document_path = $this->s3FileUpload($_FILES['document_file']);
      }
      $ackn_path = '';
      if(isset($_FILES['acknowledgement_file'])){
        $ackn_path = $this->s3FileUpload($_FILES['acknowledgement_file']);
      }
      $declaration_path = '';
      if(isset($_FILES['declaration_file'])){
        $declaration_path = $this->s3FileUpload($_FILES['declaration_file']);
      }

      echo $this->Admission_model->save_admission_documents($_POST,$document_path,$ackn_path,$declaration_path);
  }
}
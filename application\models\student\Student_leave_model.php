<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Student_leave_model
 *
 * <AUTHOR>
 */
class Student_leave_model extends CI_Model {
    private $yearId;
    private $current_branch;
    public function __construct() {
        // Call the CI_Model constructor
        parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearId();
        $this->current_branch = $this->authorization->getCurrentBranch();
    }

    public function getStudentNameInfo() {
        
        $user = $this->ion_auth->user()->result();
        $this->db->select('*');
        $this->db->join('avatar', 'avatar.user_id=users.id');
        $this->db->where('users.id', $user[0]->id);
        $this->db->from('users');
        $user_id = $this->db->get()->row();
        $this->db->select('*');
        $this->db->join('student_relation', 'student_relation.std_id=sa.id');
        $this->db->where('student_relation.id', $user_id->stakeholder_id);
        $this->db->from('student_admission sa');
        return $this->db->get()->row();
    }

    public function getStudentId($parent_id) {
        return $this->db->select('student_id')->where('id',$parent_id)->get('parent')->row()->student_id;
    }

    public function getStdIdByParentId($parentId) {
        $this->db->select('sy.id as stdId');
        $this->db->from('student_year sy');
        $this->db->join('student_admission sa', 'sa.id=sy.student_admission_id');
        $this->db->where("sa.id in (select std_id from student_relation where relation_id=$parentId)");
        return $this->db->get()->row()->stdId;
    }

    public function getStudentClassSectionInfo($param) {
        $this->db->select('*');
        $this->db->from('class_section');
        $this->db->join('student_year sy', 'sy.class_section_id=class_section.id');
        $this->db->where('sy.id', $param);
        return $this->db->get()->row();
    }

    // Get student's section ID by student_id for calendar validation
    public function getStudentSectionId($student_id) {
        $this->db->select('sy.class_section_id');
        $this->db->from('student_year sy');
        $this->db->join('student_admission sa', 'sa.id=sy.student_admission_id');
        $this->db->where('sa.id', $student_id);
        $this->db->where('sy.acad_year_id', $this->yearId);
        $result = $this->db->get()->row();
        return $result ? $result->class_section_id : null;
    }

    public function upload($filename = '', $upload_path) {
        $config['upload_path'] = $upload_path;
        $config['allowed_types'] = 'gif|jpg|png|jpeg|pdf|doc|xml|docx|GIF|JPG|PNG|JPEG|PDF|DOC|XML|DOCX|xls|xlsx';
        $config['remove_spaces'] = true;
        $config['overwrite'] = false;
        $this->load->library('upload', $config);
        $this->upload->initialize($config);
        if (!$this->upload->do_upload($filename)) {
            $error = array('status' => 'error', 'data' => $this->upload->display_errors());
            $this->session->set_flashdata('flashError', 'Failed to upload - ' . $filename);
            return $error;
        } else {
            $image = $this->upload->data();
            $success = array('status' => 'success', 'data' => $image);
            return $success;
        }
    }

    public function addStudentLeaveInfo($input, $path, $id=0) {
        $student_id = $input["student_id"];
        $leave_type = $input["leave_type"];
        $filedBy = $this->authorization->getAvatarId();
        // $filedBy = $input["filedBy"];
        $from_date_name = $input["from_date"];
        $fromdate = date("Y-m-d", strtotime($from_date_name));
        $to_date_name = $input["to_date"];
        $todate = date("Y-m-d", strtotime($to_date_name));
        // $sick_name = $input["sick_other"];
        $request_date = $input["request_date"];
        $requestdate = date("Y-m-d", strtotime($request_date));
        $reason_name = $input["reason"];
        $intervals = $input["noofdays"];
        $autoApproved = $this->settings->getSetting('student_leave_auto_approve');
        $status = 'Pending';
        $description = '';
        if (!empty($autoApproved) || $autoApproved == 1) {
            $status = 'Approved';
            $description = 'Auto Approved';
        }
        $studen_leave_db = array(
            'student_id' => $student_id,
            'leave_type' =>$leave_type,
            'leave_filed_by' =>$filedBy,
            'request_date' => $requestdate,
            'from_date' => $fromdate,
            'to_date' => $todate,
            'reason' => $reason_name,
            'status' => $status,
            'description' => $description,
            'noofdays' => $intervals,
            'acad_year_id' => $this->yearId
        );
        if($path['file_name'] != '') {
            $studen_leave_db = array_merge($studen_leave_db,['doctor_certificate' => $path['file_name']]);
        }
        if($id != 0){
            $this->db->where('id', $id);
            return $this->db->update('leave_student', $studen_leave_db);
        }
        return $this->db->insert('leave_student', $studen_leave_db);
    }

    public function getStudentLeaveInfo($avatarId) {
        $this->db->select("ls.id, ls.student_id, ls.leave_type, date_format(ls.request_date,'%d-%m-%Y') as request_date, ls.from_date, ls.to_date, ls.reason, ls.doctor_certificate, ls.status, ls.noofdays, ls.description, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as first_name, sy.class_id, sy.class_section_id");
        $this->db->from('leave_student ls');
        $this->db->join('student_admission sa','sa.id=ls.student_id');

        $this->db->join('student_year sy', 'sy.student_admission_id=sa.id');
        $this->db->where('ls.student_id', $avatarId);
        $this->db->where('sy.acad_year_id',$this->yearId);
        $this->db->order_by('ls.request_date','desc');
        return $this->db->get()->result();
        // echo $this->db->last_query();die();
        // echo "<pre>"  ;print_r($rr);die();
    }

    public function getStudentLeaveall($rowid) {

        $this->db->select('*');
        $this->db->join('student', 'student.id=leave_student.student_id');
        $this->db->where('leave_student.student_id', $rowid);
        $this->db->from('leave_student');

        return $this->db->get()->row();
    }

    public function getStudentLeaveInfoAll($classId, $SectionId) {
        $this->db->select('student.first_name,student.last_name,leave_student.id,leave_student.student_id,leave_student.request_date,leave_student.from_date,leave_student.to_date,leave_student.leave_type,leave_student.reason,leave_student.noofdays,leave_student.doctor_certificate,leave_student.status');
        $this->db->join('student', 'student.id=leave_student.student_id');
        $this->db->from('leave_student');
        return $this->db->get()->result();
    }

    public function get_student_leave_details(){
        $classTeacherId = '';
        if(!$this->authorization->isAuthorized('LEAVE.STUDENT_LEAVE_ADMIN')) {
           $classTeacherId =  $this->authorization->getAvatarStakeHolderId();
        }
        $this->db_readonly->select("ls.description,ls.id,ls.leave_type,ls.request_date,DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date,DATE_FORMAT(ls.to_date,'%d-%m-%Y') as to_date,ls.noofdays, ls.reason, CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS std_name, ls.student_id, ls.leave_filed_by, ls.leave_approved_by, concat(ifnull(c.class_name,''), '' ,ifnull(cs.section_name,'')) as classSection, doctor_certificate, (case when ls.status = 2 then 'Pending' else ls.status end) as status")
        ->from('leave_student ls')
        ->join('student_admission sa','ls.student_id=sa.id')
        ->join('student_year sy', 'sy.student_admission_id=sa.id')
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs','sy.class_section_id=cs.id')
        ->where('sy.acad_year_id',$this->yearId)
        ->where('ls.acad_year_id', $this->yearId)
        ->order_by('ls.from_date','desc');
        if ($classTeacherId) {
            $this->db_readonly->where('cs.class_teacher_id',$classTeacherId);
        }
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        return  $this->db_readonly->get()->result();


    }

    public function get_student_leave_details_filter($classId, $leave_status){
        $classTeacherId = '';
        if(!$this->authorization->isAuthorized('LEAVE.STUDENT_LEAVE_ADMIN')) {
           $classTeacherId =  $this->authorization->getAvatarStakeHolderId();
        }
        $this->db_readonly->select("ls.description,ls.id,ls.leave_type, date_format(ls.request_date,'%d-%m-%Y') as request_date,DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date,DATE_FORMAT(ls.to_date,'%d-%m-%Y') as to_date,ls.noofdays, ls.reason, CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS std_name, ls.student_id, ls.leave_filed_by, ls.leave_approved_by, concat(ifnull(c.class_name,''), '' ,ifnull(cs.section_name,'')) as classSection, doctor_certificate, (case when ls.status = 2 then 'Pending' else ls.status end) as status")
        ->from('leave_student ls')
        ->join('student_admission sa','ls.student_id=sa.id')
        ->join('student_year sy', 'sy.student_admission_id=sa.id')
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs','sy.class_section_id=cs.id')
        ->where('sy.acad_year_id',$this->yearId)
        ->where('ls.acad_year_id', $this->yearId)
        ->order_by('ls.request_date','desc');
        if ($classTeacherId) {
            $this->db_readonly->where('cs.class_teacher_id',$classTeacherId);
        }
        if ($classId) {
           $this->db_readonly->where_in('cs.id',$classId);
        }
        if ($leave_status) {
           $this->db_readonly->where('ls.status',$leave_status);
        }
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        return  $this->db_readonly->get()->result();
    }

    public function getStudentLeaves($sectionId) {
        $this->db_readonly->select("ls.description,ls.id,ls.leave_type,ls.request_date,DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date,DATE_FORMAT(ls.to_date,'%d-%m-%Y') as to_date,ls.noofdays, ls.reason, CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS std_name, ls.student_id, ls.leave_filed_by, ls.leave_approved_by, concat(ifnull(c.class_name,''), '' ,ifnull(cs.section_name,'')) as classSection, doctor_certificate, (case when ls.status = 2 then 'Pending' else ls.status end) as status")
        ->from('leave_student ls')
        ->join('student_admission sa','ls.student_id=sa.id')
        ->join('student_year sy', 'sy.student_admission_id=sa.id')
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs','sy.class_section_id=cs.id')
        ->where('sy.acad_year_id',$this->yearId)
        ->where('ls.acad_year_id', $this->yearId)
        ->order_by('ls.from_date','desc');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        return $this->db_readonly->get()->result();

        // $this->db->select('sa.first_name,sa.last_name,ls.*');
        // $this->db->from('leave_student ls');
        // $this->db->join('student_year sy', 'sy.id=ls.student_id');
        // $this->db->join('student_admission sa', 'sa.id=sy.student_admission_id');
        // return $this->db->get()->result();
    }

    public function getStudentLeavebyId($stuid, $rowid) {
        $this->db->select('sa.first_name,sa.last_name,leave_student.id,leave_student.student_id, date_format(leave_student.request_date,"%d-%m-%Y") as request_date, leave_student.description, date_format(leave_student.from_date,"%d-%m-%Y") as from_date, date_format(leave_student.to_date,"%d-%m-%Y") as to_date, leave_student.leave_type,leave_student.reason,leave_student.noofdays,leave_student.doctor_certificate,leave_student.status');
        $this->db->join('student_admission sa', 'sa.id=leave_student.student_id');
        $this->db->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
        $this->db->where('leave_student.student_id', $stuid);
        $this->db->where('leave_student.id', $rowid);
        $this->db->from('leave_student');
        return $this->db->get()->row();
        //echo "<pre>";print_r($ee);die();
    }

    public function getStudentInfoByLeaveId($id){
        $this->db->select('*');
        $this->db->where('id', $id);
        return $this->db->get('leave_student')->row();
    }

    public function updateleave($rowid, $sudid) {
        $status = $this->input->post('status');
        $data = array(
            'status' => $status,
            'description' => $this->input->post('description')
        );

        $this->db->where('student_id', $sudid);
        $this->db->where('id', $rowid);
        return $this->db->update('leave_student', $data);
    }

    public function deleteLeaveInfo($id){
        $this->db->where('id', $id);
        return $this->db->delete('leave_student');
    }

    public function getStudentLeaveColumnById($id, $column){
        $this->db->select($column);
        $this->db->where('id', $id);
        return $this->db->get('leave_student')->row();
    }

    public function getCountOfLeaves($stdId, $val=0){
        // echo $stdId; die();
        // $this->db->select('std_id');
        // $this->db->where('relation_id', $id);
        // $stdId = $this->db->get('student_relation')->row();
        if(!$val){
            $sql = "SELECT ifnull(SUM(`noofdays`),0) as `total` FROM `leave_student` WHERE `student_id`='$stdId' AND (`status`='Auto Approved' OR `status`='Approved')";
        } else {
            $sql = "SELECT ifnull(SUM(`noofdays`),0) as `total` FROM `leave_student` WHERE `student_id`=$stdId";
        }
        return $this->db->query($sql)->row()->total;
    }

    //get the class and section name to which the staff is class teacher
    public function getClassSection($id){
        $this->db->select('class_id, id as SectionId, section_name');
        $this->db->where('class_teacher_id', $id);
        return $this->db->get('class_section')->row();
    }

    //check leave already taken on this date
    public function checkAlreadyTaken($id = null){
        $stdId = $this->input->post('student_id');
        $from_date = date('Y-m-d', strtotime($this->input->post('from_date')));
        $to_date = date('Y-m-d', strtotime($this->input->post('to_date')));
        $check = "";
        if($id){
            $check = "AND `id`!='$id'";
        }

        $sql = "SELECT `id` FROM `leave_student` WHERE `student_id`=$stdId $check AND ((`from_date`<='$from_date' AND `to_date`>='$from_date') OR (`from_date`<='$to_date' AND `to_date`>='$to_date'))";
        $query = $this->db->query($sql);
        if($query->num_rows() == 0){
            return 1;
        }
        return 0;
    }

    //get holiday count
    public function getHoliday($date){
        $sql = "SELECT `id` FROM `school_calender` WHERE `event_type`!=1 AND `event_type`!=4 AND (`from_date`='$date' OR `to_date`='$date' OR (`from_date`<='$date' AND `to_date`>='$date'))";
        $query = $this->db->query($sql);
        return $query->num_rows();
    }

    public function addAdminAppliedLeave($path, $id){
        $input = $this->input->post();
        $student_id = $input["student_id"];
        $from_date_name = $input["from_date"];
        $fromdate = date("Y-m-d", strtotime($from_date_name));
        $to_date_name = $input["to_date"];
        $todate = date("Y-m-d", strtotime($to_date_name));
        $sick_name = $input["sick_other"];
        $request_date = $input["request_date"];
        $requestdate = date("Y-m-d", strtotime($request_date));
        $datetime1 = date_create($fromdate);
        $datetime2 = date_create($todate);
        $interval = date_diff($datetime2, $datetime1);
        $reason_name = $input["reason"];
        $intervals = $input["noofdays"];
        $status = 'Pending';

        $studen_leave_db = array(
            'student_id' => $student_id,
            'leave_filed_by' => $this->authorization->getAvatarId(),
            'leave_approved_by' => $this->authorization->getAvatarId(),
            'request_date' => $requestdate,
            'from_date' => $fromdate,
            'to_date' => $todate,
            'leave_type' => $sick_name,
            'reason' => $reason_name,
            'status' => $status,
            'noofdays' => $intervals,
            'acad_year_id' => $this->yearId
        );
        if($path['file_name'] != '') {
            $studen_leave_db = array_merge($studen_leave_db,['doctor_certificate' => $path['file_name']]);
        }
        $this->db->where('id',$id);
        return $this->db->update('leave_student', $studen_leave_db);
    }

    public function update_student_Leave_for_staff_by_id($path, $id){
        $input = $this->input->post();
        $student_id = $input["student_id"];
        $from_date_name = $input["from_date"];
        $fromdate = date("Y-m-d", strtotime($from_date_name));
        $to_date_name = $input["to_date"];
        $todate = date("Y-m-d", strtotime($to_date_name));
        $sick_name = $input["leave_type"];
        $request_date = $input["request_date"];
        $requestdate = date("Y-m-d", strtotime($request_date));
        $datetime1 = date_create($fromdate);
        $datetime2 = date_create($todate);
        $interval = date_diff($datetime2, $datetime1);
        $reason_name = $input["reason"];
        $intervals = $input["noofdays"];
        $status = 'Pending';

        $studen_leave_db = array(
            'student_id' => $student_id,
            'leave_filed_by' => $this->authorization->getAvatarId(),
            'leave_approved_by' => $this->authorization->getAvatarId(),
            'request_date' => $requestdate,
            'from_date' => $fromdate,
            'to_date' => $todate,
            'leave_type' => $sick_name,
            'reason' => $reason_name,
            'status' => $status,
            'noofdays' => $intervals,
            'acad_year_id' => $this->yearId
        );
        if($path['file_name'] != '') {
            $studen_leave_db = array_merge($studen_leave_db,['doctor_certificate' => $path['file_name']]);
        }
        $this->db->where('id',$id);
        return $this->db->update('leave_student', $studen_leave_db);
    }

    public function submit_student_Leave_for_staff($path){
        $input = $this->input->post();
        $student_id = $input["student_id"];
        $from_date_name = $input["from_date"];
        $fromdate = date("Y-m-d", strtotime($from_date_name));
        $to_date_name = $input["to_date"];
        $todate = date("Y-m-d", strtotime($to_date_name));
        $sick_name = $input["leave_type"];
        $request_date = date('Y-m-d');
        $requestdate = date("Y-m-d", strtotime($request_date));
        $datetime1 = date_create($fromdate);
        $datetime2 = date_create($todate);
        $interval = date_diff($datetime2, $datetime1);
        $reason_name = $input["reason"];
        $intervals = $input["noofdays"];
        $status = 'Pending';

        // Validate against assigned calendar
        $section_id = $input["section"];
        if ($section_id) {
            $calendar_validation = $this->_validateAgainstCalendar($section_id, $fromdate, $todate);
            if (!$calendar_validation['valid']) {
                // Return error code for calendar validation failure
                return 0;
            }
        }

        $studen_leave_db = array(
            'student_id' => $student_id,
            'leave_filed_by' => $this->authorization->getAvatarId(),
            'leave_approved_by' => $this->authorization->getAvatarId(),
            'request_date' => $requestdate,
            'from_date' => $fromdate,
            'to_date' => $todate,
            'leave_type' => $sick_name,
            'reason' => $reason_name,
            'status' => $status,
            'noofdays' => $intervals,
            'acad_year_id' => $this->yearId,
            'doctor_certificate'=> $path['file_name']
        );
        return $this->db->insert('leave_student', $studen_leave_db);
    }

    // Validate leave dates against assigned calendar
    private function _validateAgainstCalendar($section_id, $from_date, $to_date) {
        // Load Student_Model to get assigned calendar details
        $this->load->model('student/Student_Model');
        $calendar_details = $this->Student_Model->getassignedcalendardetails($section_id);

        if (!$calendar_details) {
            // No calendar assigned, allow leave
            return array('valid' => true, 'message' => 'No calendar assigned');
        }

        // Check if leave dates are within calendar range
        $calendar_start = new DateTime($calendar_details->start_date);
        $calendar_end = new DateTime($calendar_details->end_date);
        $leave_start = new DateTime($from_date);
        $leave_end = new DateTime($to_date);

        if ($leave_end < $calendar_start || $leave_start > $calendar_end) {
            return array(
                'valid' => false,
                'message' => 'Leave dates are outside the assigned calendar range (' .
                           date('d-m-Y', strtotime($calendar_details->start_date)) . ' to ' .
                           date('d-m-Y', strtotime($calendar_details->end_date)) . ')'
            );
        }

        return array('valid' => true, 'message' => 'Leave dates are valid');
    }

    public function get_Leaves($val=0){
        $this->db->select("ls.description,ls.id,ls.leave_type,ls.request_date,DATE_FORMAT(ls.from_date,'%D %b %Y') as from_date,DATE_FORMAT(ls.to_date,'%D %b %Y') as to_date,ls.noofdays, ls.reason, CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS std_name, ls.student_id, ls.leave_filed_by, ls.leave_approved_by, concat(ifnull(c.class_name,''), '' ,ifnull(cs.section_name,'')) as classSection, (case when ls.status = 2 then 'Pending' else ls.status end) as status ")
        ->from('leave_student ls')
        ->join('student_admission sa','ls.student_id=sa.id')
        ->join('student_year sy', 'sy.student_admission_id=sa.id')
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs','sy.class_section_id=cs.id')
        ->where('sy.acad_year_id',$this->yearId)
        ->where('ls.acad_year_id', $this->yearId);
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if($val == 1) {
            $this->db->where("ls.leave_filed_by", $this->authorization->getAvatarId());
        }
        return $this->db->get()->result();
    }

    public function getStudentsBySection($sectionId) {
        $this->db->select("sa.id, CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS std_name");
        $this->db->from('student_year sy');
        $this->db->join('student_admission sa', 'sa.id=sy.student_admission_id');
        $this->db->where('sa.admission_status', '2'); 
        $this->db->where('sy.promotion_status!=', '4'); 
        $this->db->where('sy.promotion_status!=', '5'); 
        $this->db->where('sy.acad_year_id',$this->yearId);
        $this->db->where('sy.class_section_id', $sectionId);
        return $this->db->get()->result();
    }

    public function get_apply_student_leave_by_id($id){
        $this->db_readonly->select("ls.*, c.id as class_id, cs.id as class_section_id, DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date, DATE_FORMAT(ls.to_date,'%d-%m-%Y') as to_date")
        ->from('leave_student ls')
        ->where('ls.id',$id)
        ->join('student_admission sa','ls.student_id=sa.id')
        ->join('student_year sy', 'sy.student_admission_id=sa.id')
        ->where('sy.acad_year_id',$this->yearId)
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs','sy.class_section_id=cs.id');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        return $this->db_readonly->get()->row();

    }

    public function get_ClassSection_list(){
         $this->db_readonly->select("cs.id, CONCAT(ifnull(c.class_name,''),' - ', ifnull(cs.section_name,'')) as class_name");
        $this->db_readonly->from('class_section as cs');
        $this->db_readonly->join('class c', "c.id = cs.class_id and c.acad_year_id=$this->yearId");
        $this->db_readonly->order_by('c.id, cs.id');
        $this->db_readonly->where('cs.is_placeholder!=1');

        $result = $this->db_readonly->get()->result();

        return $result;
    }

    public function getLeaveReportData($date, $class_section_id = null, $status = 'All') {
        $this->db_readonly->select("
            ls.id,
            CONCAT(IFNULL(sy.roll_no, ''), ' - ', IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) AS student_name_with_roll,
            CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) AS student_name,
            c.class_name as grade,
            cs.section_name as section,
            IFNULL(sa.admission_no, '-') as admission_no,
            IFNULL(sa.enrollment_number, '-') as enrollment_no,
            DATE_FORMAT(ls.from_date, '%d-%m-%Y') as from_date,
            DATE_FORMAT(ls.to_date, '%d-%m-%Y') as to_date,
            ls.leave_type,
            ls.reason,
            (CASE WHEN ls.status = 2 THEN 'Pending' ELSE ls.status END) as status,
            sa.id as student_admission_id,
            sy.class_section_id,
            sy.roll_no
        ");

        $this->db_readonly->from('leave_student ls');
        $this->db_readonly->join('student_admission sa', 'ls.student_id = sa.id');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id = sa.id');
        $this->db_readonly->join('class c', 'sy.class_id = c.id');
        $this->db_readonly->join('class_section cs', 'sy.class_section_id = cs.id');

        // Filter by date - check if the selected date falls within the leave period
        $this->db_readonly->where("DATE('$date') BETWEEN DATE(ls.from_date) AND DATE(ls.to_date)");

        // Filter by class section if specified
        if (!empty($class_section_id) && $class_section_id !== '') {
            $this->db_readonly->where('sy.class_section_id', $class_section_id);
        }

        // Filter by status if not 'All'
        if ($status !== 'All') {
            if ($status === 'Pending') {
                $this->db_readonly->where('ls.status', '2');
            } elseif ($status === 'Approved') {
                $this->db_readonly->where_in('ls.status', ['Approved', 'Auto Approved']);
            } else {
                $this->db_readonly->where('ls.status', $status);
            }
        }

        $this->db_readonly->where('sy.acad_year_id', $this->yearId);

        // Add branch filtering if current_branch is set
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id', $this->current_branch);
        }

        $this->db_readonly->order_by('c.class_name, cs.section_name, sy.roll_no');

        return $this->db_readonly->get()->result();
    }

    public function getStudentAttendanceStatus($student_admission_id, $date, $class_section_id) {
        // Load settings to check attendance type
        $this->load->library('settings');
        $attendance_type = $this->settings->getSetting('student_attendance_type');

        $attendance_status = 'Not Taken';

        if ($attendance_type == '' || $attendance_type == 'subject_wise') {
            // For subject-wise attendance, check the first period attendance
            $sql = "SELECT s.status
                    FROM attendance_v2_master m
                    JOIN attendance_v2_student s ON s.attendance_v2_master_id = m.id
                    WHERE m.date = '$date'
                    AND s.student_admission_id = $student_admission_id
                    AND m.type = 0
                    AND s.status != 0
                    ORDER BY m.period_no ASC, m.taken_on ASC
                    LIMIT 1";

            $result = $this->db_readonly->query($sql)->row();

            if ($result) {
                switch ($result->status) {
                    case 1:
                        $attendance_status = 'Present';
                        break;
                    case 2:
                        $attendance_status = 'Absent';
                        break;
                    case 3:
                        $attendance_status = 'Late';
                        break;
                    default:
                        $attendance_status = 'Not Taken';
                }
            }
        } else {
            // For day-wise attendance
            $sql = "SELECT s.morning_session_status, s.afternoon_session_status
                    FROM attendance_std_day_v2_students s
                    JOIN attendance_std_day_v2_session sess ON sess.id = s.attendance_day_v2_session_id
                    WHERE s.student_admission_id = $student_admission_id
                    AND sess.att_taken_date = '$date'
                    AND sess.class_section_id = $class_section_id";

            $result = $this->db_readonly->query($sql)->row();

            if ($result) {
                if ($result->morning_session_status == 1 && $result->afternoon_session_status == 1) {
                    $attendance_status = 'Present';
                } elseif ($result->morning_session_status == 0 && $result->afternoon_session_status == 0) {
                    $attendance_status = 'Absent';
                } else {
                    $attendance_status = 'Half Day';
                }
            }
        }

        return $attendance_status;
    }

}
